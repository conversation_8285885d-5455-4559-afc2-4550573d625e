load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "fulfillment",
    srcs = ["fulfillment.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/fulfillment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "fulfillment_test",
    srcs = ["fulfillment_test.go"],
    embed = [":fulfillment"],
    deps = [
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/fulfillment/mock",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
        "@tools_gotest_v3//assert",
    ],
)
