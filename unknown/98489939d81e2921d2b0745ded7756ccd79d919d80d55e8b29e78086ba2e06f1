load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "petpb_proto",
    srcs = [
        "pet.proto",
        "pet_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_envoyproxy_protoc_gen_validate//validate:validate_proto",
        "@com_google_protobuf//:empty_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:field_behavior_proto",
    ],
)

go_proto_library(
    name = "petpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "//:pgv_plugin_go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/pet/v1",
    proto = ":petpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@com_envoyproxy_protoc_gen_validate//validate:go_default_library",
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
    ],
)

go_library(
    name = "pet",
    embed = [":petpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/pet/v1",
    visibility = ["//visibility:public"],
)
