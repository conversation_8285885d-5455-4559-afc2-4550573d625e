# Bazel简介

**Bazel** 是一个由 Google 开发并开源的构建工具，广泛应用于构建和测试各种编程语言的代码库，特别是在大规模复杂项目中表现优异，能够快速高效地处理各种规模的代码库。它支持多种语言，包括 Java、C++、Python 和 Go 等，能够实现跨平台构建。

## Bazel产生的背景

1. 开源成为当前软件开发的主旋律。哪怕你是商业软件，也逃离不了社区的包围。如何方便地获取依赖，并做到平滑升级很重要。
2. 混合多语言编程成为一种选择。每种语言都有自己适用的场景，但是构建多语言的软件系统非常具有挑战性。例如，Python社区很喜欢搭配C/C++，高性能计算扔个Ｃ/C++，Python提供编程接口。
3. 代码复用：只想复用第三方的一个头文件，而不是整个系统，拒绝拷贝是优秀程序员的基本素养。
4. 增量构建：当只修改了一行代码，构建系统能够准确计算需要构建的依赖目标，而不是全构建；否则生命都浪费在编译上了。

## 特性与优势

- **速度快**：bazel 支持**增量编译**，只重新编译必须的部分，即通过依赖分析，只编译修改过的部分及其影响的路径。
  - bazel 也支持**并行编译**。将没有依赖的部分进行并行执行，可以通过`--jobs`来指定并行流的个数，一般可以是你机器`CPU`的个数。遇到大项目马力全开时，`Bazel`能把你机器的`CPU`各个核都吃满。
  - **分布式/本地缓存**，`Bazel`将构建过程视为函数式的，只要输入给定，那么输出就是一定的。而不会随着构建环境的不同而改变（当然这需要做一些限制），这样就可以分布式的缓存/复用不同模块，这点对于超大项目的速度提升极为明显。
- **伸缩性强**：bazel可用来构建Java C++ Android ios等很多语言和框架，并支持mac windows linux等不同平台
  - `Bazel`号称无论什么量级的项目都可以应对，无论是超大型单体项目`monorepo`、还是超多库的分布式项目`multirepo`。`Bazel`还可以很方便的集成`CD/CI` ，并在云端利用分布式环境进行构建。
  - 它使用沙箱机制进行编译，即将所有编译依赖隔绝在一个沙箱中，比如编译`golang`项目时，不会依赖你本机的`GOPATH`，从而做到同样源码、跨环境编译、输出相同，即构建的确定性。
- **跨语言**：如果一个项目不同模块使用不同的语言，利用`Bazel`可以使用一致的风格来管理项目外部依赖和内部依赖。典型的项目如 Ray。该项目使用`C++`构建`Ray`的核心调度组件、通过`Python/Java`来提供多语言的`API`，并将上述所有模块用单个`repo`进行管理。如此组织使其项目整合相当困难，但`Bazel`在此处理的游刃有余，大家可以去该`repo`一探究竟。
- **可拓展性**：`Bazel`使用的语法是基于`Python`裁剪而成的一门语言：`Startlark`。其表达能力强大，往小了说，可以使用户自定义一些`rules`（类似一般语言中的函数）对构建逻辑进行复用；往大了说，可以支持第三方编写适配新的语言或平台的`rules`集，比如`rules go`。 `Bazel`并不原生支持构建`golang`工程，但通过引入`rules go` ，就能以比较一致的风格来管理`golang`工程。

# Bazel 中的主要文件

使用`Bazel`管理的项目一般包含以下几种`Bazel`相关的文件：`WORKSPACE/BUILD(.bazel)/.bzl` 和 `.bazelrc` 等。其中 `WORKSPACE` 和 `.bazelrc` 一般放置于项目的根目录下，`BUILD.bazel` 放项目中的每个文件夹中（包括根目录），`.bzl`文件可以根据用户喜好自由放置，一般可放在项目根目录下的某个专用文件夹（比如 build）中。

> 如果bazel version > 7.0 官方更推荐使用 MODULE.bazel 代替WORKSPACE MoeGo项目中使用 bazel version 为 8.0, 使用MODULE.bazel 管理依赖

## WORKSPACE/MODULE.bazel

主要作用是定义项目根目录和项目名，并管理、加载 Bazel 工具、rules 集和项目外部依赖库。

下面是Moego 仓库中使用的MODULE.bazel

```Bash
###############################################################################
# Bazel now uses Bzlmod by default to manage external dependencies.
# Please consider migrating your external dependencies from WORKSPACE to MODULE.bazel.
#
# For more details, please check https://github.com/bazelbuild/bazel/issues/18958
###############################################################################

module(
    name = "moego",
    version = "0.1",
)
# bazel 依赖
bazel_dep(name = "bazel_features", version = "1.21.0")
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "rules_go", version = "0.50.1", repo_name = "io_bazel_rules_go")
bazel_dep(name = "rules_proto", version = "7.0.2")
bazel_dep(name = "gazelle", version = "0.40.0")
bazel_dep(name = "grpc", version = "1.66.0", repo_name = "com_github_grpc_grpc")
bazel_dep(name = "protobuf", version = "29.1", repo_name = "com_google_protobuf")
bazel_dep(name = "rules_license", version = "1.0.0")
bazel_dep(name = "rules_shell", version = "0.2.0")
bazel_dep(name = "rules_buf", version = "0.3.0")
bazel_dep(name = "protoc-gen-validate", version = "1.0.4", repo_name = "com_envoyproxy_protoc_gen_validate")

buf = use_extension("@rules_buf//buf:extensions.bzl", "buf")

# Override the default version of buf
buf.toolchains(version = "v1.32.1")
# bazel 编译使用的golang版本，与go mod 同步
go_sdk = use_extension("@io_bazel_rules_go//go:extensions.bzl", "go_sdk")
go_sdk.download(version = "1.23.0")
use_repo(
    go_sdk,
    "go_toolchains",
    "io_bazel_rules_nogo",
)

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
go_deps.gazelle_override(
    directives = [
        "gazelle:proto disable",
    ],
    path = "github.com/MoeGolibrary/moego-api-definitions",
)
# bazel 根据go mod 自动生成的目录
use_repo(
    go_deps,
    "com_github_bytedance_sonic",
    "com_envoyproxy_protoc_gen_validate",
    "com_github_google_uuid",
    "com_github_moegolibrary_go_lib",
    "com_github_moegolibrary_moego_api_definitions",
    "com_github_spf13_cast",
    "com_github_stretchr_testify",
    "in_gopkg_datadog_dd_trace_go_v1",
    "in_gopkg_yaml_v3",
    "io_gorm_driver_mysql",
    "io_gorm_driver_postgres",
    "io_gorm_gorm",
    "org_golang_google_grpc",
    "org_golang_google_protobuf",
    "org_uber_go_automaxprocs",
    "org_uber_go_zap",
)

register_toolchains("@io_bazel_rules_go//go:all")
```

## BUILD.bazel

存在于根目录以及源文件所在目录，用来标记源文件编译以及依赖情况，一般是自动生成。拿 go 来说，构建目标可以是 `go_binary、go_test、go_library `等。

### 根目录下的BUILD.bazel 

一般用于指引bazel build 执行时的行为，比如以下为moego 项目根目录下的BUILD.bazel文件（详情请看注释）

```Bash
# 加载bazel build的库
load("@gazelle//:def.bzl", "gazelle", "gazelle_binary")
load("@io_bazel_rules_go//proto:compiler.bzl", "go_proto_compiler")

exports_files(
    ["buf.yaml"],
    visibility = ["//visibility:public"],
)
## 注意！！！ "# gazelle" 不是注释，是bazel gazelle的语法
## 这里是bazel gazelle 提供的一些语法，包括标记prefix、指定compiler、resolve 指定内容

# gazelle:prefix github.com/MoeGolibrary/moego
# gazelle:go_grpc_compilers        @io_bazel_rules_go//proto:go_proto,@io_bazel_rules_go//proto:go_grpc_v2, //:pgv_plugin_go
# gazelle:exclude **/*.pb.validate.go
# gazelle:resolve proto validate/validate.proto @com_envoyproxy_protoc_gen_validate//validate:validate_proto
# gazelle:resolve proto go validate/validate.proto @com_envoyproxy_protoc_gen_validate//validate:go_default_library
### # gazelle:resolve go github.com/envoyproxy/protoc-gen-validate @com_envoyproxy_protoc_gen_validate//:protoc-gen-validate
gazelle(
    name = "gazelle",
    # gazelle = ":gazelle-buf",
    prefix = "github.com/MoeGolibrary/moego",
)
# 配置gazelle 行为，这里配置了一个命令 bazel run //:gazelle-update-repos
# 作用是根据 go.mod 生成依赖
gazelle(
    name = "gazelle-update-repos",
    args = [
        "-from_file=go.mod",
        "-to_macro=deps.bzl%go_dependencies",
        "-build_file_proto_mode=disable",
        "-prune",
    ],
    command = "update-repos",
    # gazelle = ":gazelle-buf",
)

# 自定义了一个go_protot compiler, 指定编译proto=>go时需要用上哪些依赖/插件
go_proto_compiler(
    name = "pgv_plugin_go",
    options = ["lang=go"],
    plugin = "@com_envoyproxy_protoc_gen_validate//:protoc-gen-validate",
    suffix = ".pb.validate.go",
    valid_archive = False,
    visibility = ["//visibility:public"],
)
```

### 子目录下的BUILD.bazel

#### Golang

**由bazel自动生成，**用于指引当前目录中`.go`、`.proto`文件编译时使用的plugins、deps

举个例子以下是todo目录下的BUILD.bazel, 同级文件还有`main.go`

```Python
load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
## bazel 会检查出目录中包含*.go, 生成go_library
go_library(
    name = "todo_lib",
    ## 该目录下所有.go结尾的文件
    srcs = ["main.go"],
    ## 以该项目为root的全局路径
    importpath = "github.com/MoeGolibrary/moego/backend/app/todo",
    ## 可见性
    visibility = ["//visibility:private"],
    ## srcs 列表文件中的依赖
    deps = [
        "//backend/app/todo/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/rpc",
        "//backend/common/rpc/rpc/log",
        "//backend/proto/todo/v1:todo",
    ],
)
## 生成的二进制
go_binary(
    name = "todo",
    embed = [":todo_lib"],
    visibility = ["//visibility:public"],
)
```

#### Proto

**也是由bazel自动生成**，以下是`proto`目录下的`BUILD.bazel`文件，同级目录还有`todo.proto`

```Python
load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

## 与golang library一样，标记了当前目录下的文件以及依赖
proto_library(
    name = "todopb_proto",
    srcs = ["todo.proto"],
    visibility = ["//visibility:public"],
    deps = ["@com_envoyproxy_protoc_gen_validate//validate:validate_proto"],
)

## 指引bazel 如何编译proto 文件
go_proto_library(
    name = "todopb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "//:pgv_plugin_go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/todo/v1",
    proto = ":todopb_proto",
    visibility = ["//visibility:public"],
    deps = ["@com_envoyproxy_protoc_gen_validate//validate:go_default_library"],
)

## 因为目录下有*.go 文件，bazel会生成go_library
go_library(
    name = "todo",
    embed = [":todopb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/todo/v1",
    visibility = ["//visibility:public"],
)
```

### 自定义 rule (*.bzl)

如果项目有一些复杂构造逻辑、或者一些需要复用的构造逻辑，那么可以将这些逻辑以函数形式保存在 `.bzl` 文件，供`WORKSPACE/MODULE.bazel`或者`BUILD`文件调用。其语法跟`Python`类似：

> 一般不需要手写，由bazel 根据MODULE.bazel 生成

```Plain
def go_dependencies():
    go_repository(
        name = "build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go",
        build_file_proto_mode = "disable",
        importpath = "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go",
        sum = "h1:AmmAwHbvaeOIxDKG2+aTn5C36HjmFIMkrdTp49rp80Q=",
        version = "v1.32.0-20240221180331-f05a6f4403ce.1",
    )
    ...
```

# 在项目中如何使用Bazel

以Moego来举例子，Moego是golang 和 proto 一体的仓库，bazel 并不能直接直接编译golang，所以依赖中需要引入相关rules，在MODULE.bazel中添加：

```Python
bazel_dep(name = "rules_go", version = "0.51.0", repo_name = "io_bazel_rules_go")
bazel_dep(name = "rules_proto", version = "7.0.2")
bazel_dep(name = "gazelle", version = "0.40.0")
bazel_dep(name = "grpc", version = "1.66.0", repo_name = "com_github_grpc_grpc")
bazel_dep(name = "protobuf", version = "29.1", repo_name = "com_google_protobuf")
```

查看下todo目录

```Python
/backend/app/todo
├── CODEOWNERS
├── README.md
├── config.yaml
├── e2e
│   ├── e2e.go
│   └── todo
│       └── todo_test.go
├── logic
│   └── greeter
│       ├── entity
│       │   └── entity.go
│       └── greeter_logic.go
├── main.go
├── repo
│   ├── gorm
│   │   ├── entity
│   │   │   └── entity.go
│   │   └── gorm.go
│   ├── repo.go
│   └── stripe
│       └── client.go
├── service
│   └── greeter_service.go
└── utils
    └── utils.go
```

使用gazelle 自动生成BUILD.bazel文件

```Bash
./backend/app/todo
├── BUILD.bazel
├── CODEOWNERS
├── README.md
├── config.yaml
├── e2e
│   ├── BUILD.bazel
│   ├── e2e.go
│   └── todo
│       ├── BUILD.bazel
│       └── todo_test.go
├── logic
│   └── greeter
│       ├── BUILD.bazel
│       ├── entity
│       │   ├── BUILD.bazel
│       │   └── entity.go
│       └── greeter_logic.go
├── main.go
├── repo
│   ├── BUILD.bazel
│   ├── gorm
│   │   ├── BUILD.bazel
│   │   ├── entity
│   │   │   ├── BUILD.bazel
│   │   │   └── entity.go
│   │   └── gorm.go
│   ├── repo.go
│   └── stripe
│       ├── BUILD.bazel
│       └── client.go
├── service
│   ├── BUILD.bazel
│   └── greeter_service.go
└── utils
    ├── BUILD.bazel
    └── utils.go
```

此时在`main.go` 同级的`BUILD.bazel` 下可以看到

```Bash
go_binary(
    name = "todo", 
    embed = [":todo_lib"],
    visibility = ["//visibility:public"],
)
```

## 编译目录

```Bash
# 这个命令是编译backend目录下所有文件夹
make build
# 独立编译, 比如编译backend/app/todo目录下所有文件
bazelisk --bazelrc=./bazel/mac.bazelrc build //backend/app/todo/...
```

通过`main.go`编译的二进制文件会保存在`bazel`的输出目录下，可以通过以下命令找到

```Bash
❯ bazelisk --bazelrc=./bazel/mac.bazelrc info bazel-bin
/private/var/tmp/_bazel_jett/78787156c78b677e4defbc4dd6ef9d37/execroot/_main/bazel-out/darwin_arm64-fastbuild/bin

❯ ls -l $(bazelisk --bazelrc=./bazel/mac.bazelrc info bazel-bin)/backend/app/todo
total 35008
drwxr-xr-x  5 <USER>  <GROUP>       160 12 20 16:35 e2e
drwxr-xr-x  3 <USER>  <GROUP>        96 12 20 16:35 logic
drwxr-xr-x  6 <USER>  <GROUP>       192 12 20 16:35 repo
drwxr-xr-x  4 <USER>  <GROUP>       128 12 20 16:36 service
-r-xr-xr-x  1 <USER>  <GROUP>  17706178 12 20 17:49 todo-server
-rwxr-xr-x  1 <USER>  <GROUP>     41518 12 20 17:49 todo-server-0.params
-r-xr-xr-x  1 <USER>  <GROUP>        13 12 20 17:49 todo-server.repo_mapping
drwxr-xr-x  5 <USER>  <GROUP>       160 12 20 17:49 todo-server.runfiles
-r-xr-xr-x  1 <USER>  <GROUP>       348 12 20 17:49 todo-server.runfiles_manifest
-r-xr-xr-x  1 <USER>  <GROUP>     51740 12 20 16:36 todo.a
-r-xr-xr-x  1 <USER>  <GROUP>     27854 12 20 16:36 todo.x
drwxr-xr-x  7 <USER>  <GROUP>       224 12 20 16:36 todo_
-r-xr-xr-x  1 <USER>  <GROUP>     51950 12 20 16:36 todo_lib.a
-r-xr-xr-x  1 <USER>  <GROUP>     27906 12 20 16:36 todo_lib.x
drwxr-xr-x  4 <USER>  <GROUP>       128 12 20 16:35 utils
```

## 运行项目

常规来说，不需要专门进入到out目录去找到可执行文件，build后可以通过bazel 提供的run 命令直接执行

```Bash
bazelisk --bazelrc=./bazel/mac.bazelrc run //backend/app/todo:todo

################################################输出：
INFO: Analyzed target //t1:t1 (0 packages loaded, 0 targets configured).
INFO: Found 1 target...
Target //t1:t1 up-to-date:
  bazel-bin/t1/t1_/t1
INFO: Elapsed time: 0.486s, Critical Path: 0.33s
INFO: 3 processes: 1 internal, 2 darwin-sandbox.
INFO: Build completed successfully, 3 total actions
INFO: Build completed successfully, 3 total actions
hello todo
```

## 测试(主要是单元测试)

根据golang的规范，当在一个logic模块执行写单测时，通常会写在`*_test.go` 文件中，`bazel`也对该模式提供了支持，举个栗子🌰：

```bash
...
├── logic
│   └── greeter
│       ├── BUILD.bazel
│       ├── entity
│       │   ├── BUILD.bazel
│       │   └── entity.go
│       ├── greeter_logic.go
│       └── greter_logic_test.go # 在logic层中添加对greeter_logic的单测文件
...
```

添加后，通过gazelle更新`BUILD.bazel`，可以看到`BUILD`文件中新增以下模块：

```python
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

...

go_test(
    name = "greeter_test",
    srcs = ["greter_logic_test.go"],
)

```

该模块指引`bazel`此处存在一（多）个`go_test`的文件，可以使用bazel提供的test命令执行单测

```bash
❯ bazelisk --bazelrc=./bazel/mac.bazelrc test //backend/app/todo/...
INFO: Analyzed 13 targets (2 packages loaded, 626 targets configured).
INFO: Found 11 targets and 2 test targets...
INFO: Elapsed time: 1.576s, Critical Path: 0.05s
INFO: 1 process: 1207 action cache hit, 1 internal.
INFO: Build completed successfully, 1 total action
//backend/app/todo/e2e/todo:todo_test                           (cached) PASSED in 0.0s
//backend/app/todo/logic/greeter:greeter_test                   (cached) PASSED in 0.0s

Executed 0 out of 2 tests: 2 tests pass.
There were tests whose specified size is too big. Use the --test_verbose_timeout_warnings command line option to see which ones these are.
```

当遇到单测失败时，bazel也会输出相对应日志

```bash
❯ bazelisk --bazelrc=./bazel/mac.bazelrc test //backend/app/todo/...
INFO: Analyzed 13 targets (0 packages loaded, 0 targets configured).
FAIL: //backend/app/todo/logic/greeter:greeter_test (Exit 1) (see /private/var/tmp/_bazel_jett/78787156c78b677e4defbc4dd6ef9d37/execroot/_main/bazel-out/darwin_arm64-fastbuild/testlogs/backend/app/todo/logic/greeter/greeter_test/test.log)
INFO: From Testing //backend/app/todo/logic/greeter:greeter_test:
==================== Test output for //backend/app/todo/logic/greeter:greeter_test:
--- FAIL: TestGreeter2 (0.00s)
    greter_logic_test.go:12: Hello, World!222
FAIL
================================================================================
INFO: Found 11 targets and 2 test targets...
INFO: Elapsed time: 0.759s, Critical Path: 0.58s
INFO: 7 processes: 1 action cache hit, 1 internal, 6 darwin-sandbox.
INFO: Build completed, 1 test FAILED, 7 total actions
//backend/app/todo/e2e/todo:todo_test                           (cached) PASSED in 0.0s
//backend/app/todo/logic/greeter:greeter_test                            FAILED in 0.0s
  /private/var/tmp/_bazel_jett/78787156c78b677e4defbc4dd6ef9d37/execroot/_main/bazel-out/darwin_arm64-fastbuild/testlogs/backend/app/todo/logic/greeter/greeter_test/test.log

Executed 1 out of 2 tests: 1 test passes and 1 fails locally.
There were tests whose specified size is too big. Use the --test_verbose_timeout_warnings command line option to see which ones these are.

❯ cat /private/var/tmp/_bazel_jett/78787156c78b677e4defbc4dd6ef9d37/execroot/_main/bazel-out/darwin_arm64-fastbuild/testlogs/backend/app/todo/logic/greeter/greeter_test/test.log
exec ${PAGER:-/usr/bin/less} "$0" || exit 1
Executing tests from //backend/app/todo/logic/greeter:greeter_test
-----------------------------------------------------------------------------
--- FAIL: TestGreeter2 (0.00s)
    greter_logic_test.go:12: Hello, World!222
FAIL
```
