package com.moego.server.retail.api;

import com.moego.server.grooming.dto.report.ReportWebAppointment;
import com.moego.server.grooming.params.ReportWebApptsRequest;
import com.moego.server.retail.dto.ReportProduct;
import com.moego.server.retail.dto.ReportWebRetailSale;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IRetailReportService {
    @Operation(summary = "获取 report 所关联的 production 列表")
    @GetMapping("/service/retail/report/getProductList")
    List<ReportProduct> getProductList(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startSeconds") Long startSeconds,
            @RequestParam("endSeconds") Long endSeconds);

    @Operation(summary = "获取 report 所关联的 sale 列表")
    @GetMapping("/service/retail/report/getWebSaleList")
    List<ReportWebRetailSale> getWebSaleList(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("reportId") Integer reportId,
            @RequestParam("startSeconds") Long startSeconds,
            @RequestParam("endSeconds") Long endSeconds);

    @Operation(summary = "根据reportID， 获取 report 所关联的 sale列表")
    @PostMapping("/service/retail/report/getSaleOnlyProduct")
    List<ReportWebAppointment> getSaleOnlyProduct(@RequestBody ReportWebApptsRequest request);
}
