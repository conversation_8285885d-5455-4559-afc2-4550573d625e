package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraA2pFailedDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SystemA2pFailedParams extends NotificationParams {

    private String title = "A2P 10DLC Registration just failed";
    private String type = NotificationEnum.TYPE_SYSTEM_A2P_REGISTER_FAIL;
    private NotificationExtraA2pFailedDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "A2P 10DLC Registration just failed";
    private String mobilePushBody = "{failReason}. Please verify and update your information.";
}
