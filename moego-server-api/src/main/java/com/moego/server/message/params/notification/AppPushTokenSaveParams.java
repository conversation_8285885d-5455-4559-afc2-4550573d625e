package com.moego.server.message.params.notification;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AppPushTokenSaveParams {

    private String pushToken;

    @Schema(description = "ios 或 android")
    private String deviceType;

    @Schema(description = "推送id所属sdk 1 expo(默认)  2 原生推送")
    private Byte tokenType;

    @Schema(description = "需要移除的旧expo token")
    private String oldExpoToken;
}
