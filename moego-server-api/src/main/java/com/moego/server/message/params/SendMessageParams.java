package com.moego.server.message.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Data
@Accessors(chain = true)
public class SendMessageParams {

    @NotNull
    private Long businessId;

    /**
     * Sender staff id
     */
    @NotNull
    private Long staffId;
    /**
     * @see com.moego.server.message.enums.MessageDetailEnum
     * such as MESSAGE_TYPE_TEXT, MESSAGE_TYPE_PIC
     */
    private Integer messageType;

    /**
     * @see com.moego.server.message.enums.MessageDetailEnum
     * Such as MESSAGE_METHOD_MSG, MESSAGE_METHOD_EMAIL, MESSAGE_METHOD_CALL, MESSAGE_METHOD_APP
     */
    private Integer messageMethod;

    /**
     * @see com.moego.server.message.enums.MessageDetailEnum
     * Such as MESSAGE_SOURCE_AUTO, MESSAGE_SOURCE_CUTOMER_EMAIL, MESSAGE_SOURCE_EMPLOYEE,
     * MESSAGE_SOURCE_MESSAGE_CENTER, MESSAGE_SOURCE_CODE, MESSAGE_SOURCE_VOICE_REPLY
     */
    private Integer messageSource;

    /**
     * Message body
     */
    @NotNull
    private String messageBody;

    /**
     * Recipient customer and phone number
     */
    @NotNull
    private SendMessageCustomerParams customer;
}
