package com.moego.server.customer.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/11 7:30 PM
 */
@Data
public class IntakeFormDetailUpdateParam {

    @NotNull
    private Integer formDetailId;

    private Integer formId;

    @Size(max = 1000)
    private String question;

    @Size(max = 255)
    private String placeholder;

    private Byte questionType;

    private Byte isShow;

    private Byte isRequired;

    private Byte type;

    private Byte isAllowDelete;

    private Byte isAllowChange;

    private Byte isAllowEdit;

    private Integer sort;

    private String extraJson;
    /**
     * for intake form client
     */
    private String key;
}
