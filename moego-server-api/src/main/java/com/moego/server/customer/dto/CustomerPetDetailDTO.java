package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020-06-14 12:25
 */
@Data
@Accessors(chain = true)
public class CustomerPetDetailDTO {

    private Integer petId;
    private String petName;
    private Integer businessId;
    private Long companyId;
    private Integer customerId;
    private Integer petTypeId;
    private String typeName;
    private String petBreed;
    private String avatarPath;
    private Integer lifeStatus;
    private String deactivateReason;
    private String breed;
    private Integer breedMix;
    private String birthday;
    private Integer gender;
    private String hairLength;
    private String weight;
    private String fixed;
    private String behavior;
    private Byte expiryNotification;
    private Byte status;

    private String vetName;
    private String vetPhone;
    private String vetAddress;
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String healthIssues;

    private String petAppearanceColor;
    private String petAppearanceNotes;

    // 宠物上传的image
    private String petPhotoImage;
    private List<VaccineBindingRecordDto> vaccineList;

    private Long playgroupId;

    /**
     * MoeGo platform pet id
     */
    private Long platformPetId;

    /**
     * pet deleted
     */
    private Boolean deleted;

    /**
     * pet passed away
     */
    private Boolean passedAway;

    /**
     * pet evaluation pass result
     */
    @Deprecated
    private Byte evaluationStatus;
}
