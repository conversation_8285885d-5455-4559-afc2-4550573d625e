package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class CustomerFilterResult {

    // 将client筛选后，返回当前页的client list和client total
    private Map<Integer, PhoneNumberEmailDto> phoneNumberMap;

    @Schema(description = "查询结果列表按顺序返回，上面map是老字段，会同时返回，兼容旧的调用")
    private List<PhoneNumberEmailDto> customerList;

    private Integer customerTotal;
}
