package com.moego.server.customer.params;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.moego.common.params.FilterParams;
import com.moego.common.params.QueryParams;
import com.moego.common.params.SortParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @since 2023/3/22
 */
@Builder(toBuilder = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public record ClientListParams(
        @Valid @Schema(description = "Filter combiner") FilterParams filters,
        @Schema(description = "Fuzzy match") QueryParams queries,
        @NotNull @Valid @Schema(description = "Sort params") SortParams sort,
        @Range(min = 1) @NotNull @Schema(description = "Page number") Integer pageNum,
        @Range(min = 1, max = 100) @NotNull @Schema(description = "Page size") Integer pageSize) {
    public ClientListParams {}
}
