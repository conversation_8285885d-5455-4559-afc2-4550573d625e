package com.moego.server.customer.api;

import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.CustomerNoteDto;
import com.moego.server.customer.params.CustomerNoteSaveVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerNoteService {
    /**
     * DONE(account structure): 无需修改，business id 没有被使用
     *
     * save custom owner question and answer
     */
    @PostMapping("/service/customer/note/createCustomerNote")
    AddResultDTO createCustomerNote(
            @RequestParam("tokenBusinessId") @Deprecated Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody CustomerNoteSaveVo saveVo);

    /**
     * DONE(account structure): 无需修改，business id 没有被使用
     */
    @PostMapping("/service/customer/note/createIdempotentCustomerNote")
    AddResultDTO createIdempotentCustomerNote(
            @RequestParam("tokenBusinessId") @Deprecated Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody CustomerNoteSaveVo saveVo);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/note/getCustomerNoteById")
    CustomerNoteDto getCustomerNoteById(@RequestParam("customerNoteId") int customerNoteId);
}
