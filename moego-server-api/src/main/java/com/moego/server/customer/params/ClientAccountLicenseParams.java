package com.moego.server.customer.params;

import com.moego.common.enums.account.RegisterSourceEnum;
import java.io.Serializable;
import java.util.Set;
import lombok.Data;

@Data
public class ClientAccountLicenseParams implements Serializable {

    private static final long serialVersionUID = 3511939039446487392L;

    private String firstName;
    private String lastName;
    private String email;

    private String phoneNumber;

    private String country;

    private String countryAlpha2Code;

    private RegisterSourceEnum source;
    private String sourceId;

    /**
     * 可以忽略校验的字段
     */
    private Set<String> ignoreFields;
    /**
     * 不可被用户修改的字段
     */
    private Set<String> immutableFields;

    private Boolean skipVerificationCode;
}
