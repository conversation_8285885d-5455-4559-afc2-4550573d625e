package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CustomerSearchItemDto {
    private Integer customerId;
    private Integer businessId;
    private String firstName;
    private String lastName;
    private String avatarPath;
    private String clientColor;
    private Byte inactive;
    private String phoneNumber;
    private List<PetNameBreedDto> petNameList;
}
