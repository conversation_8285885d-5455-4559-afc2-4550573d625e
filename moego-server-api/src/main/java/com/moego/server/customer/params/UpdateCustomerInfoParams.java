package com.moego.server.customer.params;

import com.moego.server.customer.dto.AdditionalContactDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/6/30
 * @time 10:37 上午
 */
@Data
public class UpdateCustomerInfoParams {

    @NotNull
    private Integer customerId;

    @Size(max = 255)
    private String avatarPath;

    @Size(max = 50)
    private String email;

    @Size(max = 30)
    private String phoneNumber;

    @Size(max = 50)
    private String firstName;

    @Size(max = 50)
    private String lastName;

    private Byte inactive;
    private String clientColor;
    private Byte isBlockMessage;
    private Byte isBlockOnlineBooking;
    private Integer referralSourceId;

    @Size(max = 255)
    private String referralSourceDesc;

    private Byte sendAutoEmail;
    private Byte sendAutoMessage;
    private Byte sendAppAutoMessage;

    @Deprecated
    private Byte unconfirmedReminderBy;

    @Schema(description = "对应复选框选项： 1-Message 2-email  3-phone call")
    private List<Byte> apptReminderByList;

    private Integer preferredGroomerId;
    private Integer preferredFrequencyDay;
    private Byte preferredFrequencyType;
    private String source;
    private List<Integer> customerTagIdList;

    private Integer primaryContactId;

    @Valid
    private List<CustomerContactUpdateVo> contactList;
    /**
     * add new primary address for this customer
     */
    private Integer addressId;

    private boolean addPrimaryAddressFlag = false;

    @Size(max = 255)
    private String address1;

    @Size(max = 255)
    private String city;

    @Size(max = 255)
    private String state;

    @Size(max = 10)
    private String zipcode;

    @Size(max = 255)
    private String country;

    @Size(max = 255)
    private String address2;

    @Size(max = 50)
    private String lat;

    @Size(max = 50)
    private String lng;

    @Schema(description = "share appt 状态  0 all 1 unconfirm 2confirm 3 finished")
    private Byte shareApptStatus;

    @Schema(description = "0 all  1 in x days  2 next x appointment  3 manually apptids")
    private Byte shareRangeType;

    @Schema(description = "不同type时的value")
    private Integer shareRangeValue;

    //    private String shareApptJson;
    @Schema(description = "当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效")
    private List<Integer> shareApptIds;

    @Schema(description = "customer prefer weekday, 0-Sunday, 1-6:Monday-Saturday")
    private Integer[] preferredDay;

    @Schema(description = "customer prefer time, [600, 900]")
    private Integer[] preferredTime;

    private Byte isUnsubscribed;

    private LocalDateTime birthday;

    private String googleAdsStr;

    // CRM-3555 emergency contact
    private AdditionalContactDTO emergencyContact;

    // CRM-3674 pickup contact
    private AdditionalContactDTO pickupContact;
}
