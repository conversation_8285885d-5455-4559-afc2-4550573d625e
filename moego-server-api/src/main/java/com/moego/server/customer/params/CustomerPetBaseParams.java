/*
 * @since 2021-12-09 14:57:08
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.customer.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.common.params.VaccineParams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
public class CustomerPetBaseParams {

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Integer businessId;

    @Size(max = 50)
    private String petName;

    private Integer petTypeId;

    @Size(max = 255)
    private String avatarPath;

    @Size(max = 50)
    private String breed;

    private Byte breedMix;

    @Size(max = 50)
    private String birthday;

    private Byte gender;

    @Size(max = 50)
    private String hairLength;

    @Size(max = 50)
    private String behavior;

    @Size(max = 50)
    private String weight;

    @Size(max = 50)
    private String fixed;

    private Byte status;
    private Byte lifeStatus;
    private Byte expiryNotification;

    @Size(max = 100)
    private String vetName;

    @Size(max = 30)
    private String vetPhone;

    @Size(max = 100)
    private String vetAddress;

    @Size(max = 100)
    private String emergencyContactName;

    @Size(max = 30)
    private String emergencyContactPhone;

    @Size(max = 3000)
    private String healthIssues;

    @Size(max = 512)
    private String petAppearanceNotes;

    private String petAppearanceColor;

    @Valid
    private List<@NotNull Integer> petCodeIdList;

    @Valid
    private List<@NotNull VaccineParams> vaccineList;

    private Byte evaluationStatus;

    private String rawId;
    private Long rawCreateTime;
    private Long rawUpdateTime;
    private Long playgroupId;
}
