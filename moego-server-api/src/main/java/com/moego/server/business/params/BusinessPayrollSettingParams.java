package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class BusinessPayrollSettingParams {

    @JsonIgnore
    private Integer businessId;

    @Schema(description = "默认的 split tips method: 1-by service, 2-by equally")
    @Max(2)
    @Min(1)
    private Byte splitTipsMethod;

    @Schema(description = "service commission 计算基数: 1-actual payment, 2-finish appointment")
    @Max(2)
    @Min(1)
    private Byte serviceCommissionBased;
}
