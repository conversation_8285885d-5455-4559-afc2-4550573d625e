package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmartScheduleDrivingRuleDTO {

    private Integer id;
    private Integer businessId;
    private Integer maxDist;
    private Integer maxTime;
    private Integer createdBy;
    private Integer updatedBy;
    private Date createdAt;
    private Date updatedAt;

    @Schema(description = "关联的 van id 列表")
    private List<Integer> assignedVanIdList;

    @Schema(description = "max distance，单位 mile，business setting 里的距离单位可能为 kilometer，根据单位转换为 mile 后的值")
    private Integer maxDistInMile;
}
