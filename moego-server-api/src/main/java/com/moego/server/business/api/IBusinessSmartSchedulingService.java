package com.moego.server.business.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.business.params.SmartScheduleSettingParams;
import jakarta.validation.Valid;
import java.util.Map;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessSmartSchedulingService {
    @Deprecated
    @PutMapping("/service/business/smartScheduling/updateSmartScheduleSetting")
    ResponseResult<SmartScheduleSettingDTO> updateSmartScheduleSetting(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestBody SmartScheduleSettingParams setting);

    @PostMapping("/service/business/smart-schedule/getStaffSmartScheduleSettingMap")
    Map<Integer, StaffSmartScheduleSettingDTO> getStaffSmartScheduleSettingMap(
            @Valid @RequestBody GetSmartScheduleSettingParams params);
}
