package com.moego.server.business.api;

import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.ServiceAreaInsideBatchResult;
import com.moego.server.business.dto.ServiceAreaSettingDTO;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.ServiceAreaInsideBatchRequest;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessServiceAreaService {

    @GetMapping("service/business/serviceArea/getServiceAreaSetting")
    ServiceAreaSettingDTO getServiceAreaSetting(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/business/serviceArea/isLocationInsideAreaBatch")
    ServiceAreaInsideBatchResult isLocationInsideAreaBatch(@RequestBody ServiceAreaInsideBatchRequest request);

    /**
     * TODO(account structure): 看下这个接口是否需要支持 company 维度的 service area
     */
    @PostMapping("/service/business/serviceArea/getAreasByLocation")
    Map<Long, List<CertainAreaDTO>> getAreasByLocation(@RequestBody BatchGetAreasByLocationParams params);
}
