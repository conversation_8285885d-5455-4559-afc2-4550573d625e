package com.moego.server.business.api;

import com.moego.server.business.dto.MigrateResult;
import com.moego.server.business.vo.NewAccountStructureMigrateVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IMigrateASService {
    @PostMapping("/service/business/migrate")
    MigrateResult migrate(@RequestBody NewAccountStructureMigrateVO params);

    @PostMapping("/service/business/updateMigrateStatus")
    void updateMigrateStatus(@RequestBody NewAccountStructureMigrateVO params);

    @PostMapping("/service/business/migrate/role")
    void migratePermission(@RequestBody NewAccountStructureMigrateVO params);
}
