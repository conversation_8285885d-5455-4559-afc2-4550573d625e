package com.moego.server.grooming.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class GroomingCustomerPetdetailDTO {

    private Integer petDetailId;
    private Integer petId;
    private Integer serviceId;
    private String serviceName;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Long endTime;
    private List<Long> staffIds;

    @Deprecated
    private String petName;

    @Deprecated
    private String petBreed;

    @Deprecated
    private Integer staffId;
}
