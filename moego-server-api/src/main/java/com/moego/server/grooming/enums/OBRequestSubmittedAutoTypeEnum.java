package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/20
 */
@Getter
@AllArgsConstructor
public enum OBRequestSubmittedAutoTypeEnum {
    @JsonProperty("auto_accept_all")
    AUTO_ACCEPT_ALL("auto_accept_all", "Auto-accept all requests and profile updates"),
    @JsonProperty("auto_accept_request")
    AUTO_ACCEPT_REQUEST("auto_accept_request", "Auto-accept the requests that do not have profile updates"),
    @JsonProperty("auto_move_waitlist")
    AUTO_MOVE_WAITLIST("auto_move_waitlist", "Auto-move booking requests to waitlist after 48 hours"),
    @JsonProperty("no_automation")
    NO_AUTOMATION("no_automation", "No automation");

    private final String autoType;

    private final String description;

    public static OBRequestSubmittedAutoTypeEnum fromString(String autoType) {
        if (!StringUtils.hasText(autoType)) {
            return null;
        }
        for (OBRequestSubmittedAutoTypeEnum type : OBRequestSubmittedAutoTypeEnum.values()) {
            if (type.autoType.equals(autoType)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isAutoAcceptConflict(String autoType) {
        if (!StringUtils.hasText(autoType)) {
            return false;
        }
        return Objects.equals(autoType, AUTO_ACCEPT_ALL.autoType);
    }

    public static boolean isAutoAcceptRequest(String autoType, boolean profileChanges) {
        if (!StringUtils.hasText(autoType)) {
            return false;
        }
        if (Objects.equals(autoType, AUTO_ACCEPT_ALL.autoType)) {
            return true;
        }
        if (Objects.equals(autoType, AUTO_ACCEPT_REQUEST.autoType)) {
            return !profileChanges;
        }
        return false;
    }
}
