package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.OrderDecouplingFlowMarkerDTO;
import jakarta.annotation.Nullable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IOrderDecouplingFlowMarkerService {
    /**
     * Get {@link OrderDecouplingFlowMarkerDTO} by orderId.
     *
     * @param orderId order id
     * @return Found result or null
     */
    @GetMapping("/service/grooming/getOrderDecouplingFlowMarker")
    @Nullable
    OrderDecouplingFlowMarkerDTO getOrderDecouplingFlowMarker(@RequestParam("orderId") long orderId);

    /**
     * Insert a record to mark the order as created by order decoupling flow.
     *
     * @param orderId order id
     * @return The id of the inserted record
     */
    @PostMapping("/service/grooming/insertOrderDecouplingFlowMarker")
    long insertOrderDecouplingFlowMarker(@RequestParam("orderId") long orderId);
}
