package com.moego.server.grooming.dto.printcard;

import com.moego.server.grooming.dto.LodgingTypeDTO;
import com.moego.server.grooming.dto.LodgingUnitDTO;
import java.util.List;
import lombok.Data;

@Data
public class ActivityCardDetail {

    private List<ActivityCardFeedingColumn> feedingInstructions;

    private List<ActivityCardMedicationColumn> medicationInstructions;

    private List<ActivityCardAddOnColumn> addOns;

    private List<LodgingTypeDTO> lodgingTypes;

    private List<LodgingUnitDTO> lodgingUnits;
}
