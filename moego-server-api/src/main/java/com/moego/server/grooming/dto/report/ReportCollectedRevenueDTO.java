package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 5/21/21 4:14 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportCollectedRevenueDTO {

    private Integer staffId;

    private String staffName;

    private BigDecimal collectedRevWithTipsAndTax;

    private BigDecimal collectedRev;

    private BigDecimal collectedTips;

    private BigDecimal collectedTax;

    private BigDecimal collectedDiscount;

    private BigDecimal unpaidRev;

    private Integer finishedApptNum;

    private Integer finishedPetNum;
    // partial/fully paid 预约数
    private Integer paidApptNum;
}
