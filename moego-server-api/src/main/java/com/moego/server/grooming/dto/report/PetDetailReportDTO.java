package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PetDetailReportDTO {
    private Integer id;
    private Integer groomingId;
    private Integer petId;
    private Integer staffId;
    private Integer serviceId;
    // 1-主服务 2-额外服务
    private Integer serviceType;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Long endTime;
    // 状态 1-正常 2-已删除  3因为修改的删除
    private Byte status;
    private Long updateTime;
    // 服务价格的生效范围类型  1-this appt 2 this and future(this appt和未来创建的预约会生效)
    private Integer scopeTypePrice;
    private Integer scopeTypeTime;
    private Integer starStaffId;
    private Integer packageServiceId;
    private String serviceName;
    private Integer taxId;
    private BigDecimal taxRate;
    private String serviceColorCode;
    private Boolean enableOperation;
    // work mode, 0-parallel, 1-sequence
    private Integer workMode;
    private String serviceDescription;
}
