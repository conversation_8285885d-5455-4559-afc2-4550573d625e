package com.moego.server.grooming.dto.dailyreport;

import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Grooming report 汇总信息，包括 business, pet 以及后续拓展需要的信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DailyReportSummaryInfoDTO {
    @Schema(description = "business name, logo")
    private BusinessInfo businessInfo;

    @Schema(description = "pet name, breed, avatar path, gender, weight")
    private PetInfo petInfo;

    @Schema(description = "grooming report fill in content")
    private DailyReportConfigDef reportInfo;

    private CustomerInfo customerInfo;

    private List<String> recipientEmailList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessInfo {
        private Long businessId;
        private String businessName;
        private String avatarPath;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PetInfo {

        private Long petId;
        private String petName;
        private Integer petTypeId;
        private String petBreed;
        private String avatarPath;
        private Integer gender;
        private String genderText;
        private String weight;
        private String weightWithUnit;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerInfo {
        private Long customerId;
        private String firstName;
        private String lastName;
        private String email;
    }
}
