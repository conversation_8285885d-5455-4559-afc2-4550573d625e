package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@Getter
@Setter
public class PlatformSalesRecordView {
    private Long id;
    private String link;
    private String code;
    private Long accountId;
    private Long agreementId;
    private Long companyId;
    private String email;
    private Byte companyType;
    private Integer subDiscount;
    private Integer subCouponValidMonth;
    private String subCouponCode;
    private Byte needHardware;
    private Integer hardwareDiscount;
    private String hardwareCode;
    private Integer vansNum;
    private Integer locationNum;
    private String agreementRecordUuid;
    private Date signedTime;
    private String orderShippingStatus;
    private Byte premiumType;
    private Integer status;
    private String creator;
    private Byte showAnnuallyTerm;
    private Byte showMonthlyTerm;
    private Byte showHardware;
    private Byte isBdPlan;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String terminalCardRate;
    private String nonTerminalCardRate;
    private String minMonthlyTransaction;
    private Byte isCustomRate;
    private Byte showAccounting;
    private String tier;
    private BigDecimal spif;
    private Integer customRateApprovalStatus;
    private String opportunityId;
}
