package com.moego.server.payment.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSettingParams {
    @NotNull
    private List<Integer> businessIds;

    @Schema(description = "0: not allowed, 1: allowed")
    private Byte allowCustomRate;

    /**
     ** {@link #updatePrimaryPayTypeByBusinessId(PrimaryPayTypeParams)} instead.
     */
    @Deprecated
    @Schema(description = "首选信用卡支付方式 0 默认; 1 stripe; 2 square")
    @Min(0)
    @Max(2)
    private Byte primaryPayType;

    @Deprecated
    @Schema(description = "update time of the primaryPayType field")
    @JsonIgnore
    private Date primaryPayTypeUpdateTime;
}
