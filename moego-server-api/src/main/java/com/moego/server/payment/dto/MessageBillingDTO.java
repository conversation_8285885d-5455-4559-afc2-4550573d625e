package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/25 3:59 PM
 */
@Data
@Schema(description = "短信购买结果")
public class MessageBillingDTO {

    @Schema(description = "已购买短信包 主键ID")
    private Integer msgPlanId;

    @Schema(description = "是否自动购买：  0：不自动购买  1：自动购买")
    private Byte autoReload;

    @Schema(description = "短信购买条数")
    Integer amount;

    @Schema(description = "对应价格")
    BigDecimal price;

    @Schema(description = "message的支付主体 1-company 2-enterprise")
    private Integer payerType;
}
