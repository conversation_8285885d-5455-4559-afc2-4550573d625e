package com.moego.api.thirdparty;

import com.moego.server.payment.params.PayoutReviewParam;
import com.moego.server.payment.params.SlackPostBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/11/02
 */
@FeignClient(
        name = "payment-slack-triggers-client",
        url = "${notification.slackTriggersBaseUrl}",
        contextId = "IPaymentSlackTriggersClient")
public interface IPaymentSlackTriggersClient {
    @PostMapping("${notification.payoutReview.url}")
    void sendPayoutReviewMessage(@RequestBody PayoutReviewParam param);

    @PostMapping("${notification.accountActivated.url}")
    void sendAccountActivatedMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.antiFraud.url}")
    void sendAntiFraudMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.moegoCare.success.url}")
    void sendMoeGoCareSuccessMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.moegoCare.alert.url}")
    void sendMoeGoCareAlertMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.moegoSale.success.url}")
    void sendMoeGoSaleSuccessMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.moegoSale.alert.url}")
    void sendMoeGoSaleAlertMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.subscription.url}")
    void sendSubscriptionFailedMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.split_payment.alert.url}")
    void sendSplitPaymentAlertMessage(@RequestBody SlackPostBody param);

    @PostMapping("${notification.downgrade_enterprise.success.url}")
    void sendEnterpriseSubscriptionNotification(@RequestBody SlackPostBody param);
}
