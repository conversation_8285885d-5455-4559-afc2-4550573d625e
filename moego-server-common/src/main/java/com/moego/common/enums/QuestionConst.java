package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface QuestionConst {
    String KEY_PREFIX = "custom_";
    Byte IS_ALLOW_DELETE_FALSE = 0;
    Byte IS_ALLOW_DELETE_TRUE = 1;
    Byte IS_ALLOW_CHANGE_FALSE = 0;
    Byte IS_ALLOW_CHANGE_TRUE = 1;
    Byte IS_ALLOW_EDIT_FALSE = 0;
    Byte IS_ALLOW_EDIT_TRUE = 1;
    Byte IS_SHOW_TURE = 1;
    Byte IS_SHOW_FALSE = 0;
    Byte IS_REQUIRED_TRUE = 1;
    Byte IS_REQUIRED_FALSE = 0;
    Byte QUESTION_TYPE_SHORT = 1;
    Byte QUESTION_TYPE_LONG = 2;
    Byte QUESTION_TYPE_DROP_DOWN = 3;
    Byte QUESTION_TYPE_RADIO = 4;
    Byte QUESTION_TYPE_CHECK_BOX = 5;
    Byte QUESTION_TYPE_DATE = 6; // and, fuck the garbage design of the intake form

    /**
     * 1. 客户pet的json
     * 2. 宠物pet owner的json
     * 3. boarding service 的 json
     * 4. daycare service 的 json
     */
    Byte TYPE_PET_QUESTION = 1;

    Byte TYPE_PET_OWNER_QUESTION = 2;
    Byte TYPE_BOARDING_SERVICE_QUESTION = 3;
    Byte TYPE_DAYCARE_SERVICE_QUESTION = 4;

    // default extra json for boarding feeding schedule
    String DEFAULT_EXTRA_JSON_FOR_FEEDING_SCHEDULE =
            "[{\"type\": \"feedingTime\", \"question\": \"Feeding time\", \"isRequired\": false}, {\"type\": \"feedingAmount\", \"question\": \"Feeding amount\", \"isRequired\": false}, {\"type\": \"feedingUnit\", \"question\": \"Feeding unit\", \"isRequired\": false}, {\"type\": \"foodType\", \"question\": \"Food type\", \"isRequired\": false}, {\"type\": \"foodSource\", \"question\": \"Food source\", \"isRequired\": false}, {\"type\": \"feedingInstruction\", \"question\": \"Feeding instruction\", \"isRequired\": false}, {\"type\": \"feedingNote\", \"question\": \"Feeding note\", \"isRequired\": false}]";

    // default extra json for boarding medication schedule
    String DEFAULT_EXTRA_JSON_FOR_MEDICATION_SCHEDULE =
            "[{\"question\":\"Medication time\",\"isRequired\":false,\"type\":\"medicationTime\"},{\"question\":\"Medication amount\",\"isRequired\":false,\"type\":\"medicationAmount\"},{\"question\":\"Medication unit\",\"isRequired\":false,\"type\":\"medicationUnit\"},{\"question\":\"Medication name\",\"isRequired\":false,\"type\":\"medicationName\"},{\"question\":\"Medication notes\",\"isRequired\":false,\"type\":\"medicationNotes\"}]";
}
