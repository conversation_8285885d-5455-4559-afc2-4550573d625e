package com.moego.common.enums;

/**
 * <AUTHOR>
 * @since 2023/3/29
 */
public interface ClientPropertyValueConst {
    interface ClientStatus {
        String ACTIVE = "active";
        String INACTIVE = "inactive";
        String LAPSED = "lapsed";
        String BLOCKED_FROM_MESSAGE = "blocked_from_message";
        String BLOCKED_FROM_OB = "blocked_from_ob";
    }

    interface ClientType {
        String NEW = "new";
        String RECURRING = "recurring";
        String WAITLIST = "waitlist";
        String PROSPECT = "prospect";
    }

    interface PetVaccine {
        String EXPIRED_RECORD = "expiredRecord";
        String NO_RECORD = "noRecord";
    }

    interface PerformanceHistory {
        String UNPAID_INVOICE = "unpaid_invoice_cnt";
        String NO_SHOW_APPT = "no_show_appt_cnt";
        String CANCEL_APPT = "cancelled_appt_cnt";
    }

    interface CofStatus {
        String EXPIRED = "expired";
        String NOT_EXPIRED = "not_expired";

        String SUBMITTED = "submitted";
    }
}
