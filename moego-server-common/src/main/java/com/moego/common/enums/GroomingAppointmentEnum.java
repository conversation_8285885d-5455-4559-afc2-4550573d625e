package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface GroomingAppointmentEnum {
    /**
     * ONLINE BOOKING预约
     */
    Byte BOOK_ONLINE_STATUS_OB = 1;
    /**
     * 普通预约
     */
    Byte BOOK_ONLINE_STATUS_NOT_OB = 0;
    /**
     * IS_WAITING_LIST
     */
    Byte IS_WAITING_LIST = 1;

    Byte NOT_WAITING_LIST = 0;
    /**
     * IS BLOCK
     */
    Integer IS_BLOCK_TRUE = 1;

    Integer IS_BLOCK_FALSE = 0;
    Integer UNBLOCK = 2;
    /**
     * IS_DEPRECATE
     */
    Integer IS_DEPRECATE_TRUE = 1;

    Integer IS_DEPRECATE_FALSE = 0;

    Byte GROOMING_SVC_BOOK_ONLINE_YES = 1;
    Byte GROOMING_SVC_BOOK_ONLINE_NO = 0;

    // 0-by business, 1-by customer reply msg, 2-by delete pet
    Byte CONFIRM_TYPE_BY_BUSINESS = 0;
    Byte CONFIRM_TYPE_BY_CLIENT = 1;
    Byte CONFIRM_TYPE_BY_PET_REMOVE = 2;
    Byte CONFIRM_TYPE_BY_DEPOSIT = 3;

    Byte CANCEL_TYPE_BY_BUSINESS = 0;
    Byte CANCEL_TYPE_BY_CLIENT = 1;
    Byte CANCEL_TYPE_BY_PET_REMOVE = 2;
    Byte CANCEL_TYPE_BY_CLIENT_PORTAL = 3;
    /**
     * appointment source 22168-book-online 22018-web,17216-android,17802-ios 19826-google calendar
     */
    Integer SOURCE_OB = 22168;

    Integer SOURCE_WEB = 22018;
    Integer SOURCE_ANDROID = 17216;
    Integer SOURCE_IOS = 17802;
    Integer SOURCE_AUTO_DM = 23426;
    Integer SOURCE_GOOGLE_CALENDAR = 19826;

    /**
     * 是否no show  默认值2
     */
    Byte NO_SHOW_FALSE = 2;

    Byte NO_SHOW_TRUE = 1;
    /**
     * Note type for appointment
     */
    Byte NOTE_ALERT = 1;

    Byte NOTE_COMMENT = 2;
    Byte NOTE_CANCEL = 3;
    /**
     * Only for ob requests
     */
    Byte NOTE_ADDITIONAL = 4;
    /**
     * 1,work time 冲突。2，预约冲突，3，block冲突
     */
    Byte CONFLICT_TYPE_WORK_TIME = 1;

    Byte CONFLICT_TYPE_APPT = 2;
    Byte CONFLICT_TYPE_BLOCK = 3;
    /**
     * isPaid 状态
     */
    Byte PAID = 1;

    Byte NOT_PAY = 2;
    Byte PARTIAL_PAY = 3;
    /**
     * block默认颜色
     */
    String BLOCK_DEFAULT_COLOR = "lightgray";

    /**
     * 批量操作block时，type值对应操作：1-只操作当前block，2-操作接下来所有重复的，3-同一个series重复的block
     * 常量含义扩展为：批量操作预约时，.....
     */
    Integer MODIFY_REPEAT_BLOCK_ONLY_ONE = 1;

    Integer MODIFY_REPEAT_BLOCK_FOLLOWING = 2;
    Integer MODIFY_REPEAT_BLOCK_ALL = 3;

    /**
     * Block重复类型：1-次数，2-设置结束时间
     */
    String REPEAT_TYPE_TIMES = "1";

    String REPEAT_TYPE_END_DATE = "2";

    String PPA_CANCEL_REASON = "Appointment canceled by client in pet parent app";
}
