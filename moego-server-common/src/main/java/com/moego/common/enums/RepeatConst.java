package com.moego.common.enums;

public interface RepeatConst {

    // repeat type: 1-day, 2-week, 3-month
    int REPEAT_TYPE_DAY = 1;
    int REPEAT_TYPE_WEEK = 2;
    int REPEAT_TYPE_MONTH = 3;

    // repeat every type: 1-以周为单位, 每月的第几个星期几, 2-以day为单位, 每月的第几天
    int REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY = 1;
    int REPEAT_EVERY_TYPE_SPECIFIC_DAY = 2;

    // repeat end type: 1-次数, 2-结束时间
    String REPEAT_END_TYPE_TIMES = "1";
    String REPEAT_END_TYPE_END_DATE = "2";

    // 单个 repeat 最大的重复次数
    int MAX_REPEAT_TIMES = 100;

    // schedule type: 1-普通repeat，2-ss, 3-ss not available
    Integer REPEAT_SCHEDULE_TYPE_NORMAL = 1;
    Integer REPEAT_SCHEDULE_TYPE_SS = 2;
    Integer REPEAT_SCHEDULE_TYPE_SS_NOT_AVAILABLE = 3;

    // ss flag 标记
    Byte REPEAT_SS_FLAG_FALSE = 0;
    Byte REPEAT_SS_FLAG_TRUE = 1;

    // 冲突类型：1-work time 冲突, 2-预约冲突, 3-block冲突
    Integer REPEAT_CONFLICT_TYPE_WORK_TIME = 1;
    Integer REPEAT_CONFLICT_TYPE_APPT_CONFLICT = 2;
    Integer REPEAT_CONFLICT_TYPE_BLOCK_CONFLICT = 3;

    // 查询 repeat appointment 类型
    byte REPEAT_APPOINTMENT_TYPE_HISTORY = 1;
    byte REPEAT_APPOINTMENT_TYPE_UPCOMING = 2;
    byte REPEAT_APPOINTMENT_TYPE_ALL = 3;
}
