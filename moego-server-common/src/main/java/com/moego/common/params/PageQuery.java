package com.moego.common.params;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/5
 * @deprecated Please use {@link com.moego.common.utils.Pagination} instead.
 */
@Data
@Accessors(chain = true)
@Deprecated
public class PageQuery {

    private static final int DEFAULT_PAGE_NO = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    @Schema(description = "Page number, default is 1")
    private int pageNum = DEFAULT_PAGE_NO;

    @Schema(description = "Page size, default is 10")
    private int pageSize = DEFAULT_PAGE_SIZE;

    @Schema(description = "Sort by single field")
    private SortQuery sort;

    @Schema(description = "Sort by multiple field")
    private List<SortQuery> sortList;

    public enum OrderEnum {
        asc,
        desc,
    }

    @Data
    @Accessors(chain = true)
    public static class SortQuery {

        @Schema(description = "Sort by field")
        private String sortBy;

        @Schema(description = "Order type, asc, desc")
        private OrderEnum order;
    }
}
