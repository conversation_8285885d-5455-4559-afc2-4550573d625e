package com.moego.common.utils;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

@UtilityClass
public class PhoneUtil {
    private static final Logger log = LoggerFactory.getLogger(PhoneUtil.class);

    private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    private static final int PHONE_NUMBER_LENGTH = 9;

    /**
     * 给定一个手机号, 格式化成给定国家的 E164 格式
     * @param number 手机号 (可以是 E164 格式的, 或者各国本地化格式的)
     * @param region 所属国家或地区, 可以传单词全称 (United States, United Kingdom 等) 或者 ISO 二字码 (US, GB 等)
     * @return E164 格式的手机号, 如果解析不成功会返回 null
     */
    public static String toE164Number(String number, String region) {
        if (!StringUtils.hasText(number)) {
            return null;
        }

        String isoTwoCodes = region != null && region.length() == 2 ? region : CountryUtils.getCountryTwoCodes(region);

        try {
            // isoTwoCodes 如果为 null, 但是 number 有区号前缀 (如 +1, +44 等), 也是可以解析的
            PhoneNumber numberProto = phoneNumberUtil.parse(number, isoTwoCodes);
            return phoneNumberUtil.format(numberProto, PhoneNumberUtil.PhoneNumberFormat.E164);
        } catch (NumberParseException e) {
            // isoTwoCodes 为 null, 并且 number 没有区号, 或者 number 不是一个合法的数字串, 则无法解析成 E164 格式
            return null;
        }
    }

    /**
     * 判断一个手机号是属于哪个国家的
     * @param e164Number E164 格式的手机号
     * @return 国家或地区的 ISO 二字码, 无效号码或者找不到对应国家或地区时返回 null
     */
    public static String getISOTwoLetterRegionCode(String e164Number) {
        if (!StringUtils.hasText(e164Number)) {
            return null;
        }

        try {
            PhoneNumber numberProto = phoneNumberUtil.parse(e164Number, null);

            // 一个区号可能对应多个国家, 如 +1 -> US, CA
            // 参考 com.google.i18n.phonenumbers.geocoding.PhoneNumberOfflineGeocoder#getCountryNameForNumber 的写法
            // 可以通过 PhoneNumberUtil.isValidNumberForRegion 进一步判断是哪个国家
            List<String> regionCodes = phoneNumberUtil.getRegionCodesForCountryCode(numberProto.getCountryCode());
            if (regionCodes.size() == 1) {
                return regionCodes.get(0);
            } else {
                String regionWhereNumberIsValid = "ZZ";
                for (String regionCode : regionCodes) {
                    if (phoneNumberUtil.isValidNumberForRegion(numberProto, regionCode)) {
                        // If the number has already been found valid for one region, then we don't know which
                        // region it belongs to so we return nothing.
                        if (!regionWhereNumberIsValid.equals("ZZ")) {
                            return null;
                        }
                        regionWhereNumberIsValid = regionCode;
                    }
                }
                return "ZZ".equals(regionWhereNumberIsValid) ? null : regionWhereNumberIsValid;
            }
        } catch (NumberParseException e) {
            return null;
        }
    }

    /**
     * 判断一个手机号是否是属于美国的号码
     * @param e164Number E164 格式的手机号
     * @return true or false, 如果无法确定是否是美国的手机号, 也会返回 false
     */
    public static boolean isUSPhoneNumber(String e164Number) {
        return Locale.US.getCountry().equals(getISOTwoLetterRegionCode(e164Number));
    }

    /**
     * 给出一个手机号的各种数字格式
     * e.g.
     * 输入 +441612360660
     * 输出 [+441612360660, 441612360660, 1612360660, 01612360660]
     *
     * @param e164Number E164 格式的手机号
     * @return 各种格式的手机号
     */
    public static Set<String> getVariousFormatPhoneNumber(String e164Number) {
        Set<String> numbers = new HashSet<>();

        PhoneNumber numberProto = null;
        try {
            numberProto = phoneNumberUtil.parse(e164Number, null);
        } catch (NumberParseException e) {
            return numbers;
        }

        numbers.add(e164Number);
        numbers.add(e164Number.replace("+", ""));
        numbers.add(e164Number.replace("+" + numberProto.getCountryCode(), ""));
        numbers.add(phoneNumberUtil
                .format(numberProto, PhoneNumberUtil.PhoneNumberFormat.NATIONAL)
                .replaceAll("\\D", ""));
        numbers.add(phoneNumberUtil
                .format(numberProto, PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL)
                .replaceAll("\\D", ""));
        return numbers;
    }

    public static String removePlus(String e164Number) {
        if (StringUtils.hasText(e164Number)) {
            return e164Number.replace("+", "");
        }
        return e164Number;
    }

    public static String splitLast9Digits(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return null;
        }
        if (phoneNumber.length() > PHONE_NUMBER_LENGTH) {
            phoneNumber = phoneNumber.substring(phoneNumber.length() - PHONE_NUMBER_LENGTH);
        }
        return phoneNumber;
    }

    /**
     * Remove country code for the given phone number if it has.
     *
     * <p> e.g.
     * <ul>
     *     <li> +441612360660 -> 1612360660
     *     <li> 1612360660 -> 1612360660
     * </ul>
     *
     * @param phoneNumber phone number with or without country code
     * @return phone number without country code
     */
    public static String removeCountryCode(String phoneNumber) {
        if (phoneNumber == null) {
            return null;
        }
        if (!phoneNumber.startsWith("+")) {
            return phoneNumber;
        }
        try {
            PhoneNumber numberProto = phoneNumberUtil.parse(phoneNumber, null);
            return String.valueOf(numberProto.getNationalNumber());
        } catch (NumberParseException e) {
            log.warn("Failed to parse phone number: {}", phoneNumber, e);
            return phoneNumber;
        }
    }
}
