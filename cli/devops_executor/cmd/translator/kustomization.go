package translator

import (
	"github.com/spf13/cobra"

	"github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator"
)

var translateKustomizationParams translator.TranslateKustomizationParams

var KustomizationCmd = &cobra.Command{
	Use:   "kustomization",
	Short: "Translate a kustomization.yaml file.",
	Long: `Translate a kustomization.yaml file.
Currently only supports translation of the 'patches/-/patch.target.labelSelector' field in the kustomization.yaml file.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return translator.TranslateKustomization(cmd.Context(), translateKustomizationParams)
	},
}

func init() {
	txtKustomization := "[required] Specify kustomization.yaml file path."
	txtOutput := "[optional] Specify the translated output path. If not specified, it will be the same as the kustomization path."
	txtCluster := "[required] Specify the cluster."
	txtNamespace := "[required] Specify the namespace."
	txtRepo := "[required] Specify the repo name."
	txtBranch := "[required] Specify the branch name."

	KustomizationCmd.Flags().SortFlags = false
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.File, "kustomization", "", txtKustomization)
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.Output, "output", "", txtOutput)
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.Cluster, "cluster", "", txtCluster)
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.Namespace, "namespace", "", txtNamespace)
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.Repo, "repo", "", txtRepo)
	KustomizationCmd.Flags().StringVar(&translateKustomizationParams.Branch, "branch", "", txtBranch)
	KustomizationCmd.MarkFlagRequired("kustomization")
	KustomizationCmd.MarkFlagRequired("cluster")
	KustomizationCmd.MarkFlagRequired("namespace")
	KustomizationCmd.MarkFlagRequired("repo")
	KustomizationCmd.MarkFlagRequired("branch")
}
