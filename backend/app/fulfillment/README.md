# moego-template-svc-go

Server for go svc server template

## Development
project structure
```
.
├── CODEOWNERS
├── README.md
├── config
│   └── config
│       ├── production
│       │   └── config.yaml
│       ├── staging
│       │   └── config.yaml
│       └── testing
│           └── config.yaml
├── e2e
│   ├── e2e.go
│   └── todo
│       └── todo_test.go
├── entity
│   └── entity.go
├── logic
│   └── greeter
│       └── greeter_logic.go
├── main.go
├── repo
│   ├── gorm
│   │   └── gorm.go
│   ├── repo.go
│   └── stripe
│       └── client.go
├── service
│   └── greeter_service.go
└── utils
    └── utils.go

```

其中:
- `config` 配置文件目录，按环境分为子目录，每个子目录包含对应的 config.yaml 文件
- `e2e` 存放测试代码
- `entity` 存放服务内部的实体定义
- `logic` 存放服务的业务逻辑，该层以 entity 定义进行操作，会调用 repo 获取外部资源
- `repo` 提供外部依赖接口定义及实现，包括 rpc、db、cache 等
- `service` GRPC 接口的实现，wire.go 用于依赖注入，该层会将对外 proto 转化为 entity 并调用 logic 层
- `utils` 常用的工具函数
- `main.go` 服务启动的入口
- `.gitignore` gitignore 文件
- `CODEOWNERS` 包含对应目录的 Code Review necessary reviewers
- `README.md` 项目说明
