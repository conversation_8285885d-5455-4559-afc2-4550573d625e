load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "google",
    srcs = [
        "google.go",
        "grpc_conn.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/google",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/open_platform/v1:open_platform",
        "@com_github_shenzhencenter_google_ads_pb//services",
        "@org_golang_google_api//googleapi",
        "@org_golang_google_api//oauth2/v2:oauth2",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//status",
        "@org_golang_x_oauth2//:oauth2",
        "@org_golang_x_oauth2//google",
    ],
)
