// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/google_ads_setting/setting.go
//
// Generated by this command:
//
//	mockgen -source=./db/google_ads_setting/setting.go -destination=./db/google_ads_setting/mock/setting_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	googleadssetting "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/google_ads_setting"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, setting *googleadssetting.GoogleAdsSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, setting any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, setting)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id, staffID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id, staffID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id, staffID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id, staffID)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, datum *googleadssetting.ListDatum) ([]*googleadssetting.GoogleAdsSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, datum)
	ret0, _ := ret[0].([]*googleadssetting.GoogleAdsSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, datum)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, setting *googleadssetting.GoogleAdsSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, setting any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, setting)
}
