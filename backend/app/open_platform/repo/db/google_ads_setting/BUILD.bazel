load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "google_ads_setting",
    srcs = [
        "entity.go",
        "setting.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db/google_ads_setting",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/open_platform/repo/db",
        "//backend/common/rpc/framework/log",
        "@com_github_lib_pq//:pq",
        "@io_gorm_gorm//:gorm",
    ],
)
