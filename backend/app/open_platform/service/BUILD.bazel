load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "oauth_handler.go",
        "open_platform_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/open_platform/constdef",
        "//backend/app/open_platform/logic/oauth",
        "//backend/proto/open_platform/v1:open_platform",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
