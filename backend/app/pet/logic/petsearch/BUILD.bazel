load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "petsearch",
    srcs = [
        "converter.go",
        "entity.go",
        "pet_document.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet/logic/petsearch",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/pet/repo/gorm/customer",
        "//backend/app/pet/repo/gorm/pet",
        "//backend/app/pet/repo/search",
        "//backend/app/search/utils",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/struct",
        "//backend/proto/pet/v1:pet",
        "//backend/proto/search/v1:search",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "petsearch_test",
    srcs = [
        "converter_test.go",
        "pet_document_test.go",
    ],
    deps = [
        ":petsearch",
        "//backend/app/pet/repo/gorm",
        "//backend/app/pet/repo/gorm/customer",
        "//backend/app/pet/repo/gorm/mock/customer",
        "//backend/app/pet/repo/gorm/mock/pet",
        "//backend/app/pet/repo/gorm/pet",
        "//backend/app/pet/repo/search",
        "//backend/app/pet/repo/search/mock",
        "//backend/proto/pet/v1:pet",
        "//backend/proto/search/v1:search",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
