package pet_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	ggorm "gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/pet/logic/pet"
	"github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/customer"
	customermock "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/mock/customer"
	petmock "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/mock/pet"
	petrepo "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet"
	"github.com/MoeGolibrary/moego/backend/common/utils/pagination"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

func NewMockPet(prw petrepo.ReadWriter,
	cwrw customerrepo.ReadWriter) *pet.Logic {
	mock := pet.NewByParams(
		prw,
		cwrw,
	)
	return mock
}

func TestNew(t *testing.T) {
	t.Run("test new pet logic success", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		prw := petmock.NewMockReadWriter(ctrl)
		cwrw := customermock.NewMockReadWriter(ctrl)

		logic := NewMockPet(prw, cwrw)
		require.NotNil(t, logic)
	})

	t.Run("测试NewLogic创建实例成功", func(t *testing.T) {

		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 使用 Set 函数设置模拟的数据库连接
		mockDB := &ggorm.DB{} // 创建一个空的 gorm.DB 实例
		gorm.Set(mockDB)

		// 创建Logic实例
		logic := pet.NewLogic()

		// 验证实例不为空
		require.NotNil(t, logic)

		// 验证实例中的字段已正确初始化
		require.NotNil(t, logic)
	})
}

func TestCreate(t *testing.T) {
	t.Run("测试成功创建宠物", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerID := int64(1)
		petName := "旺财"
		petType := petpb.Pet_DOG
		petGender := petpb.Pet_MALE
		petBreed := "拉布拉多"
		companyID := int64(100)
		businessID := int64(200)
		expectedID := int64(1)

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Create(gomock.Any(), &petrepo.CustomerPet{
			CustomerID: int(customerID),
			PetName:    petName,
			PetTypeID:  uint(petType),
			Gender:     int8(petGender),
			Breed:      petBreed,
			CompanyID:  companyID,
			BusinessID: int(businessID),
			CreateTime: uint64(time.Now().Unix()),
			UpdateTime: uint64(time.Now().Unix()),
			Status:     1,
		}).Return(expectedID, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		id, err := logic.Create(ctx, &pet.CreatePetParams{
			CustomerID: customerID,
			PetName:    petName,
			PetType:    petType,
			PetGender:  petGender,
			PetBreed:   petBreed,
			CompanyID:  companyID,
			BusinessID: businessID,
			Mixed:      false,
		})

		// 验证结果
		require.NoError(t, err)
		require.Equal(t, expectedID, id)
	})

	t.Run("测试创建宠物失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerID := int64(1)
		petName := "旺财"
		petType := petpb.Pet_DOG
		petGender := petpb.Pet_MALE
		petBreed := "拉布拉多"
		companyID := int64(100)
		businessID := int64(200)
		expectedErr := errors.New("创建宠物失败")

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(0), expectedErr)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		id, err := logic.Create(ctx, &pet.CreatePetParams{
			CustomerID: customerID,
			PetName:    petName,
			PetType:    petType,
			PetGender:  petGender,
			PetBreed:   petBreed,
			CompanyID:  companyID,
			BusinessID: businessID,
		})

		// 验证结果
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Equal(t, int64(0), id)
	})
}

func TestUpdate(t *testing.T) {
	t.Run("测试成功更新宠物", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		petID := int64(1)
		petName := "小黑"
		petType := petpb.Pet_CAT
		petGender := petpb.Pet_FEMALE
		petBreed := "暹罗猫"
		expectedID := int64(1)

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Update(gomock.Any(), &petrepo.CustomerPet{
			ID:         uint(petID),
			PetName:    petName,
			PetTypeID:  uint(petType.Number()),
			Gender:     int8(petGender.Number()),
			Breed:      petBreed,
			UpdateTime: uint64(time.Now().Unix()),
		}).Return(expectedID, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		id, err := logic.Update(ctx, &pet.UpdatePetParams{
			PetID:     petID,
			PetName:   petName,
			PetType:   petType,
			PetGender: petGender,
			PetBreed:  petBreed,
		})

		// 验证结果
		require.NoError(t, err)
		require.Equal(t, expectedID, id)
	})

	t.Run("测试更新宠物失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		petID := int64(1)
		petName := "小黑"
		petType := petpb.Pet_CAT
		petGender := petpb.Pet_FEMALE
		petBreed := "暹罗猫"
		expectedErr := errors.New("更新宠物失败")

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Update(gomock.Any(), gomock.Any()).Return(int64(0), expectedErr)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		id, err := logic.Update(ctx, &pet.UpdatePetParams{
			PetID:     petID,
			PetName:   petName,
			PetType:   petType,
			PetGender: petGender,
			PetBreed:  petBreed,
		})

		// 验证结果
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Equal(t, int64(0), id)
	})
}

func TestList(t *testing.T) {
	t.Run("测试成功获取宠物列表", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerIDs := []int64{1, 2}
		petIDs := []int64{10, 20}
		companyIDs := []int64{100}
		page := 1
		pageSize := 10
		orderBy := "id DESC"

		// 模拟数据库返回的宠物数据
		dbPets := []*petrepo.CustomerPet{
			{
				ID:         10,
				CustomerID: 1,
				PetName:    "旺财",
				PetTypeID:  uint(petpb.Pet_DOG),
				Gender:     int8(petpb.Pet_MALE),
				Breed:      "拉布拉多",
				CompanyID:  100,
				BusinessID: 200,
			},
			{
				ID:         20,
				CustomerID: 2,
				PetName:    "小黑",
				PetTypeID:  uint(petpb.Pet_CAT),
				Gender:     int8(petpb.Pet_FEMALE),
				Breed:      "暹罗猫",
				CompanyID:  100,
				BusinessID: 201,
			},
		}

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)

		// 修复 List 方法调用，按照正确的接口定义提供四个参数
		prw.EXPECT().List(
			gomock.Any(),
			&petrepo.Query{
				CustomerIDs: customerIDs,
				PetIDs:      petIDs,
				CompanyIDs:  companyIDs,
			},
			&petrepo.Filter{
				Status: 1,
			},
			&pagination.Pagination{
				Offset:  (page - 1) * pageSize,
				Limit:   pageSize,
				OrderBy: &orderBy,
			},
		).Return(dbPets, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		pets, err := logic.List(ctx, &pet.ListPetParams{
			CustomerIDs: customerIDs,
			PetIDs:      petIDs,
			CompanyIDs:  companyIDs,
			Page:        page,
			PageSize:    pageSize,
			OrderBy:     orderBy,
		})

		// 验证结果
		require.NoError(t, err)
		require.Len(t, pets, 2)

		// 验证第一个宠物数据
		require.Equal(t, int64(10), pets[0].ID)
		require.Equal(t, "旺财", pets[0].PetName)
		require.Equal(t, petpb.Pet_DOG, pets[0].PetType)
		require.Equal(t, petpb.Pet_MALE, pets[0].PetGender)
		require.Equal(t, "拉布拉多", pets[0].Breed)
		require.Equal(t, int64(100), pets[0].CompanyID)
		require.Equal(t, int64(200), pets[0].BusinessID)

		// 验证第二个宠物数据
		require.Equal(t, int64(20), pets[1].ID)
		require.Equal(t, "小黑", pets[1].PetName)
		require.Equal(t, petpb.Pet_CAT, pets[1].PetType)
		require.Equal(t, petpb.Pet_FEMALE, pets[1].PetGender)
		require.Equal(t, "暹罗猫", pets[1].Breed)
		require.Equal(t, int64(100), pets[1].CompanyID)
		require.Equal(t, int64(201), pets[1].BusinessID)
	})

	t.Run("测试获取宠物列表失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerIDs := []int64{1, 2}
		petIDs := []int64{10, 20}
		companyIDs := []int64{100}
		page := 1
		pageSize := 10
		orderBy := "id DESC"
		expectedErr := errors.New("获取宠物列表失败")

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)

		// 修复 List 方法调用，按照正确的接口定义提供四个参数
		prw.EXPECT().List(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return(nil, expectedErr)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		pets, err := logic.List(ctx, &pet.ListPetParams{
			CustomerIDs: customerIDs,
			PetIDs:      petIDs,
			CompanyIDs:  companyIDs,
			Page:        page,
			PageSize:    pageSize,
			OrderBy:     orderBy,
		})

		// 验证结果
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Nil(t, pets)
	})

	t.Run("测试获取空宠物列表", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerIDs := []int64{1, 2}
		page := 1
		pageSize := 10

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)

		// 修复 List 方法调用，按照正确的接口定义提供四个参数
		prw.EXPECT().List(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]*petrepo.CustomerPet{}, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		pets, err := logic.List(ctx, &pet.ListPetParams{
			CustomerIDs: customerIDs,
			Page:        page,
			PageSize:    pageSize,
		})

		// 验证结果
		require.NoError(t, err)
		require.Empty(t, pets)
	})
}

func TestPetToPB(t *testing.T) {
	t.Run("测试Pet转换为PB对象", func(t *testing.T) {
		// 准备测试数据
		petObj := &pet.Pet{
			ID:         123,
			PetName:    "小花",
			PetType:    petpb.Pet_DOG,
			PetGender:  petpb.Pet_FEMALE,
			Breed:      "哈士奇",
			CompanyID:  456,
			BusinessID: 789,
		}

		// 执行转换
		pbPet := petObj.ToPB()

		// 验证结果
		require.NotNil(t, pbPet)
		require.Equal(t, int64(123), pbPet.Id)
		require.Equal(t, "小花", pbPet.Name)
		require.Equal(t, petpb.Pet_DOG, pbPet.PetType)
		require.Equal(t, petpb.Pet_FEMALE, pbPet.Gender)
		require.Equal(t, "哈士奇", pbPet.Breed)
		require.Equal(t, int64(456), pbPet.CompanyId)
		require.Equal(t, int64(789), pbPet.BusinessId)
	})
}

func TestDelete(t *testing.T) {
	t.Run("测试成功删除宠物", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		petID := int64(1)
		expectedID := int64(1)

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Update(gomock.Any(), &petrepo.CustomerPet{
			ID:     uint(petID),
			Status: 2,
		}).Return(expectedID, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		err := logic.Delete(ctx, petID)

		// 验证结果
		require.NoError(t, err)
	})

	t.Run("测试删除宠物失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		petID := int64(1)
		expectedErr := errors.New("删除宠物失败")

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().Update(gomock.Any(), &petrepo.CustomerPet{
			ID:     uint(petID),
			Status: 2,
		}).Return(int64(0), expectedErr)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		err := logic.Delete(ctx, petID)

		// 验证结果
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestBatchCreate(t *testing.T) {
	t.Run("测试批量创建宠物", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerID := int64(1)
		petName := "旺财"
		petType := petpb.Pet_DOG
		petGender := petpb.Pet_MALE
		petBreed := "拉布拉多"
		companyID := int64(100)
		businessID := int64(200)
		expectedIDs := []int64{1, 2}

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(expectedIDs, nil)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		ids, err := logic.BatchCreate(ctx, []*pet.CreatePetParams{
			{
				CustomerID: customerID,
				PetName:    petName,
				PetType:    petType,
				PetGender:  petGender,
				PetBreed:   petBreed,
				CompanyID:  companyID,
				BusinessID: businessID,
			},
		})

		// 验证结果
		require.NoError(t, err)
		require.Equal(t, expectedIDs, ids)
	})

	t.Run("测试批量创建宠物失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 准备测试数据
		customerID := int64(1)
		petName := "旺财"
		petType := petpb.Pet_DOG
		petGender := petpb.Pet_MALE
		petBreed := "拉布拉多"
		companyID := int64(100)
		businessID := int64(200)
		expectedErr := errors.New("批量创建宠物失败")

		// 设置mock期望
		prw := petmock.NewMockReadWriter(ctrl)
		prw.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		cwrw := customermock.NewMockReadWriter(ctrl)

		// 创建测试实例
		logic := NewMockPet(prw, cwrw)

		// 执行测试
		ctx := context.Background()
		ids, err := logic.BatchCreate(ctx, []*pet.CreatePetParams{
			{
				CustomerID: customerID,
				PetName:    petName,
				PetType:    petType,
				PetGender:  petGender,
				PetBreed:   petBreed,
				CompanyID:  companyID,
				BusinessID: businessID,
			},
		})

		// 验证结果
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Nil(t, ids)
	})
}
