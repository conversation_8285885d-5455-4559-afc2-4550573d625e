package customerview

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type CustomerView struct {
	ID        int64                        `gorm:"column:id"`
	CompanyID int64                        `gorm:"column:company_id"`
	StaffID   int64                        `gorm:"column:staff_id"`
	IsDefault int32                        `gorm:"column:is_default"`
	Title     string                       `gorm:"column:title"`
	Fields    *string                      `gorm:"column:fields"`   // NULLable 字段使用指针
	OrderBy   *string                      `gorm:"column:order_by"` // NULLable 字段使用指针
	Filter    string                       `gorm:"column:filter"`
	CreatedAt time.Time                    `gorm:"column:created_at"`
	UpdatedAt time.Time                    `gorm:"column:updated_at"`
	DeletedAt *time.Time                   `gorm:"column:deleted_at"` // NULLable 字段使用指针
	Type      customerpb.CustomerView_Type `gorm:"column:type;serializer:proto_enum"`
}

// TableName sets the insert table name for this struct type
func (CustomerView) TableName() string {
	return "moe_customer_filter"
}

// CustomerViewFilter CustomerView.Filter结构体定义
// revive:disable:exported
type CustomerViewFilter struct {
	Type     *string               `json:"type"`
	Filters  []*CustomerViewFilter `json:"filters"`
	Operator *string               `json:"operator"`
	Property *string               `json:"property"`
	Value    *string               `json:"value"`
	Values   *[]string             `json:"values"` // 指针，序列化为 null
}
