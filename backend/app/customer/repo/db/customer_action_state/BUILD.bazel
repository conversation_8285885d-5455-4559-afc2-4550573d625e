load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_action_state",
    srcs = [
        "customer_action_state.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_action_state",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
