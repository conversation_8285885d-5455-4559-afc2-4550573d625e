// Package state 包含处理任务状态的逻辑。
package state

import (
	"context"
	"sync"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// QueueBatch 定义了任务队列的批量大小。
const QueueBatch = 1024

// TaskLoopInterval 定义了任务循环处理的间隔时间。
const TaskLoopInterval = time.Second * 30

type TaskLoop struct {
	queue   chan Task
	after   time.Time
	seen    sync.Map // 用于记录已经入队的任务ID
	repo    deployplatform.ReadWriter
	factory *PhaseTaskFactory
	ticker  *time.Ticker // 新增定时器字段
}

// NewTaskLoop 创建并返回一个新的TaskLoop实例。
func NewTaskLoop() *TaskLoop {
	repo := deployplatform.New()
	factory := &PhaseTaskFactory{}
	ticker := time.NewTicker(time.Minute * 5) // 初始化定时器，设置为5分钟
	return &TaskLoop{
		queue:   make(chan Task, QueueBatch),
		after:   time.Unix(0, 0),
		seen:    sync.Map{}, // 使用 sync.Map 确保线程安全
		repo:    repo,
		factory: factory,
		ticker:  ticker, // 设置定时器
	}
}

func (e *TaskLoop) tryEnqueueDBTask(ctx context.Context, after time.Time) {
	dbTasks, err := e.repo.GetDeployTasksNotEnded(ctx, after)
	if err != nil {
		log.Errorf("failed to get deploy tasks not ended: %v, after: %v", err, after)
		return
	}

	if len(dbTasks) == 0 {
		return
	}

	e.after = dbTasks[len(dbTasks)-1].UpdatedAt

	for _, dbTask := range dbTasks {
		taskID := dbTask.ID
		if _, exists := e.seen.Load(taskID); exists {
			log.Warnf("task already in queue, skipping, taskID: %d", taskID)
			continue
		}

		task, err := e.factory.NewPhaseTask(e.repo, dbTask)
		if err != nil {
			log.Warnf("new phase task error, will continue: %v, taskID: %d", err, dbTask.ID)
			continue
		}

		select {
		case e.queue <- task:
			e.seen.Store(taskID, struct{}{})
		default:
			e.after = dbTask.UpdatedAt
			log.Warnf("queue is full, task skipped, taskID: %d", taskID)
			return
		}
	}
}

// TryDequeue 尝试从队列中取出一个任务。
func (e *TaskLoop) TryDequeue(ctx context.Context) (task Task, get bool) {
	select {
	case task = <-e.queue:
		get = true
		taskID := task.ID()
		e.seen.Delete(taskID)
	default:
		get = false
		e.tryEnqueueDBTask(ctx, e.after)
	}
	return
}

// Loop 是TaskLoop的主要循环逻辑，负责持续处理任务队列中的任务。
func (e *TaskLoop) Loop(ctx context.Context) {
	defer e.ticker.Stop()
	for {
		select {
		case <-ctx.Done(): // 提供退出机制
			log.Infof("task loop stopped")
			return
		case <-e.ticker.C: // 定时器触发时拉取任务
			e.tryEnqueueDBTask(ctx, time.Unix(0, 0))
		default:
		}

		taskState, get := e.TryDequeue(ctx)
		if !get {
			time.Sleep(TaskLoopInterval)
			continue
		}

		go func(task Task) {
			log.Debugf("enter task loop: taskID: %d, state: %s", taskState.ID(), taskState.StateType())
			newState, err := task.Change(nil)
			if err != nil {
				log.Errorf("change state error: %v, taskID: %d", err, task.ID())
				return
			}
			if newState == nil {
				log.Warnf("new state is nil, taskID: %d", task.ID())
				return
			}
			log.Debugf("exit task loop: taskID: %d, new state: %s", taskState.ID(), newState.StateType())
		}(taskState)
	}
}
