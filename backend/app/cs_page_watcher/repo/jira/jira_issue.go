package jira

import "time"

// Assignee represents the assignee of a Jira issue.
type Assignee struct {
	DisplayName  string
	EmailAddress string
}

type Issue struct {
	ID            string
	Key           string
	Summary       string
	Status        string
	Comment       string
	Created       time.Time
	Updated       time.Time
	Assignee      Assignee // 添加Assignee字段
	T1OrGeneral   string
	IssuePriority string
	CustomerStage string
	Components    []string // 添加 Components 字段
	// CustomFields  map[string]interface{} // 用于存储用户自定义的字段
}
