load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "sales",
    srcs = [
        "db.go",
        "opportunity.go",
        "opportunity_line_item.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/repo/salesforce",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
