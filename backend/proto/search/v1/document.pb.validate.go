// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/search/v1/document.proto

package searchpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BulkDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkDocumentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkDocumentRequestMultiError, or nil if none found.
func (m *BulkDocumentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetOperations()); l < 1 || l > 5000 {
		err := BulkDocumentRequestValidationError{
			field:  "Operations",
			reason: "value must contain between 1 and 5000 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetOperations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkDocumentRequestValidationError{
						field:  fmt.Sprintf("Operations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkDocumentRequestValidationError{
						field:  fmt.Sprintf("Operations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkDocumentRequestValidationError{
					field:  fmt.Sprintf("Operations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BulkDocumentRequestMultiError(errors)
	}

	return nil
}

// BulkDocumentRequestMultiError is an error wrapping multiple validation
// errors returned by BulkDocumentRequest.ValidateAll() if the designated
// constraints aren't met.
type BulkDocumentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentRequestMultiError) AllErrors() []error { return m }

// BulkDocumentRequestValidationError is the validation error returned by
// BulkDocumentRequest.Validate if the designated constraints aren't met.
type BulkDocumentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentRequestValidationError) ErrorName() string {
	return "BulkDocumentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentRequestValidationError{}

// Validate checks the field values on BulkDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkDocumentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkDocumentResponseMultiError, or nil if none found.
func (m *BulkDocumentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HasErrors

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkDocumentResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkDocumentResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkDocumentResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Took

	if len(errors) > 0 {
		return BulkDocumentResponseMultiError(errors)
	}

	return nil
}

// BulkDocumentResponseMultiError is an error wrapping multiple validation
// errors returned by BulkDocumentResponse.ValidateAll() if the designated
// constraints aren't met.
type BulkDocumentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentResponseMultiError) AllErrors() []error { return m }

// BulkDocumentResponseValidationError is the validation error returned by
// BulkDocumentResponse.Validate if the designated constraints aren't met.
type BulkDocumentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentResponseValidationError) ErrorName() string {
	return "BulkDocumentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentResponseValidationError{}

// Validate checks the field values on BulkDocumentRequest_BulkOperation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BulkDocumentRequest_BulkOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentRequest_BulkOperation
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BulkDocumentRequest_BulkOperationMultiError, or nil if none found.
func (m *BulkDocumentRequest_BulkOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentRequest_BulkOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _BulkDocumentRequest_BulkOperation_OperationType_NotInLookup[m.GetOperationType()]; ok {
		err := BulkDocumentRequest_BulkOperationValidationError{
			field:  "OperationType",
			reason: "value must not be in list [OPERATION_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := OperationType_name[int32(m.GetOperationType())]; !ok {
		err := BulkDocumentRequest_BulkOperationValidationError{
			field:  "OperationType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTarget() == nil {
		err := BulkDocumentRequest_BulkOperationValidationError{
			field:  "Target",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkDocumentRequest_BulkOperationValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkDocumentRequest_BulkOperationValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkDocumentRequest_BulkOperationValidationError{
				field:  "Target",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Document != nil {

		if all {
			switch v := interface{}(m.GetDocument()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkDocumentRequest_BulkOperationValidationError{
						field:  "Document",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkDocumentRequest_BulkOperationValidationError{
						field:  "Document",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDocument()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkDocumentRequest_BulkOperationValidationError{
					field:  "Document",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BulkDocumentRequest_BulkOperationMultiError(errors)
	}

	return nil
}

// BulkDocumentRequest_BulkOperationMultiError is an error wrapping multiple
// validation errors returned by
// BulkDocumentRequest_BulkOperation.ValidateAll() if the designated
// constraints aren't met.
type BulkDocumentRequest_BulkOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentRequest_BulkOperationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentRequest_BulkOperationMultiError) AllErrors() []error { return m }

// BulkDocumentRequest_BulkOperationValidationError is the validation error
// returned by BulkDocumentRequest_BulkOperation.Validate if the designated
// constraints aren't met.
type BulkDocumentRequest_BulkOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentRequest_BulkOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentRequest_BulkOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentRequest_BulkOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentRequest_BulkOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentRequest_BulkOperationValidationError) ErrorName() string {
	return "BulkDocumentRequest_BulkOperationValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentRequest_BulkOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentRequest_BulkOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentRequest_BulkOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentRequest_BulkOperationValidationError{}

var _BulkDocumentRequest_BulkOperation_OperationType_NotInLookup = map[OperationType]struct{}{
	0: {},
}

// Validate checks the field values on
// BulkDocumentRequest_BulkOperation_BulkTarget with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BulkDocumentRequest_BulkOperation_BulkTarget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BulkDocumentRequest_BulkOperation_BulkTarget with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BulkDocumentRequest_BulkOperation_BulkTargetMultiError, or nil if none found.
func (m *BulkDocumentRequest_BulkOperation_BulkTarget) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentRequest_BulkOperation_BulkTarget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetIndex()); l < 1 || l > 100 {
		err := BulkDocumentRequest_BulkOperation_BulkTargetValidationError{
			field:  "Index",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Id != nil {
		// no validation rules for Id
	}

	if len(errors) > 0 {
		return BulkDocumentRequest_BulkOperation_BulkTargetMultiError(errors)
	}

	return nil
}

// BulkDocumentRequest_BulkOperation_BulkTargetMultiError is an error wrapping
// multiple validation errors returned by
// BulkDocumentRequest_BulkOperation_BulkTarget.ValidateAll() if the
// designated constraints aren't met.
type BulkDocumentRequest_BulkOperation_BulkTargetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentRequest_BulkOperation_BulkTargetMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentRequest_BulkOperation_BulkTargetMultiError) AllErrors() []error { return m }

// BulkDocumentRequest_BulkOperation_BulkTargetValidationError is the
// validation error returned by
// BulkDocumentRequest_BulkOperation_BulkTarget.Validate if the designated
// constraints aren't met.
type BulkDocumentRequest_BulkOperation_BulkTargetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) ErrorName() string {
	return "BulkDocumentRequest_BulkOperation_BulkTargetValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentRequest_BulkOperation_BulkTargetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentRequest_BulkOperation_BulkTarget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentRequest_BulkOperation_BulkTargetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentRequest_BulkOperation_BulkTargetValidationError{}

// Validate checks the field values on BulkDocumentResponse_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkDocumentResponse_Error) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentResponse_Error with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkDocumentResponse_ErrorMultiError, or nil if none found.
func (m *BulkDocumentResponse_Error) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentResponse_Error) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrorType

	// no validation rules for ErrorReason

	if len(errors) > 0 {
		return BulkDocumentResponse_ErrorMultiError(errors)
	}

	return nil
}

// BulkDocumentResponse_ErrorMultiError is an error wrapping multiple
// validation errors returned by BulkDocumentResponse_Error.ValidateAll() if
// the designated constraints aren't met.
type BulkDocumentResponse_ErrorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentResponse_ErrorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentResponse_ErrorMultiError) AllErrors() []error { return m }

// BulkDocumentResponse_ErrorValidationError is the validation error returned
// by BulkDocumentResponse_Error.Validate if the designated constraints aren't met.
type BulkDocumentResponse_ErrorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentResponse_ErrorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentResponse_ErrorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentResponse_ErrorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentResponse_ErrorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentResponse_ErrorValidationError) ErrorName() string {
	return "BulkDocumentResponse_ErrorValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentResponse_ErrorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentResponse_Error.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentResponse_ErrorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentResponse_ErrorValidationError{}

// Validate checks the field values on BulkDocumentResponse_Shards with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkDocumentResponse_Shards) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentResponse_Shards with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkDocumentResponse_ShardsMultiError, or nil if none found.
func (m *BulkDocumentResponse_Shards) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentResponse_Shards) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Successful

	// no validation rules for Failed

	if len(errors) > 0 {
		return BulkDocumentResponse_ShardsMultiError(errors)
	}

	return nil
}

// BulkDocumentResponse_ShardsMultiError is an error wrapping multiple
// validation errors returned by BulkDocumentResponse_Shards.ValidateAll() if
// the designated constraints aren't met.
type BulkDocumentResponse_ShardsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentResponse_ShardsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentResponse_ShardsMultiError) AllErrors() []error { return m }

// BulkDocumentResponse_ShardsValidationError is the validation error returned
// by BulkDocumentResponse_Shards.Validate if the designated constraints
// aren't met.
type BulkDocumentResponse_ShardsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentResponse_ShardsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentResponse_ShardsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentResponse_ShardsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentResponse_ShardsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentResponse_ShardsValidationError) ErrorName() string {
	return "BulkDocumentResponse_ShardsValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentResponse_ShardsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentResponse_Shards.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentResponse_ShardsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentResponse_ShardsValidationError{}

// Validate checks the field values on BulkDocumentResponse_OperationResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BulkDocumentResponse_OperationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkDocumentResponse_OperationResult
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BulkDocumentResponse_OperationResultMultiError, or nil if none found.
func (m *BulkDocumentResponse_OperationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkDocumentResponse_OperationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperationType

	// no validation rules for Index

	// no validation rules for DocumentId

	// no validation rules for Status

	// no validation rules for Result

	// no validation rules for Version

	if all {
		switch v := interface{}(m.GetShards()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkDocumentResponse_OperationResultValidationError{
					field:  "Shards",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkDocumentResponse_OperationResultValidationError{
					field:  "Shards",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShards()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkDocumentResponse_OperationResultValidationError{
				field:  "Shards",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Error != nil {

		if all {
			switch v := interface{}(m.GetError()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BulkDocumentResponse_OperationResultValidationError{
						field:  "Error",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BulkDocumentResponse_OperationResultValidationError{
						field:  "Error",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetError()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BulkDocumentResponse_OperationResultValidationError{
					field:  "Error",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BulkDocumentResponse_OperationResultMultiError(errors)
	}

	return nil
}

// BulkDocumentResponse_OperationResultMultiError is an error wrapping multiple
// validation errors returned by
// BulkDocumentResponse_OperationResult.ValidateAll() if the designated
// constraints aren't met.
type BulkDocumentResponse_OperationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkDocumentResponse_OperationResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkDocumentResponse_OperationResultMultiError) AllErrors() []error { return m }

// BulkDocumentResponse_OperationResultValidationError is the validation error
// returned by BulkDocumentResponse_OperationResult.Validate if the designated
// constraints aren't met.
type BulkDocumentResponse_OperationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkDocumentResponse_OperationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkDocumentResponse_OperationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkDocumentResponse_OperationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkDocumentResponse_OperationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkDocumentResponse_OperationResultValidationError) ErrorName() string {
	return "BulkDocumentResponse_OperationResultValidationError"
}

// Error satisfies the builtin error interface
func (e BulkDocumentResponse_OperationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkDocumentResponse_OperationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkDocumentResponse_OperationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkDocumentResponse_OperationResultValidationError{}
