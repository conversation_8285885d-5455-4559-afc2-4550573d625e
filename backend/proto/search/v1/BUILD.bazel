load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "searchpb_proto",
    srcs = [
        "document.proto",
        "search_service.proto",
        "search_strategy.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_envoyproxy_protoc_gen_validate//validate:validate_proto",
        "@com_google_protobuf//:struct_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:field_behavior_proto",
    ],
)

go_proto_library(
    name = "searchpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "//:pgv_plugin_go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/search/v1",
    proto = ":searchpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@com_envoyproxy_protoc_gen_validate//validate:go_default_library",
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
    ],
)

go_library(
    name = "search",
    embed = [":searchpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/search/v1",
    visibility = ["//visibility:public"],
)
