// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/environment_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterEnvironmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterEnvironmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterEnvironmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterEnvironmentsRequestMultiError, or nil if none found.
func (m *RegisterEnvironmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterEnvironmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEnvironments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterEnvironmentsRequestValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterEnvironmentsRequestValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterEnvironmentsRequestValidationError{
					field:  fmt.Sprintf("Environments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RegisterEnvironmentsRequestMultiError(errors)
	}

	return nil
}

// RegisterEnvironmentsRequestMultiError is an error wrapping multiple
// validation errors returned by RegisterEnvironmentsRequest.ValidateAll() if
// the designated constraints aren't met.
type RegisterEnvironmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterEnvironmentsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterEnvironmentsRequestMultiError) AllErrors() []error { return m }

// RegisterEnvironmentsRequestValidationError is the validation error returned
// by RegisterEnvironmentsRequest.Validate if the designated constraints
// aren't met.
type RegisterEnvironmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterEnvironmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterEnvironmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterEnvironmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterEnvironmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterEnvironmentsRequestValidationError) ErrorName() string {
	return "RegisterEnvironmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterEnvironmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterEnvironmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterEnvironmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterEnvironmentsRequestValidationError{}

// Validate checks the field values on RegisterEnvironmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterEnvironmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterEnvironmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterEnvironmentsResponseMultiError, or nil if none found.
func (m *RegisterEnvironmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterEnvironmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEnvironments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterEnvironmentsResponseValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterEnvironmentsResponseValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterEnvironmentsResponseValidationError{
					field:  fmt.Sprintf("Environments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RegisterEnvironmentsResponseMultiError(errors)
	}

	return nil
}

// RegisterEnvironmentsResponseMultiError is an error wrapping multiple
// validation errors returned by RegisterEnvironmentsResponse.ValidateAll() if
// the designated constraints aren't met.
type RegisterEnvironmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterEnvironmentsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterEnvironmentsResponseMultiError) AllErrors() []error { return m }

// RegisterEnvironmentsResponseValidationError is the validation error returned
// by RegisterEnvironmentsResponse.Validate if the designated constraints
// aren't met.
type RegisterEnvironmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterEnvironmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterEnvironmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterEnvironmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterEnvironmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterEnvironmentsResponseValidationError) ErrorName() string {
	return "RegisterEnvironmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterEnvironmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterEnvironmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterEnvironmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterEnvironmentsResponseValidationError{}

// Validate checks the field values on CheckEnvironmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEnvironmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEnvironmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEnvironmentRequestMultiError, or nil if none found.
func (m *CheckEnvironmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEnvironmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEnvironmentRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckEnvironmentRequestMultiError(errors)
	}

	return nil
}

// CheckEnvironmentRequestMultiError is an error wrapping multiple validation
// errors returned by CheckEnvironmentRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckEnvironmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEnvironmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEnvironmentRequestMultiError) AllErrors() []error { return m }

// CheckEnvironmentRequestValidationError is the validation error returned by
// CheckEnvironmentRequest.Validate if the designated constraints aren't met.
type CheckEnvironmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEnvironmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEnvironmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEnvironmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEnvironmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEnvironmentRequestValidationError) ErrorName() string {
	return "CheckEnvironmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEnvironmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEnvironmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEnvironmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEnvironmentRequestValidationError{}

// Validate checks the field values on CheckEnvironmentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEnvironmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEnvironmentResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEnvironmentResponseMultiError, or nil if none found.
func (m *CheckEnvironmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEnvironmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckEnvironmentResponseValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckEnvironmentResponseValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckEnvironmentResponseValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Healthy

	if len(errors) > 0 {
		return CheckEnvironmentResponseMultiError(errors)
	}

	return nil
}

// CheckEnvironmentResponseMultiError is an error wrapping multiple validation
// errors returned by CheckEnvironmentResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckEnvironmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEnvironmentResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEnvironmentResponseMultiError) AllErrors() []error { return m }

// CheckEnvironmentResponseValidationError is the validation error returned by
// CheckEnvironmentResponse.Validate if the designated constraints aren't met.
type CheckEnvironmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEnvironmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEnvironmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEnvironmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEnvironmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEnvironmentResponseValidationError) ErrorName() string {
	return "CheckEnvironmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEnvironmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEnvironmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEnvironmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEnvironmentResponseValidationError{}

// Validate checks the field values on ListEnvironmentsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListEnvironmentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEnvironmentsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEnvironmentsRequestMultiError, or nil if none found.
func (m *ListEnvironmentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEnvironmentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetStatuses()) > 5 {
		err := ListEnvironmentsRequestValidationError{
			field:  "Statuses",
			reason: "value must contain no more than 5 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_ListEnvironmentsRequest_Statuses_Unique := make(map[string]struct{}, len(m.GetStatuses()))

	for idx, item := range m.GetStatuses() {
		_, _ = idx, item

		if _, exists := _ListEnvironmentsRequest_Statuses_Unique[item]; exists {
			err := ListEnvironmentsRequestValidationError{
				field:  fmt.Sprintf("Statuses[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_ListEnvironmentsRequest_Statuses_Unique[item] = struct{}{}
		}

		// no validation rules for Statuses[idx]
	}

	if len(errors) > 0 {
		return ListEnvironmentsRequestMultiError(errors)
	}

	return nil
}

// ListEnvironmentsRequestMultiError is an error wrapping multiple validation
// errors returned by ListEnvironmentsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListEnvironmentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEnvironmentsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEnvironmentsRequestMultiError) AllErrors() []error { return m }

// ListEnvironmentsRequestValidationError is the validation error returned by
// ListEnvironmentsRequest.Validate if the designated constraints aren't met.
type ListEnvironmentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEnvironmentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEnvironmentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEnvironmentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEnvironmentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEnvironmentsRequestValidationError) ErrorName() string {
	return "ListEnvironmentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListEnvironmentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEnvironmentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEnvironmentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEnvironmentsRequestValidationError{}

// Validate checks the field values on ListEnvironmentsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListEnvironmentsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEnvironmentsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEnvironmentsResponseMultiError, or nil if none found.
func (m *ListEnvironmentsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEnvironmentsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEnvironments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListEnvironmentsResponseValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListEnvironmentsResponseValidationError{
						field:  fmt.Sprintf("Environments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListEnvironmentsResponseValidationError{
					field:  fmt.Sprintf("Environments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListEnvironmentsResponseMultiError(errors)
	}

	return nil
}

// ListEnvironmentsResponseMultiError is an error wrapping multiple validation
// errors returned by ListEnvironmentsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListEnvironmentsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEnvironmentsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEnvironmentsResponseMultiError) AllErrors() []error { return m }

// ListEnvironmentsResponseValidationError is the validation error returned by
// ListEnvironmentsResponse.Validate if the designated constraints aren't met.
type ListEnvironmentsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEnvironmentsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEnvironmentsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEnvironmentsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEnvironmentsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEnvironmentsResponseValidationError) ErrorName() string {
	return "ListEnvironmentsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListEnvironmentsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEnvironmentsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEnvironmentsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEnvironmentsResponseValidationError{}

// Validate checks the field values on GetEnvironmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEnvironmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEnvironmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEnvironmentRequestMultiError, or nil if none found.
func (m *GetEnvironmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEnvironmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEnvironmentRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEnvironmentRequestMultiError(errors)
	}

	return nil
}

// GetEnvironmentRequestMultiError is an error wrapping multiple validation
// errors returned by GetEnvironmentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEnvironmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEnvironmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEnvironmentRequestMultiError) AllErrors() []error { return m }

// GetEnvironmentRequestValidationError is the validation error returned by
// GetEnvironmentRequest.Validate if the designated constraints aren't met.
type GetEnvironmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEnvironmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEnvironmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEnvironmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEnvironmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEnvironmentRequestValidationError) ErrorName() string {
	return "GetEnvironmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEnvironmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEnvironmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEnvironmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEnvironmentRequestValidationError{}

// Validate checks the field values on UpdateEnvironmentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEnvironmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEnvironmentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEnvironmentRequestMultiError, or nil if none found.
func (m *UpdateEnvironmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEnvironmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEnvironmentRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEnvironmentRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDatabases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("Databases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEnvironmentRequestValidationError{
					field:  fmt.Sprintf("Databases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("Caches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEnvironmentRequestValidationError{
					field:  fmt.Sprintf("Caches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMessageQueues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  fmt.Sprintf("MessageQueues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEnvironmentRequestValidationError{
					field:  fmt.Sprintf("MessageQueues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Status != nil {
		// no validation rules for Status
	}

	if m.IsManaged != nil {
		// no validation rules for IsManaged
	}

	if m.Name != nil {

		if utf8.RuneCountInString(m.GetName()) > 64 {
			err := UpdateEnvironmentRequestValidationError{
				field:  "Name",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Description != nil {

		if utf8.RuneCountInString(m.GetDescription()) > 1024 {
			err := UpdateEnvironmentRequestValidationError{
				field:  "Description",
				reason: "value length must be at most 1024 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Extra != nil {

		if all {
			switch v := interface{}(m.GetExtra()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateEnvironmentRequestValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateEnvironmentRequestValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateEnvironmentRequestMultiError(errors)
	}

	return nil
}

// UpdateEnvironmentRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateEnvironmentRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateEnvironmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEnvironmentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEnvironmentRequestMultiError) AllErrors() []error { return m }

// UpdateEnvironmentRequestValidationError is the validation error returned by
// UpdateEnvironmentRequest.Validate if the designated constraints aren't met.
type UpdateEnvironmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEnvironmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEnvironmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEnvironmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEnvironmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEnvironmentRequestValidationError) ErrorName() string {
	return "UpdateEnvironmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEnvironmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEnvironmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEnvironmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEnvironmentRequestValidationError{}
