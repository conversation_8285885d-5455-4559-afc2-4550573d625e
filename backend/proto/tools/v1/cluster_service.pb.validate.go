// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/cluster_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListClustersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClustersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClustersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClustersRequestMultiError, or nil if none found.
func (m *ListClustersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClustersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Platform != nil {

		if utf8.RuneCountInString(m.GetPlatform()) > 128 {
			err := ListClustersRequestValidationError{
				field:  "Platform",
				reason: "value length must be at most 128 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListClustersRequestMultiError(errors)
	}

	return nil
}

// ListClustersRequestMultiError is an error wrapping multiple validation
// errors returned by ListClustersRequest.ValidateAll() if the designated
// constraints aren't met.
type ListClustersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClustersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClustersRequestMultiError) AllErrors() []error { return m }

// ListClustersRequestValidationError is the validation error returned by
// ListClustersRequest.Validate if the designated constraints aren't met.
type ListClustersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClustersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClustersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClustersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClustersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClustersRequestValidationError) ErrorName() string {
	return "ListClustersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListClustersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClustersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClustersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClustersRequestValidationError{}

// Validate checks the field values on ListClustersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClustersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClustersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClustersResponseMultiError, or nil if none found.
func (m *ListClustersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClustersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClusters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClustersResponseValidationError{
						field:  fmt.Sprintf("Clusters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClustersResponseValidationError{
						field:  fmt.Sprintf("Clusters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClustersResponseValidationError{
					field:  fmt.Sprintf("Clusters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClustersResponseMultiError(errors)
	}

	return nil
}

// ListClustersResponseMultiError is an error wrapping multiple validation
// errors returned by ListClustersResponse.ValidateAll() if the designated
// constraints aren't met.
type ListClustersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClustersResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClustersResponseMultiError) AllErrors() []error { return m }

// ListClustersResponseValidationError is the validation error returned by
// ListClustersResponse.Validate if the designated constraints aren't met.
type ListClustersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClustersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClustersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClustersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClustersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClustersResponseValidationError) ErrorName() string {
	return "ListClustersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListClustersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClustersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClustersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClustersResponseValidationError{}

// Validate checks the field values on GetClusterRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetClusterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClusterRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClusterRequestMultiError, or nil if none found.
func (m *GetClusterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClusterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClusterRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClusterRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClusterRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetClusterRequestMultiError(errors)
	}

	return nil
}

// GetClusterRequestMultiError is an error wrapping multiple validation errors
// returned by GetClusterRequest.ValidateAll() if the designated constraints
// aren't met.
type GetClusterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClusterRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClusterRequestMultiError) AllErrors() []error { return m }

// GetClusterRequestValidationError is the validation error returned by
// GetClusterRequest.Validate if the designated constraints aren't met.
type GetClusterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClusterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClusterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClusterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClusterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClusterRequestValidationError) ErrorName() string {
	return "GetClusterRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClusterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClusterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClusterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClusterRequestValidationError{}

// Validate checks the field values on ListNodeGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNodeGroupsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodeGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNodeGroupsRequestMultiError, or nil if none found.
func (m *ListNodeGroupsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodeGroupsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlatform()) > 128 {
		err := ListNodeGroupsRequestValidationError{
			field:  "Platform",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := ListNodeGroupsRequestValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListNodeGroupsRequestMultiError(errors)
	}

	return nil
}

// ListNodeGroupsRequestMultiError is an error wrapping multiple validation
// errors returned by ListNodeGroupsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListNodeGroupsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodeGroupsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodeGroupsRequestMultiError) AllErrors() []error { return m }

// ListNodeGroupsRequestValidationError is the validation error returned by
// ListNodeGroupsRequest.Validate if the designated constraints aren't met.
type ListNodeGroupsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodeGroupsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodeGroupsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodeGroupsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodeGroupsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodeGroupsRequestValidationError) ErrorName() string {
	return "ListNodeGroupsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListNodeGroupsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodeGroupsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodeGroupsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodeGroupsRequestValidationError{}

// Validate checks the field values on ListNodeGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNodeGroupsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodeGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNodeGroupsResponseMultiError, or nil if none found.
func (m *ListNodeGroupsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodeGroupsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNodeGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNodeGroupsResponseValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNodeGroupsResponseValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNodeGroupsResponseValidationError{
					field:  fmt.Sprintf("NodeGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListNodeGroupsResponseMultiError(errors)
	}

	return nil
}

// ListNodeGroupsResponseMultiError is an error wrapping multiple validation
// errors returned by ListNodeGroupsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListNodeGroupsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodeGroupsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodeGroupsResponseMultiError) AllErrors() []error { return m }

// ListNodeGroupsResponseValidationError is the validation error returned by
// ListNodeGroupsResponse.Validate if the designated constraints aren't met.
type ListNodeGroupsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodeGroupsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodeGroupsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodeGroupsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodeGroupsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodeGroupsResponseValidationError) ErrorName() string {
	return "ListNodeGroupsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNodeGroupsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodeGroupsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodeGroupsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodeGroupsResponseValidationError{}

// Validate checks the field values on GetNodeGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNodeGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNodeGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNodeGroupRequestMultiError, or nil if none found.
func (m *GetNodeGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNodeGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNodeGroupRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNodeGroupRequestValidationError{
					field:  "Identifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNodeGroupRequestValidationError{
				field:  "Identifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNodeGroupRequestMultiError(errors)
	}

	return nil
}

// GetNodeGroupRequestMultiError is an error wrapping multiple validation
// errors returned by GetNodeGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNodeGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNodeGroupRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNodeGroupRequestMultiError) AllErrors() []error { return m }

// GetNodeGroupRequestValidationError is the validation error returned by
// GetNodeGroupRequest.Validate if the designated constraints aren't met.
type GetNodeGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNodeGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNodeGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNodeGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNodeGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNodeGroupRequestValidationError) ErrorName() string {
	return "GetNodeGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNodeGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNodeGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNodeGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNodeGroupRequestValidationError{}

// Validate checks the field values on ListNodesRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListNodesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNodesRequestMultiError, or nil if none found.
func (m *ListNodesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlatform()) > 128 {
		err := ListNodesRequestValidationError{
			field:  "Platform",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := ListNodesRequestValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListNodesRequestMultiError(errors)
	}

	return nil
}

// ListNodesRequestMultiError is an error wrapping multiple validation errors
// returned by ListNodesRequest.ValidateAll() if the designated constraints
// aren't met.
type ListNodesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodesRequestMultiError) AllErrors() []error { return m }

// ListNodesRequestValidationError is the validation error returned by
// ListNodesRequest.Validate if the designated constraints aren't met.
type ListNodesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodesRequestValidationError) ErrorName() string { return "ListNodesRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListNodesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodesRequestValidationError{}

// Validate checks the field values on ListNodesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListNodesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNodesResponseMultiError, or nil if none found.
func (m *ListNodesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNodesResponseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNodesResponseValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNodesResponseValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListNodesResponseMultiError(errors)
	}

	return nil
}

// ListNodesResponseMultiError is an error wrapping multiple validation errors
// returned by ListNodesResponse.ValidateAll() if the designated constraints
// aren't met.
type ListNodesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodesResponseMultiError) AllErrors() []error { return m }

// ListNodesResponseValidationError is the validation error returned by
// ListNodesResponse.Validate if the designated constraints aren't met.
type ListNodesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodesResponseValidationError) ErrorName() string {
	return "ListNodesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNodesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodesResponseValidationError{}

// Validate checks the field values on GetNodeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNodeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetNodeRequestMultiError,
// or nil if none found.
func (m *GetNodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := GetNodeRequestValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	if len(errors) > 0 {
		return GetNodeRequestMultiError(errors)
	}

	return nil
}

// GetNodeRequestMultiError is an error wrapping multiple validation errors
// returned by GetNodeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetNodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNodeRequestMultiError) AllErrors() []error { return m }

// GetNodeRequestValidationError is the validation error returned by
// GetNodeRequest.Validate if the designated constraints aren't met.
type GetNodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNodeRequestValidationError) ErrorName() string { return "GetNodeRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetNodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNodeRequestValidationError{}

// Validate checks the field values on ListNamespacesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNamespacesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNamespacesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNamespacesRequestMultiError, or nil if none found.
func (m *ListNamespacesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNamespacesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPlatform()) > 128 {
		err := ListNamespacesRequestValidationError{
			field:  "Platform",
			reason: "value length must be at most 128 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCluster()) > 63 {
		err := ListNamespacesRequestValidationError{
			field:  "Cluster",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListNamespacesRequestMultiError(errors)
	}

	return nil
}

// ListNamespacesRequestMultiError is an error wrapping multiple validation
// errors returned by ListNamespacesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListNamespacesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNamespacesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNamespacesRequestMultiError) AllErrors() []error { return m }

// ListNamespacesRequestValidationError is the validation error returned by
// ListNamespacesRequest.Validate if the designated constraints aren't met.
type ListNamespacesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNamespacesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNamespacesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNamespacesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNamespacesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNamespacesRequestValidationError) ErrorName() string {
	return "ListNamespacesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListNamespacesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNamespacesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNamespacesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNamespacesRequestValidationError{}

// Validate checks the field values on ListNamespacesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNamespacesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNamespacesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNamespacesResponseMultiError, or nil if none found.
func (m *ListNamespacesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNamespacesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNamespaces() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNamespacesResponseValidationError{
						field:  fmt.Sprintf("Namespaces[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNamespacesResponseValidationError{
						field:  fmt.Sprintf("Namespaces[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNamespacesResponseValidationError{
					field:  fmt.Sprintf("Namespaces[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListNamespacesResponseMultiError(errors)
	}

	return nil
}

// ListNamespacesResponseMultiError is an error wrapping multiple validation
// errors returned by ListNamespacesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListNamespacesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNamespacesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNamespacesResponseMultiError) AllErrors() []error { return m }

// ListNamespacesResponseValidationError is the validation error returned by
// ListNamespacesResponse.Validate if the designated constraints aren't met.
type ListNamespacesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNamespacesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNamespacesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNamespacesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNamespacesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNamespacesResponseValidationError) ErrorName() string {
	return "ListNamespacesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNamespacesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNamespacesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNamespacesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNamespacesResponseValidationError{}

// Validate checks the field values on GetNamespaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNamespaceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNamespaceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNamespaceRequestMultiError, or nil if none found.
func (m *GetNamespaceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNamespaceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Environment != nil {

		if all {
			switch v := interface{}(m.GetEnvironment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNamespaceRequestValidationError{
						field:  "Environment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNamespaceRequestValidationError{
						field:  "Environment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNamespaceRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNamespaceRequestMultiError(errors)
	}

	return nil
}

// GetNamespaceRequestMultiError is an error wrapping multiple validation
// errors returned by GetNamespaceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNamespaceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNamespaceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNamespaceRequestMultiError) AllErrors() []error { return m }

// GetNamespaceRequestValidationError is the validation error returned by
// GetNamespaceRequest.Validate if the designated constraints aren't met.
type GetNamespaceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNamespaceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNamespaceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNamespaceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNamespaceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNamespaceRequestValidationError) ErrorName() string {
	return "GetNamespaceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNamespaceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNamespaceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNamespaceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNamespaceRequestValidationError{}

// Validate checks the field values on ListResourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListResourcesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListResourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListResourcesRequestMultiError, or nil if none found.
func (m *ListResourcesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListResourcesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListResourcesRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListResourcesRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListResourcesRequestValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Kind

	// no validation rules for PageSize

	// no validation rules for PageToken

	if m.ApiVersion != nil {
		// no validation rules for ApiVersion
	}

	if len(errors) > 0 {
		return ListResourcesRequestMultiError(errors)
	}

	return nil
}

// ListResourcesRequestMultiError is an error wrapping multiple validation
// errors returned by ListResourcesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListResourcesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListResourcesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListResourcesRequestMultiError) AllErrors() []error { return m }

// ListResourcesRequestValidationError is the validation error returned by
// ListResourcesRequest.Validate if the designated constraints aren't met.
type ListResourcesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListResourcesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListResourcesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListResourcesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListResourcesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListResourcesRequestValidationError) ErrorName() string {
	return "ListResourcesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListResourcesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListResourcesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListResourcesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListResourcesRequestValidationError{}

// Validate checks the field values on ListResourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListResourcesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListResourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListResourcesResponseMultiError, or nil if none found.
func (m *ListResourcesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListResourcesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListResourcesResponseValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return ListResourcesResponseMultiError(errors)
	}

	return nil
}

// ListResourcesResponseMultiError is an error wrapping multiple validation
// errors returned by ListResourcesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListResourcesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListResourcesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListResourcesResponseMultiError) AllErrors() []error { return m }

// ListResourcesResponseValidationError is the validation error returned by
// ListResourcesResponse.Validate if the designated constraints aren't met.
type ListResourcesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListResourcesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListResourcesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListResourcesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListResourcesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListResourcesResponseValidationError) ErrorName() string {
	return "ListResourcesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListResourcesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListResourcesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListResourcesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListResourcesResponseValidationError{}

// Validate checks the field values on GetResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetResourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetResourceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetResourceRequestMultiError, or nil if none found.
func (m *GetResourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetResourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Name

	if m.Environment != nil {

		if all {
			switch v := interface{}(m.GetEnvironment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetResourceRequestValidationError{
						field:  "Environment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetResourceRequestValidationError{
						field:  "Environment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetResourceRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ApiVersion != nil {
		// no validation rules for ApiVersion
	}

	if len(errors) > 0 {
		return GetResourceRequestMultiError(errors)
	}

	return nil
}

// GetResourceRequestMultiError is an error wrapping multiple validation errors
// returned by GetResourceRequest.ValidateAll() if the designated constraints
// aren't met.
type GetResourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetResourceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetResourceRequestMultiError) AllErrors() []error { return m }

// GetResourceRequestValidationError is the validation error returned by
// GetResourceRequest.Validate if the designated constraints aren't met.
type GetResourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetResourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetResourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetResourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetResourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetResourceRequestValidationError) ErrorName() string {
	return "GetResourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetResourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetResourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetResourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetResourceRequestValidationError{}

// Validate checks the field values on ResetNodeGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetNodeGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetNodeGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetNodeGroupRequestMultiError, or nil if none found.
func (m *ResetNodeGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetNodeGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetLabels()) > 256 {
		err := ResetNodeGroupRequestValidationError{
			field:  "Labels",
			reason: "value must contain no more than 256 pair(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]string, len(m.GetLabels()))
		i := 0
		for key := range m.GetLabels() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLabels()[key]
			_ = val

			if utf8.RuneCountInString(key) > 255 {
				err := ResetNodeGroupRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 255 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if utf8.RuneCountInString(val) > 63 {
				err := ResetNodeGroupRequestValidationError{
					field:  fmt.Sprintf("Labels[%v]", key),
					reason: "value length must be at most 63 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	if m.Source != nil {

		if all {
			switch v := interface{}(m.GetSource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Source",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Source",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetNodeGroupRequestValidationError{
					field:  "Source",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Target != nil {

		if all {
			switch v := interface{}(m.GetTarget()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Target",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Target",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetNodeGroupRequestValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.InstanceClass != nil {
		// no validation rules for InstanceClass
	}

	if m.Image != nil {
		// no validation rules for Image
	}

	if m.MinSize != nil {
		// no validation rules for MinSize
	}

	if m.MaxSize != nil {
		// no validation rules for MaxSize
	}

	if m.DesiredSize != nil {
		// no validation rules for DesiredSize
	}

	if m.DiskSize != nil {
		// no validation rules for DiskSize
	}

	if m.Extra != nil {

		if all {
			switch v := interface{}(m.GetExtra()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetNodeGroupRequestValidationError{
						field:  "Extra",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetNodeGroupRequestValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResetNodeGroupRequestMultiError(errors)
	}

	return nil
}

// ResetNodeGroupRequestMultiError is an error wrapping multiple validation
// errors returned by ResetNodeGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetNodeGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetNodeGroupRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetNodeGroupRequestMultiError) AllErrors() []error { return m }

// ResetNodeGroupRequestValidationError is the validation error returned by
// ResetNodeGroupRequest.Validate if the designated constraints aren't met.
type ResetNodeGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetNodeGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetNodeGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetNodeGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetNodeGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetNodeGroupRequestValidationError) ErrorName() string {
	return "ResetNodeGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetNodeGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetNodeGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetNodeGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetNodeGroupRequestValidationError{}

// Validate checks the field values on ResetNodeGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetNodeGroupsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetNodeGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetNodeGroupsRequestMultiError, or nil if none found.
func (m *ResetNodeGroupsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetNodeGroupsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNodeGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetNodeGroupsRequestValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetNodeGroupsRequestValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetNodeGroupsRequestValidationError{
					field:  fmt.Sprintf("NodeGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResetNodeGroupsRequestMultiError(errors)
	}

	return nil
}

// ResetNodeGroupsRequestMultiError is an error wrapping multiple validation
// errors returned by ResetNodeGroupsRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetNodeGroupsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetNodeGroupsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetNodeGroupsRequestMultiError) AllErrors() []error { return m }

// ResetNodeGroupsRequestValidationError is the validation error returned by
// ResetNodeGroupsRequest.Validate if the designated constraints aren't met.
type ResetNodeGroupsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetNodeGroupsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetNodeGroupsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetNodeGroupsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetNodeGroupsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetNodeGroupsRequestValidationError) ErrorName() string {
	return "ResetNodeGroupsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetNodeGroupsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetNodeGroupsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetNodeGroupsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetNodeGroupsRequestValidationError{}

// Validate checks the field values on ResetNodeGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetNodeGroupsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetNodeGroupsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetNodeGroupsResponseMultiError, or nil if none found.
func (m *ResetNodeGroupsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetNodeGroupsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNodeGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetNodeGroupsResponseValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetNodeGroupsResponseValidationError{
						field:  fmt.Sprintf("NodeGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetNodeGroupsResponseValidationError{
					field:  fmt.Sprintf("NodeGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResetNodeGroupsResponseMultiError(errors)
	}

	return nil
}

// ResetNodeGroupsResponseMultiError is an error wrapping multiple validation
// errors returned by ResetNodeGroupsResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetNodeGroupsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetNodeGroupsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetNodeGroupsResponseMultiError) AllErrors() []error { return m }

// ResetNodeGroupsResponseValidationError is the validation error returned by
// ResetNodeGroupsResponse.Validate if the designated constraints aren't met.
type ResetNodeGroupsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetNodeGroupsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetNodeGroupsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetNodeGroupsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetNodeGroupsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetNodeGroupsResponseValidationError) ErrorName() string {
	return "ResetNodeGroupsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetNodeGroupsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetNodeGroupsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetNodeGroupsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetNodeGroupsResponseValidationError{}

// Validate checks the field values on ResetResourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetResourcesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetResourcesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetResourcesRequestMultiError, or nil if none found.
func (m *ResetResourcesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetResourcesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetResourcesRequestValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.SourceEnvironment != nil {

		if all {
			switch v := interface{}(m.GetSourceEnvironment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  "SourceEnvironment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  "SourceEnvironment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSourceEnvironment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetResourcesRequestValidationError{
					field:  "SourceEnvironment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.TargetEnvironment != nil {

		if all {
			switch v := interface{}(m.GetTargetEnvironment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  "TargetEnvironment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetResourcesRequestValidationError{
						field:  "TargetEnvironment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTargetEnvironment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetResourcesRequestValidationError{
					field:  "TargetEnvironment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResetResourcesRequestMultiError(errors)
	}

	return nil
}

// ResetResourcesRequestMultiError is an error wrapping multiple validation
// errors returned by ResetResourcesRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetResourcesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetResourcesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetResourcesRequestMultiError) AllErrors() []error { return m }

// ResetResourcesRequestValidationError is the validation error returned by
// ResetResourcesRequest.Validate if the designated constraints aren't met.
type ResetResourcesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetResourcesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetResourcesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetResourcesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetResourcesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetResourcesRequestValidationError) ErrorName() string {
	return "ResetResourcesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetResourcesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetResourcesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetResourcesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetResourcesRequestValidationError{}

// Validate checks the field values on ResetResourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetResourcesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetResourcesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetResourcesResponseMultiError, or nil if none found.
func (m *ResetResourcesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetResourcesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetResourcesResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetResourcesResponseValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResetResourcesResponseMultiError(errors)
	}

	return nil
}

// ResetResourcesResponseMultiError is an error wrapping multiple validation
// errors returned by ResetResourcesResponse.ValidateAll() if the designated
// constraints aren't met.
type ResetResourcesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetResourcesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetResourcesResponseMultiError) AllErrors() []error { return m }

// ResetResourcesResponseValidationError is the validation error returned by
// ResetResourcesResponse.Validate if the designated constraints aren't met.
type ResetResourcesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetResourcesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetResourcesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetResourcesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetResourcesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetResourcesResponseValidationError) ErrorName() string {
	return "ResetResourcesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetResourcesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetResourcesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetResourcesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetResourcesResponseValidationError{}

// Validate checks the field values on RestartWorkloadsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestartWorkloadsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestartWorkloadsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestartWorkloadsRequestMultiError, or nil if none found.
func (m *RestartWorkloadsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RestartWorkloadsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RestartWorkloadsRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RestartWorkloadsRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RestartWorkloadsRequestValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RestartWorkloadsRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RestartWorkloadsRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RestartWorkloadsRequestValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RestartWorkloadsRequestMultiError(errors)
	}

	return nil
}

// RestartWorkloadsRequestMultiError is an error wrapping multiple validation
// errors returned by RestartWorkloadsRequest.ValidateAll() if the designated
// constraints aren't met.
type RestartWorkloadsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestartWorkloadsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestartWorkloadsRequestMultiError) AllErrors() []error { return m }

// RestartWorkloadsRequestValidationError is the validation error returned by
// RestartWorkloadsRequest.Validate if the designated constraints aren't met.
type RestartWorkloadsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestartWorkloadsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestartWorkloadsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestartWorkloadsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestartWorkloadsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestartWorkloadsRequestValidationError) ErrorName() string {
	return "RestartWorkloadsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RestartWorkloadsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestartWorkloadsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestartWorkloadsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestartWorkloadsRequestValidationError{}

// Validate checks the field values on RestartWorkloadsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestartWorkloadsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestartWorkloadsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestartWorkloadsResponseMultiError, or nil if none found.
func (m *RestartWorkloadsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RestartWorkloadsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RestartWorkloadsResponseValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RestartWorkloadsResponseValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RestartWorkloadsResponseValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RestartWorkloadsResponseMultiError(errors)
	}

	return nil
}

// RestartWorkloadsResponseMultiError is an error wrapping multiple validation
// errors returned by RestartWorkloadsResponse.ValidateAll() if the designated
// constraints aren't met.
type RestartWorkloadsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestartWorkloadsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestartWorkloadsResponseMultiError) AllErrors() []error { return m }

// RestartWorkloadsResponseValidationError is the validation error returned by
// RestartWorkloadsResponse.Validate if the designated constraints aren't met.
type RestartWorkloadsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestartWorkloadsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestartWorkloadsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestartWorkloadsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestartWorkloadsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestartWorkloadsResponseValidationError) ErrorName() string {
	return "RestartWorkloadsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RestartWorkloadsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestartWorkloadsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestartWorkloadsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestartWorkloadsResponseValidationError{}

// Validate checks the field values on DeployApplicationsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployApplicationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployApplicationsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployApplicationsRequestMultiError, or nil if none found.
func (m *DeployApplicationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployApplicationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployApplicationsRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployApplicationsRequestValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployApplicationsRequestValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetApplications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeployApplicationsRequestValidationError{
						field:  fmt.Sprintf("Applications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeployApplicationsRequestValidationError{
						field:  fmt.Sprintf("Applications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeployApplicationsRequestValidationError{
					field:  fmt.Sprintf("Applications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeployApplicationsRequestMultiError(errors)
	}

	return nil
}

// DeployApplicationsRequestMultiError is an error wrapping multiple validation
// errors returned by DeployApplicationsRequest.ValidateAll() if the
// designated constraints aren't met.
type DeployApplicationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployApplicationsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployApplicationsRequestMultiError) AllErrors() []error { return m }

// DeployApplicationsRequestValidationError is the validation error returned by
// DeployApplicationsRequest.Validate if the designated constraints aren't met.
type DeployApplicationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployApplicationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployApplicationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployApplicationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployApplicationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployApplicationsRequestValidationError) ErrorName() string {
	return "DeployApplicationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeployApplicationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployApplicationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployApplicationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployApplicationsRequestValidationError{}

// Validate checks the field values on DeployApplicationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeployApplicationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeployApplicationsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeployApplicationsResponseMultiError, or nil if none found.
func (m *DeployApplicationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployApplicationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEnvironment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeployApplicationsResponseValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeployApplicationsResponseValidationError{
					field:  "Environment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnvironment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeployApplicationsResponseValidationError{
				field:  "Environment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetApplications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeployApplicationsResponseValidationError{
						field:  fmt.Sprintf("Applications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeployApplicationsResponseValidationError{
						field:  fmt.Sprintf("Applications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeployApplicationsResponseValidationError{
					field:  fmt.Sprintf("Applications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DeployApplicationsResponseMultiError(errors)
	}

	return nil
}

// DeployApplicationsResponseMultiError is an error wrapping multiple
// validation errors returned by DeployApplicationsResponse.ValidateAll() if
// the designated constraints aren't met.
type DeployApplicationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployApplicationsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployApplicationsResponseMultiError) AllErrors() []error { return m }

// DeployApplicationsResponseValidationError is the validation error returned
// by DeployApplicationsResponse.Validate if the designated constraints aren't met.
type DeployApplicationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployApplicationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployApplicationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployApplicationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployApplicationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployApplicationsResponseValidationError) ErrorName() string {
	return "DeployApplicationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeployApplicationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployApplicationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployApplicationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployApplicationsResponseValidationError{}

// Validate checks the field values on ResetResourcesRequest_ResourceItems with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ResetResourcesRequest_ResourceItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetResourcesRequest_ResourceItems
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ResetResourcesRequest_ResourceItemsMultiError, or nil if none found.
func (m *ResetResourcesRequest_ResourceItems) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetResourcesRequest_ResourceItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResetResourcesRequest_ResourceItemsValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResetResourcesRequest_ResourceItemsValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResetResourcesRequest_ResourceItemsValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ApiVersion != nil {
		// no validation rules for ApiVersion
	}

	if len(errors) > 0 {
		return ResetResourcesRequest_ResourceItemsMultiError(errors)
	}

	return nil
}

// ResetResourcesRequest_ResourceItemsMultiError is an error wrapping multiple
// validation errors returned by
// ResetResourcesRequest_ResourceItems.ValidateAll() if the designated
// constraints aren't met.
type ResetResourcesRequest_ResourceItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetResourcesRequest_ResourceItemsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetResourcesRequest_ResourceItemsMultiError) AllErrors() []error { return m }

// ResetResourcesRequest_ResourceItemsValidationError is the validation error
// returned by ResetResourcesRequest_ResourceItems.Validate if the designated
// constraints aren't met.
type ResetResourcesRequest_ResourceItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetResourcesRequest_ResourceItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetResourcesRequest_ResourceItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetResourcesRequest_ResourceItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetResourcesRequest_ResourceItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetResourcesRequest_ResourceItemsValidationError) ErrorName() string {
	return "ResetResourcesRequest_ResourceItemsValidationError"
}

// Error satisfies the builtin error interface
func (e ResetResourcesRequest_ResourceItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetResourcesRequest_ResourceItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetResourcesRequest_ResourceItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetResourcesRequest_ResourceItemsValidationError{}

// Validate checks the field values on DeployApplicationsRequest_DeployContext
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeployApplicationsRequest_DeployContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeployApplicationsRequest_DeployContext with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DeployApplicationsRequest_DeployContextMultiError, or nil if none found.
func (m *DeployApplicationsRequest_DeployContext) ValidateAll() error {
	return m.validate(true)
}

func (m *DeployApplicationsRequest_DeployContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for App

	if m.Image != nil {
		// no validation rules for Image
	}

	if m.Tag != nil {
		// no validation rules for Tag
	}

	if m.Repo != nil {
		// no validation rules for Repo
	}

	if m.Branch != nil {
		// no validation rules for Branch
	}

	if m.Version != nil {
		// no validation rules for Version
	}

	if len(errors) > 0 {
		return DeployApplicationsRequest_DeployContextMultiError(errors)
	}

	return nil
}

// DeployApplicationsRequest_DeployContextMultiError is an error wrapping
// multiple validation errors returned by
// DeployApplicationsRequest_DeployContext.ValidateAll() if the designated
// constraints aren't met.
type DeployApplicationsRequest_DeployContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeployApplicationsRequest_DeployContextMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeployApplicationsRequest_DeployContextMultiError) AllErrors() []error { return m }

// DeployApplicationsRequest_DeployContextValidationError is the validation
// error returned by DeployApplicationsRequest_DeployContext.Validate if the
// designated constraints aren't met.
type DeployApplicationsRequest_DeployContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeployApplicationsRequest_DeployContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeployApplicationsRequest_DeployContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeployApplicationsRequest_DeployContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeployApplicationsRequest_DeployContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeployApplicationsRequest_DeployContextValidationError) ErrorName() string {
	return "DeployApplicationsRequest_DeployContextValidationError"
}

// Error satisfies the builtin error interface
func (e DeployApplicationsRequest_DeployContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployApplicationsRequest_DeployContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeployApplicationsRequest_DeployContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeployApplicationsRequest_DeployContextValidationError{}
