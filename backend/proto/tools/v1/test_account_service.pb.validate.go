// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/tools/v1/test_account_service.proto

package toolspb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TestAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TestAccountMultiError, or
// nil if none found.
func (m *TestAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *TestAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Email

	// no validation rules for Password

	// no validation rules for Owner

	// no validation rules for Disposable

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestAccountValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestAccountValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestAccountValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Occupied

	if len(errors) > 0 {
		return TestAccountMultiError(errors)
	}

	return nil
}

// TestAccountMultiError is an error wrapping multiple validation errors
// returned by TestAccount.ValidateAll() if the designated constraints aren't met.
type TestAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestAccountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestAccountMultiError) AllErrors() []error { return m }

// TestAccountValidationError is the validation error returned by
// TestAccount.Validate if the designated constraints aren't met.
type TestAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestAccountValidationError) ErrorName() string { return "TestAccountValidationError" }

// Error satisfies the builtin error interface
func (e TestAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestAccountValidationError{}

// Validate checks the field values on Attributes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Attributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Attributes with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttributesMultiError, or
// nil if none found.
func (m *Attributes) ValidateAll() error {
	return m.validate(true)
}

func (m *Attributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.RegionCode != nil {

		if _, ok := _Attributes_RegionCode_InLookup[m.GetRegionCode()]; !ok {
			err := AttributesValidationError{
				field:  "RegionCode",
				reason: "value must be in list [US CA GB AU CN]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.EnableBoardingDaycare != nil {
		// no validation rules for EnableBoardingDaycare
	}

	if m.EnableOnlineBooking != nil {
		// no validation rules for EnableOnlineBooking
	}

	if m.EnableStripe != nil {
		// no validation rules for EnableStripe
	}

	if m.HasSmsCredit != nil {
		// no validation rules for HasSmsCredit
	}

	if m.HasEmailCredit != nil {
		// no validation rules for HasEmailCredit
	}

	if len(errors) > 0 {
		return AttributesMultiError(errors)
	}

	return nil
}

// AttributesMultiError is an error wrapping multiple validation errors
// returned by Attributes.ValidateAll() if the designated constraints aren't met.
type AttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttributesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttributesMultiError) AllErrors() []error { return m }

// AttributesValidationError is the validation error returned by
// Attributes.Validate if the designated constraints aren't met.
type AttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttributesValidationError) ErrorName() string { return "AttributesValidationError" }

// Error satisfies the builtin error interface
func (e AttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttributesValidationError{}

var _Attributes_RegionCode_InLookup = map[string]struct{}{
	"US": {},
	"CA": {},
	"GB": {},
	"AU": {},
	"CN": {},
}

// Validate checks the field values on BorrowTestAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BorrowTestAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BorrowTestAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BorrowTestAccountRequestMultiError, or nil if none found.
func (m *BorrowTestAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BorrowTestAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetBorrower()); l < 1 || l > 255 {
		err := BorrowTestAccountRequestValidationError{
			field:  "Borrower",
			reason: "value length must be between 1 and 255 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *BorrowTestAccountRequest_Id:
		if v == nil {
			err := BorrowTestAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetId() <= 0 {
			err := BorrowTestAccountRequestValidationError{
				field:  "Id",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *BorrowTestAccountRequest_Email:
		if v == nil {
			err := BorrowTestAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if utf8.RuneCountInString(m.GetEmail()) < 1 {
			err := BorrowTestAccountRequestValidationError{
				field:  "Email",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if m.Attributes != nil {

		if all {
			switch v := interface{}(m.GetAttributes()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BorrowTestAccountRequestValidationError{
						field:  "Attributes",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BorrowTestAccountRequestValidationError{
						field:  "Attributes",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BorrowTestAccountRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Shared != nil {
		// no validation rules for Shared
	}

	if len(errors) > 0 {
		return BorrowTestAccountRequestMultiError(errors)
	}

	return nil
}

// BorrowTestAccountRequestMultiError is an error wrapping multiple validation
// errors returned by BorrowTestAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type BorrowTestAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BorrowTestAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BorrowTestAccountRequestMultiError) AllErrors() []error { return m }

// BorrowTestAccountRequestValidationError is the validation error returned by
// BorrowTestAccountRequest.Validate if the designated constraints aren't met.
type BorrowTestAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BorrowTestAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BorrowTestAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BorrowTestAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BorrowTestAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BorrowTestAccountRequestValidationError) ErrorName() string {
	return "BorrowTestAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BorrowTestAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBorrowTestAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BorrowTestAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BorrowTestAccountRequestValidationError{}

// Validate checks the field values on BorrowTestAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BorrowTestAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BorrowTestAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BorrowTestAccountResponseMultiError, or nil if none found.
func (m *BorrowTestAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BorrowTestAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Email

	// no validation rules for Password

	// no validation rules for ContractId

	if len(errors) > 0 {
		return BorrowTestAccountResponseMultiError(errors)
	}

	return nil
}

// BorrowTestAccountResponseMultiError is an error wrapping multiple validation
// errors returned by BorrowTestAccountResponse.ValidateAll() if the
// designated constraints aren't met.
type BorrowTestAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BorrowTestAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BorrowTestAccountResponseMultiError) AllErrors() []error { return m }

// BorrowTestAccountResponseValidationError is the validation error returned by
// BorrowTestAccountResponse.Validate if the designated constraints aren't met.
type BorrowTestAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BorrowTestAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BorrowTestAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BorrowTestAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BorrowTestAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BorrowTestAccountResponseValidationError) ErrorName() string {
	return "BorrowTestAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BorrowTestAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBorrowTestAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BorrowTestAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BorrowTestAccountResponseValidationError{}

// Validate checks the field values on ReturnTestAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReturnTestAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReturnTestAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReturnTestAccountRequestMultiError, or nil if none found.
func (m *ReturnTestAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReturnTestAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ContractId

	if len(errors) > 0 {
		return ReturnTestAccountRequestMultiError(errors)
	}

	return nil
}

// ReturnTestAccountRequestMultiError is an error wrapping multiple validation
// errors returned by ReturnTestAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type ReturnTestAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReturnTestAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReturnTestAccountRequestMultiError) AllErrors() []error { return m }

// ReturnTestAccountRequestValidationError is the validation error returned by
// ReturnTestAccountRequest.Validate if the designated constraints aren't met.
type ReturnTestAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReturnTestAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReturnTestAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReturnTestAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReturnTestAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReturnTestAccountRequestValidationError) ErrorName() string {
	return "ReturnTestAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReturnTestAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReturnTestAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReturnTestAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReturnTestAccountRequestValidationError{}

// Validate checks the field values on ReturnTestAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReturnTestAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReturnTestAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReturnTestAccountResponseMultiError, or nil if none found.
func (m *ReturnTestAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReturnTestAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReturnTestAccountResponseMultiError(errors)
	}

	return nil
}

// ReturnTestAccountResponseMultiError is an error wrapping multiple validation
// errors returned by ReturnTestAccountResponse.ValidateAll() if the
// designated constraints aren't met.
type ReturnTestAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReturnTestAccountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReturnTestAccountResponseMultiError) AllErrors() []error { return m }

// ReturnTestAccountResponseValidationError is the validation error returned by
// ReturnTestAccountResponse.Validate if the designated constraints aren't met.
type ReturnTestAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReturnTestAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReturnTestAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReturnTestAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReturnTestAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReturnTestAccountResponseValidationError) ErrorName() string {
	return "ReturnTestAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReturnTestAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReturnTestAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReturnTestAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReturnTestAccountResponseValidationError{}

// Validate checks the field values on CreateTestAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTestAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTestAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTestAccountRequestMultiError, or nil if none found.
func (m *CreateTestAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTestAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTestAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTestAccountRequestValidationError{
					field:  "TestAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTestAccountRequestValidationError{
					field:  "TestAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTestAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTestAccountRequestValidationError{
				field:  "TestAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.TestAccountId != nil {
		// no validation rules for TestAccountId
	}

	if len(errors) > 0 {
		return CreateTestAccountRequestMultiError(errors)
	}

	return nil
}

// CreateTestAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTestAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTestAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTestAccountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTestAccountRequestMultiError) AllErrors() []error { return m }

// CreateTestAccountRequestValidationError is the validation error returned by
// CreateTestAccountRequest.Validate if the designated constraints aren't met.
type CreateTestAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTestAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTestAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTestAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTestAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTestAccountRequestValidationError) ErrorName() string {
	return "CreateTestAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTestAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTestAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTestAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTestAccountRequestValidationError{}

// Validate checks the field values on ReleaseTestAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseTestAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountsRequestMultiError, or nil if none found.
func (m *ReleaseTestAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Overdue

	if len(errors) > 0 {
		return ReleaseTestAccountsRequestMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by ReleaseTestAccountsRequest.ValidateAll() if
// the designated constraints aren't met.
type ReleaseTestAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountsRequestMultiError) AllErrors() []error { return m }

// ReleaseTestAccountsRequestValidationError is the validation error returned
// by ReleaseTestAccountsRequest.Validate if the designated constraints aren't met.
type ReleaseTestAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountsRequestValidationError) ErrorName() string {
	return "ReleaseTestAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountsRequestValidationError{}

// Validate checks the field values on ReleaseTestAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseTestAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountsResponseMultiError, or nil if none found.
func (m *ReleaseTestAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReleaseTestAccountsResponseMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by ReleaseTestAccountsResponse.ValidateAll() if
// the designated constraints aren't met.
type ReleaseTestAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountsResponseMultiError) AllErrors() []error { return m }

// ReleaseTestAccountsResponseValidationError is the validation error returned
// by ReleaseTestAccountsResponse.Validate if the designated constraints
// aren't met.
type ReleaseTestAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountsResponseValidationError) ErrorName() string {
	return "ReleaseTestAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountsResponseValidationError{}

// Validate checks the field values on ListTestAccountsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTestAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTestAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTestAccountsRequestMultiError, or nil if none found.
func (m *ListTestAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTestAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.PageSize != nil {

		if m.GetPageSize() <= 0 {
			err := ListTestAccountsRequestValidationError{
				field:  "PageSize",
				reason: "value must be greater than 0",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.PageToken != nil {

		if utf8.RuneCountInString(m.GetPageToken()) < 1 {
			err := ListTestAccountsRequestValidationError{
				field:  "PageToken",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListTestAccountsRequestMultiError(errors)
	}

	return nil
}

// ListTestAccountsRequestMultiError is an error wrapping multiple validation
// errors returned by ListTestAccountsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListTestAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTestAccountsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTestAccountsRequestMultiError) AllErrors() []error { return m }

// ListTestAccountsRequestValidationError is the validation error returned by
// ListTestAccountsRequest.Validate if the designated constraints aren't met.
type ListTestAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTestAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTestAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTestAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTestAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTestAccountsRequestValidationError) ErrorName() string {
	return "ListTestAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTestAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTestAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTestAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTestAccountsRequestValidationError{}

// Validate checks the field values on ListTestAccountsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTestAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTestAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTestAccountsResponseMultiError, or nil if none found.
func (m *ListTestAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTestAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTestAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTestAccountsResponseValidationError{
						field:  fmt.Sprintf("TestAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTestAccountsResponseValidationError{
						field:  fmt.Sprintf("TestAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTestAccountsResponseValidationError{
					field:  fmt.Sprintf("TestAccounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalSize

	if m.NextPageToken != nil {
		// no validation rules for NextPageToken
	}

	if len(errors) > 0 {
		return ListTestAccountsResponseMultiError(errors)
	}

	return nil
}

// ListTestAccountsResponseMultiError is an error wrapping multiple validation
// errors returned by ListTestAccountsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListTestAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTestAccountsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTestAccountsResponseMultiError) AllErrors() []error { return m }

// ListTestAccountsResponseValidationError is the validation error returned by
// ListTestAccountsResponse.Validate if the designated constraints aren't met.
type ListTestAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTestAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTestAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTestAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTestAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTestAccountsResponseValidationError) ErrorName() string {
	return "ListTestAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTestAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTestAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTestAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTestAccountsResponseValidationError{}
