// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

syntax = "proto3";

package backend.proto.customer.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v1";

// customer info
message Customer {
  // LifeCycle 表示客户生命周期状态
  enum LifeCycle {
    // 未指定的生命周期状态
    LIFE_CYCLE_UNSPECIFIED = 0;
    // 潜在客户
    LIFE_CYCLE_LEAD = 1;
    // 商机
    LIFE_CYCLE_OPPORTUNITY = 2;
    // 垃圾线索
    LIFE_CYCLE_TRASH = 3;
    // 休眠客户
    LIFE_CYCLE_DORMANT_LEAD = 4;
  }

  // ActionState 表示客户行动状态
  enum ActionState {
    // 未指定的行动状态
    ACTION_STATE_UNSPECIFIED = 0;
    // 已符合资格
    LEAD_STATE = 1;
    // 已完成TOUR
    TOUR_COMPLETED = 2;
    // 客户服务代表跟进
    CSR_FOLLOWUP = 3;
    // 首次会议
    FIRST_RESERVATION = 4;
    // 会面问候
    MEET_AND_GREET = 5;
    // 客户未出现
    CLIENT_NO_SHOW = 6;
    // 新的符合资格
    QUALIFIED_NEW = 7;
    // 不符合资格
    UNQUALIFIED = 8;
    // 尝试联系
    ATTEMPTED_TO_CONTACT = 9;
    // 已建立联系
    CONNECTED = 10;
    // 时机不合适
    BAD_TIMING = 11;
    // 进行中
    IN_PROGRESS = 12;

    // for k9
    // Lead Send email #1
    NEW_LEADS_TARGET_EMAIL_1 = 50;
    // Lead Send email #2
    NEW_LEADS_TARGET_EMAIL_2 = 51;
    // Lead Send email #3
    NEW_LEADS_TARGET_EMAIL_3 = 52;
    // Lead Send email #4
    NEW_LEADS_TARGET_EMAIL_4 = 53;
    // Opportunity Send email #1
    OPPORTUNITY_TARGET_EMAIL_1 = 54;
    // Opportunity Send email #2
    OPPORTUNITY_TARGET_EMAIL_2 = 55;
    // Opportunity Send email #3
    OPPORTUNITY_TARGET_EMAIL_3 = 56;
  }

  // state 表示客户状态
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 正常状态
    NORMAL = 1;
    // 已删除
    DELETED = 2;
  }

  // Type 表示客户类型
  enum Type {
    // 未指定的状态
    TYPE_UNSPECIFIED = 0;
    // 默认类型
    CUSTOMER = 1;
    // 潜客
    LEAD = 2;
  }

  // OB additional info
  message AdditionalInfo {
    // Referral source
    int64 referral_source_id = 1;
    // Referral source desc
    string referral_source_desc = 2;
    // Preferred groomer
    int64 preferred_groomer_id = 3;
    // Preferred frequency
    int64 preferred_frequency_day = 4;
    // Preferred frequency type (0-by days, 1-by weeks)
    int64 preferred_frequency_type = 5;
    // Preferred days of the week
    repeated int64 preferred_day = 6;
    // Preferred times of the day
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: @jett ob 的垃圾设计使我不得不兼容这里的int64 --)
    repeated int64 preferred_time = 7;
  }

  // 客户ID
  int64 id = 1;
  // 公司ID
  int64 company_id = 2;
  // 首选 business ID (客户属于哪一个business)
  int64 preferred_business_id = 3;
  // 账户ID
  int64 account_id = 4;
  // 电话号码
  string phone_number = 5;
  // 客户编码
  string customer_code = 6;
  // 生命周期状态
  LifeCycle life_cycle = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 行动状态
  ActionState action_state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 客户来源
  string source = 9;
  // 创建时间
  google.protobuf.Timestamp create_time = 10;
  // 更新时间
  google.protobuf.Timestamp update_time = 11;

  // 头像路径
  string avatar_path = 12;
  // 名
  string given_name = 13;
  // 姓
  string family_name = 14;
  // 邮箱
  string email = 15;

  // 生日
  google.protobuf.Timestamp birth_time = 16;

  // 用户类型
  Type type = 17;

  // 地址
  Address address = 18;

  // 联系人
  CustomerContact contact = 19;

  // 关联的员工ID
  int64 allocate_staff_id = 20;

  // OB additional info
  AdditionalInfo additional_info = 21;

  // state
  State state = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // 关联自定义生命周期ID
  int64 customize_life_cycle_id = 23;
  // 关联自定义行动状态ID
  int64 customize_action_state_id = 24;

  // 用户头像颜色
  string client_color = 25;
}

// Address 表示地址信息
message Address {
  // State 表示地址状态
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 启用
    NORMAL = 1;
    // 删除
    DELETED = 2;
  }

  // IsPrimary 表示是否为主地址
  enum IsPrimary {
    // 未指定的类型
    IS_PRIMARY_UNSPECIFIED = 0;
    // 主要地址
    PRIMARY = 1;
    // 附加地址
    NO_PRIMARY = 2;
  }
  // id
  int64 id = 1;
  // 公司ID
  int64 company_id = 2;
  // 客户ID
  int64 customer_id = 3;
  // 地址1
  string address1 = 4;
  // 地址2
  string address2 = 5;
  // 城市
  string city = 6;
  // 州/省
  string state = 7;
  // 区域代码 (替代 country)
  string region_code = 8;
  // 邮编
  string zipcode = 9;
  // 纬度
  string lat = 10;
  // 经度
  string lng = 11;
  // 状态
  State status = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 是否为主地址
  // 注意, 这里的枚举和数据库不一致
  // 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
  IsPrimary is_primary = 13;
  // 创建时间
  google.protobuf.Timestamp create_time = 14;
  // 更新时间
  google.protobuf.Timestamp update_time = 15;
}

// contact 表示联系信息
message CustomerContact {
  // Status 表示联系人状态
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 启用
    NORMAL = 1;
    // 删除
    DELETED = 2;
  }

  // 联系人类型
  enum Type {
    // 未指定的类型
    TYPE_UNSPECIFIED = 0;
    // 主要联系人
    MAIN = 1;
    // 附加联系人
    ADDITIONAL = 2;
  }

  // IsPrimary 表示是否为主联系人
  enum IsPrimary {
    // 未指定的类型
    IS_PRIMARY_UNSPECIFIED = 0;
    // 主要联系人
    PRIMARY = 1;
    // 附加联系人
    NO_PRIMARY = 2;
  }

  // 联系人ID
  int64 id = 1;
  // 商户ID
  int64 business_id = 2;
  // 客户ID
  int64 customer_id = 3;
  // 名
  string given_name = 4;
  // 姓
  string family_name = 5;
  // 电话号码
  string phone_number = 6;
  // 邮箱
  string email = 7;
  // 职位
  string title = 8;
  // 类型 1 主要联系人 2 附加联系人
  Type type = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
  // is_primary 表示是否为主联系人
  // 注意, 这里的枚举和数据库不一致
  // 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
  IsPrimary is_primary = 10;
  // 状态 1 启用 2 删除
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 创建时间
  google.protobuf.Timestamp create_time = 12;
  // 更新时间
  google.protobuf.Timestamp update_time = 13;
  // 公司ID
  int64 company_id = 14;
  // E164格式电话号码
  string e164_phone_number = 15;
}

// Task 表示任务信息
message Task {
  // State 表示任务状态
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 新任务
    NEW = 1;
    // 已完成
    FINISH = 2;
  }

  // 任务ID
  int64 id = 1;
  // 任务名称
  string name = 2;
  // 分配员工
  optional int64 allocate_staff_id = 3;
  // 预期完成的时间
  optional google.protobuf.Timestamp complete_time = 4;
  // 任务状态
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// HistoryLog 表示历史记录
message HistoryLog {
  // SMS 表示短信历史记录
  message Message {
    // State 表示短信状态
    enum State {
      // 未指定的状态
      STATE_UNSPECIFIED = 0;
      // 发送成功
      SUCCEEDED = 1;
      // 发送失败
      FAILED = 2;
    }
    // Direction 表示收发方向
    enum Direction {
      // 未指定的状态
      DIRECTION_UNSPECIFIED = 0;
      // 发送
      SEND = 1;
      // 接收
      RECEIVE = 2;
    }
    // 短信ID
    int64 message_id = 1;
    // 短信内容
    string text = 2;
    // 发送状态
    State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 失败原因
    string fail_reason = 4;
    // 收发方向
    Direction direction = 5;
  }

  // Call 表示通话历史记录
  message Call {
    // State 表示通话状态
    enum State {
      // 未指定的状态
      STATE_UNSPECIFIED = 0;
      // 接通
      ANSWERED = 1;
      // 未接
      NO_ANSWER = 2;
    }
    // Direction 表示收发方向
    enum Direction {
      // 未指定的状态
      DIRECTION_UNSPECIFIED = 0;
      // 用户联系商家
      INCOMING = 1;
      // 商家联系用户
      OUTGOING = 2;
    }
    // 通话ID
    int64 call_id = 1;
    // 通话记录
    string text = 2;
    // 通话状态
    State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 失败原因
    string fail_reason = 4;
    // 收发方向
    Direction direction = 5;
  }

  // Note 表示备注历史记录
  message Note {
    // 备注内容
    string text = 1;
  }

  // Task 表示任务历史记录
  message Task {
    // Type 表示任务历史记录类型
    enum Type {
      // 未指定的记录类型
      TYPE_UNSPECIFIED = 0;
      // 创建任务
      CREATE = 1;
      // 更新任务
      UPDATE = 2;
      // 完成任务
      FINISH = 3;
      // 删除任务
      DELETE = 4;
    }
    // 记录类型
    Type type = 1;
    // 任务信息
    backend.proto.customer.v1.Task task = 2;
  }

  // Convert 表示类型转化记录
  message Convert {
    // 转化前类型
    Customer.Type origin_type = 1;
    // 转化后的类型
    Customer.Type target_type = 2;
  }

  // Create 表示用户创建记录
  message Create {}

  // Type 表示历史记录类型
  enum Type {
    // 未指定的记录类型
    TYPE_UNSPECIFIED = 0;
    // 短信记录
    MESSAGE = 1;
    // 通话记录
    CALL = 2;
    // 备注记录
    NOTE = 3;
    // 任务记录
    TASK = 4;
    // 类型转化的记录
    CONVERT = 5;
    // 用户创建记录
    CREATE = 6;
  }

  // Source 表示历史记录来源
  enum Source {
    // 未指定的记录类型
    SOURCE_UNSPECIFIED = 0;
    // 员工操作
    STAFF = 1;
    // 预约
    APPOINTMENT = 2;
    // OB
    ONLINE_BOOKING = 3;
    // product(retail)
    PRODUCT = 4;
    // package
    PACKAGE = 5;
    // fulfillment(new appointment)
    FULFILLMENT = 6;
    // membership
    MEMBERSHIP = 7;
  }

  // actions
  message Action {
    oneof action {
      // 短信消息
      Message message = 1;
      // 通话记录
      Call call = 2;
      // 备注记录
      Note note = 3;
      // 任务记录
      Task task = 4;
      // 转化记录
      Convert convert = 5;
      // 创建记录
      Create create = 6;
    }
  }
  // 记录ID
  int64 id = 1;
  // 客户ID
  int64 customer_id = 2;
  // 客户名称
  string customer_name = 10;
  // 客户电话
  string customer_phone_number = 11;
  // 记录类型
  Type type = 3;
  // 记录数据
  Action action = 4;
  // 记录操作人
  int64 staff_id = 5;
  // 创建时间
  google.protobuf.Timestamp create_time = 6;
  // 记录来源
  Source source = 7;
  // 记录来源ID
  int64 source_id = 8;
  // 记录来源名称
  string source_name = 9;
}

// 客户自定义生命周期状态
message CustomizeLifeCycle {
  // ID
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 是否默认 0-否 1-是
  int32 is_default = 4;
}

// 客户自定义行动状态
message CustomizeActionState {
  // ID
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 颜色
  string color = 4;
}
