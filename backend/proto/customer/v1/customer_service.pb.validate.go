// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/customer/v1/customer_service.proto

package customerpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerRequestMultiError, or nil if none found.
func (m *CreateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerRequestValidationError{
				field:  "Customer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.StaffId != nil {
		// no validation rules for StaffId
	}

	if m.StaffName != nil {
		// no validation rules for StaffName
	}

	if len(errors) > 0 {
		return CreateCustomerRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerRequestMultiError) AllErrors() []error { return m }

// CreateCustomerRequestValidationError is the validation error returned by
// CreateCustomerRequest.Validate if the designated constraints aren't met.
type CreateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerRequestValidationError) ErrorName() string {
	return "CreateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerRequestValidationError{}

// Validate checks the field values on CreateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerResponseMultiError, or nil if none found.
func (m *CreateCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerResponseValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerResponseValidationError{
				field:  "Customer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsExist

	if len(errors) > 0 {
		return CreateCustomerResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerResponseMultiError) AllErrors() []error { return m }

// CreateCustomerResponseValidationError is the validation error returned by
// CreateCustomerResponse.Validate if the designated constraints aren't met.
type CreateCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerResponseValidationError) ErrorName() string {
	return "CreateCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerResponseValidationError{}

// Validate checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequestMultiError, or nil if none found.
func (m *UpdateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerRequestValidationError{
					field:  "Customer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerRequestValidationError{
				field:  "Customer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCustomerRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerRequestValidationError is the validation error returned by
// UpdateCustomerRequest.Validate if the designated constraints aren't met.
type UpdateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequestValidationError) ErrorName() string {
	return "UpdateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequestValidationError{}

// Validate checks the field values on UpdateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerResponseMultiError, or nil if none found.
func (m *UpdateCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCustomerResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerResponseValidationError is the validation error returned by
// UpdateCustomerResponse.Validate if the designated constraints aren't met.
type UpdateCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerResponseValidationError) ErrorName() string {
	return "UpdateCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerResponseValidationError{}

// Validate checks the field values on ListCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersRequestMultiError, or nil if none found.
func (m *ListCustomersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListCustomersRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Filter != nil {

		if all {
			switch v := interface{}(m.GetFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomersRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomersRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomersRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.PageSize != nil {

		if val := m.GetPageSize(); val < 0 || val > 1000 {
			err := ListCustomersRequestValidationError{
				field:  "PageSize",
				reason: "value must be inside range [0, 1000]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.PageNum != nil {

		if m.GetPageNum() < 1 {
			err := ListCustomersRequestValidationError{
				field:  "PageNum",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.OrderField != nil {
		// no validation rules for OrderField
	}

	if m.OrderDirection != nil {
		// no validation rules for OrderDirection
	}

	if len(errors) > 0 {
		return ListCustomersRequestMultiError(errors)
	}

	return nil
}

// ListCustomersRequestMultiError is an error wrapping multiple validation
// errors returned by ListCustomersRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCustomersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersRequestMultiError) AllErrors() []error { return m }

// ListCustomersRequestValidationError is the validation error returned by
// ListCustomersRequest.Validate if the designated constraints aren't met.
type ListCustomersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersRequestValidationError) ErrorName() string {
	return "ListCustomersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersRequestValidationError{}

// Validate checks the field values on ListCustomersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersResponseMultiError, or nil if none found.
func (m *ListCustomersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomersResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomersResponseValidationError{
						field:  fmt.Sprintf("Customers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomersResponseValidationError{
					field:  fmt.Sprintf("Customers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCustomersResponseMultiError(errors)
	}

	return nil
}

// ListCustomersResponseMultiError is an error wrapping multiple validation
// errors returned by ListCustomersResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCustomersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersResponseMultiError) AllErrors() []error { return m }

// ListCustomersResponseValidationError is the validation error returned by
// ListCustomersResponse.Validate if the designated constraints aren't met.
type ListCustomersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersResponseValidationError) ErrorName() string {
	return "ListCustomersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersResponseValidationError{}

// Validate checks the field values on GetCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerRequestMultiError, or nil if none found.
func (m *GetCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return GetCustomerRequestMultiError(errors)
	}

	return nil
}

// GetCustomerRequestMultiError is an error wrapping multiple validation errors
// returned by GetCustomerRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerRequestMultiError) AllErrors() []error { return m }

// GetCustomerRequestValidationError is the validation error returned by
// GetCustomerRequest.Validate if the designated constraints aren't met.
type GetCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerRequestValidationError) ErrorName() string {
	return "GetCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerRequestValidationError{}

// Validate checks the field values on DeleteCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomerRequestMultiError, or nil if none found.
func (m *DeleteCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return DeleteCustomerRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerRequestMultiError) AllErrors() []error { return m }

// DeleteCustomerRequestValidationError is the validation error returned by
// DeleteCustomerRequest.Validate if the designated constraints aren't met.
type DeleteCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerRequestValidationError) ErrorName() string {
	return "DeleteCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerRequestValidationError{}

// Validate checks the field values on SyncCustomerSearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncCustomerSearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncCustomerSearchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncCustomerSearchRequestMultiError, or nil if none found.
func (m *SyncCustomerSearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncCustomerSearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return SyncCustomerSearchRequestMultiError(errors)
	}

	return nil
}

// SyncCustomerSearchRequestMultiError is an error wrapping multiple validation
// errors returned by SyncCustomerSearchRequest.ValidateAll() if the
// designated constraints aren't met.
type SyncCustomerSearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncCustomerSearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncCustomerSearchRequestMultiError) AllErrors() []error { return m }

// SyncCustomerSearchRequestValidationError is the validation error returned by
// SyncCustomerSearchRequest.Validate if the designated constraints aren't met.
type SyncCustomerSearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncCustomerSearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncCustomerSearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncCustomerSearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncCustomerSearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncCustomerSearchRequestValidationError) ErrorName() string {
	return "SyncCustomerSearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncCustomerSearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncCustomerSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncCustomerSearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncCustomerSearchRequestValidationError{}

// Validate checks the field values on SyncCustomerSearchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncCustomerSearchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncCustomerSearchResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncCustomerSearchResponseMultiError, or nil if none found.
func (m *SyncCustomerSearchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncCustomerSearchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncCustomerSearchResponseMultiError(errors)
	}

	return nil
}

// SyncCustomerSearchResponseMultiError is an error wrapping multiple
// validation errors returned by SyncCustomerSearchResponse.ValidateAll() if
// the designated constraints aren't met.
type SyncCustomerSearchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncCustomerSearchResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncCustomerSearchResponseMultiError) AllErrors() []error { return m }

// SyncCustomerSearchResponseValidationError is the validation error returned
// by SyncCustomerSearchResponse.Validate if the designated constraints aren't met.
type SyncCustomerSearchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncCustomerSearchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncCustomerSearchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncCustomerSearchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncCustomerSearchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncCustomerSearchResponseValidationError) ErrorName() string {
	return "SyncCustomerSearchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncCustomerSearchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncCustomerSearchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncCustomerSearchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncCustomerSearchResponseValidationError{}

// Validate checks the field values on ConvertCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConvertCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConvertCustomerRequestMultiError, or nil if none found.
func (m *ConvertCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return ConvertCustomerRequestMultiError(errors)
	}

	return nil
}

// ConvertCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ConvertCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomerRequestMultiError) AllErrors() []error { return m }

// ConvertCustomerRequestValidationError is the validation error returned by
// ConvertCustomerRequest.Validate if the designated constraints aren't met.
type ConvertCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomerRequestValidationError) ErrorName() string {
	return "ConvertCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomerRequestValidationError{}

// Validate checks the field values on ConvertCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConvertCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConvertCustomerResponseMultiError, or nil if none found.
func (m *ConvertCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ConvertCustomerResponseMultiError(errors)
	}

	return nil
}

// ConvertCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ConvertCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomerResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomerResponseMultiError) AllErrors() []error { return m }

// ConvertCustomerResponseValidationError is the validation error returned by
// ConvertCustomerResponse.Validate if the designated constraints aren't met.
type ConvertCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomerResponseValidationError) ErrorName() string {
	return "ConvertCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomerResponseValidationError{}

// Validate checks the field values on ConvertCustomersAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertCustomersAttributeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomersAttributeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConvertCustomersAttributeRequestMultiError, or nil if none found.
func (m *ConvertCustomersAttributeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomersAttributeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CustomizeLifeCycleId != nil {
		// no validation rules for CustomizeLifeCycleId
	}

	if m.CustomizeActionStateId != nil {
		// no validation rules for CustomizeActionStateId
	}

	if len(errors) > 0 {
		return ConvertCustomersAttributeRequestMultiError(errors)
	}

	return nil
}

// ConvertCustomersAttributeRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConvertCustomersAttributeRequest.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomersAttributeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomersAttributeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomersAttributeRequestMultiError) AllErrors() []error { return m }

// ConvertCustomersAttributeRequestValidationError is the validation error
// returned by ConvertCustomersAttributeRequest.Validate if the designated
// constraints aren't met.
type ConvertCustomersAttributeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomersAttributeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomersAttributeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomersAttributeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomersAttributeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomersAttributeRequestValidationError) ErrorName() string {
	return "ConvertCustomersAttributeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomersAttributeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomersAttributeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomersAttributeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomersAttributeRequestValidationError{}

// Validate checks the field values on ConvertCustomersAttributeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConvertCustomersAttributeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConvertCustomersAttributeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConvertCustomersAttributeResponseMultiError, or nil if none found.
func (m *ConvertCustomersAttributeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConvertCustomersAttributeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ConvertCustomersAttributeResponseMultiError(errors)
	}

	return nil
}

// ConvertCustomersAttributeResponseMultiError is an error wrapping multiple
// validation errors returned by
// ConvertCustomersAttributeResponse.ValidateAll() if the designated
// constraints aren't met.
type ConvertCustomersAttributeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConvertCustomersAttributeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConvertCustomersAttributeResponseMultiError) AllErrors() []error { return m }

// ConvertCustomersAttributeResponseValidationError is the validation error
// returned by ConvertCustomersAttributeResponse.Validate if the designated
// constraints aren't met.
type ConvertCustomersAttributeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConvertCustomersAttributeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConvertCustomersAttributeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConvertCustomersAttributeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConvertCustomersAttributeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConvertCustomersAttributeResponseValidationError) ErrorName() string {
	return "ConvertCustomersAttributeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConvertCustomersAttributeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConvertCustomersAttributeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConvertCustomersAttributeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConvertCustomersAttributeResponseValidationError{}

// Validate checks the field values on CreateCustomerHistoryLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerHistoryLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerHistoryLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCustomerHistoryLogRequestMultiError, or nil if none found.
func (m *CreateCustomerHistoryLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerHistoryLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for CustomerName

	// no validation rules for CustomerPhoneNumber

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCustomerHistoryLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCustomerHistoryLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCustomerHistoryLogRequestValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	if m.Source != nil {
		// no validation rules for Source
	}

	if m.SourceId != nil {
		// no validation rules for SourceId
	}

	if m.SourceName != nil {
		// no validation rules for SourceName
	}

	if len(errors) > 0 {
		return CreateCustomerHistoryLogRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerHistoryLogRequestMultiError is an error wrapping multiple
// validation errors returned by CreateCustomerHistoryLogRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateCustomerHistoryLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerHistoryLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerHistoryLogRequestMultiError) AllErrors() []error { return m }

// CreateCustomerHistoryLogRequestValidationError is the validation error
// returned by CreateCustomerHistoryLogRequest.Validate if the designated
// constraints aren't met.
type CreateCustomerHistoryLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerHistoryLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerHistoryLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerHistoryLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerHistoryLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerHistoryLogRequestValidationError) ErrorName() string {
	return "CreateCustomerHistoryLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerHistoryLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerHistoryLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerHistoryLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerHistoryLogRequestValidationError{}

// Validate checks the field values on CreateCustomerHistoryLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateCustomerHistoryLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerHistoryLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateCustomerHistoryLogResponseMultiError, or nil if none found.
func (m *CreateCustomerHistoryLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerHistoryLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogId

	if len(errors) > 0 {
		return CreateCustomerHistoryLogResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerHistoryLogResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateCustomerHistoryLogResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateCustomerHistoryLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerHistoryLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerHistoryLogResponseMultiError) AllErrors() []error { return m }

// CreateCustomerHistoryLogResponseValidationError is the validation error
// returned by CreateCustomerHistoryLogResponse.Validate if the designated
// constraints aren't met.
type CreateCustomerHistoryLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerHistoryLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerHistoryLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerHistoryLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerHistoryLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerHistoryLogResponseValidationError) ErrorName() string {
	return "CreateCustomerHistoryLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerHistoryLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerHistoryLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerHistoryLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerHistoryLogResponseValidationError{}

// Validate checks the field values on UpdateCustomerHistoryLogRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerHistoryLogRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerHistoryLogRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateCustomerHistoryLogRequestMultiError, or nil if none found.
func (m *UpdateCustomerHistoryLogRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerHistoryLogRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogId

	// no validation rules for StaffId

	if m.Action != nil {

		if all {
			switch v := interface{}(m.GetAction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerHistoryLogRequestValidationError{
						field:  "Action",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerHistoryLogRequestValidationError{
						field:  "Action",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerHistoryLogRequestValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateCustomerHistoryLogRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerHistoryLogRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateCustomerHistoryLogRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateCustomerHistoryLogRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerHistoryLogRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerHistoryLogRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerHistoryLogRequestValidationError is the validation error
// returned by UpdateCustomerHistoryLogRequest.Validate if the designated
// constraints aren't met.
type UpdateCustomerHistoryLogRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerHistoryLogRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerHistoryLogRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerHistoryLogRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerHistoryLogRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerHistoryLogRequestValidationError) ErrorName() string {
	return "UpdateCustomerHistoryLogRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerHistoryLogRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerHistoryLogRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerHistoryLogRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerHistoryLogRequestValidationError{}

// Validate checks the field values on UpdateCustomerHistoryLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerHistoryLogResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerHistoryLogResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateCustomerHistoryLogResponseMultiError, or nil if none found.
func (m *UpdateCustomerHistoryLogResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerHistoryLogResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCustomerHistoryLogResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerHistoryLogResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerHistoryLogResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerHistoryLogResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerHistoryLogResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerHistoryLogResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerHistoryLogResponseValidationError is the validation error
// returned by UpdateCustomerHistoryLogResponse.Validate if the designated
// constraints aren't met.
type UpdateCustomerHistoryLogResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerHistoryLogResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerHistoryLogResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerHistoryLogResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerHistoryLogResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerHistoryLogResponseValidationError) ErrorName() string {
	return "UpdateCustomerHistoryLogResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerHistoryLogResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerHistoryLogResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerHistoryLogResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerHistoryLogResponseValidationError{}

// Validate checks the field values on ListCustomerHistoryLogsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerHistoryLogsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerHistoryLogsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerHistoryLogsRequestMultiError, or nil if none found.
func (m *ListCustomerHistoryLogsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerHistoryLogsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CustomerId != nil {
		// no validation rules for CustomerId
	}

	if m.Filter != nil {

		if all {
			switch v := interface{}(m.GetFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerHistoryLogsRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerHistoryLogsRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerHistoryLogsRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.PageSize != nil {

		if val := m.GetPageSize(); val < 0 || val > 1000 {
			err := ListCustomerHistoryLogsRequestValidationError{
				field:  "PageSize",
				reason: "value must be inside range [0, 1000]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.PageNum != nil {

		if m.GetPageNum() < 1 {
			err := ListCustomerHistoryLogsRequestValidationError{
				field:  "PageNum",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListCustomerHistoryLogsRequestMultiError(errors)
	}

	return nil
}

// ListCustomerHistoryLogsRequestMultiError is an error wrapping multiple
// validation errors returned by ListCustomerHistoryLogsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListCustomerHistoryLogsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerHistoryLogsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerHistoryLogsRequestMultiError) AllErrors() []error { return m }

// ListCustomerHistoryLogsRequestValidationError is the validation error
// returned by ListCustomerHistoryLogsRequest.Validate if the designated
// constraints aren't met.
type ListCustomerHistoryLogsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerHistoryLogsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerHistoryLogsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerHistoryLogsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerHistoryLogsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerHistoryLogsRequestValidationError) ErrorName() string {
	return "ListCustomerHistoryLogsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerHistoryLogsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerHistoryLogsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerHistoryLogsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerHistoryLogsRequestValidationError{}

// Validate checks the field values on ListCustomerHistoryLogsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerHistoryLogsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerHistoryLogsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListCustomerHistoryLogsResponseMultiError, or nil if none found.
func (m *ListCustomerHistoryLogsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerHistoryLogsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHistoryLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerHistoryLogsResponseValidationError{
						field:  fmt.Sprintf("HistoryLogs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerHistoryLogsResponseValidationError{
						field:  fmt.Sprintf("HistoryLogs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerHistoryLogsResponseValidationError{
					field:  fmt.Sprintf("HistoryLogs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCustomerHistoryLogsResponseMultiError(errors)
	}

	return nil
}

// ListCustomerHistoryLogsResponseMultiError is an error wrapping multiple
// validation errors returned by ListCustomerHistoryLogsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListCustomerHistoryLogsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerHistoryLogsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerHistoryLogsResponseMultiError) AllErrors() []error { return m }

// ListCustomerHistoryLogsResponseValidationError is the validation error
// returned by ListCustomerHistoryLogsResponse.Validate if the designated
// constraints aren't met.
type ListCustomerHistoryLogsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerHistoryLogsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerHistoryLogsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerHistoryLogsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerHistoryLogsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerHistoryLogsResponseValidationError) ErrorName() string {
	return "ListCustomerHistoryLogsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerHistoryLogsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerHistoryLogsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerHistoryLogsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerHistoryLogsResponseValidationError{}

// Validate checks the field values on CreateCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerTaskRequestMultiError, or nil if none found.
func (m *CreateCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateCustomerTaskRequestValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// CreateCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by CreateCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerTaskRequestMultiError) AllErrors() []error { return m }

// CreateCustomerTaskRequestValidationError is the validation error returned by
// CreateCustomerTaskRequest.Validate if the designated constraints aren't met.
type CreateCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerTaskRequestValidationError) ErrorName() string {
	return "CreateCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerTaskRequestValidationError{}

// Validate checks the field values on CreateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCustomerTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCustomerTaskResponseMultiError, or nil if none found.
func (m *CreateCustomerTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCustomerTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return CreateCustomerTaskResponseMultiError(errors)
	}

	return nil
}

// CreateCustomerTaskResponseMultiError is an error wrapping multiple
// validation errors returned by CreateCustomerTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateCustomerTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCustomerTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCustomerTaskResponseMultiError) AllErrors() []error { return m }

// CreateCustomerTaskResponseValidationError is the validation error returned
// by CreateCustomerTaskResponse.Validate if the designated constraints aren't met.
type CreateCustomerTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCustomerTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCustomerTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCustomerTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCustomerTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCustomerTaskResponseValidationError) ErrorName() string {
	return "CreateCustomerTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCustomerTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCustomerTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCustomerTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCustomerTaskResponseValidationError{}

// Validate checks the field values on UpdateCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerTaskRequestMultiError, or nil if none found.
func (m *UpdateCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StaffId

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.CompleteTime != nil {

		if all {
			switch v := interface{}(m.GetCompleteTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerTaskRequestValidationError{
						field:  "CompleteTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCompleteTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerTaskRequestValidationError{
					field:  "CompleteTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.State != nil {
		// no validation rules for State
	}

	if len(errors) > 0 {
		return UpdateCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerTaskRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerTaskRequestValidationError is the validation error returned by
// UpdateCustomerTaskRequest.Validate if the designated constraints aren't met.
type UpdateCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerTaskRequestValidationError) ErrorName() string {
	return "UpdateCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerTaskRequestValidationError{}

// Validate checks the field values on UpdateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerTaskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerTaskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerTaskResponseMultiError, or nil if none found.
func (m *UpdateCustomerTaskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerTaskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCustomerTaskResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerTaskResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateCustomerTaskResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateCustomerTaskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerTaskResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerTaskResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerTaskResponseValidationError is the validation error returned
// by UpdateCustomerTaskResponse.Validate if the designated constraints aren't met.
type UpdateCustomerTaskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerTaskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerTaskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerTaskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerTaskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerTaskResponseValidationError) ErrorName() string {
	return "UpdateCustomerTaskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerTaskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerTaskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerTaskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerTaskResponseValidationError{}

// Validate checks the field values on ListCustomerTasksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerTasksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerTasksRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomerTasksRequestMultiError, or nil if none found.
func (m *ListCustomerTasksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerTasksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	if len(errors) > 0 {
		return ListCustomerTasksRequestMultiError(errors)
	}

	return nil
}

// ListCustomerTasksRequestMultiError is an error wrapping multiple validation
// errors returned by ListCustomerTasksRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerTasksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerTasksRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerTasksRequestMultiError) AllErrors() []error { return m }

// ListCustomerTasksRequestValidationError is the validation error returned by
// ListCustomerTasksRequest.Validate if the designated constraints aren't met.
type ListCustomerTasksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerTasksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerTasksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerTasksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerTasksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerTasksRequestValidationError) ErrorName() string {
	return "ListCustomerTasksRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerTasksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerTasksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerTasksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerTasksRequestValidationError{}

// Validate checks the field values on ListCustomerTasksResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomerTasksResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerTasksResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomerTasksResponseMultiError, or nil if none found.
func (m *ListCustomerTasksResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerTasksResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTasks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCustomerTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCustomerTasksResponseValidationError{
						field:  fmt.Sprintf("Tasks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCustomerTasksResponseValidationError{
					field:  fmt.Sprintf("Tasks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCustomerTasksResponseMultiError(errors)
	}

	return nil
}

// ListCustomerTasksResponseMultiError is an error wrapping multiple validation
// errors returned by ListCustomerTasksResponse.ValidateAll() if the
// designated constraints aren't met.
type ListCustomerTasksResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerTasksResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerTasksResponseMultiError) AllErrors() []error { return m }

// ListCustomerTasksResponseValidationError is the validation error returned by
// ListCustomerTasksResponse.Validate if the designated constraints aren't met.
type ListCustomerTasksResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerTasksResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerTasksResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerTasksResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerTasksResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerTasksResponseValidationError) ErrorName() string {
	return "ListCustomerTasksResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerTasksResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerTasksResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerTasksResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerTasksResponseValidationError{}

// Validate checks the field values on DeleteCustomerTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteCustomerTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCustomerTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCustomerTaskRequestMultiError, or nil if none found.
func (m *DeleteCustomerTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCustomerTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteCustomerTaskRequestMultiError(errors)
	}

	return nil
}

// DeleteCustomerTaskRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteCustomerTaskRequest.ValidateAll() if the
// designated constraints aren't met.
type DeleteCustomerTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCustomerTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCustomerTaskRequestMultiError) AllErrors() []error { return m }

// DeleteCustomerTaskRequestValidationError is the validation error returned by
// DeleteCustomerTaskRequest.Validate if the designated constraints aren't met.
type DeleteCustomerTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCustomerTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCustomerTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCustomerTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCustomerTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCustomerTaskRequestValidationError) ErrorName() string {
	return "DeleteCustomerTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCustomerTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCustomerTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCustomerTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCustomerTaskRequestValidationError{}

// Validate checks the field values on CreateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAddressRequestMultiError, or nil if none found.
func (m *CreateAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCustomerId() <= 0 {
		err := CreateAddressRequestValidationError{
			field:  "CustomerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAddressRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAddressRequestMultiError(errors)
	}

	return nil
}

// CreateAddressRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAddressRequestMultiError) AllErrors() []error { return m }

// CreateAddressRequestValidationError is the validation error returned by
// CreateAddressRequest.Validate if the designated constraints aren't met.
type CreateAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAddressRequestValidationError) ErrorName() string {
	return "CreateAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAddressRequestValidationError{}

// Validate checks the field values on CreateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAddressResponseMultiError, or nil if none found.
func (m *CreateAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressId

	if len(errors) > 0 {
		return CreateAddressResponseMultiError(errors)
	}

	return nil
}

// CreateAddressResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAddressResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAddressResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAddressResponseMultiError) AllErrors() []error { return m }

// CreateAddressResponseValidationError is the validation error returned by
// CreateAddressResponse.Validate if the designated constraints aren't met.
type CreateAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAddressResponseValidationError) ErrorName() string {
	return "CreateAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAddressResponseValidationError{}

// Validate checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAddressRequestMultiError, or nil if none found.
func (m *UpdateAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAddressRequestMultiError(errors)
	}

	return nil
}

// UpdateAddressRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressRequestMultiError) AllErrors() []error { return m }

// UpdateAddressRequestValidationError is the validation error returned by
// UpdateAddressRequest.Validate if the designated constraints aren't met.
type UpdateAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressRequestValidationError) ErrorName() string {
	return "UpdateAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressRequestValidationError{}

// Validate checks the field values on UpdateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAddressResponseMultiError, or nil if none found.
func (m *UpdateAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateAddressResponseMultiError(errors)
	}

	return nil
}

// UpdateAddressResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAddressResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressResponseMultiError) AllErrors() []error { return m }

// UpdateAddressResponseValidationError is the validation error returned by
// UpdateAddressResponse.Validate if the designated constraints aren't met.
type UpdateAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressResponseValidationError) ErrorName() string {
	return "UpdateAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressResponseValidationError{}

// Validate checks the field values on DeleteAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAddressRequestMultiError, or nil if none found.
func (m *DeleteAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressId

	if len(errors) > 0 {
		return DeleteAddressRequestMultiError(errors)
	}

	return nil
}

// DeleteAddressRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAddressRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAddressRequestMultiError) AllErrors() []error { return m }

// DeleteAddressRequestValidationError is the validation error returned by
// DeleteAddressRequest.Validate if the designated constraints aren't met.
type DeleteAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAddressRequestValidationError) ErrorName() string {
	return "DeleteAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAddressRequestValidationError{}

// Validate checks the field values on ListAddressesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesRequestMultiError, or nil if none found.
func (m *ListAddressesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Parent

	if m.PageSize != nil {

		if val := m.GetPageSize(); val < 0 || val > 1000 {
			err := ListAddressesRequestValidationError{
				field:  "PageSize",
				reason: "value must be inside range [0, 1000]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.PageNum != nil {

		if m.GetPageNum() < 1 {
			err := ListAddressesRequestValidationError{
				field:  "PageNum",
				reason: "value must be greater than or equal to 1",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.OrderField != nil {
		// no validation rules for OrderField
	}

	if m.OrderDirection != nil {
		// no validation rules for OrderDirection
	}

	if len(errors) > 0 {
		return ListAddressesRequestMultiError(errors)
	}

	return nil
}

// ListAddressesRequestMultiError is an error wrapping multiple validation
// errors returned by ListAddressesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAddressesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesRequestMultiError) AllErrors() []error { return m }

// ListAddressesRequestValidationError is the validation error returned by
// ListAddressesRequest.Validate if the designated constraints aren't met.
type ListAddressesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesRequestValidationError) ErrorName() string {
	return "ListAddressesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesRequestValidationError{}

// Validate checks the field values on ListAddressesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAddressesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAddressesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAddressesResponseMultiError, or nil if none found.
func (m *ListAddressesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAddressesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAddressesResponseValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListAddressesResponseMultiError(errors)
	}

	return nil
}

// ListAddressesResponseMultiError is an error wrapping multiple validation
// errors returned by ListAddressesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListAddressesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAddressesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAddressesResponseMultiError) AllErrors() []error { return m }

// ListAddressesResponseValidationError is the validation error returned by
// ListAddressesResponse.Validate if the designated constraints aren't met.
type ListAddressesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAddressesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAddressesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAddressesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAddressesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAddressesResponseValidationError) ErrorName() string {
	return "ListAddressesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListAddressesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAddressesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAddressesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAddressesResponseValidationError{}

// Validate checks the field values on CreateLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLifeCycleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLifeCycleRequestMultiError, or nil if none found.
func (m *CreateLifeCycleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLifeCycleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	if len(errors) > 0 {
		return CreateLifeCycleRequestMultiError(errors)
	}

	return nil
}

// CreateLifeCycleRequestMultiError is an error wrapping multiple validation
// errors returned by CreateLifeCycleRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateLifeCycleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLifeCycleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLifeCycleRequestMultiError) AllErrors() []error { return m }

// CreateLifeCycleRequestValidationError is the validation error returned by
// CreateLifeCycleRequest.Validate if the designated constraints aren't met.
type CreateLifeCycleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLifeCycleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLifeCycleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLifeCycleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLifeCycleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLifeCycleRequestValidationError) ErrorName() string {
	return "CreateLifeCycleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLifeCycleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLifeCycleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLifeCycleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLifeCycleRequestValidationError{}

// Validate checks the field values on CreateLifeCycleResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLifeCycleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLifeCycleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLifeCycleResponseMultiError, or nil if none found.
func (m *CreateLifeCycleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLifeCycleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateLifeCycleResponseMultiError(errors)
	}

	return nil
}

// CreateLifeCycleResponseMultiError is an error wrapping multiple validation
// errors returned by CreateLifeCycleResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateLifeCycleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLifeCycleResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLifeCycleResponseMultiError) AllErrors() []error { return m }

// CreateLifeCycleResponseValidationError is the validation error returned by
// CreateLifeCycleResponse.Validate if the designated constraints aren't met.
type CreateLifeCycleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLifeCycleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLifeCycleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLifeCycleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLifeCycleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLifeCycleResponseValidationError) ErrorName() string {
	return "CreateLifeCycleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLifeCycleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLifeCycleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLifeCycleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLifeCycleResponseValidationError{}

// Validate checks the field values on UpdateLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLifeCyclesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLifeCyclesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLifeCyclesRequestMultiError, or nil if none found.
func (m *UpdateLifeCyclesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateLifeCyclesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateLifeCyclesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateLifeCyclesRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateLifeCyclesRequestMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateLifeCyclesRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesRequestMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesRequestValidationError is the validation error returned by
// UpdateLifeCyclesRequest.Validate if the designated constraints aren't met.
type UpdateLifeCyclesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesRequestValidationError) ErrorName() string {
	return "UpdateLifeCyclesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesRequestValidationError{}

// Validate checks the field values on UpdateLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLifeCyclesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLifeCyclesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLifeCyclesResponseMultiError, or nil if none found.
func (m *UpdateLifeCyclesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateLifeCyclesResponseMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateLifeCyclesResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesResponseMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesResponseValidationError is the validation error returned by
// UpdateLifeCyclesResponse.Validate if the designated constraints aren't met.
type UpdateLifeCyclesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesResponseValidationError) ErrorName() string {
	return "UpdateLifeCyclesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesResponseValidationError{}

// Validate checks the field values on ListLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLifeCyclesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLifeCyclesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLifeCyclesRequestMultiError, or nil if none found.
func (m *ListLifeCyclesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLifeCyclesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListLifeCyclesRequestMultiError(errors)
	}

	return nil
}

// ListLifeCyclesRequestMultiError is an error wrapping multiple validation
// errors returned by ListLifeCyclesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListLifeCyclesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLifeCyclesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLifeCyclesRequestMultiError) AllErrors() []error { return m }

// ListLifeCyclesRequestValidationError is the validation error returned by
// ListLifeCyclesRequest.Validate if the designated constraints aren't met.
type ListLifeCyclesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLifeCyclesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLifeCyclesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLifeCyclesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLifeCyclesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLifeCyclesRequestValidationError) ErrorName() string {
	return "ListLifeCyclesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListLifeCyclesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLifeCyclesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLifeCyclesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLifeCyclesRequestValidationError{}

// Validate checks the field values on ListLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListLifeCyclesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListLifeCyclesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListLifeCyclesResponseMultiError, or nil if none found.
func (m *ListLifeCyclesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListLifeCyclesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLifeCycles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListLifeCyclesResponseValidationError{
						field:  fmt.Sprintf("LifeCycles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListLifeCyclesResponseValidationError{
						field:  fmt.Sprintf("LifeCycles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListLifeCyclesResponseValidationError{
					field:  fmt.Sprintf("LifeCycles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListLifeCyclesResponseMultiError(errors)
	}

	return nil
}

// ListLifeCyclesResponseMultiError is an error wrapping multiple validation
// errors returned by ListLifeCyclesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListLifeCyclesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListLifeCyclesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListLifeCyclesResponseMultiError) AllErrors() []error { return m }

// ListLifeCyclesResponseValidationError is the validation error returned by
// ListLifeCyclesResponse.Validate if the designated constraints aren't met.
type ListLifeCyclesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListLifeCyclesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListLifeCyclesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListLifeCyclesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListLifeCyclesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListLifeCyclesResponseValidationError) ErrorName() string {
	return "ListLifeCyclesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListLifeCyclesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListLifeCyclesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListLifeCyclesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListLifeCyclesResponseValidationError{}

// Validate checks the field values on DeleteLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteLifeCycleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteLifeCycleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteLifeCycleRequestMultiError, or nil if none found.
func (m *DeleteLifeCycleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteLifeCycleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteLifeCycleRequestMultiError(errors)
	}

	return nil
}

// DeleteLifeCycleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteLifeCycleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteLifeCycleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteLifeCycleRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteLifeCycleRequestMultiError) AllErrors() []error { return m }

// DeleteLifeCycleRequestValidationError is the validation error returned by
// DeleteLifeCycleRequest.Validate if the designated constraints aren't met.
type DeleteLifeCycleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteLifeCycleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteLifeCycleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteLifeCycleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteLifeCycleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteLifeCycleRequestValidationError) ErrorName() string {
	return "DeleteLifeCycleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteLifeCycleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteLifeCycleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteLifeCycleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteLifeCycleRequestValidationError{}

// Validate checks the field values on CreateActionStateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActionStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActionStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActionStateRequestMultiError, or nil if none found.
func (m *CreateActionStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActionStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for BusinessId

	// no validation rules for StaffId

	// no validation rules for Name

	// no validation rules for Sort

	// no validation rules for Color

	if len(errors) > 0 {
		return CreateActionStateRequestMultiError(errors)
	}

	return nil
}

// CreateActionStateRequestMultiError is an error wrapping multiple validation
// errors returned by CreateActionStateRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateActionStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActionStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActionStateRequestMultiError) AllErrors() []error { return m }

// CreateActionStateRequestValidationError is the validation error returned by
// CreateActionStateRequest.Validate if the designated constraints aren't met.
type CreateActionStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActionStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActionStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActionStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActionStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActionStateRequestValidationError) ErrorName() string {
	return "CreateActionStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActionStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActionStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActionStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActionStateRequestValidationError{}

// Validate checks the field values on CreateActionStateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActionStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActionStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActionStateResponseMultiError, or nil if none found.
func (m *CreateActionStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActionStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateActionStateResponseMultiError(errors)
	}

	return nil
}

// CreateActionStateResponseMultiError is an error wrapping multiple validation
// errors returned by CreateActionStateResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateActionStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActionStateResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActionStateResponseMultiError) AllErrors() []error { return m }

// CreateActionStateResponseValidationError is the validation error returned by
// CreateActionStateResponse.Validate if the designated constraints aren't met.
type CreateActionStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActionStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActionStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActionStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActionStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActionStateResponseValidationError) ErrorName() string {
	return "CreateActionStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActionStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActionStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActionStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActionStateResponseValidationError{}

// Validate checks the field values on UpdateActionStatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateActionStatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateActionStatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateActionStatesRequestMultiError, or nil if none found.
func (m *UpdateActionStatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionStatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUpdates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateActionStatesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateActionStatesRequestValidationError{
						field:  fmt.Sprintf("Updates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateActionStatesRequestValidationError{
					field:  fmt.Sprintf("Updates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return UpdateActionStatesRequestMultiError(errors)
	}

	return nil
}

// UpdateActionStatesRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateActionStatesRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateActionStatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionStatesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionStatesRequestMultiError) AllErrors() []error { return m }

// UpdateActionStatesRequestValidationError is the validation error returned by
// UpdateActionStatesRequest.Validate if the designated constraints aren't met.
type UpdateActionStatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionStatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionStatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionStatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionStatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionStatesRequestValidationError) ErrorName() string {
	return "UpdateActionStatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionStatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionStatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionStatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionStatesRequestValidationError{}

// Validate checks the field values on UpdateActionsStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateActionsStatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateActionsStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateActionsStatesResponseMultiError, or nil if none found.
func (m *UpdateActionsStatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionsStatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateActionsStatesResponseMultiError(errors)
	}

	return nil
}

// UpdateActionsStatesResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateActionsStatesResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateActionsStatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionsStatesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionsStatesResponseMultiError) AllErrors() []error { return m }

// UpdateActionsStatesResponseValidationError is the validation error returned
// by UpdateActionsStatesResponse.Validate if the designated constraints
// aren't met.
type UpdateActionsStatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionsStatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionsStatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionsStatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionsStatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionsStatesResponseValidationError) ErrorName() string {
	return "UpdateActionsStatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionsStatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionsStatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionsStatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionsStatesResponseValidationError{}

// Validate checks the field values on ListActionStatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActionStatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActionStatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActionStatesRequestMultiError, or nil if none found.
func (m *ListActionStatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActionStatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	if len(errors) > 0 {
		return ListActionStatesRequestMultiError(errors)
	}

	return nil
}

// ListActionStatesRequestMultiError is an error wrapping multiple validation
// errors returned by ListActionStatesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListActionStatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActionStatesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActionStatesRequestMultiError) AllErrors() []error { return m }

// ListActionStatesRequestValidationError is the validation error returned by
// ListActionStatesRequest.Validate if the designated constraints aren't met.
type ListActionStatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActionStatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActionStatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActionStatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActionStatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActionStatesRequestValidationError) ErrorName() string {
	return "ListActionStatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListActionStatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActionStatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActionStatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActionStatesRequestValidationError{}

// Validate checks the field values on ListActionStatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActionStatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActionStatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActionStatesResponseMultiError, or nil if none found.
func (m *ListActionStatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActionStatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetActionStates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListActionStatesResponseValidationError{
						field:  fmt.Sprintf("ActionStates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListActionStatesResponseValidationError{
						field:  fmt.Sprintf("ActionStates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListActionStatesResponseValidationError{
					field:  fmt.Sprintf("ActionStates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListActionStatesResponseMultiError(errors)
	}

	return nil
}

// ListActionStatesResponseMultiError is an error wrapping multiple validation
// errors returned by ListActionStatesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListActionStatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActionStatesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActionStatesResponseMultiError) AllErrors() []error { return m }

// ListActionStatesResponseValidationError is the validation error returned by
// ListActionStatesResponse.Validate if the designated constraints aren't met.
type ListActionStatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActionStatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActionStatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActionStatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActionStatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActionStatesResponseValidationError) ErrorName() string {
	return "ListActionStatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListActionStatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActionStatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActionStatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActionStatesResponseValidationError{}

// Validate checks the field values on DeleteActionStateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteActionStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteActionStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteActionStateRequestMultiError, or nil if none found.
func (m *DeleteActionStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteActionStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteActionStateRequestMultiError(errors)
	}

	return nil
}

// DeleteActionStateRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteActionStateRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteActionStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteActionStateRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteActionStateRequestMultiError) AllErrors() []error { return m }

// DeleteActionStateRequestValidationError is the validation error returned by
// DeleteActionStateRequest.Validate if the designated constraints aren't met.
type DeleteActionStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteActionStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteActionStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteActionStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteActionStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteActionStateRequestValidationError) ErrorName() string {
	return "DeleteActionStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteActionStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteActionStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteActionStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteActionStateRequestValidationError{}

// Validate checks the field values on CreateViewRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateViewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateViewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateViewRequestMultiError, or nil if none found.
func (m *CreateViewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateViewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	if all {
		switch v := interface{}(m.GetOrderBy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateViewRequestValidationError{
					field:  "OrderBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateViewRequestValidationError{
					field:  "OrderBy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderBy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateViewRequestValidationError{
				field:  "OrderBy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateViewRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateViewRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateViewRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StaffId

	// no validation rules for CompanyId

	// no validation rules for Type

	if len(errors) > 0 {
		return CreateViewRequestMultiError(errors)
	}

	return nil
}

// CreateViewRequestMultiError is an error wrapping multiple validation errors
// returned by CreateViewRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateViewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateViewRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateViewRequestMultiError) AllErrors() []error { return m }

// CreateViewRequestValidationError is the validation error returned by
// CreateViewRequest.Validate if the designated constraints aren't met.
type CreateViewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateViewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateViewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateViewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateViewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateViewRequestValidationError) ErrorName() string {
	return "CreateViewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateViewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateViewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateViewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateViewRequestValidationError{}

// Validate checks the field values on CreateViewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateViewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateViewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateViewResponseMultiError, or nil if none found.
func (m *CreateViewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateViewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateViewResponseMultiError(errors)
	}

	return nil
}

// CreateViewResponseMultiError is an error wrapping multiple validation errors
// returned by CreateViewResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateViewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateViewResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateViewResponseMultiError) AllErrors() []error { return m }

// CreateViewResponseValidationError is the validation error returned by
// CreateViewResponse.Validate if the designated constraints aren't met.
type CreateViewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateViewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateViewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateViewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateViewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateViewResponseValidationError) ErrorName() string {
	return "CreateViewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateViewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateViewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateViewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateViewResponseValidationError{}

// Validate checks the field values on UpdateViewRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateViewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateViewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateViewRequestMultiError, or nil if none found.
func (m *UpdateViewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateViewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for StaffId

	// no validation rules for CompanyId

	if m.Title != nil {
		// no validation rules for Title
	}

	if m.OrderBy != nil {

		if all {
			switch v := interface{}(m.GetOrderBy()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateViewRequestValidationError{
						field:  "OrderBy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateViewRequestValidationError{
						field:  "OrderBy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOrderBy()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateViewRequestValidationError{
					field:  "OrderBy",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Filter != nil {

		if all {
			switch v := interface{}(m.GetFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateViewRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateViewRequestValidationError{
						field:  "Filter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateViewRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateViewRequestMultiError(errors)
	}

	return nil
}

// UpdateViewRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateViewRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateViewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateViewRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateViewRequestMultiError) AllErrors() []error { return m }

// UpdateViewRequestValidationError is the validation error returned by
// UpdateViewRequest.Validate if the designated constraints aren't met.
type UpdateViewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateViewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateViewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateViewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateViewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateViewRequestValidationError) ErrorName() string {
	return "UpdateViewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateViewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateViewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateViewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateViewRequestValidationError{}

// Validate checks the field values on UpdateViewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateViewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateViewResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateViewResponseMultiError, or nil if none found.
func (m *UpdateViewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateViewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateViewResponseMultiError(errors)
	}

	return nil
}

// UpdateViewResponseMultiError is an error wrapping multiple validation errors
// returned by UpdateViewResponse.ValidateAll() if the designated constraints
// aren't met.
type UpdateViewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateViewResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateViewResponseMultiError) AllErrors() []error { return m }

// UpdateViewResponseValidationError is the validation error returned by
// UpdateViewResponse.Validate if the designated constraints aren't met.
type UpdateViewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateViewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateViewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateViewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateViewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateViewResponseValidationError) ErrorName() string {
	return "UpdateViewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateViewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateViewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateViewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateViewResponseValidationError{}

// Validate checks the field values on ListViewsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListViewsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListViewsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListViewsRequestMultiError, or nil if none found.
func (m *ListViewsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListViewsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompanyId

	// no validation rules for StaffId

	if m.Type != nil {
		// no validation rules for Type
	}

	if len(errors) > 0 {
		return ListViewsRequestMultiError(errors)
	}

	return nil
}

// ListViewsRequestMultiError is an error wrapping multiple validation errors
// returned by ListViewsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListViewsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListViewsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListViewsRequestMultiError) AllErrors() []error { return m }

// ListViewsRequestValidationError is the validation error returned by
// ListViewsRequest.Validate if the designated constraints aren't met.
type ListViewsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListViewsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListViewsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListViewsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListViewsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListViewsRequestValidationError) ErrorName() string { return "ListViewsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListViewsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListViewsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListViewsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListViewsRequestValidationError{}

// Validate checks the field values on ListViewsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListViewsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListViewsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListViewsResponseMultiError, or nil if none found.
func (m *ListViewsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListViewsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListViewsResponseValidationError{
						field:  fmt.Sprintf("Views[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListViewsResponseValidationError{
						field:  fmt.Sprintf("Views[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListViewsResponseValidationError{
					field:  fmt.Sprintf("Views[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListViewsResponseMultiError(errors)
	}

	return nil
}

// ListViewsResponseMultiError is an error wrapping multiple validation errors
// returned by ListViewsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListViewsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListViewsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListViewsResponseMultiError) AllErrors() []error { return m }

// ListViewsResponseValidationError is the validation error returned by
// ListViewsResponse.Validate if the designated constraints aren't met.
type ListViewsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListViewsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListViewsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListViewsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListViewsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListViewsResponseValidationError) ErrorName() string {
	return "ListViewsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListViewsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListViewsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListViewsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListViewsResponseValidationError{}

// Validate checks the field values on DeleteViewRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteViewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteViewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteViewRequestMultiError, or nil if none found.
func (m *DeleteViewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteViewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CompanyId

	// no validation rules for StaffId

	if len(errors) > 0 {
		return DeleteViewRequestMultiError(errors)
	}

	return nil
}

// DeleteViewRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteViewRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteViewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteViewRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteViewRequestMultiError) AllErrors() []error { return m }

// DeleteViewRequestValidationError is the validation error returned by
// DeleteViewRequest.Validate if the designated constraints aren't met.
type DeleteViewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteViewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteViewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteViewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteViewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteViewRequestValidationError) ErrorName() string {
	return "DeleteViewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteViewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteViewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteViewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteViewRequestValidationError{}

// Validate checks the field values on UpdateCustomerRequest_UpdateCustomer
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateCustomerRequest_UpdateCustomer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest_UpdateCustomer
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequest_UpdateCustomerMultiError, or nil if none found.
func (m *UpdateCustomerRequest_UpdateCustomer) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest_UpdateCustomer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerRequest_UpdateCustomerValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContact()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "Contact",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContact()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCustomerRequest_UpdateCustomerValidationError{
				field:  "Contact",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.LifeCycle != nil {
		// no validation rules for LifeCycle
	}

	if m.ActionState != nil {
		// no validation rules for ActionState
	}

	if m.Source != nil {
		// no validation rules for Source
	}

	if m.PreferredBusinessId != nil {
		// no validation rules for PreferredBusinessId
	}

	if m.AvatarPath != nil {
		// no validation rules for AvatarPath
	}

	if m.GivenName != nil {
		// no validation rules for GivenName
	}

	if m.FamilyName != nil {
		// no validation rules for FamilyName
	}

	if m.Email != nil {
		// no validation rules for Email
	}

	if m.PhoneNumber != nil {
		// no validation rules for PhoneNumber
	}

	if m.BirthTime != nil {

		if all {
			switch v := interface{}(m.GetBirthTime()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
						field:  "BirthTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
						field:  "BirthTime",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBirthTime()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "BirthTime",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.AllocateStaffId != nil {
		// no validation rules for AllocateStaffId
	}

	if m.AdditionalInfo != nil {

		if all {
			switch v := interface{}(m.GetAdditionalInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
						field:  "AdditionalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateCustomerRequest_UpdateCustomerValidationError{
						field:  "AdditionalInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAdditionalInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateCustomerRequest_UpdateCustomerValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.CustomizeLifeCycleId != nil {
		// no validation rules for CustomizeLifeCycleId
	}

	if m.CustomizeActionStateId != nil {
		// no validation rules for CustomizeActionStateId
	}

	if m.ClientColor != nil {
		// no validation rules for ClientColor
	}

	if len(errors) > 0 {
		return UpdateCustomerRequest_UpdateCustomerMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequest_UpdateCustomerMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerRequest_UpdateCustomer.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateCustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequest_UpdateCustomerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequest_UpdateCustomerMultiError) AllErrors() []error { return m }

// UpdateCustomerRequest_UpdateCustomerValidationError is the validation error
// returned by UpdateCustomerRequest_UpdateCustomer.Validate if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateCustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequest_UpdateCustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequest_UpdateCustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequest_UpdateCustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequest_UpdateCustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequest_UpdateCustomerValidationError) ErrorName() string {
	return "UpdateCustomerRequest_UpdateCustomerValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequest_UpdateCustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest_UpdateCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequest_UpdateCustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequest_UpdateCustomerValidationError{}

// Validate checks the field values on UpdateCustomerRequest_UpdateAddress with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerRequest_UpdateAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest_UpdateAddress
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequest_UpdateAddressMultiError, or nil if none found.
func (m *UpdateCustomerRequest_UpdateAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest_UpdateAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Address1 != nil {
		// no validation rules for Address1
	}

	if m.Address2 != nil {
		// no validation rules for Address2
	}

	if m.City != nil {
		// no validation rules for City
	}

	if m.State != nil {
		// no validation rules for State
	}

	if m.RegionCode != nil {
		// no validation rules for RegionCode
	}

	if m.Zipcode != nil {
		// no validation rules for Zipcode
	}

	if m.Lat != nil {
		// no validation rules for Lat
	}

	if m.Lng != nil {
		// no validation rules for Lng
	}

	if m.IsPrimary != nil {
		// no validation rules for IsPrimary
	}

	if len(errors) > 0 {
		return UpdateCustomerRequest_UpdateAddressMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequest_UpdateAddressMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerRequest_UpdateAddress.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequest_UpdateAddressMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequest_UpdateAddressMultiError) AllErrors() []error { return m }

// UpdateCustomerRequest_UpdateAddressValidationError is the validation error
// returned by UpdateCustomerRequest_UpdateAddress.Validate if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequest_UpdateAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequest_UpdateAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequest_UpdateAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequest_UpdateAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequest_UpdateAddressValidationError) ErrorName() string {
	return "UpdateCustomerRequest_UpdateAddressValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequest_UpdateAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest_UpdateAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequest_UpdateAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequest_UpdateAddressValidationError{}

// Validate checks the field values on UpdateCustomerRequest_UpdateContact with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateCustomerRequest_UpdateContact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest_UpdateContact
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequest_UpdateContactMultiError, or nil if none found.
func (m *UpdateCustomerRequest_UpdateContact) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest_UpdateContact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.GivenName != nil {
		// no validation rules for GivenName
	}

	if m.FamilyName != nil {
		// no validation rules for FamilyName
	}

	if m.PhoneNumber != nil {
		// no validation rules for PhoneNumber
	}

	if m.Email != nil {
		// no validation rules for Email
	}

	if m.Title != nil {
		// no validation rules for Title
	}

	if m.IsPrimary != nil {
		// no validation rules for IsPrimary
	}

	if m.E164PhoneNumber != nil {
		// no validation rules for E164PhoneNumber
	}

	if len(errors) > 0 {
		return UpdateCustomerRequest_UpdateContactMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequest_UpdateContactMultiError is an error wrapping multiple
// validation errors returned by
// UpdateCustomerRequest_UpdateContact.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequest_UpdateContactMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequest_UpdateContactMultiError) AllErrors() []error { return m }

// UpdateCustomerRequest_UpdateContactValidationError is the validation error
// returned by UpdateCustomerRequest_UpdateContact.Validate if the designated
// constraints aren't met.
type UpdateCustomerRequest_UpdateContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequest_UpdateContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequest_UpdateContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequest_UpdateContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequest_UpdateContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequest_UpdateContactValidationError) ErrorName() string {
	return "UpdateCustomerRequest_UpdateContactValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequest_UpdateContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest_UpdateContact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequest_UpdateContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequest_UpdateContactValidationError{}

// Validate checks the field values on ListCustomersRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCustomersRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomersRequest_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCustomersRequest_FilterMultiError, or nil if none found.
func (m *ListCustomersRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomersRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.ActionState != nil {
		// no validation rules for ActionState
	}

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.Keyword != nil {
		// no validation rules for Keyword
	}

	if m.LifeCycle != nil {
		// no validation rules for LifeCycle
	}

	if m.CustomizeLifeCycleId != nil {
		// no validation rules for CustomizeLifeCycleId
	}

	if m.CustomizeActionStateId != nil {
		// no validation rules for CustomizeActionStateId
	}

	if m.MainPhoneNumber != nil {
		// no validation rules for MainPhoneNumber
	}

	if len(errors) > 0 {
		return ListCustomersRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomersRequest_FilterMultiError is an error wrapping multiple
// validation errors returned by ListCustomersRequest_Filter.ValidateAll() if
// the designated constraints aren't met.
type ListCustomersRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomersRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomersRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomersRequest_FilterValidationError is the validation error returned
// by ListCustomersRequest_Filter.Validate if the designated constraints
// aren't met.
type ListCustomersRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomersRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomersRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomersRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomersRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomersRequest_FilterValidationError) ErrorName() string {
	return "ListCustomersRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomersRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomersRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomersRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomersRequest_FilterValidationError{}

// Validate checks the field values on ListCustomerHistoryLogsRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCustomerHistoryLogsRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCustomerHistoryLogsRequest_Filter
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListCustomerHistoryLogsRequest_FilterMultiError, or nil if none found.
func (m *ListCustomerHistoryLogsRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCustomerHistoryLogsRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.Type != nil {
		// no validation rules for Type
	}

	if m.CompanyId != nil {
		// no validation rules for CompanyId
	}

	if len(errors) > 0 {
		return ListCustomerHistoryLogsRequest_FilterMultiError(errors)
	}

	return nil
}

// ListCustomerHistoryLogsRequest_FilterMultiError is an error wrapping
// multiple validation errors returned by
// ListCustomerHistoryLogsRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type ListCustomerHistoryLogsRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCustomerHistoryLogsRequest_FilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCustomerHistoryLogsRequest_FilterMultiError) AllErrors() []error { return m }

// ListCustomerHistoryLogsRequest_FilterValidationError is the validation error
// returned by ListCustomerHistoryLogsRequest_Filter.Validate if the
// designated constraints aren't met.
type ListCustomerHistoryLogsRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCustomerHistoryLogsRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCustomerHistoryLogsRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCustomerHistoryLogsRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCustomerHistoryLogsRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCustomerHistoryLogsRequest_FilterValidationError) ErrorName() string {
	return "ListCustomerHistoryLogsRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListCustomerHistoryLogsRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCustomerHistoryLogsRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCustomerHistoryLogsRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCustomerHistoryLogsRequest_FilterValidationError{}

// Validate checks the field values on UpdateAddressRequest_UpdateAddress with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateAddressRequest_UpdateAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressRequest_UpdateAddress
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateAddressRequest_UpdateAddressMultiError, or nil if none found.
func (m *UpdateAddressRequest_UpdateAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressRequest_UpdateAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressId

	if m.Address1 != nil {
		// no validation rules for Address1
	}

	if m.Address2 != nil {
		// no validation rules for Address2
	}

	if m.City != nil {
		// no validation rules for City
	}

	if m.State != nil {
		// no validation rules for State
	}

	if m.RegionCode != nil {
		// no validation rules for RegionCode
	}

	if m.Zipcode != nil {
		// no validation rules for Zipcode
	}

	if m.Lat != nil {
		// no validation rules for Lat
	}

	if m.Lng != nil {
		// no validation rules for Lng
	}

	if m.IsPrimary != nil {
		// no validation rules for IsPrimary
	}

	if len(errors) > 0 {
		return UpdateAddressRequest_UpdateAddressMultiError(errors)
	}

	return nil
}

// UpdateAddressRequest_UpdateAddressMultiError is an error wrapping multiple
// validation errors returned by
// UpdateAddressRequest_UpdateAddress.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressRequest_UpdateAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressRequest_UpdateAddressMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressRequest_UpdateAddressMultiError) AllErrors() []error { return m }

// UpdateAddressRequest_UpdateAddressValidationError is the validation error
// returned by UpdateAddressRequest_UpdateAddress.Validate if the designated
// constraints aren't met.
type UpdateAddressRequest_UpdateAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressRequest_UpdateAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressRequest_UpdateAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressRequest_UpdateAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressRequest_UpdateAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressRequest_UpdateAddressValidationError) ErrorName() string {
	return "UpdateAddressRequest_UpdateAddressValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressRequest_UpdateAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressRequest_UpdateAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressRequest_UpdateAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressRequest_UpdateAddressValidationError{}

// Validate checks the field values on UpdateLifeCyclesRequest_UpdateLifeCycle
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateLifeCyclesRequest_UpdateLifeCycle with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateLifeCyclesRequest_UpdateLifeCycleMultiError, or nil if none found.
func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLifeCyclesRequest_UpdateLifeCycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if len(errors) > 0 {
		return UpdateLifeCyclesRequest_UpdateLifeCycleMultiError(errors)
	}

	return nil
}

// UpdateLifeCyclesRequest_UpdateLifeCycleMultiError is an error wrapping
// multiple validation errors returned by
// UpdateLifeCyclesRequest_UpdateLifeCycle.ValidateAll() if the designated
// constraints aren't met.
type UpdateLifeCyclesRequest_UpdateLifeCycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLifeCyclesRequest_UpdateLifeCycleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLifeCyclesRequest_UpdateLifeCycleMultiError) AllErrors() []error { return m }

// UpdateLifeCyclesRequest_UpdateLifeCycleValidationError is the validation
// error returned by UpdateLifeCyclesRequest_UpdateLifeCycle.Validate if the
// designated constraints aren't met.
type UpdateLifeCyclesRequest_UpdateLifeCycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) ErrorName() string {
	return "UpdateLifeCyclesRequest_UpdateLifeCycleValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLifeCyclesRequest_UpdateLifeCycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLifeCyclesRequest_UpdateLifeCycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLifeCyclesRequest_UpdateLifeCycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLifeCyclesRequest_UpdateLifeCycleValidationError{}

// Validate checks the field values on
// UpdateActionStatesRequest_UpdateActionState with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateActionStatesRequest_UpdateActionState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateActionStatesRequest_UpdateActionState with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateActionStatesRequest_UpdateActionStateMultiError, or nil if none found.
func (m *UpdateActionStatesRequest_UpdateActionState) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateActionStatesRequest_UpdateActionState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if m.Name != nil {
		// no validation rules for Name
	}

	if m.Sort != nil {
		// no validation rules for Sort
	}

	if m.Color != nil {
		// no validation rules for Color
	}

	if len(errors) > 0 {
		return UpdateActionStatesRequest_UpdateActionStateMultiError(errors)
	}

	return nil
}

// UpdateActionStatesRequest_UpdateActionStateMultiError is an error wrapping
// multiple validation errors returned by
// UpdateActionStatesRequest_UpdateActionState.ValidateAll() if the designated
// constraints aren't met.
type UpdateActionStatesRequest_UpdateActionStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateActionStatesRequest_UpdateActionStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateActionStatesRequest_UpdateActionStateMultiError) AllErrors() []error { return m }

// UpdateActionStatesRequest_UpdateActionStateValidationError is the validation
// error returned by UpdateActionStatesRequest_UpdateActionState.Validate if
// the designated constraints aren't met.
type UpdateActionStatesRequest_UpdateActionStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) ErrorName() string {
	return "UpdateActionStatesRequest_UpdateActionStateValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateActionStatesRequest_UpdateActionStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateActionStatesRequest_UpdateActionState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateActionStatesRequest_UpdateActionStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateActionStatesRequest_UpdateActionStateValidationError{}
