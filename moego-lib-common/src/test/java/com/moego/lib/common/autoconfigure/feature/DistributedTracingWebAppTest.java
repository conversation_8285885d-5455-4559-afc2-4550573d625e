package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.tracing.sentry.grpc.GrpcSentryClientInterceptor;
import com.moego.lib.common.observability.tracing.sentry.grpc.GrpcSentryServerInterceptor;
import feign.Capability;
import io.sentry.HubAdapter;
import io.sentry.IHub;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link DistributedTracing} tester.
 */
public class DistributedTracingWebAppTest {

    private final WebApplicationContextRunner runner = new WebApplicationContextRunner()
            .withUserConfiguration(DistributedTracing.class)
            .withBean(IHub.class, HubAdapter::getInstance);

    @Test
    public void testDefaultBehavior() {
        runner.withPropertyValues("sentry.dsn=xx").run(context -> {
            assertThat(context).hasSingleBean(Capability.class);
            assertThat(context).hasSingleBean(GrpcSentryClientInterceptor.class);
            assertThat(context).doesNotHaveBean(GrpcSentryServerInterceptor.class);
        });
    }

    @Test
    public void testHttpDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(Capability.class);
        });
    }

    @Test
    public void testGrpcDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(GrpcSentryClientInterceptor.class);
            assertThat(context).doesNotHaveBean(GrpcSentryServerInterceptor.class);
        });
    }

    @Test
    public void testGrpcEnabledButGrpcClientDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".client.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcSentryClientInterceptor.class);
                    assertThat(context).doesNotHaveBean(GrpcSentryServerInterceptor.class);
                });
    }
}
