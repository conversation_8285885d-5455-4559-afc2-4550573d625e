package com.moego.lib.common.grey.grpc;

import static java.lang.Thread.sleep;
import static org.assertj.core.api.Assertions.assertThat;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

/**
 * {@link Cache} tester.
 *
 * <AUTHOR>
 */
class CacheTest {

    @Test
    @SneakyThrows
    void testCacheExpire() {
        Cache<String> cache = new Cache<>(300, 100);
        cache.put("a", "a");

        sleep(200);
        // not expired, and refresh last used time
        assertThat(cache.get("a")).isEqualTo("a");

        sleep(200);
        // refreshed last used time, shouldn't be expired
        assertThat(cache.get("a")).isEqualTo("a");

        sleep(350);
        // must be expired
        assertThat(cache.get("a")).isNull();
    }

    @Test
    @SneakyThrows
    void testGetOrSupply() {
        Cache<String> cache = new Cache<>(300, 100);
        assertThat(cache.getOrSupply("a", () -> "a")).isEqualTo("a");
        assertThat(cache.getOrSupply("a", () -> "b")).isEqualTo("a");

        sleep(310);
        assertThat(cache.getOrSupply("a", () -> "b")).isEqualTo("b");
    }

    @Test
    @SneakyThrows
    void testGetOrSupplyConcurrencySafety() {
        Cache<String> cache = new Cache<>(3000, 1000);

        CountDownLatch latch = new CountDownLatch(100);
        AtomicInteger executeCount = new AtomicInteger(0);
        for (int i = 0; i < latch.getCount(); i++) {
            new Thread(() -> {
                        cache.getOrSupply("a", () -> veryTimeConsumingOperation(executeCount));
                        latch.countDown();
                    })
                    .start();
        }

        latch.await();

        assertThat(cache.get("a")).isNotNull();
        assertThat(executeCount.get()).isEqualTo(1);
    }

    @SneakyThrows
    private String veryTimeConsumingOperation(AtomicInteger count) {
        count.incrementAndGet();
        sleep(1000);
        return "a";
    }
}
