package com.moego.lib.common.autoconfigure.grpc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import com.moego.lib.common.grpc.ServerStarter;
import com.moego.lib.common.grpc.server.GrpcRequestContextServerInterceptor;
import com.moego.lib.common.grpc.server.GrpcServer;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link GrpcServerConfiguration} tester.
 */
class GrpcServerConfigurationTest {

    private final ApplicationContextRunner runner = new ApplicationContextRunner()
            .withUserConfiguration(GrpcServerConfiguration.class)
            .withBean(GrpcProperties.class, GrpcProperties::new);

    @Test
    void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(GrpcServer.class);
            assertThat(context).hasSingleBean(ServerStarter.class);
            assertThat(context).hasSingleBean(GrpcRequestContextServerInterceptor.class);
            assertThat(context).doesNotHaveBean("reflectionBindableService");
        });
    }

    @Test
    void testDebugMode() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.debug-enabled=true")
                .run(context -> {
                    assertThat(context).hasBean("reflectionBindableService");
                });

        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.debug-enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean("reflectionBindableService");
                });

        // if not false, then true
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.debug-enabled=whatever")
                .run(context -> {
                    assertThat(context).hasBean("reflectionBindableService");
                });
    }

    @Test
    void testDisableGrpcServer() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcServer.class);
                    assertThat(context).doesNotHaveBean(ServerStarter.class);
                    assertThat(context).doesNotHaveBean(GrpcRequestContextServerInterceptor.class);
                });
    }

    @Test
    void testGetGrpcServerExecutor() {
        assertThatCode(() -> Class.forName("com.moego.lib.common.grpc.server.GrpcServer")
                        .getDeclaredField("server"))
                .doesNotThrowAnyException();

        assertThatCode(() -> Class.forName("io.grpc.internal.ServerImpl").getDeclaredField("executor"))
                .doesNotThrowAnyException();
    }
}
