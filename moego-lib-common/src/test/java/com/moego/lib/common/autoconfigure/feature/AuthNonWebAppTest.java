package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.auth.grpc.AuthServerInterceptor;
import com.moego.lib.common.auth.grpc.GrpcMethodAuthHolder;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link Auth} tester.
 */
public class AuthNonWebAppTest {

    private final ApplicationContextRunner runner = new ApplicationContextRunner()
            .withUserConfiguration(Auth.class)
            .withBean(HttpProperties.class, HttpProperties::new);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean("authWebMvcConfigurer");
            assertThat(context).hasSingleBean(GrpcMethodAuthHolder.class);
            assertThat(context).hasSingleBean(AuthServerInterceptor.class);
        });
    }

    @Test
    public void testGrpcDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(GrpcMethodAuthHolder.class);
            assertThat(context).doesNotHaveBean(AuthServerInterceptor.class);
        });
    }

    @Test
    public void testGrpcEnabledButGrpcServerDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcMethodAuthHolder.class);
                    assertThat(context).doesNotHaveBean(AuthServerInterceptor.class);
                });
    }

    @Test
    public void testDisableAuth() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.auth.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcMethodAuthHolder.class);
                    assertThat(context).doesNotHaveBean(AuthServerInterceptor.class);
                });
    }
}
