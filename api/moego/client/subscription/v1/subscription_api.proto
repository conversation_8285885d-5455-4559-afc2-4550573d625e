syntax = "proto3";

package moego.client.subscription.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/subscription/v1;subscriptionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.subscription.v1";

// the subscription service
service SubscriptionService {
  // get credit
  rpc GetCredit(GetCreditParams) returns (GetCreditResult);
}

// GetCreditParams
message GetCreditParams {
  // company id
  int64 company_id = 1 [(validate.rules) = {
    int64: {gt: 0}
  }];
}

// GetCreditResult
message GetCreditResult {
  // credit, the unit is cents
  int64 credit = 1;
}
