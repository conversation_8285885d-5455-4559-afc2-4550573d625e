syntax = "proto3";

package moego.client.account.v1;

import "google/type/latlng.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.account.v1";

// The params message for ListAccountAddress.
message ListAccountAddressParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListAccountAddress.
message ListAccountAddressResult {
  // The list of account address.
  repeated moego.models.business_customer.v1.BusinessCustomerAddressView addresses = 1;
}

// The params message for CreateAccountAddress.
message CreateAccountAddressParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // address params
  AccountAddressDef address = 2 [(validate.rules).message = {required: true}];
}

// The result message for CreateAccountAddress.
message CreateAccountAddressResult {
  // Created account address id.
  int64 address_id = 1;
}

// The params message for UpdateAccountAddress.
message UpdateAccountAddressParams {
  // address id
  int64 address_id = 1 [(validate.rules).int64 = {gt: 0}];

  // address params
  AccountAddressDef address = 2 [(validate.rules).message = {required: true}];
}

// The result message for UpdateAccountAddress.
message UpdateAccountAddressResult {}

// AccountAddressDef is the definition of account address.
message AccountAddressDef {
  // address 1
  string address1 = 1 [(validate.rules).string = {max_len: 255}];

  // address 2
  string address2 = 2 [(validate.rules).string = {max_len: 255}];

  // city
  string city = 3 [(validate.rules).string = {max_len: 50}];

  // state
  string state = 4 [(validate.rules).string = {max_len: 50}];

  // country
  string country = 5 [(validate.rules).string = {max_len: 50}];

  // zipcode
  string zipcode = 6 [(validate.rules).string = {max_len: 10}];

  // coordinate
  google.type.LatLng coordinate = 7 [(validate.rules).message = {required: true}];
}

// AccountAddressService is the service for manage account address. Requires C-side login
service AccountAddressService {
  // List account address.
  rpc ListAccountAddress(ListAccountAddressParams) returns (ListAccountAddressResult);

  // Create a new account address.
  rpc CreateAccountAddress(CreateAccountAddressParams) returns (CreateAccountAddressResult);

  // Update a existing account address.
  rpc UpdateAccountAddress(UpdateAccountAddressParams) returns (UpdateAccountAddressResult);
}
