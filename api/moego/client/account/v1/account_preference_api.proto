syntax = "proto3";

package moego.client.account.v1;

import "moego/models/organization/v1/company_enums.proto";
import "moego/models/organization/v1/country_defs.proto";
import "moego/models/organization/v1/time_zone.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.account.v1";

// get account preference params
message GetAccountPreferenceParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get account preference result
message GetAccountPreferenceResult {
  // preference setting
  CompanyPreferenceSettingView preference_setting = 1;
}

// company preference setting view
message CompanyPreferenceSettingView {
  // currency code
  string currency_code = 1;
  // currency symbol
  string currency_symbol = 2;
  // date format
  models.organization.v1.DateFormat date_format_type = 3;
  // time format
  models.organization.v1.TimeFormat time_format_type = 4;
  // unit of weight
  models.organization.v1.WeightUnit unit_of_weight_type = 5;
  // unit of distance
  models.organization.v1.DistanceUnit unit_of_distance_type = 6;
  // country
  models.organization.v1.CountryDef country = 8;
  // timezone
  models.organization.v1.TimeZone time_zone = 9;
}

// Preference set up
service AccountPreferenceService {
  // get account preference detail, contains currency, date format, time format, weight unit, distance unit etc.
  rpc GetAccountPreference(GetAccountPreferenceParams) returns (GetAccountPreferenceResult);
}
