syntax = "proto3";

package moego.client.customer.v1;

import "moego/models/customer/v1/customer_pet_breed_models.proto";
import "moego/models/customer/v1/customer_pet_metadata_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// get pet metadata list request
message GetPetMetadataListRequest {}

// get pet metadata list response
message GetPetMetadataListResponse {
  // pet type list
  repeated moego.models.customer.v1.PetMetadataModel pet_types = 1;
  // hair length list
  repeated moego.models.customer.v1.PetMetadataModel hair_lengths = 2;
  // behavior list
  repeated moego.models.customer.v1.PetMetadataModel behaviors = 3;
  // fixed list
  repeated moego.models.customer.v1.PetMetadataModel fixed = 4;
  // vaccine list
  repeated moego.models.customer.v1.PetMetadataModel vaccines = 5;
  // weight unit list
  repeated moego.models.customer.v1.PetMetadataModel weight_units = 6;
}

//  pet breed list response
message GetPetBreedListRequest {}

//  pet breed list response
message GetPetBreedListResponse {
  // pet breed list
  repeated moego.models.customer.v1.PetBreedModel breeds = 1;
}

// pet metadata service
service PetMetadataService {
  // get pet metadata list
  rpc GetPetMetadataList(GetPetMetadataListRequest) returns (GetPetMetadataListResponse);
  // get pet breed list
  rpc GetPetBreedList(GetPetBreedListRequest) returns (GetPetBreedListResponse);
}
