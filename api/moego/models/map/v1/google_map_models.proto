syntax = "proto3";

package moego.models.map.v1;

import "google/geo/type/viewport.proto";
import "google/type/latlng.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.map.v1";

// GooglePlace
// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places#resource:-place
message GooglePlace {
  // The unique identifier of a place.
  string id = 1;

  // This Place's resource name, in `places/{place_id}` format.  Can be used to
  // look up the Place.
  string name = 2;

  // A set of type tags for this result. For example, "political" and
  // "locality". For the complete list of possible values, see Table A and Table
  // B at
  // https://developers.google.com/maps/documentation/places/web-service/place-types
  repeated string types = 3;

  // A full, human-readable address for this place.
  string formatted_address = 4;

  // A short, human-readable address for this place.
  string short_formatted_address = 5;

  // Repeated components for each locality level.
  // Note the following facts about the address_components[] array:
  // - The array of address components may contain more components than the
  // formatted_address.
  // - The array does not necessarily include all the political entities that
  // contain an address, apart from those included in the formatted_address. To
  // retrieve all the political entities that contain a specific address, you
  // should use reverse geocoding, passing the latitude/longitude of the address
  // as a parameter to the request.
  // - The format of the response is not guaranteed to remain the same between
  // requests. In particular, the number of address_components varies based on
  // the address requested and can change over time for the same address. A
  // component can change position in the array. The type of the component can
  // change. A particular component may be missing in a later response.
  repeated AddressComponent address_components = 6;

  // Plus code of the place location lat/long.
  PlusCode plus_code = 7;

  // The position of this place.
  google.type.LatLng location = 8;

  // A viewport suitable for displaying the place on an average-sized map.
  google.geo.type.Viewport viewport = 9;

  // Information (including references) about photos of this place. A maximum of
  // 10 photos can be returned.
  repeated Photo photos = 10;

  // The place's address in adr microformat: http://microformats.org/wiki/adr.
  string adr_format_address = 11;

  // A set of data provider that must be shown with this result.
  repeated Attribution attributions = 12;

  // The structured components that form the formatted address, if this
  // information is available.
  message AddressComponent {
    // The full text description or name of the address component. For example,
    // an address component for the country Australia may have a long_name of
    // "Australia".
    string long_text = 1;

    // An abbreviated textual name for the address component, if available. For
    // example, an address component for the country of Australia may have a
    // short_name of "AU".
    string short_text = 2;

    // An array indicating the type(s) of the address component.
    repeated string types = 3;

    // The language used to format this components, in CLDR notation.
    string language_code = 4;
  }

  // Plus code (http://plus.codes) is a location reference with two formats:
  // global code defining a 14mx14m (1/8000th of a degree) or smaller rectangle,
  // and compound code, replacing the prefix with a reference location.
  message PlusCode {
    // Place's global (full) code, such as "9FWM33GV+HQ", representing an
    // 1/8000 by 1/8000 degree area (~14 by 14 meters).
    string global_code = 1;

    // Place's compound code, such as "33GV+HQ, Ramberg, Norway", containing
    // the suffix of the global code and replacing the prefix with a formatted
    // name of a reference entity.
    string compound_code = 2;
  }

  // Information about data providers of this place.
  message Attribution {
    // Name of the Place's data provider.
    string provider = 1;

    // URI to the Place's data provider.
    string provider_uri = 2;
  }
}

// Information about a photo of a place.
message Photo {
  // Identifier. A reference representing this place photo which may be used to
  // look up this place photo again (also called the API "resource" name:
  // `places/{place_id}/photos/{photo}`).
  string name = 1;

  // The maximum available width, in pixels.
  int32 width_px = 2;

  // The maximum available height, in pixels.
  int32 height_px = 3;

  // This photo's authors.
  repeated AuthorAttribution author_attributions = 4;
}

// Information about the author of the UGC data. Used in
// [Photo][google.maps.places.v1.Photo], and
// [Review][google.maps.places.v1.Review].
message AuthorAttribution {
  // Name of the author of the [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string display_name = 1;

  // URI of the author of the [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string uri = 2;

  // Profile photo URI of the author of the
  // [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string photo_uri = 3;
}
