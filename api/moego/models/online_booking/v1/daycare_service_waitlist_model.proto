// @gencoder.generated: api/moego/models/online_booking/v1/daycare_service_waitlist_model.proto

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// table: daycare_service_waitlist
// comment:
// indexes:
//   daycare_service_waitlist_pkey: (id)
message DaycareServiceWaitlistModel {
  // id
  int64 id = 1;
  // booking request id
  int64 booking_request_id = 2;
  // service detail id
  int64 service_detail_id = 3;
  // specific dates
  repeated google.type.Date specific_dates = 4;
  // created at
  google.protobuf.Timestamp created_at = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
}
