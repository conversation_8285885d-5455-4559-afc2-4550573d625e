// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob profile update condition
enum ProfileUpdateCondition {
  //  unspecified
  PROFILE_UPDATE_CONDITION_UNSPECIFIED = 0;
  // all
  PROFILE_UPDATE_CONDITION_ALL = 1;
  // without update
  PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob vaccine status condition
enum VaccineStatusCondition {
  //  unspecified
  VACCINE_STATUS_CONDITION_UNSPECIFIED = 0;
  // all
  VACCINE_STATUS_CONDITION_ALL = 1;
  // no missing or expired
  VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED = 2;
}
