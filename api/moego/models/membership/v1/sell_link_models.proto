// @since 2024-06-12 14:10:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The SellLink model
message SellLinkModel {
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // customer_id
  int64 customer_id = 3;
  // business id
  int64 business_id = 7;
  // membership id
  int64 membership_id = 4;
  // expires at
  google.protobuf.Timestamp expires_at = 5;
  // token, hidden in general
  optional string token = 6;

  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // operator staff id
  int64 operator_staff_id = 16;
}
