// @since 2024-06-12 14:10:20
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The SellLink Full Definition
message SellLinkCreateDef {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // membership id
  int64 membership_id = 2 [(validate.rules).int64 = {gt: 0}];
  // membership revision
  optional int32 membership_revision = 3 [(validate.rules).int32 = {gt: 0}];
}

// The SellLink Partial Definition
message SellLinkUpdateDef {}
