syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// appointment note type
enum AppointmentNoteType {
  // unspecified
  APPOINTMENT_NOTE_TYPE_UNSPECIFIED = 0;
  // Alert notes
  ALERT_NOTES = 1;
  // Ticket comments, Block description
  COMMENT = 2;
  // Cancel reason
  CANCEL = 3;
  // Booking request additional note
  ADDITIONAL = 4;
}
