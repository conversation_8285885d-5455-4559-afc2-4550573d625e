syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/latlng.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// appointment tracking
message AppointmentTracking {
  // appointment id
  int64 appointment_id = 1;
  // customer address
  Address customer_address = 2;
  // staff location status
  StaffLocationStatus staff_location_status = 3;
  // staff who share location, 0 means no staff share location
  int64 location_sharing_staff_id = 4;
  // device id of the staff who share location
  string location_sharing_device_id = 5;
  // the last time when staff location status is change to transit
  google.protobuf.Timestamp last_in_transit_at = 6;
  // estimated travel seconds from staff current location to customer location
  int64 estimated_travel_seconds = 7;
  // last estimate time for estimated_travel_seconds
  google.protobuf.Timestamp last_estimate_at = 8;
  // travel distance from staff current location to customer location
  int64 travel_distance = 9;
  // delay status from staff current location to customer location
  DelayedStatus delayed_status = 10;
  // estimated travel seconds from address of last in transit appointment to customer location
  int64 estimated_travel_seconds_from_last_in_transit = 11;
  // last estimate time for estimated_travel_seconds_from_last_in_transit
  google.protobuf.Timestamp from_last_in_transit_last_estimate_at = 12;
  // last in transit appointment of the staff
  int64 last_in_transit_appointment_id = 13;
  // travel distance from address of last in transit appointment to customer location
  int64 travel_distance_from_last_in_transit = 14;
  // delay status from address of last in transit appointment to customer location
  DelayedStatus delayed_status_from_last_in_transit = 15;
  // staff address, only available when staff location is sharing.
  // The corresponding StaffLocationStatus is IN_TRANSIT.
  optional Address staff_address = 16;
}

// appointment tracking view
message AppointmentTrackingView {
  // appointment id
  int64 appointment_id = 1;
  // customer address
  Address customer_address = 2;
  // staff location status
  StaffLocationStatus staff_location_status = 3;
  // staff who share location, 0 means no staff share location
  int64 location_sharing_staff_id = 4;
  // device id of the staff who share location
  string location_sharing_device_id = 5;
  // the last time when staff location status is change to transit
  google.protobuf.Timestamp last_in_transit_at = 6;
  // estimated travel seconds from staff current location to customer location
  int64 estimated_travel_seconds = 7;
  // last estimate time for estimated_travel_seconds
  google.protobuf.Timestamp last_estimate_at = 8;
  // travel distance from staff current location to customer location
  int64 travel_distance = 9;
  // delay status from staff current location to customer location
  DelayedStatus delayed_status = 10;
  // estimated travel seconds from address of last in transit appointment to customer location
  int64 estimated_travel_seconds_from_last_in_transit = 11;
  // last estimate time for estimated_travel_seconds_from_last_in_transit
  google.protobuf.Timestamp from_last_in_transit_last_estimate_at = 12;
  // last in transit appointment of the staff
  int64 last_in_transit_appointment_id = 13;
  // travel distance from address of last in transit appointment to customer location
  int64 travel_distance_from_last_in_transit = 14;
  // delay status from address of last in transit appointment to customer location
  DelayedStatus delayed_status_from_last_in_transit = 15;
  // staff address, only available when staff location is sharing.
  // The corresponding StaffLocationStatus is IN_TRANSIT.
  optional Address staff_address = 16;
}

// LocationStatus represents the status of an employee's location.
enum StaffLocationStatus {
  // Default value, unspecified status.
  STAFF_LOCATION_STATUS_UNSPECIFIED = 0;
  // The employee has not started the journey.
  NOT_STARTED = 1;
  // The employee is on the way.
  IN_TRANSIT = 2;
  // The employee has stopped sharing their location.
  SHARING_STOPPED = 3;
  // The employee has reached the destination.
  ARRIVED = 4;
}

// delay status
enum DelayedStatus {
  // Default value, unspecified status.
  DELAYED_STATUS_UNSPECIFIED = 0;
  // staff will be arrived on time.
  ON_TIME = 1;
  // staff will de late slightly.
  DELAYED_SLIGHTLY = 2;
  // staff will be late seriously.
  DELAYED_SERIOUSLY = 3;
}

// address
message Address {
  // address 1
  string address1 = 1;
  // address 2
  string address2 = 2;
  // country
  string country = 3;
  // city
  string city = 4;
  // state
  string state = 5;
  // zipcode
  string zipcode = 6;
  // coordinate, include latitude and longitude
  optional google.type.LatLng coordinate = 7;
}

// update appointment tracking definition
message UpdateAppointmentTrackingDef {
  // staff who share location, 0 means no staff share location
  optional int64 location_sharing_staff_id = 1;
  // staff location status
  optional StaffLocationStatus staff_location_status = 2;
  // staff address, only available when staff location is sharing.
  // The corresponding StaffLocationStatus is IN_TRANSIT.
  optional Address staff_address = 3;
  // device id
  optional string location_sharing_device_id = 4;
}
