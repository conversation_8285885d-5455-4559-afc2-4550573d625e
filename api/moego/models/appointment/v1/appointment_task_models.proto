syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_task_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// The model of appointment task
message AppointmentTaskModel {
  // id
  int64 id = 1;
  // The category of task
  models.appointment.v1.AppointmentTaskCategory task_category = 2;
  // The instruction
  string instruction = 3;
  // The appointment id
  int64 appointment_id = 4;
  // The service id
  int64 service_id = 5;
  // The pet id
  int64 pet_id = 6;
  // The lodging id
  int64 lodging_id = 7;
  // The staff id
  int64 staff_id = 8;
  // The start date
  google.type.Date start_date = 9;
  // The start time, the minutes of the day
  optional int32 start_time = 10;
  // The start time label
  optional string time_label = 11;
  // The duration
  optional int32 duration = 12;
  // The status of task
  models.appointment.v1.AppointmentTaskStatus status = 13;
  // The note status
  optional string note_status = 14;
  // The note content
  optional string note_content = 15;
  // The created at
  google.protobuf.Timestamp created_at = 16;
  // The updated at
  google.protobuf.Timestamp updated_at = 17;
  // The deleted at
  optional google.protobuf.Timestamp deleted_at = 18;
  // The company id
  int64 company_id = 19;
  // The business id
  int64 business_id = 20;
  // The main service care type
  models.offering.v1.ServiceItemType care_type = 21;
  // The add-on id
  int64 add_on_id = 22;
}

// The view of appointment task
message AppointmentTaskView {
  // id
  int64 id = 1;
  // The category of task
  models.appointment.v1.AppointmentTaskCategory task_category = 2;
  // The instruction
  string instruction = 3;
  // The appointment id
  int64 appointment_id = 4;
  // The service id
  int64 service_id = 5;
  // The pet id
  int64 pet_id = 6;
  // The lodging id
  int64 lodging_id = 7;
  // The staff id
  int64 staff_id = 8;
  // The start date
  google.type.Date start_date = 9;
  // The start time, the minutes of the day
  optional int32 start_time = 10;
  // The start time label
  optional string time_label = 11;
  // The duration
  optional int32 duration = 12;
  // The status of task
  models.appointment.v1.AppointmentTaskStatus status = 13;
  // The note status
  optional string note_status = 14;
  // The note content
  optional string note_content = 15;
  // The main service care type
  models.offering.v1.ServiceItemType care_type = 21;
}
