syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account model
message AccountImpersonateLogModel {
  // log id
  int64 id = 1;

  // impersonator
  string impersonator = 2;

  // target account id
  int64 target_account_id = 3;

  // source
  string source = 4;

  // session id
  int64 session_id = 5;

  // impersonate at
  google.protobuf.Timestamp impersonate_at = 6;
}
