syntax = "proto3";

package moego.models.order.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// food source id list
message FoodSourceDef {
  // food source ids
  repeated int64 food_source_ids = 1 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // is all food source
  optional bool is_all_food_source = 2;
}
