syntax = "proto3";

package moego.models.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_line_discount_models.proto";
import "moego/models/order/v1/order_line_extra_fee_models.proto";
import "moego/models/order/v1/order_line_item_models.proto";
import "moego/models/order/v1/order_line_tax_models.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/order_promotion_models.proto";
import "moego/models/order/v1/refund_order_models.proto";
import "moego/models/order/v1/split_tips_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * Order detail with line items, discounts, taxes, extra fees
 */
message OrderDetailModel {
  // order info
  OrderModel order = 1;
  // order line items
  repeated OrderLineItemModel line_items = 2;
  // order line discounts
  repeated OrderLineDiscountModel line_discounts = 3;
  // order line taxes
  repeated OrderLineTaxModel line_taxes = 4;
  // order line extra fee
  repeated OrderLineExtraFeeModel line_extra_fees = 5;
}

// OrderDetailModeV1 适用于支持 RefundByItem 之后的订单。
// 结构与 RefundOrderDetail 基本对齐。
message OrderDetailModelV1 {
  // Order.
  OrderModelV1 order = 1;
  // OrderItems
  repeated OrderItemModel order_items = 2;

  // Tips 的分配情况.
  repeated CustomizedTipConfig tips_detail = 3;
  // Payments.
  repeated OrderPaymentModel order_payments = 4;
  // Refund Payments.
  repeated RefundOrderPaymentModel refund_order_payments = 5;

  // Order discounts.
  // 仅包含订单纬度的 Discount (Item 的已经在 Item 中).
  repeated OrderLineDiscountModelV1 order_discounts = 6;
  // order promotions
  // 订单纬度所有的优惠都在这里(包括 Discount,Package,Membership,Store Credit,ETC.)
  repeated OrderPromotionModel order_promotions = 7;
}

// Order detail view.
message OrderDetailView {
  // 订单详情.
  OrderDetailModelV1 order_detail = 1;
  // 关联的宠物的基本信息.
  repeated PetBrief pet_briefs = 2;
  // 关联的 Staff 的基本信息.
  repeated StaffBrief staff_briefs = 3;
  // 按 Order item 的 Type 聚合之后的，Item.TotalAmount 的和（即折后税前价）
  map<string, google.type.Money> subtotal_by_item_type = 4;
  // 按 Tax ID 纬度统计聚合之后的 Tax Amount，不是 Item Subtotal.
  repeated Tax subtotal_by_tax = 5;
  // 按照 Order Item 的 Type 聚合之后的，Item.Subtotal 的和（即折前税前价）
  map<string, google.type.Money> pre_discount_subtotal_by_item_type = 6;

  // 基础的 Staff 信息.
  message StaffBrief {
    // Staff ID.
    int64 id = 1;
    // Staff first name.
    string first_name = 2;
    // Staff last name.
    string last_name = 3;
  }

  // 基础的宠物信息
  message PetBrief {
    // Pet ID.
    int64 id = 1;
    // Pet name.
    string name = 2;
    // Pet breed.
    string breed = 3;
  }

  // Tax.
  message Tax {
    // Tax ID.
    int64 id = 1;
    // Tax name.
    string name = 2;
    // Tax rate. 仅用作记录，不参与计算.
    google.type.Decimal rate = 3;
    // Tax amount.
    google.type.Money amount = 4;
  }
}

// Refund order detail view.
message RefundOrderDetailView {
  // Refund order detail.
  RefundOrderDetailModel refund_order_detail = 1;
  // 关联的宠物的基本信息.
  repeated OrderDetailView.PetBrief pet_briefs = 2;
  // 关联的 Staff 的基本信息.
  repeated OrderDetailView.StaffBrief staff_briefs = 3;
  // 按 Refund order item 的 Type 聚合之后的 Subtotal.
  map<string, google.type.Money> subtotal_by_item_type = 4;
  // 按 Tax ID 纬度统计聚合之后的结果.
  repeated OrderDetailView.Tax subtotal_by_tax = 5;
}
