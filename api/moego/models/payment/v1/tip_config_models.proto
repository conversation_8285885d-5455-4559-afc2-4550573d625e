syntax = "proto3";

package moego.models.payment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/payment/v1/tip_config_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// smart tip config model
message TipConfigModel {
  // tip id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // is enable
  bool is_enable = 3;
  // customized amount for splitting different tips config
  double threshold = 4;
  // lower tip type
  TipPriceType lower_tip_type = 5;
  // lower preferred tip type
  PreferredTipType lower_preferred_tip = 6;
  // lower tip price config
  TipPriceModel lower_tip = 7;
  // upper tip type
  TipPriceType upper_tip_type = 8;
  // upper preferred tip type
  PreferredTipType upper_preferred_tip = 9;
  // upper tip price config
  TipPriceModel upper_tip = 10;
  // create time
  google.protobuf.Timestamp create_time = 11;
  // update time
  google.protobuf.Timestamp update_time = 12;
}

// tip price model
message TipPriceModel {
  // percentage config
  TipPriceLevelModel percentage_config = 1;
  // amount config
  TipPriceLevelModel amount_config = 2;
}

// tip price model
message TipPriceLevelModel {
  // low price
  double low = 1;
  // medium price
  double medium = 2;
  // high price
  double high = 3;
}

// smart tip config model in c app client view
message TipConfigModelClientView {
  // is enable
  bool is_enable = 3;
  // customized amount for splitting different tips config
  double threshold = 4;
  // lower tip type
  TipPriceType lower_tip_type = 5;
  // lower preferred tip type
  PreferredTipType lower_preferred_tip = 6;
  // lower tip price config
  TipPriceModel lower_tip = 7;
  // upper tip type
  TipPriceType upper_tip_type = 8;
  // upper preferred tip type
  PreferredTipType upper_preferred_tip = 9;
  // upper tip price config
  TipPriceModel upper_tip = 10;
}
