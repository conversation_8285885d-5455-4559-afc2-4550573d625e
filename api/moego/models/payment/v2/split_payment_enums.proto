syntax = "proto3";

package moego.models.payment.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// SplitSyncRecordStatus 分账同步单据状态
enum SplitSyncRecordStatus {
  // Unspecified
  SPLIT_SYNC_RECORD_STATUS_UNSPECIFIED = 0;
  // created 初始化
  CREATED = 1;
  // SUCCEEDED，分账同步成功，这是最终状态
  SUCCEEDED = 2;
  // Cancelled，分账同步取消，这是最终状态
  CANCELLED = 3;
  // Failed，分账同步失败，这是最终状态
  FAILED = 4;
}

// SplitSyncMode 分账同步模式
enum SplitSyncMode {
  // Unspecified
  SPLIT_SYNC_MODE_UNSPECIFIED = 0;
  // SYNC 同步执行，只关注分账受理结果，不关心分账最终结果
  SYNC = 1;
  // ASYNC 异步执行，既关注受理结果，也关注分账最终结果
  ASYNC = 2;
}

// SplitSyncRecordType 分账同步单据类型
enum SplitSyncRecordType {
  // Unspecified
  SPLIT_SYNC_RECORD_TYPE_UNSPECIFIED = 0;
  // Payment
  PAYMENT = 1;
  // Refund
  REFUND = 2;
}
