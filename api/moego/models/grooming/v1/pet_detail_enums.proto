syntax = "proto3";

package moego.models.grooming.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// work mode
enum WorkMode {
  // parallel
  WORK_MODE_PARALLEL = 0;
  // sequence
  WORK_MODE_SEQUENCE = 1;
}

// pet detail status
enum PetDetailStatus {
  // unspecified
  PET_DETAIL_STATUS_UNSPECIFIED = 0;
  // normal
  PET_DETAIL_STATUS_NORMAL = 1;
  // deleted
  PET_DETAIL_STATUS_DELETED = 2;
  // deletion caused by modification
  PET_DE<PERSON>IL_STATUS_MODIFIED = 3;
}
