// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.business.v1;

import "google/type/latlng.proto";
import "moego/models/business/v1/business_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business.v1";

// BusinessModel
message BusinessModel {
  // basic info
  // business id
  int64 id = 1;
  // business name
  string business_name = 2;
  // business avatar
  string avatar_path = 3;
  // business phone number
  string phone_number = 4;
  // business owner email
  string owner_email = 5;

  reserved 6 to 19;

  // preference info
  // currency code
  string currency_code = 20;
  // currency symbol
  string currency_symbol = 21;
  // calendar format
  string calendar_format = 22;
  // calendar format type
  int32 calendar_format_type = 23;
  // date format
  string date_format = 24;
  // date format type
  int32 date_format_type = 25;
  // time format
  string time_format = 26;
  // time format type
  int32 time_format_type = 27;
  // timezone name
  string timezone_name = 28;
  // number format
  string number_format = 29;
  // number format type
  int32 number_format_type = 30;
  // unit of weight
  string unit_of_weight = 31;
  // unit of weight type
  int32 unit_of_weight_type = 32;
  reserved 33 to 49;

  // business mode, 0-Mobile, 1-Salon
  int32 business_mode = 50;

  // private fields starts from 51
  // business address
  string address = 51;
  // company id
  int64 company_id = 52;
  // business app type
  BusinessAppType app_type = 53;

  // address1
  string address1 = 54;
  // address2
  string address2 = 55;
  // city
  string address_city = 56;
  // state
  string address_state = 57;
  // zipcode
  string address_zipcode = 58;
  // country
  string address_country = 59;
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 60;
  // unit of distance type
  int32 unit_of_distance_type = 61;
  // ISO 3166-1 alpha-2 country code
  string country_alpha2_code = 62;
}

// business base view
message BusinessModelBasicView {
  // basic info
  // business id
  int64 id = 1;
  // business name
  string business_name = 2;
  // business avatar
  string avatar_path = 3;

  reserved 4 to 19;
}

// business base info and preference
message BusinessModelPublicView {
  // basic info
  // business id
  int64 id = 1;
  // business name
  string business_name = 2;
  // business avatar
  string avatar_path = 3;

  reserved 4 to 19;

  // preference info
  // currency code
  string currency_code = 20;
  // currency symbol
  string currency_symbol = 21;
  // calendar format
  string calendar_format = 22;
  // calendar format type
  int32 calendar_format_type = 23;
  // date format
  string date_format = 24;
  // date format type
  int32 date_format_type = 25;
  // time format
  string time_format = 26;
  // time format type
  int32 time_format_type = 27;
  // timezone name
  string timezone_name = 28;
  // number format
  string number_format = 29;
  // number format type
  int32 number_format_type = 30;
  // unit of weight
  string unit_of_weight = 31;
  // unit of weight type
  int32 unit_of_weight_type = 32;

  reserved 33 to 49;
}

// business mode in c app appt list view
message BusinessModelClientListView {
  // business id
  int64 id = 1;
  // business name
  string business_name = 2;
  // business avatar path
  string avatar_path = 3;
  // business app type
  BusinessAppType app_type = 4;
}

// business mode in c app appt detail view
message BusinessModelClientView {
  // business id
  int64 id = 1;
  // business name
  string business_name = 2;
  // business avatar path
  string avatar_path = 3;
  // business phone number
  string phone_number = 4;
  // address1
  string address1 = 5;
  // address2
  string address2 = 6;
  // city
  string address_city = 7;
  // state
  string address_state = 8;
  // zipcode
  string address_zipcode = 9;
  // country
  string address_country = 10;
  // business app type
  BusinessAppType app_type = 11;
  // primary pay type
  BusinessPayType primary_pay_type = 12;
  // coordinate, include latitude and longitude
  google.type.LatLng coordinate = 13;
  // currency code
  string currency_code = 14;
  // currency symbol
  string currency_symbol = 15;
  // ISO 3166-1 alpha-2 country code
  string country_alpha2_code = 16;
}
