// @since 2024-12-24 16:54:45
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The ServiceBundleSaleMapping model
message ServiceBundleSaleMappingModel {
  // the unique id
  int64 id = 1;

  // company id
  int64 company_id = 2;

  // service id
  int64 service_id = 3;

  // bundle service id
  int64 bundle_service_id = 4;

  // the create time
  google.protobuf.Timestamp created_at = 5;
}
