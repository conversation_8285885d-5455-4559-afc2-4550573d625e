syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// category
message CategoryModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // service item type
  ServiceItemType service_item_type = 3;
  // sort
  int32 sort = 4;
  // service type
  ServiceType service_type = 5;
}
