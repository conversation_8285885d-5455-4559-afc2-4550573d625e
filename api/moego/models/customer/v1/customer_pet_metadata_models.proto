syntax = "proto3";

package moego.models.customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// pet metadata category
enum PetMetadataCategory {
  // unspecified
  PET_METADATA_CATEGORY_UNSPECIFIED = 0;
  // pet type
  PET_METADATA_CATEGORY_PET_TYPE = 1;
  // hair length
  PET_METADATA_CATEGORY_HAIR_LENGTH = 2;
  // behavior
  PET_METADATA_CATEGORY_BEHAVIOR = 3;
  // fixed
  PET_METADATA_CATEGORY_FIXED = 4;
  // vaccine
  PET_METADATA_CATEGORY_VACCINE = 5;
  // weight unit
  PET_METADATA_CATEGORY_WEIGHT_UNIT = 6;
}

// pet metadata model
message PetMetadataModel {
  // id
  int32 id = 1;
  // category
  PetMetadataCategory category = 2;
  // name
  string name = 3;
  // sort
  int32 sort = 4;
  // is enable
  bool is_enable = 5;
}
