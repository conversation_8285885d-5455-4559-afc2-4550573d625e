syntax = "proto3";

package moego.models.message.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// the system supported message type
// see customer detail -> Message/Email preference
enum MessageType {
  // unspecified
  MESSAGE_TYPE_UNSPECIFIED = 0;
  // sms
  MESSAGE_TYPE_SMS = 1;
  // email
  MESSAGE_TYPE_EMAIL = 2;
  // call
  MESSAGE_TYPE_CALL = 3;
  // app
  MESSAGE_TYPE_APP = 4;
}

// message type list
message MessageTypeList {
  // message type list
  repeated MessageType message_types = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// target type
// see moego-server-api/src/main/java/com/moego/server/message/enums/MessageTargetTypeEnums.java
enum TargetType {
  // unspecified
  TARGET_TYPE_UNSPECIFIED = 0;
  // thread id
  TARGET_TYPE_THREAD = 1;
  // batch id
  TARGET_TYPE_BATCH = 2;
  // pick up
  TARGET_TYPE_AUTO_PICKUP = 3;
  // auto agreement
  TARGET_TYPE_AUTO_AGREEMENT = 4;
  // agreement
  TARGET_TYPE_AGREEMENT = 5;
  // review
  TARGET_TYPE_REVIEW = 6;
  // review booster reply
  TARGET_TYPE_REVIEW_REPLY = 7;
  // review booster appointment
  TARGET_TYPE_REMINDER_APPOINTMENT_FIRST = 8;
  // review booster appointment
  TARGET_TYPE_REMINDER_APPOINTMENT_SECOND = 9;
  // review booster appointment
  TARGET_TYPE_REMINDER_APPOINTMENT_REMIND = 10;
  // review booster birthday
  TARGET_TYPE_REMINDER_PET_BIRTHDAY = 11;
  // review booster rebook
  TARGET_TYPE_REMINDER_REBOOK = 12;
  // auto appointment book
  TARGET_TYPE_AUTO_APPOINTMENT_BOOK = 13;
  // auto appointment rebook
  TARGET_TYPE_AUTO_APPOINTMENT_RESCHEDULED = 14;
  // auto appointment rebook
  TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED = 15;
  // forget password
  TARGET_TYPE_FORGET_PASSWORD = 16;
  // business daily
  TARGET_TYPE_BUSINESS_DAILY = 17;
  // verification code
  TARGET_TYPE_VERIFICATION_CODE = 18;
  // ob send to business email
  TARGET_TYPE_OB_BUSINESS_EMAIL = 19;
  // ob send to client email
  TARGET_TYPE_OB_CLIENT_EMAIL = 20;
  // ob send to client message
  TARGET_TYPE_OB_CLIENT_MESSAGE = 21;
  // mobile calendar push reminder
  TARGET_TYPE_CALENDAR_REMINDER = 22;
  // auto appointment confirmed by client
  TARGET_TYPE_AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT = 23;
  // auto appointment cancelled by client
  TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED_BY_CLIENT = 24;
  // auto receipt
  TARGET_TYPE_AUTO_RECEIPT = 25;
  // pay online
  TARGET_TYPE_PAY_ONLINE = 26;
  // cof link
  TARGET_TYPE_COF_LINK = 27;
  // auto appointment moved to wait list
  TARGET_TYPE_AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST = 28;
  // grooming report
  TARGET_TYPE_GROOMING_REPORT = 29;
  // abandoned schedule message
  TARGET_TYPE_ABANDONED_SCHEDULE_MESSAGE = 30;
  // daily report
  TARGET_TYPE_DAILY_REPORT = 31;
}

// sender type
enum SenderType {
  // unspecified
  SENDER_TYPE_UNSPECIFIED = 0;
  // by business
  SENDER_TYPE_BUSINESS = 1;
  // bt customer
  SENDER_TYPE_CUSTOMER = 2;
}

// business settings auto message type
enum AutoMessageType {
  // unspecified
  AUTO_MESSAGE_TYPE_UNSPECIFIED = 0;
  // When new appt is booked
  APPT_BOOKED = 1;
  // When appt is rescheduled
  APPT_RESCHEDULED = 2;
  // When appt is cancelled
  APPT_CANCELLED = 3;
  // Ready for pick up
  READY_FOR_PICK_UP = 4;
  // Send ETA
  SEND_ETA = 5;
  // When appt is confirmed by client
  APPT_CONFIRMED_BY_CLIENT = 6;
  // When appt is cancelled by client
  APPT_CANCELLED_BY_CLIENT = 7;
  // Send receipt when a ticket is fully paid
  SEND_FULLY_PAID_RECEIPT = 8;
  // Send invoice to pay online
  PAY_ONLINE = 9;
  // When appt moved to waiting list
  APPT_MOVED_TO_WAITLIST = 10;
}

// schedule message status
enum ScheduleMessageStatus {
  // unspecified
  SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED = 0;
  // scheduled
  SCHEDULED = 1;
  // sent successfully
  SENT_SUCCESSFULLY = 2;
  // sent failed
  SENT_FAILED = 3;
  // deleted
  DELETED = 4;
  // prepared
  PREPARED = 5;
}

// the message send out method
// column of mysql.moe_message.moe_business_message_detail
enum Method {
  // unspecified
  METHOD_UNSPECIFIED = 0;
  // sms
  METHOD_SMS = 1;
  // email
  METHOD_EMAIL = 2;
  // reserved 3
  reserved 3;
  // call
  METHOD_CALL = 4;
  // app
  METHOD_APP = 5;
}
