syntax = "proto3";

package moego.models.message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// twilio number assign status
enum TwilioNumberAssignStatus {
  // unspecified
  TWILIO_NUMBER_ASSIGN_STATUS_UNSPECIFIED = 0;
  // assign finished
  TWILIO_NUMBER_ASSIGN_STATUS_FINISHED = 1;
  // wait assign
  TWILIO_NUMBER_ASSIGN_STATUS_WAIT_ASSIGN = 2;
  // assign phone
  TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_MOBILE = 3;
  // assign sub account
  TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_SUB_ACCOUNT = 4;
  // failure
  TWILIO_NUMBER_ASSIGN_STATUS_BINDING_FAILURE = 9;
}

// twilio number use status
enum TwilioNumberUseStatus {
  // unspecified
  TWILIO_NUMBER_USE_STATUS_UNSPECIFIED = 0;
  // using
  TWILIO_NUMBER_USE_STATUS_USING = 1;
  // unused
  TWILIO_NUMBER_USE_STATUS_UNUSED = 2;
  // closed
  TWILIO_NUMBER_USE_STATUS_CLOSED = 3;
}
