syntax = "proto3";

package moego.models.open_platform.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/open_platform/v1;openplatformpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.open_platform.v1";

// Client represents a client that can access the Open API.
message Client {
  // Type represents the type of client.
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // company internal
    INTERNAL = 1;
    // MoeGo public
    PUBLIC = 2;
    // from third-party
    THIRD_PARTY = 3;
  }
  // Status represents the status of a client.
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
    // abandoned
    ABANDONED = 3;
    // deleted
    DELETED = 4;
  }
  // ID represents the unique identifier of a client.
  string id = 1;
  // Secret represents the secret of a client.
  string secret = 2;
  // Redirect<PERSON>I represents the redirect URI of a client.
  string redirect_uri = 3;
  // Name represents the name of a client.
  string name = 4;
  // Description represents the description of a client.
  string description = 5;
  // Type represents the type of a client.
  Type type = 6;
  // Status represents the status of a client.
  Status status = 7;
  // Scopes represents the scopes of a client.
  repeated string scopes = 8;
}

// User represents a user that can access the Open API.
message User {
  // Type represents the type of a user.
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // enterprise user representing a enterprise, for enterprise-level authorization
    ENTERPRISE = 1;
    // client user representing a client of a company, for client-level authorization
    CLIENT = 2;
    // business user representing a staff in a client of a company, for user-level authorization
    STAFF = 3;
    // oauth user representing a user from third-party oauth, for third provider account binding
    OAUTH_USER = 4;
  }

  // id represents the Open ID of a user
  string id = 1;
  // type represents the type of a user.
  Type type = 2;
  // account_id represents the account ID of a user.
  string account_id = 3;
  // client_id represents the client ID of a user.
  string client_id = 4;
  // name represents the name of a user.
  string name = 5;
  // email represents the email of a user.
  string email = 6;
  // phone represents the phone of a user.
  string phone = 7;
  // avatar represents the avatar of a user.
  string avatar = 8;
  // identity represents the identity of a user.
  oneof identity {
    // staff_identity represents the staff identity of a user.
    StaffIdentity staff_identity = 9;
    // admin_identity represents the admin identity of a user.
    AdminIdentity admin_identity = 10;
  }
}

// StaffIdentity represents the identity of a staff.
message StaffIdentity {
  // enterprise_id represents the enterprise ID of a user.
  string enterprise_id = 1;
  // staff_id represents the staff ID of a user.
  string staff_id = 2;
}

// AdminIdentity represents the identity of an admin.
message AdminIdentity {}

// APIKey is the representation of a key that can be used to access a resource.
message APIKey {
  // Unique id in UUID4 format
  string id = 1;
  // The name of API key
  string name = 2;
  // The Secret Key string
  string secret = 3;
  // Key restrictions
  Restrictions restrictions = 4;
  // Key created timestamp
  google.protobuf.Timestamp created_time = 5;
  // Key updated timestamp
  google.protobuf.Timestamp updated_time = 6;
  // Key expired timestamp
  google.protobuf.Timestamp expired_time = 7;
}

// Restrictions are restrictions that are applied to a key.
message Restrictions {
  // The scopes that are allowed to be requested by the key.
  repeated string scopes = 1;
  // The client-side restrictions that are allowed to use the key.
  // You can specify only one type of client restrictions per key.
  oneof client_restrictions {
    // The IP addresses of callers that are allowed to use the key.
    ServerRestrictions server_restrictions = 2;
    // The organizations that are allowed to be requested by the key.
    OrganizationRestrictions organization_restrictions = 3;
  }
}

// ServerRestrictions
message ServerRestrictions {
  // The IP address (IPv4 or IPv6) of the client that issued the HTTP
  // request. Examples: `"***********"`, `"FE80::0202:B3FF:FE1E:8329"`.
  repeated string allowed_ips = 1 [(validate.rules).repeated = {
    items: {
      string: {ip: true}
    }
  }];
}

// OrganizationRestrictions
message OrganizationRestrictions {
  // The organizations that are allowed to be requested by the key.
  repeated Organization organizations = 1;
}

// Organization represents a MoeGo organization.
message Organization {
  // Type
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // business
    BUSINESS = 1;
    // company
    COMPANY = 2;
    // enterprise
    ENTERPRISE = 3;
  }
  // ID represents the unique identifier of an organization.
  string id = 1;
  // Type represents the type of organization.
  Type type = 2;
}
