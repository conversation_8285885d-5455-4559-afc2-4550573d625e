syntax = "proto3";

package moego.models.notification.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/notification/v1/notification_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// the notification template model
message NotificationTemplateModel {
  // setting id
  int64 id = 1;
  // source
  NotificationSource source = 2;
  // company id
  int64 company_id = 3;
  // business id
  int64 business_id = 4;
  // notification type
  NotificationType type = 5;
  // title
  string title = 6;
  // content
  string content = 7;
  // is enabled
  bool is_enabled = 8;
  // created by
  int64 created_by = 9;
  // updated by
  int64 updated_by = 10;
  // created at
  google.protobuf.Timestamp created_at = 11;
  // updated at
  google.protobuf.Timestamp updated_at = 12;
}
