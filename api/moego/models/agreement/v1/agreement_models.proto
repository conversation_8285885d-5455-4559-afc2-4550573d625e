syntax = "proto3";

package moego.models.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/utils/v1/status_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.agreement.v1";

// AgreementModel
message AgreementModel {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // creator id
  int64 creator_id = 3;
  // status: normal, deleted
  moego.utils.v1.Status status = 4;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 5;
  // apply for services type, see the enum definition in ServiceType
  // notes: service_types is a combined value, For example:
  //   if you want to apply the agreement to grooming and boarding services,
  //   the service_types value is 3, logical OR result of enumeration values for grooming(1) and boarding(2)
  // include ob available
  int32 service_types = 6;
  // agreement title
  string agreement_title = 7;
  // agreement content
  string agreement_content = 8;
  // sms template
  string sms_template = 9;
  // email template title
  string email_template_title = 10;
  // email template body
  string email_template_body = 11;
  // last requested time: exceed this time will need to be re-signed
  int64 last_required_time = 12;
  // last edit time: milliseconds
  int64 last_edit_time = 13;
  // create time: milliseconds
  int64 create_time = 14;
  // update time: milliseconds
  int64 update_time = 15;
  // company_id id
  int64 company_id = 16;
  // source
  Source source = 17;
}

// simplified version of AgreementModel for return list
message AgreementModelSimpleView {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // creator id
  int64 creator_id = 3;
  // status: normal, deleted
  moego.utils.v1.Status status = 4;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 5;
  // apply for services type, see the enum definition in ServiceType
  // notes: service_types is a combined value, For example:
  //   if you want to apply the agreement to grooming and boarding services,
  //   the service_types value is 3, logical OR result of enumeration values for grooming(1) and boarding(2)
  int32 service_types = 6;
  // agreement title
  string agreement_title = 7;
  // last requested time: exceed this time will need to be re-signed
  int64 last_required_time = 8;
  // last edit time: milliseconds
  int64 last_edit_time = 9;
  // create time: milliseconds
  int64 create_time = 10;
  // update time: milliseconds
  int64 update_time = 11;
  // source
  Source source = 12;
}

// simplified version of AgreementModel for return list
message AgreementModelContentView {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // creator id
  int64 creator_id = 3;
  // status: normal, deleted
  moego.utils.v1.Status status = 4;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 5;
  // apply for services type, see the enum definition in ServiceType
  // notes: service_types is a combined value, For example:
  //   if you want to apply the agreement to grooming and boarding services,
  //   the service_types value is 3, logical OR result of enumeration values for grooming(1) and boarding(2)
  int32 service_types = 6;
  // agreement title
  string agreement_title = 7;
  // agreement content
  string agreement_content = 8;
  // last requested time: exceed this time will need to be re-signed
  int64 last_required_time = 9;
  // last edit time: milliseconds
  int64 last_edit_time = 10;
  // create time: milliseconds
  int64 create_time = 11;
  // update time: milliseconds
  int64 update_time = 12;
  // source
  Source source = 13;
}

// simplified version of AgreementModel with whether need to sign for return list
message AgreementSignStatusView {
  // agreement id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 4;
  // services type, see the enum definition in ServiceType
  int32 service_types = 5;
  // agreement title
  string agreement_title = 6;
  // last requested time: exceed this time will need to be re-signed
  int64 last_required_time = 7;
  // whether need to sign
  bool is_need_to_sign = 8;
}

// agreement model for c app view
message AgreementModelClientView {
  // agreement id
  int64 id = 1;
  // agreement title
  string agreement_title = 2;
  // agreement content
  string agreement_content = 3;
}
