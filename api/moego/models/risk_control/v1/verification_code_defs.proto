syntax = "proto3";

package moego.models.risk_control.v1;

import "moego/models/risk_control/v1/verification_code_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1;riskcontrolpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.risk_control.v1";

// verification code definition
message VerificationIdentifierDef {
  // account identifier
  oneof identifier {
    option (validate.required) = true;
    // phone number
    string phone_number = 1 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
    // email
    string email = 2 [(validate.rules).string = {email: true}];
  }
  // scenario
  moego.models.risk_control.v1.VerificationCodeScenario scenario = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // method
  moego.models.risk_control.v1.VerificationCodeMethod method = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// verification code definition
message VerificationCodeDef {
  // account identifier
  VerificationIdentifierDef identifier = 1;
  // verification token
  string token = 2;
  // verification code
  string code = 3;
}
