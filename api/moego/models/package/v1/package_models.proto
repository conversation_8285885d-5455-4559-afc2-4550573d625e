// @since 2025-03-15 21:57:06
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.models.package.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/package/v1;packagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pkg.v1";

// The Package model
message PackageModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // description
  string description = 3;
  // price
  double price = 4;
  // total value
  double total_value = 5;
  // tax id
  int64 tax_id = 6;
  // sold quantity
  int32 sold_quantity = 7;
  // create time
  google.protobuf.Timestamp create_time = 8;
  // update time
  google.protobuf.Timestamp update_time = 9;
  // is active
  bool is_active = 10;
  // enable online booking
  bool enable_online_booking = 11;
  // expiration days
  optional int32 expiration_days = 12;
  // items
  repeated Item items = 13;
  // company id
  optional int64 company_id = 14;
  // business id
  optional int64 business_id = 15;
}

// item
message Item {
  // quantity
  int32 quantity = 1;
  // services
  repeated int64 service_ids = 2;
}
