syntax = "proto3";

package moego.models.package.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/package/v1;packagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pkg.v1";

// The customer package model, indicate the package sold to the customer
message CustomerPackageModel {
  // id
  int64 id = 1;
  // package id
  int64 package_id = 2;
  // customer id
  int64 customer_id = 3;
  // package name
  string package_name = 4;
  // package description
  string package_description = 5;
  // package price
  double package_price = 6;
  // purchase time
  google.protobuf.Timestamp purchase_time = 7;
  // start time, the time when the package is valid
  google.protobuf.Timestamp start_time = 8;
  // end time, the time when the package is expired
  google.protobuf.Timestamp end_time = 9;
  // items
  repeated RemainingItem remaining_items = 10;
  // business id
  int64 business_id = 11;
}

// remaining items
message RemainingItem {
  // services
  repeated int64 service_ids = 1;
  // quantity
  int32 total_quantity = 2;
  // remaining quantity
  int32 remaining_quantity = 3;
}
