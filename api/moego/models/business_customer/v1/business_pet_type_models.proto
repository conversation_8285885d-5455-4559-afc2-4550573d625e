syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet type model
// Pet type can not be deleted, only can be set to unavailable.
message BusinessPetTypeModel {
  // id, primary key of pet type record in database
  int64 id = 1;

  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 2;

  // pet type name, e.g. Dog, Cat, etc.
  string name = 3;

  // if the pet type is available
  bool is_available = 4;

  // pet type sort. The larger the sort number, the higher the priority.
  int32 sort = 5;
}
