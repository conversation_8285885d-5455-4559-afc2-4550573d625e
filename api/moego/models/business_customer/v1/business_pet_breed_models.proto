syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet breed model
// `sort` is never used in pet breed model, because pet breeds are sorted by name
message BusinessPetBreedModel {
  // pet breed id
  int64 id = 1;

  // pet type id
  moego.models.customer.v1.PetType pet_type_id = 2;

  // pet breed name
  string name = 3;

  // if the pet breed is deleted
  bool deleted = 5;
}
