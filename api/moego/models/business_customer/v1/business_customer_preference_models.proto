syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/dayofweek.proto";
import "moego/utils/v1/time_of_day_interval.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Communication preference for a business customer
// 沟通相关的设置项, 涉及到是否接受短信, 邮件, 电话, 是否屏蔽某个用户的消息
message BusinessCustomerCommunicationPreferenceModel {
  // If block message from this customer
  bool block_message = 1;

  // Enable auto message - SMS
  bool enable_auto_message_sms = 2;
  // Enable auto message - email
  bool enable_auto_message_email = 3;
  // Enable auto message - call  = 4?
  // Enable auto message - app
  bool enable_auto_message_app = 11;

  // Enable appointment reminder - SMS
  bool enable_appointment_reminder_sms = 5;
  // Enable appointment reminder - email
  bool enable_appointment_reminder_email = 6;
  // Enable appointment reminder - call
  bool enable_appointment_reminder_call = 7;
  // Enable appointment reminder - app
  bool enable_appointment_reminder_app = 12;

  // Enable marketing - email
  bool enable_marketing_email = 8;
  // Enable marketing - SMS / call = 9 / 10?
}

// Appointment preference for a business customer
// 预约相关的设置项, 涉及到是否有指定的美容师, 预约频率, 预约时间等
message BusinessCustomerAppointmentPreferenceModel {
  // Preferred groomer id. If not set, this customer does not have a preferred groomer
  optional int64 preferred_groomer_id = 1;
  // Preferred grooming frequency
  utils.v1.TimePeriod preferred_grooming_frequency = 2;
  // Preferred day of week. If empty, no preferred day of week
  repeated google.type.DayOfWeek preferred_day_of_week = 3;
  // Preferred time of day
  utils.v1.TimeOfDayInterval preferred_time_of_day = 4;
}

// Payment preference for a business customer
// 支付相关的设置项, 涉及到是否开启自动小费, 小费类型, 小费金额等
message BusinessCustomerPaymentPreferenceModel {
  // Enable auto tipping
  bool enable_auto_tipping = 1;

  // Auto tipping type
  AutoTippingType auto_tipping_type = 2;

  // Auto tipping percentage, range from 1 to 100.
  int32 auto_tipping_percentage = 3;
  // Auto tipping amount, should be greater than or equal to 0, and keep at most two decimals. e.g. 1, 1.1, 1.01.
  double auto_tipping_amount = 4;

  // Auto tipping type
  enum AutoTippingType {
    // Unspecified
    AUTO_TIPPING_TYPE_UNSPECIFIED = 0;
    // By percentage
    BY_PERCENTAGE = 1;
    // By amount
    BY_AMOUNT = 2;
  }
}
