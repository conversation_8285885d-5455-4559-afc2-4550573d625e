syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for pet breed
message BusinessPetBreedCreateDef {
  // pet breed name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: "^(\\S.*\\S|\\S)$"
  }];
}

// update def for pet breed
message BusinessPetBreedUpdateDef {
  // pet breed name
  optional string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: "^(\\S.*\\S|\\S)$"
  }];
}

// upsert def for pet breed
message BusinessPetBreedUpsertDef {
  // pet breed name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: "^(\\S.*\\S|\\S)$"
  }];
}
