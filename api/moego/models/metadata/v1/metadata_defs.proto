// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.metadata.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/metadata/v1/metadata_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1;metadatapb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.metadata.v1";

// the full key def, for add a new key
message KeyFullDef {
  // the name of a key
  string name = 1 [(validate.rules).string = {
    min_len: 2
    max_len: 50
  }];
  // the description of a key
  string description = 10 [(validate.rules).string = {
    min_len: 2
    max_len: 255
  }];
  // the target type
  OwnerType owner_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the default value
  string default_value = 3 [(validate.rules).string = {max_len: 1024}];
  // the start time, 0 or null means works any time
  optional google.protobuf.Timestamp start_at = 4;
  // the end time, 0 or null means works forever
  optional google.protobuf.Timestamp end_at = 5;
  // the permission level
  PermissionLevel permission_level = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the key group
  string group = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// the partial key def, for update key
message KeyPartialDef {
  // the description of a key
  optional string description = 10 [(validate.rules).string = {
    min_len: 2
    max_len: 255
  }];
  // the target type
  optional OwnerType owner_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the default value
  optional string default_value = 3 [(validate.rules).string = {max_len: 1024}];
  // the start time, 0 or null means works any time
  optional google.protobuf.Timestamp start_at = 4;
  // the end time, 0 or null means works forever
  optional google.protobuf.Timestamp end_at = 5;
  // the operator type
  optional PermissionLevel permission_level = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the key group
  optional string group = 9 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// the owner identifier
message OwnerDef {
  // the owner type
  OwnerType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // the owner id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}
