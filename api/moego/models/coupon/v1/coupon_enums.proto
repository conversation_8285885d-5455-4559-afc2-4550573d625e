// @since 2-23-12-6
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.coupon.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/coupon/v1;couponpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.coupon.v1";

// business category
// this enumeration is mainly used to identify the business type that the coupon belongs to,
// such as platform care or pay admin.
// it is differentiated based on the enumeration for easy querying
enum CouponBusinessCategory {
  // unspecified
  COUPON_BUSINESS_CATEGORY_UNSPECIFIED = 0;
  // platform care
  COUPON_BUSINESS_CATEGORY_PLATFORM_CARE = 1;
  // pay admin
  COUPON_BUSINESS_CATEGORY_PAY_ADMIN = 2;
  // referral
  COUPON_BUSINESS_CATEGORY_REFERRAL = 3;
  // MOEGO CARE USER FACING COUPON
  COUPON_BUSINESS_CATEGORY_CARE = 4;
  // HARDWARE
  COUPON_BUSINESS_CATEGORY_HARDWARE = 5;
  // HARDWARE
  COUPON_BUSINESS_CATEGORY_PLATFORM_SALES = 6;
}
