syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// create TenantInfoDef
message CreateTenantInfoDef {
  // own type
  models.enterprise.v1.TenantModel.OwnType own_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // own first name
  optional string own_first_name = 2;
  // own last name
  optional string own_last_name = 3;
  // account email
  optional string email = 4;
  // phone number
  optional string phone_number = 5 [(validate.rules).string = {pattern: "^\\+[1-9]\\d{1,18}$"}];
  // color code
  optional InviteLinkSendMethodType method_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // whether to send invite link now or not
  optional bool is_send_invite_link = 7;
}

// group def
message GroupDef {
  // group name
  repeated string new_group_names = 1;
  // group id
  repeated int64 binding_group_ids = 2;
}

// update TenantInfoDef
message UpdateTenantInfoDef {
  // own type
  optional models.enterprise.v1.TenantModel.OwnType own_type = 1;
  // own first name
  optional string own_first_name = 2;
  // own last name
  optional string own_last_name = 3;
  // account email
  optional string email = 4;
  // phone number
  optional string phone_number = 5;
  // color code
  optional InviteLinkSendMethodType method_type = 6;
  // whether to send invite link now or not
  optional bool is_send_invite_link = 7;
}

// create tenant def
message CreateTenantDef {
  // name
  string name = 1;
  // own type
  CreateTenantInfoDef tenant_info = 2;
  // color code
  string color_code = 3 [(validate.rules).string = {max_len: 16}];
  // joined time
  google.protobuf.Timestamp joined_date = 4;
  // territory id
  optional int64 territory_id = 5 [(validate.rules).int64 = {gte: 0}];
  // assigned uint
  int32 assigned_unit = 6 [(validate.rules).int32 = {gte: 0}];
  // assigned van
  int32 assigned_van = 7 [(validate.rules).int32 = {gte: 0}];
  // tenant template id
  int64 tenant_template_id = 8 [(validate.rules).int64 = {gt: 0}];
  // state
  string state = 9 [(validate.rules).string = {max_len: 255}];
  // city
  string city = 10 [(validate.rules).string = {max_len: 255}];
}

// update tenant def
message UpdateTenantDef {
  // name
  optional string name = 1;
  // update tenant info def
  optional UpdateTenantInfoDef tenant_info = 2;
  // color code
  optional string color_code = 3;
  // joined time
  optional google.protobuf.Timestamp joined_date = 4;
  // territory id
  optional int64 territory_id = 5 [(validate.rules).int64 = {gte: 0}];
  // assigned uint
  optional int32 assigned_unit = 6 [(validate.rules).int32 = {gte: 0}];
  // assigned van
  optional int32 assigned_van = 7 [(validate.rules).int32 = {gte: 0}];
  // tenant template id
  optional int64 tenant_template_id = 8 [(validate.rules).int64 = {gte: 0}];
  // state
  optional string state = 9 [(validate.rules).string = {max_len: 255}];
  // city
  optional string city = 10 [(validate.rules).string = {max_len: 255}];
}

// franchise invite link send method type enum
enum InviteLinkSendMethodType {
  // send invite link unspecified
  INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED = 0;
  // send invite link via sms
  SMS = 1;
  // send invite link via email
  EMAIL = 2;
}

// tenant group map
message TenantGroupDef {
  // tenant id
  int64 tenant_id = 1;
  // group ids
  repeated int64 group_ids = 2;
}

// group tenant map
message GroupTenantDef {
  // tenant id
  int64 group_id = 1;
  // group name
  string group_name = 2;
  // tenant ids
  repeated int64 tenant_ids = 3;
}

// create tenant group def
message CreateTenantGroupDef {
  // tenant id
  repeated int64 group_ids = 1;
  // group ids
  repeated int64 group_names = 2;
}
