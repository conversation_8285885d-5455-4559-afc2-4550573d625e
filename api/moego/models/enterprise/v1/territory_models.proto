syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// territory model
message TerritoryModel {
  // enum for territory status
  enum Status {
    // UNSPECIFIED is the default value
    TERRITORY_STATUS_UNSPECIFIED = 0;
    // normal
    NORMAL = 1;
    // 删除
    DELETE = 2;
  }

  // enum for territory type
  enum Type {
    // UNSPECIFIED is the default value
    TERRITORY_TYPE_UNSPECIFIED = 0;
    // zip code
    ZIP_CODE = 1;
    // draw
    DRAW = 2;
  }
  // territory id
  int64 id = 1;
  // enterprise id
  string name = 2;
  // territory status
  Status status = 3;
  // territory type
  Type type = 4;
  // color code
  string color_code = 5;
  //zip codes
  repeated string zip_codes = 6;
}
