syntax = "proto3";

package moego.admin.file.v1;

import "moego/models/file/v2/file_defs.proto";
import "moego/models/file/v2/file_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/file/v1;fileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.file.v1";

// describe files request
message DescribeFilesParams {
  // creator id
  optional int64 creator_id = 1 [(validate.rules).int64 = {gte: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // owner type
  optional string owner_type_like = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // usage
  optional string usage_like = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // id
  optional int64 id = 5 [(validate.rules).int64 = {gt: 0}];
  // default not include
  optional bool include_deleted = 6;
  // owner id
  optional int64 owner_id = 7 [(validate.rules).int64 = {gte: 0}];
  // company id
  optional int64 company_id = 8 [(validate.rules).int64 = {gte: 0}];
  // order by
  optional moego.utils.v2.OrderBy order_by = 14;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// describe files result
message DescribeFilesResult {
  // files
  repeated moego.models.file.v2.FileModel files = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// upload file params
message UploadFileParams {
  // source
  oneof source {
    option (validate.required) = true;
    // platform
    moego.models.file.v2.PlatformSourceDef platform = 1;
    // tenant
    moego.models.file.v2.TenantSourceDef tenant = 2;
  }
  // usage
  string usage = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner type
  string owner_type = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  optional int64 owner_id = 5 [(validate.rules).int64 = {gte: 0}];
  // file name
  string file_name = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // file content
  bytes file_content = 7 [(validate.rules).bytes = {
    min_len: 1
    max_len: 104857600
  }];
}

// upload file result
message UploadFileResult {
  // file id
  int64 id = 1;
  // access url
  string access_url = 2;
}

// pre sign download url params
message PreSignDownloadUrlParams {
  // bucket
  string bucket = 1 [(validate.rules).string = {min_len: 1}];
  // key
  string key = 2 [(validate.rules).string = {min_len: 1}];
}

// pre sign download url result
message PreSignDownloadUrlResult {
  // url
  string url = 1;
}

// the file service
service FileService {
  // describe files
  rpc DescribeFiles(DescribeFilesParams) returns (DescribeFilesResult);
  // upload file
  rpc UploadFile(UploadFileParams) returns (UploadFileResult);
  // pre sign download url
  rpc PreSignDownloadUrl(PreSignDownloadUrlParams) returns (PreSignDownloadUrlResult);
}
