syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_fixed_defs.proto";
import "moego/models/business_customer/v1/business_pet_fixed_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// list pet fixed template params
message ListPetFixedTemplateParams {}

// list pet fixed template result
message ListPetFixedTemplateResult {
  // pet fixed list
  repeated moego.models.business_customer.v1.BusinessPetFixedNameView fixeds = 1;
}

// list pet fixed params
message ListPetFixedParams {}

// list pet fixed result
message ListPetFixedResult {
  // pet fixed list
  repeated moego.models.business_customer.v1.BusinessPetFixedModel fixeds = 1;
}

// create pet fixed params
message CreatePetFixedParams {
  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedCreateDef fixed = 1 [(validate.rules).message.required = true];
}

// create pet fixed result
message CreatePetFixedResult {
  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedModel fixed = 1;
}

// update pet fixed params
message UpdatePetFixedParams {
  // pet fixed id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pet fixed
  moego.models.business_customer.v1.BusinessPetFixedUpdateDef fixed = 2 [(validate.rules).message.required = true];
}

// update pet fixed result
message UpdatePetFixedResult {}

// sort pet fixed params
message SortPetFixedParams {
  // pet fixed id list, should contain all pet fixed ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// sort pet fixed result
message SortPetFixedResult {}

// delete pet fixed params
message DeletePetFixedParams {
  // pet fixed id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// delete pet fixed result
message DeletePetFixedResult {}

// API for pet fixed settings
service BusinessPetFixedService {
  // List pet fixed template
  rpc ListPetFixedTemplate(ListPetFixedTemplateParams) returns (ListPetFixedTemplateResult);

  // List pet fixed of current company
  rpc ListPetFixed(ListPetFixedParams) returns (ListPetFixedResult);

  // Create a pet fixed
  rpc CreatePetFixed(CreatePetFixedParams) returns (CreatePetFixedResult);

  // Update a pet fixed
  rpc UpdatePetFixed(UpdatePetFixedParams) returns (UpdatePetFixedResult);

  // Sort pet fixed
  rpc SortPetFixed(SortPetFixedParams) returns (SortPetFixedResult);

  // Delete a pet fixed
  rpc DeletePetFixed(DeletePetFixedParams) returns (DeletePetFixedResult);
}
