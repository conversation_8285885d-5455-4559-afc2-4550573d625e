syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/latlng.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_tracking.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// appointment tracking service
service AppointmentTrackingService {
  // get appointment tracking
  rpc GetAppointmentTracking(GetAppointmentTrackingParams) returns (GetAppointmentTrackingResult);
  // update appointment tracking status
  rpc UpdateAppointmentTrackingStatus(UpdateAppointmentTrackingStatusParams) returns (UpdateAppointmentTrackingStatusResult);
  // list staff appointment tracking
  rpc ListStaffAppointmentTracking(ListStaffAppointmentTrackingParams) returns (ListStaffAppointmentTrackingResult);
}

// update appointment tracking status request
message UpdateAppointmentTrackingStatusParams {
  // appointment id
  int64 appointment_id = 1;
  // staff location status
  moego.models.appointment.v1.StaffLocationStatus staff_location_status = 2;
  // staff coordinate, only available when staff location status is change to IN_TRANSIT
  optional google.type.LatLng staff_coordinate = 3;
  // staff device id, must have when staff location status is change to IN_TRANSIT
  // if not provided, will use the header device id
  optional string location_sharing_device_id = 4;
}

// update appointment tracking status response
message UpdateAppointmentTrackingStatusResult {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 1;
}

// get appointment tracking request
message GetAppointmentTrackingParams {
  // appointment id
  int64 appointment_id = 1;
}

// get appointment tracking response
message GetAppointmentTrackingResult {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 1;
}

// list staff appointment tracking request
// use staff id in session data
message ListStaffAppointmentTrackingParams {
  // filter
  message Filter {
    // status
    repeated moego.models.appointment.v1.StaffLocationStatus staff_location_statuses = 1 [(validate.rules).repeated = {
      max_items: 100
      items: {
        enum: {defined_only: true}
      }
    }];
    // appointment ids
    repeated int64 appointment_ids = 2 [(validate.rules).repeated = {max_items: 1000}];
  }
  // extra info request
  message ExtraInfoRequest {
    // request appointment info
    bool request_appointment_info = 1;
    // request customer info
    bool request_customer_info = 2;
  }
  // pagination
  utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // filter
  Filter filter = 2;
  // extra info request
  ExtraInfoRequest extra_info_request = 3;
}

// list staff appointment tracking response
message ListStaffAppointmentTrackingResult {
  // Customer view
  message CustomerView {
    // customer profile
    models.business_customer.v1.BusinessCustomerInfoModel customer_profile = 1;
  }
  // Appointment view
  message AppointmentView {
    // appointment
    models.appointment.v1.AppointmentModel appointment = 1;
  }

  // appointment tracking list
  repeated moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
  // appointment info, key is appointment id
  map<int64, AppointmentView> appointment_info = 3;
  // customer info, key is customer id
  map<int64, CustomerView> customer_info = 4;
}
