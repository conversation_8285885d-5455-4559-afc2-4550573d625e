syntax = "proto3";

package moego.api.customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

// update customer package request params
message UpdateCustomerPackageParams {
  // package id
  int64 package_id = 1 [(validate.rules).int64 = {gt: 0}];
  // extend validity day number
  optional int32 extend_validity_day = 2;
}

// update customer package response day
message UpdateCustomerPackageResponse {}

// customer package service
message UpdateCustomerPackageServiceParams {
  // package service id
  int64 package_service_id = 1 [(validate.rules).int64 = {gt: 0}];
  // remaining quantity
  optional int32 remaining_quantity = 2 [(validate.rules).int32 = {gte: 0}];
}

// update customer package service response
message UpdateCustomerPackageServiceResponse {}

// customer package service service
service CustomerPackageService {
  // update customer package
  rpc UpdateCustomerPackage(UpdateCustomerPackageParams) returns (UpdateCustomerPackageResponse);
  // update customer package service
  rpc UpdateCustomerPackageService(UpdateCustomerPackageServiceParams) returns (UpdateCustomerPackageServiceResponse);
}
