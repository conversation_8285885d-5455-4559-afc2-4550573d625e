syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/online_booking/v1/ob_access_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// online booking check identifier request
message OBCheckIdentifierRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // client uniquely identifies
  oneof identifier {
    option (validate.required) = true;
    // owner phone number
    string phone_number = 3 [(validate.rules).string = {
      max_len: 17
      pattern: "^[0-9]{4}[0-9]+$"
    }];

    // email
    string email = 4 [(validate.rules).string = {max_len: 50}];
  }

  // include possible clients
  bool include_possible_clients = 5;
}

// online booking check identifier response
message OBCheckIdentifierResponse {
  // if the identifier exists
  bool exist = 1;

  // possible clients
  // 如果 request 中的 identifier 可以准确定位到一个 client (即 exist = true)，那么 `possible_clients` 不返回任何东西
  // 如果 request 中的 identifier 无法准确定位到一个 client (即 exist = false)，并且入参设置了 include_possible_clients = true,
  // 那么后端会根据一些规则搜索一些可能匹配的 client 作为 `possible_clients`, 返回给前端选择.
  // 如果匹配不到任何可能的 client, `possible_clients` 为空
  //
  // 目前 possible_clients 的查找规则是:
  // 当 identifier 是 phone number 时, 如果 phone number 无法作为 main contact 直接找到对应的 client,
  // 则将其作为 additional contact 查找, 如果能找到对应的 client, 则返回 (最多不超过 3 个)
  repeated PossibleClient possible_clients = 2;

  // possible client
  message PossibleClient {
    // id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // avatar
    string avatar_path = 4;
    // masked phone number
    // possible client 不一定是用户本人, 考虑到隐私问题, 返回的 phone number 会被 mask
    string masked_phone_number = 5;
  }
}

// online booking get verification setting request
message OBGetVerificationSettingRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// online booking get verification setting response
message OBGetVerificationSettingResponse {
  // send verification code to existing client
  bool existing_client_verification_code = 1;
}

// online booking send verification code request
message OBSendVerificationCodeRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // client uniquely identifies
  oneof identifier {
    option (validate.required) = true;
    // owner phone number
    string phone_number = 3 [(validate.rules).string = {
      max_len: 17
      pattern: "^[0-9]{4}[0-9]+$"
    }];
    // possible client id, send to primary contact
    int64 possible_client_id = 4 [deprecated = true];

    // additional contact, send to additional contact
    AdditionalContact additional_contact = 5;
  }
  // access type, phone number or email, If it is an email, you need to find the associated email if you pass the phone
  moego.models.online_booking.v1.AccessType access_type = 8 [(validate.rules).enum = {defined_only: true}];
}

// The message of additional contact
message AdditionalContact {
  // additional contact phone number
  string phone_number = 1 [(validate.rules).string = {
    max_len: 17
    pattern: "^[0-9]{4}[0-9]+$"
  }];
  // related client id
  int64 related_client_id = 2;
}

// online booking send verification code to phone response
message OBSendVerificationCodeResponse {
  // verification code related token
  string token = 1;
  // send verification code success
  bool success = 2;
}

// online booking login by phone request
message OBLoginRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // login method
  oneof login_method {
    option (validate.required) = true;
    // login by verification code
    OBLoginByVerificationCodeDef by_verification_code = 5;
    // login by ppp token
    OBLoginByPPPTokenDef by_ppp = 6;
  }
  // ob login ads data
  optional OBLoginAdsDataDef ads_data = 7;
}

// online booking login by verification code
message OBLoginByVerificationCodeDef {
  // client uniquely identifies
  oneof identifier {
    option (validate.required) = true;
    // owner phone number
    string phone_number = 1 [(validate.rules).string = {
      max_len: 17
      pattern: "^[0-9]{4}[0-9]+$"
    }];
    // possible client id
    int64 possible_client_id = 2 [deprecated = true];
    // additional contact
    AdditionalContact additional_contact = 3;
  }
  // access type, phone number or email, If it is an email, you need to find the associated email if you pass the phone
  moego.models.online_booking.v1.AccessType access_type = 8;
  // verification code
  string code = 9 [(validate.rules).string = {max_len: 6}];

  // verification code related token
  string token = 10 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
}

// online booking login by pet parent portal token
message OBLoginByPPPTokenDef {
  // client portal token
  string token = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
}

// online booking login collect ads data
message OBLoginAdsDataDef {
  // google collect ads data
  string google_ads_str = 1;
}

// online booking login response
message OBLoginResponse {}

// online booking logout request
message OBLogoutRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// online booking logout response
message OBLogoutResponse {}

// online booking get dev mode request
message OBDevModeRequest {}

// online booking get dev mode response
message OBDevModeResponse {
  // dev mode flag
  bool dev_mode = 1;
}

// online booking access service
service OBAccessService {
  // Check if the identifier exists. If the identifier does not exist, recommend some possible clients
  rpc CheckIdentifier(OBCheckIdentifierRequest) returns (OBCheckIdentifierResponse);

  // get verification setting
  rpc GetVerificationSetting(OBGetVerificationSettingRequest) returns (OBGetVerificationSettingResponse);

  // send verification code
  rpc SendVerificationCode(OBSendVerificationCodeRequest) returns (OBSendVerificationCodeResponse);
  // login
  rpc Login(OBLoginRequest) returns (OBLoginResponse);
  // logout
  rpc Logout(OBLogoutRequest) returns (OBLogoutResponse);
  // get dev mode
  rpc GetDevMode(OBDevModeRequest) returns (OBDevModeResponse);
}
