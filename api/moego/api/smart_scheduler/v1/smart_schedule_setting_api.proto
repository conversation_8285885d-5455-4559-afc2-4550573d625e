syntax = "proto3";

package moego.api.smart_scheduler.v1;

import "moego/models/smart_scheduler/v1/smart_schedule_setting_defs.proto";
import "moego/models/smart_scheduler/v1/smart_schedule_setting_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/smart_scheduler/v1;smartschedulerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.smart_scheduler.v1";

// get smart schedule setting params
message GetSmartScheduleSettingParams {}

// get smart schedule setting result
message GetSmartScheduleSettingResult {
  // smart schedule setting
  models.smart_scheduler.v1.SmartScheduleSettingModel smart_schedule_setting = 1;
  // business setting override list
  repeated models.smart_scheduler.v1.BusinessSettingOverrideModel business_override_list = 2;
}

// update smart schedule setting params
message UpdateSmartScheduleSettingParams {
  // update smart schedule setting
  models.smart_scheduler.v1.UpdateSmartScheduleSettingDef smart_schedule_setting = 1;
}

// update smart schedule setting result
message UpdateSmartScheduleSettingResult {}

// smart schedule setting api
service SmartScheduleSettingService {
  // get smart schedule setting
  rpc GetSmartScheduleSetting(GetSmartScheduleSettingParams) returns (GetSmartScheduleSettingResult);
  // update smart schedule setting
  rpc UpdateSmartScheduleSetting(UpdateSmartScheduleSettingParams) returns (UpdateSmartScheduleSettingResult);
}
