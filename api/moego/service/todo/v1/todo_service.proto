// @since 2022-05-30 17:05:07
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.todo.v1;

import "google/protobuf/empty.proto";
import "moego/models/todo/v1/todo_models.proto";
import "moego/models/universal/v1/entities_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/todo/v1;todosvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.todo.v1";

// add todo input
message AddTodoRequest {
  // user id
  int64 user_id = 1 [(validate.rules).int64 = {gt: 0}];
  // title
  string title = 2 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
  }];
}

// list all todo input
message ListTodoRequest {
  // user id
  int64 user_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// 多租隔离, 所有 service 层接口都需要校验数据所有权
// update todo input
message UpdateTodoRequest {
  // user id
  int64 user_id = 1 [(validate.rules).int64 = {gt: 0}];
  // id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
  // title
  string title = 3 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
    ignore_empty: true
  }];
  // status
  moego.models.todo.v1.TodoModel.Status status = 4;
}

//response body
message HelloChannyResponse {
  //response code
  string hello_channy = 1;
}

//response body
message HelloDongResponse {
  // response content
  string hello_dong = 1;
}

//response body
message HelloJettResponse {
  // response content
  string hello_jett = 1;
}

// EchoHzRequest
message EchoHzRequest {
  // msg
  string msg = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 100
    ignore_empty: true
  }];
  // id
  int64 id = 2;
}

// EchoHzResponse
message EchoHzResponse {
  // msg
  string msg = 1;
  // id
  int64 id = 2;
}

// HelloArkRequest
message HelloArkRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_bytes: 1
    max_bytes: 512
  }];
}

// HelloArkResponse
message HelloArkResponse {
  // reply
  string reply = 1;
}

// HelloBetterRequest
message HelloBetterRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 512
  }];
}

// HelloBetterResponse
message HelloBetterResponse {
  // reply
  string reply = 1;
}

// HelloPerqinRequest
message HelloPerqinRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloPerqinResponse
message HelloPerqinResponse {
  // reply
  string reply = 1;
}

// HelloYueyueRequest
message HelloYueyueRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloYueyueResponse
message HelloYueyueResponse {
  // reply
  string reply = 1;
}

// HelloYueyueRequest
message HelloKaiRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloYueyueResponse
message HelloKaiResponse {
  // reply
  string reply = 1;
}

// HelloKurokoRequest
message HelloKurokoRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloKurokoResponse
message HelloKurokoResponse {
  // reply
  string reply = 1;
}

// HelloBrysonRequest
message HelloBrysonRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloBrysonResponse
message HelloBrysonResponse {
  // reply
  string reply = 1;
}

// HelloHarvieRequest
message HelloHarvieRequest {
  // message
  string message = 1 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
}

// HelloHarvieResponse
message HelloHarvieResponse {
  // reply
  string reply = 1;
}

// todo service
service TodoService {
  // 添加 Todo
  rpc AddTodo(AddTodoRequest) returns (moego.models.todo.v1.TodoModel) {}
  // 根据 id 获取单个 Todo
  rpc GetTodo(moego.utils.v1.OwnId) returns (moego.models.todo.v1.TodoModel) {}
  // 查询 Todo 列表
  rpc ListTodo(ListTodoRequest) returns (moego.models.universal.v1.EntityListModel) {}
  // 更新 Todo
  rpc UpdateTodo(UpdateTodoRequest) returns (moego.models.todo.v1.TodoModel) {}
  // 删除 Todo
  rpc DeleteTodo(moego.utils.v1.OwnId) returns (google.protobuf.Empty) {}

  // hello world api by jett
  rpc HelloJett(google.protobuf.Empty) returns (HelloJettResponse) {}
  // echo api by haozhi
  rpc EchoHz(EchoHzRequest) returns (EchoHzResponse);
  // hello world api by ark
  rpc HelloArk(HelloArkRequest) returns (HelloArkResponse);

  // hello world api by better
  rpc HelloBetter(HelloBetterRequest) returns (HelloBetterResponse);
  // hello world api by perqin
  rpc HelloPerqin(HelloPerqinRequest) returns (HelloPerqinResponse);

  //  hello world api by yueyue
  rpc HelloYueyue(HelloYueyueRequest) returns (HelloYueyueResponse);

  //  hello world api by kai
  rpc HelloKai(HelloKaiRequest) returns (HelloKaiResponse);

  //  hello world api by kuroko
  rpc HelloKuroko(HelloKurokoRequest) returns (HelloKurokoResponse);

  //  hello world api by Bryson
  rpc HelloBryson(HelloBrysonRequest) returns (HelloBrysonResponse);

  //  hello world api by Harvie
  rpc HelloHarvie(HelloHarvieRequest) returns (HelloHarvieResponse);
}
