syntax = "proto3";

package moego.service.finance_gw.v1;

import "moego/models/finance_gw/v1/webhook_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.finance_gw.v1";

// HandleWebhookEventRequest, downstream needs to receive this request.
message HandleWebhookEventRequest {
  // The source (a.k.a. channel) of the event.
  enum ChannelType {
    // Unspecified.
    CHANNEL_TYPE_UNSPECIFIED = 0;
    // The event is from Stripe.
    STRIPE = 1;
    // The event is from KANMON.
    KANMON = 2;
    // The event is from Adyen.
    ADYEN = 3;
  }

  // The source (a.k.a. channel) of the event.
  ChannelType channel_type = 1;
  // Webhook event from HTTP outbound.
  moego.models.finance_gw.v1.HttpWebhookEvent http_webhook_event = 2;
}

// HandleWebhookEventResponse, downstream needs to receive this response.
message HandleWebhookEventResponse {}
