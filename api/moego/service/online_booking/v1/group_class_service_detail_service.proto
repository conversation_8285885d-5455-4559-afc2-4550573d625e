syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create GroupClassServiceDetail request
message CreateGroupClassServiceDetailRequest {
  // The id of pet, associated with the current service
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of staff, associated with the current service
  optional int64 staff_id = 3;
  // The id of current service
  int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
  // The id of group class instance,
  int64 class_instance_id = 5 [(validate.rules).int64 = {gt: 0}];
  // The date list of the group class session, ["yyyy-MM-dd"]
  repeated string specific_dates = 6;
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 7 [(validate.rules).int32 = {gte: 0}];
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 8 [(validate.rules).int32 = {gte: 0}];
  // Duration of each session in minutes
  int32 duration_per_session = 9 [(validate.rules).int32 = {gte: 0}];
  // Service price
  optional double service_price = 10;
  // createdAt
  optional google.protobuf.Timestamp created_at = 13;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 14;
}

// GroupClassDetail service
service GroupClassDetailService {}
