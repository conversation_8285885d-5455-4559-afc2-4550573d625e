#!/bin/bash
source scripts/common.sh

run_command() {
    local message=$1
    local command=$2
    local show_output=${3:-false} 
    printf "%-50s" "$message"
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}${CHECK}${NC}"
        return 0
    else
        echo -e "${RED}${CROSS}${NC}"
        if [ "$show_output" = true ]; then
            eval "$command"
        fi
        return 1
    fi
}


run_proto_lint() {
  local proto_dir=backend/proto
  local deps=$proto_dir/deps
  local config_path=$proto_dir/api-linter-config.yaml
  local report_path=$proto_dir/api-linter-report.yaml

  # list all proto files in backend/proto
  local files=$(find $proto_dir -name '*.proto' -print)
  # 排除 helloworld 和 deps 文件夹下的 proto 文件
  files=$(echo "$files" | grep -v "$proto_dir/helloworld/" | grep -v "$proto_dir/deps/")

  buf export buf.build/envoyproxy/protoc-gen-validate -o $deps
  buf export buf.build/googleapis/googleapis -o $deps

  rm -f $report_path

  api-linter \
  --set-exit-status \
  --config $config_path \
  -o $report_path \
  --output-format yaml \
  -I $deps \
  -I . \
  $files

  local linter_exit_status=$?

  # 状态异常且 report 文件存在
  if [ $linter_exit_status -ne 0 ] && [ -f $report_path ]; then
    echo "please check the report file: $proto_dir/api-linter-report.yaml"
  fi

  rm -rf $deps

  return $linter_exit_status
}

echo "Running lint checks..."

echo "--------------------------- Proto Files ---------------------------"
# run_command "Running buf lint... " "buf lint" true || exit 1
run_command "Running api linter... " "run_proto_lint" true || exit 1

echo "--------------------------- Code Linting -------------------------"
run_command "Running go mod tidy... " "go mod tidy" false || exit 1
run_command "Running goimports-reviser... " \
    "goimports-reviser -company-prefixes github.com/MoeGolibrary -excludes '*backend/proto/' -set-alias -rm-unused -format ./..." \
    false || exit 1
run_command "Running golangci-lint... " \
    "golangci-lint run -v --allow-parallel-runners --fix --tests=false" \
    true || exit 1

echo "--------------------------- Bazel ---------------------------"
run_command "Running Bazel files buildifier... " "${BAZEL_COMMAND} run //:buildifier.fix" true || exit 1

echo -e "${GREEN}All lint checks passed! ✨${NC}"
