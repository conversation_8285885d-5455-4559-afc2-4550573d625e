// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/utils/v1/status_messages.proto

package utilsV1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// record status
// Deprecated: force use local defined enum
//
// Deprecated: Do not use.
type Status int32

const (
	// unspecified
	Status_STATUS_UNSPECIFIED Status = 0
	// normal
	Status_STATUS_NORMAL Status = 1
	// deleted
	Status_STATUS_DELETED Status = 2
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_NORMAL",
		2: "STATUS_DELETED",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_NORMAL":      1,
		"STATUS_DELETED":     2,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_utils_v1_status_messages_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_moego_utils_v1_status_messages_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_utils_v1_status_messages_proto_rawDescGZIP(), []int{0}
}

var File_moego_utils_v1_status_messages_proto protoreflect.FileDescriptor

var file_moego_utils_v1_status_messages_proto_rawDesc = []byte{
	0x0a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x1a,
	0x02, 0x18, 0x01, 0x42, 0x67, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x4b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x31, 0x3b, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_utils_v1_status_messages_proto_rawDescOnce sync.Once
	file_moego_utils_v1_status_messages_proto_rawDescData = file_moego_utils_v1_status_messages_proto_rawDesc
)

func file_moego_utils_v1_status_messages_proto_rawDescGZIP() []byte {
	file_moego_utils_v1_status_messages_proto_rawDescOnce.Do(func() {
		file_moego_utils_v1_status_messages_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_utils_v1_status_messages_proto_rawDescData)
	})
	return file_moego_utils_v1_status_messages_proto_rawDescData
}

var file_moego_utils_v1_status_messages_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_utils_v1_status_messages_proto_goTypes = []interface{}{
	(Status)(0), // 0: moego.utils.v1.Status
}
var file_moego_utils_v1_status_messages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_utils_v1_status_messages_proto_init() }
func file_moego_utils_v1_status_messages_proto_init() {
	if File_moego_utils_v1_status_messages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_utils_v1_status_messages_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_utils_v1_status_messages_proto_goTypes,
		DependencyIndexes: file_moego_utils_v1_status_messages_proto_depIdxs,
		EnumInfos:         file_moego_utils_v1_status_messages_proto_enumTypes,
	}.Build()
	File_moego_utils_v1_status_messages_proto = out.File
	file_moego_utils_v1_status_messages_proto_rawDesc = nil
	file_moego_utils_v1_status_messages_proto_goTypes = nil
	file_moego_utils_v1_status_messages_proto_depIdxs = nil
}
