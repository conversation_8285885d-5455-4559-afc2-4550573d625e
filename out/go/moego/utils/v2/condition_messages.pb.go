// @since 2022-05-30 17:23:56
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/utils/v2/condition_messages.proto

package utilsV2

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// operator
type Operator int32

const (
	// unspecified
	Operator_OPERATOR_UNSPECIFIED Operator = 0
	// equal
	Operator_OPERATOR_EQ Operator = 1
	// not equal
	Operator_OPERATOR_NE Operator = 2
	// greater than
	Operator_OPERATOR_GT Operator = 3
	// greater than or equal
	Operator_OPERATOR_GE Operator = 4
	// less than
	Operator_OPERATOR_LT Operator = 5
	// less than or equal
	Operator_OPERATOR_LE Operator = 6
	// like
	Operator_OPERATOR_LIKE Operator = 7
	// not like
	Operator_OPERATOR_NOT_LIKE Operator = 8
	// prefix like
	Operator_OPERATOR_PREFIX_LIKE Operator = 9
	// prefix not like
	Operator_OPERATOR_PREFIX_NOT_LIKE Operator = 10
	// suffix like
	Operator_OPERATOR_SUFFIX_LIKE Operator = 11
	// suffix not like
	Operator_OPERATOR_SUFFIX_NOT_LIKE Operator = 12
	// in
	Operator_OPERATOR_IN Operator = 13
	// not in
	Operator_OPERATOR_NOT_IN Operator = 14
	// between
	Operator_OPERATOR_BETWEEN Operator = 15
	// not between
	Operator_OPERATOR_NOT_BETWEEN Operator = 16
)

// Enum value maps for Operator.
var (
	Operator_name = map[int32]string{
		0:  "OPERATOR_UNSPECIFIED",
		1:  "OPERATOR_EQ",
		2:  "OPERATOR_NE",
		3:  "OPERATOR_GT",
		4:  "OPERATOR_GE",
		5:  "OPERATOR_LT",
		6:  "OPERATOR_LE",
		7:  "OPERATOR_LIKE",
		8:  "OPERATOR_NOT_LIKE",
		9:  "OPERATOR_PREFIX_LIKE",
		10: "OPERATOR_PREFIX_NOT_LIKE",
		11: "OPERATOR_SUFFIX_LIKE",
		12: "OPERATOR_SUFFIX_NOT_LIKE",
		13: "OPERATOR_IN",
		14: "OPERATOR_NOT_IN",
		15: "OPERATOR_BETWEEN",
		16: "OPERATOR_NOT_BETWEEN",
	}
	Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED":     0,
		"OPERATOR_EQ":              1,
		"OPERATOR_NE":              2,
		"OPERATOR_GT":              3,
		"OPERATOR_GE":              4,
		"OPERATOR_LT":              5,
		"OPERATOR_LE":              6,
		"OPERATOR_LIKE":            7,
		"OPERATOR_NOT_LIKE":        8,
		"OPERATOR_PREFIX_LIKE":     9,
		"OPERATOR_PREFIX_NOT_LIKE": 10,
		"OPERATOR_SUFFIX_LIKE":     11,
		"OPERATOR_SUFFIX_NOT_LIKE": 12,
		"OPERATOR_IN":              13,
		"OPERATOR_NOT_IN":          14,
		"OPERATOR_BETWEEN":         15,
		"OPERATOR_NOT_BETWEEN":     16,
	}
)

func (x Operator) Enum() *Operator {
	p := new(Operator)
	*p = x
	return p
}

func (x Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_utils_v2_condition_messages_proto_enumTypes[0].Descriptor()
}

func (Operator) Type() protoreflect.EnumType {
	return &file_moego_utils_v2_condition_messages_proto_enumTypes[0]
}

func (x Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operator.Descriptor instead.
func (Operator) EnumDescriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{0}
}

// wrapper of number(s)
type NumberWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value
	Value *int64 `protobuf:"varint,1,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// values
	Values []int64 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *NumberWrapper) Reset() {
	*x = NumberWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberWrapper) ProtoMessage() {}

func (x *NumberWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberWrapper.ProtoReflect.Descriptor instead.
func (*NumberWrapper) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{0}
}

func (x *NumberWrapper) GetValue() int64 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

func (x *NumberWrapper) GetValues() []int64 {
	if x != nil {
		return x.Values
	}
	return nil
}

// wrapper of string(s)
type StringWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value
	Value *string `protobuf:"bytes,1,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// values
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *StringWrapper) Reset() {
	*x = StringWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringWrapper) ProtoMessage() {}

func (x *StringWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringWrapper.ProtoReflect.Descriptor instead.
func (*StringWrapper) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{1}
}

func (x *StringWrapper) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *StringWrapper) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// wrapper of time
type TimeWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// timestamp
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3,oneof" json:"timestamp,omitempty"`
	// date
	Date *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3,oneof" json:"date,omitempty"`
}

func (x *TimeWrapper) Reset() {
	*x = TimeWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeWrapper) ProtoMessage() {}

func (x *TimeWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeWrapper.ProtoReflect.Descriptor instead.
func (*TimeWrapper) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{2}
}

func (x *TimeWrapper) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *TimeWrapper) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

// wrapper of money
type MoneyWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value
	Value *money.Money `protobuf:"bytes,1,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// values
	Values []*money.Money `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *MoneyWrapper) Reset() {
	*x = MoneyWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoneyWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoneyWrapper) ProtoMessage() {}

func (x *MoneyWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoneyWrapper.ProtoReflect.Descriptor instead.
func (*MoneyWrapper) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{3}
}

func (x *MoneyWrapper) GetValue() *money.Money {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *MoneyWrapper) GetValues() []*money.Money {
	if x != nil {
		return x.Values
	}
	return nil
}

// predicate
type Predicate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field name
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// operator
	Operator Operator `protobuf:"varint,2,opt,name=operator,proto3,enum=moego.utils.v2.Operator" json:"operator,omitempty"`
	// value, required
	//
	// Types that are assignable to Value:
	//
	//	*Predicate_Number
	//	*Predicate_String_
	//	*Predicate_Time
	//	*Predicate_Money
	Value isPredicate_Value `protobuf_oneof:"value"`
	// with predicate, optional
	//
	// Types that are assignable to WithPredicate:
	//
	//	*Predicate_And
	//	*Predicate_Or
	WithPredicate isPredicate_WithPredicate `protobuf_oneof:"with_predicate"`
}

func (x *Predicate) Reset() {
	*x = Predicate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Predicate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Predicate) ProtoMessage() {}

func (x *Predicate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Predicate.ProtoReflect.Descriptor instead.
func (*Predicate) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{4}
}

func (x *Predicate) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *Predicate) GetOperator() Operator {
	if x != nil {
		return x.Operator
	}
	return Operator_OPERATOR_UNSPECIFIED
}

func (m *Predicate) GetValue() isPredicate_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Predicate) GetNumber() *NumberWrapper {
	if x, ok := x.GetValue().(*Predicate_Number); ok {
		return x.Number
	}
	return nil
}

func (x *Predicate) GetString_() *StringWrapper {
	if x, ok := x.GetValue().(*Predicate_String_); ok {
		return x.String_
	}
	return nil
}

func (x *Predicate) GetTime() *TimeWrapper {
	if x, ok := x.GetValue().(*Predicate_Time); ok {
		return x.Time
	}
	return nil
}

func (x *Predicate) GetMoney() *MoneyWrapper {
	if x, ok := x.GetValue().(*Predicate_Money); ok {
		return x.Money
	}
	return nil
}

func (m *Predicate) GetWithPredicate() isPredicate_WithPredicate {
	if m != nil {
		return m.WithPredicate
	}
	return nil
}

func (x *Predicate) GetAnd() *Predicate {
	if x, ok := x.GetWithPredicate().(*Predicate_And); ok {
		return x.And
	}
	return nil
}

func (x *Predicate) GetOr() *Predicate {
	if x, ok := x.GetWithPredicate().(*Predicate_Or); ok {
		return x.Or
	}
	return nil
}

type isPredicate_Value interface {
	isPredicate_Value()
}

type Predicate_Number struct {
	// number value(s)
	Number *NumberWrapper `protobuf:"bytes,5,opt,name=number,proto3,oneof"`
}

type Predicate_String_ struct {
	// string value(s)
	String_ *StringWrapper `protobuf:"bytes,6,opt,name=string,proto3,oneof"`
}

type Predicate_Time struct {
	// time wrapper
	Time *TimeWrapper `protobuf:"bytes,7,opt,name=time,proto3,oneof"`
}

type Predicate_Money struct {
	// money wrapper
	Money *MoneyWrapper `protobuf:"bytes,8,opt,name=money,proto3,oneof"`
}

func (*Predicate_Number) isPredicate_Value() {}

func (*Predicate_String_) isPredicate_Value() {}

func (*Predicate_Time) isPredicate_Value() {}

func (*Predicate_Money) isPredicate_Value() {}

type isPredicate_WithPredicate interface {
	isPredicate_WithPredicate()
}

type Predicate_And struct {
	// and
	And *Predicate `protobuf:"bytes,3,opt,name=and,proto3,oneof"`
}

type Predicate_Or struct {
	// or
	Or *Predicate `protobuf:"bytes,4,opt,name=or,proto3,oneof"`
}

func (*Predicate_And) isPredicate_WithPredicate() {}

func (*Predicate_Or) isPredicate_WithPredicate() {}

// order by
type OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field name
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// asc, if not set, default is true
	Asc *bool `protobuf:"varint,2,opt,name=asc,proto3,oneof" json:"asc,omitempty"`
}

func (x *OrderBy) Reset() {
	*x = OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderBy) ProtoMessage() {}

func (x *OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderBy.ProtoReflect.Descriptor instead.
func (*OrderBy) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{5}
}

func (x *OrderBy) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *OrderBy) GetAsc() bool {
	if x != nil && x.Asc != nil {
		return *x.Asc
	}
	return false
}

// Range
type Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start
	Start *int64 `protobuf:"varint,1,opt,name=start,proto3,oneof" json:"start,omitempty"`
	// end
	End *int64 `protobuf:"varint,2,opt,name=end,proto3,oneof" json:"end,omitempty"`
}

func (x *Range) Reset() {
	*x = Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Range) ProtoMessage() {}

func (x *Range) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Range.ProtoReflect.Descriptor instead.
func (*Range) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{6}
}

func (x *Range) GetStart() int64 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *Range) GetEnd() int64 {
	if x != nil && x.End != nil {
		return *x.End
	}
	return 0
}

// StringDateRange represents a range of dates
type StringDateRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *StringDateRange) Reset() {
	*x = StringDateRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringDateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringDateRange) ProtoMessage() {}

func (x *StringDateRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_condition_messages_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringDateRange.ProtoReflect.Descriptor instead.
func (*StringDateRange) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_condition_messages_proto_rawDescGZIP(), []int{7}
}

func (x *StringDateRange) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *StringDateRange) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

var File_moego_utils_v2_condition_messages_proto protoreflect.FileDescriptor

var file_moego_utils_v2_condition_messages_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4c, 0x0a, 0x0d,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4c, 0x0a, 0x0d, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x0b, 0x54, 0x69, 0x6d,
	0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x48, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x22, 0x73, 0x0a, 0x0c, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2a, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xd1, 0x03, 0x0a, 0x09, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3e, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x37, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x03, 0x61,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x03, 0x61, 0x6e, 0x64, 0x12, 0x2b, 0x0a, 0x02, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x48, 0x01, 0x52, 0x02, 0x6f, 0x72, 0x42, 0x0c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x15, 0x0a, 0x0e, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x03, 0xf8, 0x42, 0x00, 0x22, 0x52, 0x0a, 0x07,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x15, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x03, 0x61, 0x73, 0x63, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x73, 0x63,
	0x22, 0x4b, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x65, 0x6e, 0x64, 0x22, 0x83, 0x01,
	0x0a, 0x0f, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a,
	0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x2a, 0xfa, 0x02, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x47, 0x54, 0x10, 0x03, 0x12, 0x0f, 0x0a,
	0x0b, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x47, 0x45, 0x10, 0x04, 0x12, 0x0f,
	0x0a, 0x0b, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x54, 0x10, 0x05, 0x12,
	0x0f, 0x0a, 0x0b, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x45, 0x10, 0x06,
	0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x4b,
	0x45, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x49, 0x58, 0x5f, 0x4c, 0x49,
	0x4b, 0x45, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52,
	0x5f, 0x50, 0x52, 0x45, 0x46, 0x49, 0x58, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4b, 0x45,
	0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x53,
	0x55, 0x46, 0x46, 0x49, 0x58, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x10, 0x0b, 0x12, 0x1c, 0x0a, 0x18,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x46, 0x46, 0x49, 0x58, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10, 0x0e,
	0x12, 0x14, 0x0a, 0x10, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x42, 0x45, 0x54,
	0x57, 0x45, 0x45, 0x4e, 0x10, 0x0f, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54,
	0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x10, 0x10,
	0x42, 0x67, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x4b, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x3b, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x56, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_utils_v2_condition_messages_proto_rawDescOnce sync.Once
	file_moego_utils_v2_condition_messages_proto_rawDescData = file_moego_utils_v2_condition_messages_proto_rawDesc
)

func file_moego_utils_v2_condition_messages_proto_rawDescGZIP() []byte {
	file_moego_utils_v2_condition_messages_proto_rawDescOnce.Do(func() {
		file_moego_utils_v2_condition_messages_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_utils_v2_condition_messages_proto_rawDescData)
	})
	return file_moego_utils_v2_condition_messages_proto_rawDescData
}

var file_moego_utils_v2_condition_messages_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_utils_v2_condition_messages_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_utils_v2_condition_messages_proto_goTypes = []interface{}{
	(Operator)(0),                 // 0: moego.utils.v2.Operator
	(*NumberWrapper)(nil),         // 1: moego.utils.v2.NumberWrapper
	(*StringWrapper)(nil),         // 2: moego.utils.v2.StringWrapper
	(*TimeWrapper)(nil),           // 3: moego.utils.v2.TimeWrapper
	(*MoneyWrapper)(nil),          // 4: moego.utils.v2.MoneyWrapper
	(*Predicate)(nil),             // 5: moego.utils.v2.Predicate
	(*OrderBy)(nil),               // 6: moego.utils.v2.OrderBy
	(*Range)(nil),                 // 7: moego.utils.v2.Range
	(*StringDateRange)(nil),       // 8: moego.utils.v2.StringDateRange
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
	(*money.Money)(nil),           // 10: google.type.Money
}
var file_moego_utils_v2_condition_messages_proto_depIdxs = []int32{
	9,  // 0: moego.utils.v2.TimeWrapper.timestamp:type_name -> google.protobuf.Timestamp
	9,  // 1: moego.utils.v2.TimeWrapper.date:type_name -> google.protobuf.Timestamp
	10, // 2: moego.utils.v2.MoneyWrapper.value:type_name -> google.type.Money
	10, // 3: moego.utils.v2.MoneyWrapper.values:type_name -> google.type.Money
	0,  // 4: moego.utils.v2.Predicate.operator:type_name -> moego.utils.v2.Operator
	1,  // 5: moego.utils.v2.Predicate.number:type_name -> moego.utils.v2.NumberWrapper
	2,  // 6: moego.utils.v2.Predicate.string:type_name -> moego.utils.v2.StringWrapper
	3,  // 7: moego.utils.v2.Predicate.time:type_name -> moego.utils.v2.TimeWrapper
	4,  // 8: moego.utils.v2.Predicate.money:type_name -> moego.utils.v2.MoneyWrapper
	5,  // 9: moego.utils.v2.Predicate.and:type_name -> moego.utils.v2.Predicate
	5,  // 10: moego.utils.v2.Predicate.or:type_name -> moego.utils.v2.Predicate
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_utils_v2_condition_messages_proto_init() }
func file_moego_utils_v2_condition_messages_proto_init() {
	if File_moego_utils_v2_condition_messages_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_utils_v2_condition_messages_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoneyWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Predicate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_condition_messages_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringDateRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_utils_v2_condition_messages_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_utils_v2_condition_messages_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_utils_v2_condition_messages_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_utils_v2_condition_messages_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_utils_v2_condition_messages_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*Predicate_Number)(nil),
		(*Predicate_String_)(nil),
		(*Predicate_Time)(nil),
		(*Predicate_Money)(nil),
		(*Predicate_And)(nil),
		(*Predicate_Or)(nil),
	}
	file_moego_utils_v2_condition_messages_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_utils_v2_condition_messages_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_utils_v2_condition_messages_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_utils_v2_condition_messages_proto_goTypes,
		DependencyIndexes: file_moego_utils_v2_condition_messages_proto_depIdxs,
		EnumInfos:         file_moego_utils_v2_condition_messages_proto_enumTypes,
		MessageInfos:      file_moego_utils_v2_condition_messages_proto_msgTypes,
	}.Build()
	File_moego_utils_v2_condition_messages_proto = out.File
	file_moego_utils_v2_condition_messages_proto_rawDesc = nil
	file_moego_utils_v2_condition_messages_proto_goTypes = nil
	file_moego_utils_v2_condition_messages_proto_depIdxs = nil
}
