// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/file/v1/file_api.proto

package fileapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FileServiceClient is the client API for FileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileServiceClient interface {
	// get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.
	// set header 'x-amz-acl: public-read' to let the file be public
	GetUploadPresignedURL(ctx context.Context, in *GetUploadPresignedURLParams, opts ...grpc.CallOption) (*GetUploadPresignedURLResult, error)
	// query file
	QueryFile(ctx context.Context, in *QueryFileParams, opts ...grpc.CallOption) (*QueryFileResult, error)
}

type fileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileServiceClient(cc grpc.ClientConnInterface) FileServiceClient {
	return &fileServiceClient{cc}
}

func (c *fileServiceClient) GetUploadPresignedURL(ctx context.Context, in *GetUploadPresignedURLParams, opts ...grpc.CallOption) (*GetUploadPresignedURLResult, error) {
	out := new(GetUploadPresignedURLResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.file.v1.FileService/GetUploadPresignedURL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) QueryFile(ctx context.Context, in *QueryFileParams, opts ...grpc.CallOption) (*QueryFileResult, error) {
	out := new(QueryFileResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.file.v1.FileService/QueryFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileServiceServer is the server API for FileService service.
// All implementations must embed UnimplementedFileServiceServer
// for forward compatibility
type FileServiceServer interface {
	// get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.
	// set header 'x-amz-acl: public-read' to let the file be public
	GetUploadPresignedURL(context.Context, *GetUploadPresignedURLParams) (*GetUploadPresignedURLResult, error)
	// query file
	QueryFile(context.Context, *QueryFileParams) (*QueryFileResult, error)
	mustEmbedUnimplementedFileServiceServer()
}

// UnimplementedFileServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFileServiceServer struct {
}

func (UnimplementedFileServiceServer) GetUploadPresignedURL(context.Context, *GetUploadPresignedURLParams) (*GetUploadPresignedURLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUploadPresignedURL not implemented")
}
func (UnimplementedFileServiceServer) QueryFile(context.Context, *QueryFileParams) (*QueryFileResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFile not implemented")
}
func (UnimplementedFileServiceServer) mustEmbedUnimplementedFileServiceServer() {}

// UnsafeFileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileServiceServer will
// result in compilation errors.
type UnsafeFileServiceServer interface {
	mustEmbedUnimplementedFileServiceServer()
}

func RegisterFileServiceServer(s grpc.ServiceRegistrar, srv FileServiceServer) {
	s.RegisterService(&FileService_ServiceDesc, srv)
}

func _FileService_GetUploadPresignedURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUploadPresignedURLParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).GetUploadPresignedURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.file.v1.FileService/GetUploadPresignedURL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).GetUploadPresignedURL(ctx, req.(*GetUploadPresignedURLParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_QueryFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFileParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).QueryFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.file.v1.FileService/QueryFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).QueryFile(ctx, req.(*QueryFileParams))
	}
	return interceptor(ctx, in, info, handler)
}

// FileService_ServiceDesc is the grpc.ServiceDesc for FileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.file.v1.FileService",
	HandlerType: (*FileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUploadPresignedURL",
			Handler:    _FileService_GetUploadPresignedURL_Handler,
		},
		{
			MethodName: "QueryFile",
			Handler:    _FileService_QueryFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/file/v1/file_api.proto",
}
