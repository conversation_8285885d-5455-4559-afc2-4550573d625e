// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/session/v1/session_api.proto

package sessionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetSessionDataParams
type GetSessionDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSessionDataParams) Reset() {
	*x = GetSessionDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_session_v1_session_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionDataParams) ProtoMessage() {}

func (x *GetSessionDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_session_v1_session_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionDataParams.ProtoReflect.Descriptor instead.
func (*GetSessionDataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_session_v1_session_api_proto_rawDescGZIP(), []int{0}
}

// GetSessionDataResult
type GetSessionDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session id
	SessionId int64 `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	// account info
	Account *v1.AccountModel `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	// staff info
	Staff *v11.StaffModel `protobuf:"bytes,3,opt,name=staff,proto3" json:"staff,omitempty"`
	// enterprise info
	Enterprise *v11.EnterpriseModel `protobuf:"bytes,4,opt,name=enterprise,proto3" json:"enterprise,omitempty"`
}

func (x *GetSessionDataResult) Reset() {
	*x = GetSessionDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_session_v1_session_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSessionDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSessionDataResult) ProtoMessage() {}

func (x *GetSessionDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_session_v1_session_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSessionDataResult.ProtoReflect.Descriptor instead.
func (*GetSessionDataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_session_v1_session_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetSessionDataResult) GetSessionId() int64 {
	if x != nil {
		return x.SessionId
	}
	return 0
}

func (x *GetSessionDataResult) GetAccount() *v1.AccountModel {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetSessionDataResult) GetStaff() *v11.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *GetSessionDataResult) GetEnterprise() *v11.EnterpriseModel {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

var File_moego_enterprise_session_v1_session_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_session_v1_session_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x16, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x81, 0x02, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3f, 0x0a,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4b, 0x0a, 0x0a,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x32, 0x8a, 0x01, 0x0a, 0x0e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x78, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x86, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x3b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_session_v1_session_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_session_v1_session_api_proto_rawDescData = file_moego_enterprise_session_v1_session_api_proto_rawDesc
)

func file_moego_enterprise_session_v1_session_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_session_v1_session_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_session_v1_session_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_session_v1_session_api_proto_rawDescData)
	})
	return file_moego_enterprise_session_v1_session_api_proto_rawDescData
}

var file_moego_enterprise_session_v1_session_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_enterprise_session_v1_session_api_proto_goTypes = []interface{}{
	(*GetSessionDataParams)(nil), // 0: moego.enterprise.session.v1.GetSessionDataParams
	(*GetSessionDataResult)(nil), // 1: moego.enterprise.session.v1.GetSessionDataResult
	(*v1.AccountModel)(nil),      // 2: moego.models.account.v1.AccountModel
	(*v11.StaffModel)(nil),       // 3: moego.models.enterprise.v1.StaffModel
	(*v11.EnterpriseModel)(nil),  // 4: moego.models.enterprise.v1.EnterpriseModel
}
var file_moego_enterprise_session_v1_session_api_proto_depIdxs = []int32{
	2, // 0: moego.enterprise.session.v1.GetSessionDataResult.account:type_name -> moego.models.account.v1.AccountModel
	3, // 1: moego.enterprise.session.v1.GetSessionDataResult.staff:type_name -> moego.models.enterprise.v1.StaffModel
	4, // 2: moego.enterprise.session.v1.GetSessionDataResult.enterprise:type_name -> moego.models.enterprise.v1.EnterpriseModel
	0, // 3: moego.enterprise.session.v1.SessionService.GetSessionData:input_type -> moego.enterprise.session.v1.GetSessionDataParams
	1, // 4: moego.enterprise.session.v1.SessionService.GetSessionData:output_type -> moego.enterprise.session.v1.GetSessionDataResult
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_enterprise_session_v1_session_api_proto_init() }
func file_moego_enterprise_session_v1_session_api_proto_init() {
	if File_moego_enterprise_session_v1_session_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_session_v1_session_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_session_v1_session_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSessionDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_session_v1_session_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_session_v1_session_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_session_v1_session_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_session_v1_session_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_session_v1_session_api_proto = out.File
	file_moego_enterprise_session_v1_session_api_proto_rawDesc = nil
	file_moego_enterprise_session_v1_session_api_proto_goTypes = nil
	file_moego_enterprise_session_v1_session_api_proto_depIdxs = nil
}
