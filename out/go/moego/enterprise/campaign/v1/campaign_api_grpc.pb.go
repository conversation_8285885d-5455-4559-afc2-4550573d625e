// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/campaign/v1/campaign_api.proto

package campaignapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CampaignServiceClient is the client API for CampaignService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CampaignServiceClient interface {
	// Send a test email
	SendTestEmail(ctx context.Context, in *SendTestEmailParams, opts ...grpc.CallOption) (*SendTestEmailResult, error)
	// Create a campaign template
	CreateTemplate(ctx context.Context, in *CreateTemplateParams, opts ...grpc.CallOption) (*CreateTemplateResult, error)
	// Get a campaign template
	GetTemplate(ctx context.Context, in *GetTemplateParams, opts ...grpc.CallOption) (*GetTemplateResult, error)
	// Update a campaign template
	UpdateTemplate(ctx context.Context, in *UpdateTemplateParams, opts ...grpc.CallOption) (*UpdateTemplateResult, error)
	// List campaign templates
	ListTemplates(ctx context.Context, in *ListTemplatesParams, opts ...grpc.CallOption) (*ListTemplatesResult, error)
	// Push campaign templates to tenants
	PushTemplates(ctx context.Context, in *PushTemplatesParams, opts ...grpc.CallOption) (*PushTemplatesResult, error)
	// List template variables
	ListTemplatePlaceholders(ctx context.Context, in *ListTemplatePlaceholdersParams, opts ...grpc.CallOption) (*ListTemplatePlaceholdersResult, error)
}

type campaignServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCampaignServiceClient(cc grpc.ClientConnInterface) CampaignServiceClient {
	return &campaignServiceClient{cc}
}

func (c *campaignServiceClient) SendTestEmail(ctx context.Context, in *SendTestEmailParams, opts ...grpc.CallOption) (*SendTestEmailResult, error) {
	out := new(SendTestEmailResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/SendTestEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) CreateTemplate(ctx context.Context, in *CreateTemplateParams, opts ...grpc.CallOption) (*CreateTemplateResult, error) {
	out := new(CreateTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/CreateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) GetTemplate(ctx context.Context, in *GetTemplateParams, opts ...grpc.CallOption) (*GetTemplateResult, error) {
	out := new(GetTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/GetTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) UpdateTemplate(ctx context.Context, in *UpdateTemplateParams, opts ...grpc.CallOption) (*UpdateTemplateResult, error) {
	out := new(UpdateTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/UpdateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) ListTemplates(ctx context.Context, in *ListTemplatesParams, opts ...grpc.CallOption) (*ListTemplatesResult, error) {
	out := new(ListTemplatesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/ListTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) PushTemplates(ctx context.Context, in *PushTemplatesParams, opts ...grpc.CallOption) (*PushTemplatesResult, error) {
	out := new(PushTemplatesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/PushTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *campaignServiceClient) ListTemplatePlaceholders(ctx context.Context, in *ListTemplatePlaceholdersParams, opts ...grpc.CallOption) (*ListTemplatePlaceholdersResult, error) {
	out := new(ListTemplatePlaceholdersResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.campaign.v1.CampaignService/ListTemplatePlaceholders", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CampaignServiceServer is the server API for CampaignService service.
// All implementations must embed UnimplementedCampaignServiceServer
// for forward compatibility
type CampaignServiceServer interface {
	// Send a test email
	SendTestEmail(context.Context, *SendTestEmailParams) (*SendTestEmailResult, error)
	// Create a campaign template
	CreateTemplate(context.Context, *CreateTemplateParams) (*CreateTemplateResult, error)
	// Get a campaign template
	GetTemplate(context.Context, *GetTemplateParams) (*GetTemplateResult, error)
	// Update a campaign template
	UpdateTemplate(context.Context, *UpdateTemplateParams) (*UpdateTemplateResult, error)
	// List campaign templates
	ListTemplates(context.Context, *ListTemplatesParams) (*ListTemplatesResult, error)
	// Push campaign templates to tenants
	PushTemplates(context.Context, *PushTemplatesParams) (*PushTemplatesResult, error)
	// List template variables
	ListTemplatePlaceholders(context.Context, *ListTemplatePlaceholdersParams) (*ListTemplatePlaceholdersResult, error)
	mustEmbedUnimplementedCampaignServiceServer()
}

// UnimplementedCampaignServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCampaignServiceServer struct {
}

func (UnimplementedCampaignServiceServer) SendTestEmail(context.Context, *SendTestEmailParams) (*SendTestEmailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTestEmail not implemented")
}
func (UnimplementedCampaignServiceServer) CreateTemplate(context.Context, *CreateTemplateParams) (*CreateTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTemplate not implemented")
}
func (UnimplementedCampaignServiceServer) GetTemplate(context.Context, *GetTemplateParams) (*GetTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplate not implemented")
}
func (UnimplementedCampaignServiceServer) UpdateTemplate(context.Context, *UpdateTemplateParams) (*UpdateTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTemplate not implemented")
}
func (UnimplementedCampaignServiceServer) ListTemplates(context.Context, *ListTemplatesParams) (*ListTemplatesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplates not implemented")
}
func (UnimplementedCampaignServiceServer) PushTemplates(context.Context, *PushTemplatesParams) (*PushTemplatesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushTemplates not implemented")
}
func (UnimplementedCampaignServiceServer) ListTemplatePlaceholders(context.Context, *ListTemplatePlaceholdersParams) (*ListTemplatePlaceholdersResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePlaceholders not implemented")
}
func (UnimplementedCampaignServiceServer) mustEmbedUnimplementedCampaignServiceServer() {}

// UnsafeCampaignServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CampaignServiceServer will
// result in compilation errors.
type UnsafeCampaignServiceServer interface {
	mustEmbedUnimplementedCampaignServiceServer()
}

func RegisterCampaignServiceServer(s grpc.ServiceRegistrar, srv CampaignServiceServer) {
	s.RegisterService(&CampaignService_ServiceDesc, srv)
}

func _CampaignService_SendTestEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTestEmailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).SendTestEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/SendTestEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).SendTestEmail(ctx, req.(*SendTestEmailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_CreateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).CreateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/CreateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).CreateTemplate(ctx, req.(*CreateTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_GetTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).GetTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/GetTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).GetTemplate(ctx, req.(*GetTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_UpdateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).UpdateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/UpdateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).UpdateTemplate(ctx, req.(*UpdateTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_ListTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).ListTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/ListTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).ListTemplates(ctx, req.(*ListTemplatesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_PushTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTemplatesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).PushTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/PushTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).PushTemplates(ctx, req.(*PushTemplatesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CampaignService_ListTemplatePlaceholders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePlaceholdersParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CampaignServiceServer).ListTemplatePlaceholders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.campaign.v1.CampaignService/ListTemplatePlaceholders",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CampaignServiceServer).ListTemplatePlaceholders(ctx, req.(*ListTemplatePlaceholdersParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CampaignService_ServiceDesc is the grpc.ServiceDesc for CampaignService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CampaignService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.campaign.v1.CampaignService",
	HandlerType: (*CampaignServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendTestEmail",
			Handler:    _CampaignService_SendTestEmail_Handler,
		},
		{
			MethodName: "CreateTemplate",
			Handler:    _CampaignService_CreateTemplate_Handler,
		},
		{
			MethodName: "GetTemplate",
			Handler:    _CampaignService_GetTemplate_Handler,
		},
		{
			MethodName: "UpdateTemplate",
			Handler:    _CampaignService_UpdateTemplate_Handler,
		},
		{
			MethodName: "ListTemplates",
			Handler:    _CampaignService_ListTemplates_Handler,
		},
		{
			MethodName: "PushTemplates",
			Handler:    _CampaignService_PushTemplates_Handler,
		},
		{
			MethodName: "ListTemplatePlaceholders",
			Handler:    _CampaignService_ListTemplatePlaceholders_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/campaign/v1/campaign_api.proto",
}
