// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/branded_app/v1/branded_location_service.proto

package brandedappsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/branded_app/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The request message for ListBrandedLocations
type ListBrandedLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the branded app id
	BrandedAppId string `protobuf:"bytes,1,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
}

func (x *ListBrandedLocationsRequest) Reset() {
	*x = ListBrandedLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBrandedLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBrandedLocationsRequest) ProtoMessage() {}

func (x *ListBrandedLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBrandedLocationsRequest.ProtoReflect.Descriptor instead.
func (*ListBrandedLocationsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_branded_app_v1_branded_location_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListBrandedLocationsRequest) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

// The response message for ListBrandedLocations
type ListBrandedLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the list of branded locations
	Locations []*v1.BrandedLocationModel `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *ListBrandedLocationsResponse) Reset() {
	*x = ListBrandedLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBrandedLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBrandedLocationsResponse) ProtoMessage() {}

func (x *ListBrandedLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBrandedLocationsResponse.ProtoReflect.Descriptor instead.
func (*ListBrandedLocationsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_branded_app_v1_branded_location_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListBrandedLocationsResponse) GetLocations() []*v1.BrandedLocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

var File_moego_service_branded_app_v1_branded_location_service_proto protoreflect.FileDescriptor

var file_moego_service_branded_app_v1_branded_location_service_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x39, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x4d, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01,
	0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x22, 0x6f,
	0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f,
	0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32,
	0xa8, 0x01, 0x0a, 0x16, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x14, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8b, 0x01, 0x0a, 0x24, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x61, 0x70, 0x70, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_branded_app_v1_branded_location_service_proto_rawDescOnce sync.Once
	file_moego_service_branded_app_v1_branded_location_service_proto_rawDescData = file_moego_service_branded_app_v1_branded_location_service_proto_rawDesc
)

func file_moego_service_branded_app_v1_branded_location_service_proto_rawDescGZIP() []byte {
	file_moego_service_branded_app_v1_branded_location_service_proto_rawDescOnce.Do(func() {
		file_moego_service_branded_app_v1_branded_location_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_branded_app_v1_branded_location_service_proto_rawDescData)
	})
	return file_moego_service_branded_app_v1_branded_location_service_proto_rawDescData
}

var file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_branded_app_v1_branded_location_service_proto_goTypes = []interface{}{
	(*ListBrandedLocationsRequest)(nil),  // 0: moego.service.branded_app.v1.ListBrandedLocationsRequest
	(*ListBrandedLocationsResponse)(nil), // 1: moego.service.branded_app.v1.ListBrandedLocationsResponse
	(*v1.BrandedLocationModel)(nil),      // 2: moego.models.branded_app.v1.BrandedLocationModel
}
var file_moego_service_branded_app_v1_branded_location_service_proto_depIdxs = []int32{
	2, // 0: moego.service.branded_app.v1.ListBrandedLocationsResponse.locations:type_name -> moego.models.branded_app.v1.BrandedLocationModel
	0, // 1: moego.service.branded_app.v1.BrandedLocationService.ListBrandedLocations:input_type -> moego.service.branded_app.v1.ListBrandedLocationsRequest
	1, // 2: moego.service.branded_app.v1.BrandedLocationService.ListBrandedLocations:output_type -> moego.service.branded_app.v1.ListBrandedLocationsResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_branded_app_v1_branded_location_service_proto_init() }
func file_moego_service_branded_app_v1_branded_location_service_proto_init() {
	if File_moego_service_branded_app_v1_branded_location_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBrandedLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBrandedLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_branded_app_v1_branded_location_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_branded_app_v1_branded_location_service_proto_goTypes,
		DependencyIndexes: file_moego_service_branded_app_v1_branded_location_service_proto_depIdxs,
		MessageInfos:      file_moego_service_branded_app_v1_branded_location_service_proto_msgTypes,
	}.Build()
	File_moego_service_branded_app_v1_branded_location_service_proto = out.File
	file_moego_service_branded_app_v1_branded_location_service_proto_rawDesc = nil
	file_moego_service_branded_app_v1_branded_location_service_proto_goTypes = nil
	file_moego_service_branded_app_v1_branded_location_service_proto_depIdxs = nil
}
