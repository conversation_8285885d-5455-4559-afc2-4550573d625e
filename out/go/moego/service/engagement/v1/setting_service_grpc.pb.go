// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/engagement/v1/setting_service.proto

package engagementsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SettingServiceClient is the client API for SettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SettingServiceClient interface {
	// GetSetting
	GetSetting(ctx context.Context, in *GetSettingRequest, opts ...grpc.CallOption) (*GetSettingResponse, error)
	// UpdateSetting
	UpdateSetting(ctx context.Context, in *UpdateSettingRequest, opts ...grpc.CallOption) (*UpdateSettingResponse, error)
	// tmp calling seats
	TmpCallingSeats(ctx context.Context, in *TmpCallingSeatsRequest, opts ...grpc.CallOption) (*TmpCallingSeatsResponse, error)
}

type settingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSettingServiceClient(cc grpc.ClientConnInterface) SettingServiceClient {
	return &settingServiceClient{cc}
}

func (c *settingServiceClient) GetSetting(ctx context.Context, in *GetSettingRequest, opts ...grpc.CallOption) (*GetSettingResponse, error) {
	out := new(GetSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.engagement.v1.SettingService/GetSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) UpdateSetting(ctx context.Context, in *UpdateSettingRequest, opts ...grpc.CallOption) (*UpdateSettingResponse, error) {
	out := new(UpdateSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.engagement.v1.SettingService/UpdateSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) TmpCallingSeats(ctx context.Context, in *TmpCallingSeatsRequest, opts ...grpc.CallOption) (*TmpCallingSeatsResponse, error) {
	out := new(TmpCallingSeatsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.engagement.v1.SettingService/TmpCallingSeats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettingServiceServer is the server API for SettingService service.
// All implementations must embed UnimplementedSettingServiceServer
// for forward compatibility
type SettingServiceServer interface {
	// GetSetting
	GetSetting(context.Context, *GetSettingRequest) (*GetSettingResponse, error)
	// UpdateSetting
	UpdateSetting(context.Context, *UpdateSettingRequest) (*UpdateSettingResponse, error)
	// tmp calling seats
	TmpCallingSeats(context.Context, *TmpCallingSeatsRequest) (*TmpCallingSeatsResponse, error)
	mustEmbedUnimplementedSettingServiceServer()
}

// UnimplementedSettingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSettingServiceServer struct {
}

func (UnimplementedSettingServiceServer) GetSetting(context.Context, *GetSettingRequest) (*GetSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSetting not implemented")
}
func (UnimplementedSettingServiceServer) UpdateSetting(context.Context, *UpdateSettingRequest) (*UpdateSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSetting not implemented")
}
func (UnimplementedSettingServiceServer) TmpCallingSeats(context.Context, *TmpCallingSeatsRequest) (*TmpCallingSeatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TmpCallingSeats not implemented")
}
func (UnimplementedSettingServiceServer) mustEmbedUnimplementedSettingServiceServer() {}

// UnsafeSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SettingServiceServer will
// result in compilation errors.
type UnsafeSettingServiceServer interface {
	mustEmbedUnimplementedSettingServiceServer()
}

func RegisterSettingServiceServer(s grpc.ServiceRegistrar, srv SettingServiceServer) {
	s.RegisterService(&SettingService_ServiceDesc, srv)
}

func _SettingService_GetSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).GetSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.engagement.v1.SettingService/GetSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).GetSetting(ctx, req.(*GetSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_UpdateSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).UpdateSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.engagement.v1.SettingService/UpdateSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).UpdateSetting(ctx, req.(*UpdateSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_TmpCallingSeats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TmpCallingSeatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).TmpCallingSeats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.engagement.v1.SettingService/TmpCallingSeats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).TmpCallingSeats(ctx, req.(*TmpCallingSeatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SettingService_ServiceDesc is the grpc.ServiceDesc for SettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.engagement.v1.SettingService",
	HandlerType: (*SettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSetting",
			Handler:    _SettingService_GetSetting_Handler,
		},
		{
			MethodName: "UpdateSetting",
			Handler:    _SettingService_UpdateSetting_Handler,
		},
		{
			MethodName: "TmpCallingSeats",
			Handler:    _SettingService_TmpCallingSeats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/engagement/v1/setting_service.proto",
}
