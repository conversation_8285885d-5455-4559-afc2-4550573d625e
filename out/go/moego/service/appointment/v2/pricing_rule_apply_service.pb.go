// @since 2025-03-05 10:27:11
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v2/pricing_rule_apply_service.proto

package appointmentsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pricing_rule_apply request
type ListPricingRuleApplyLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source id
	SourceId int64 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v2.PricingRuleApplySourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=moego.models.appointment.v2.PricingRuleApplySourceType" json:"source_type,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListPricingRuleApplyLogRequest) Reset() {
	*x = ListPricingRuleApplyLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleApplyLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleApplyLogRequest) ProtoMessage() {}

func (x *ListPricingRuleApplyLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleApplyLogRequest.ProtoReflect.Descriptor instead.
func (*ListPricingRuleApplyLogRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListPricingRuleApplyLogRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *ListPricingRuleApplyLogRequest) GetSourceType() v2.PricingRuleApplySourceType {
	if x != nil {
		return x.SourceType
	}
	return v2.PricingRuleApplySourceType(0)
}

func (x *ListPricingRuleApplyLogRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// list pricing_rule_apply response
type ListPricingRuleApplyLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing_rule_apply
	PricingRuleApplyLogs []*v2.PricingRuleApplyLogModel `protobuf:"bytes,1,rep,name=pricing_rule_apply_logs,json=pricingRuleApplyLogs,proto3" json:"pricing_rule_apply_logs,omitempty"`
}

func (x *ListPricingRuleApplyLogResponse) Reset() {
	*x = ListPricingRuleApplyLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleApplyLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleApplyLogResponse) ProtoMessage() {}

func (x *ListPricingRuleApplyLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleApplyLogResponse.ProtoReflect.Descriptor instead.
func (*ListPricingRuleApplyLogResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListPricingRuleApplyLogResponse) GetPricingRuleApplyLogs() []*v2.PricingRuleApplyLogModel {
	if x != nil {
		return x.PricingRuleApplyLogs
	}
	return nil
}

// Update upcoming appointment pet details request
type UpdateUpcomingAppointmentUsingPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// associated service ids
	// Appointments with associated service IDs will be updated
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	// The staff id of the staff who updated the service
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) Reset() {
	*x = UpdateUpcomingAppointmentUsingPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingAppointmentUsingPricingRuleRequest) ProtoMessage() {}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingAppointmentUsingPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingAppointmentUsingPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Update upcoming appt pet details response
type UpdateUpcomingAppointmentUsingPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected appointment count
	AffectedAppointmentCount int64 `protobuf:"varint,1,opt,name=affected_appointment_count,json=affectedAppointmentCount,proto3" json:"affected_appointment_count,omitempty"`
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleResponse) Reset() {
	*x = UpdateUpcomingAppointmentUsingPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpcomingAppointmentUsingPricingRuleResponse) ProtoMessage() {}

func (x *UpdateUpcomingAppointmentUsingPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpcomingAppointmentUsingPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateUpcomingAppointmentUsingPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUpcomingAppointmentUsingPricingRuleResponse) GetAffectedAppointmentCount() int64 {
	if x != nil {
		return x.AffectedAppointmentCount
	}
	return 0
}

// apply pricing rule request
type ApplyPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v2.PricingRuleApplySourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=moego.models.appointment.v2.PricingRuleApplySourceType" json:"source_type,omitempty"`
	// pet detail list
	PetDetails []*v21.PetDetailCalculateDef `protobuf:"bytes,6,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *ApplyPricingRuleRequest) Reset() {
	*x = ApplyPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyPricingRuleRequest) ProtoMessage() {}

func (x *ApplyPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*ApplyPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{4}
}

func (x *ApplyPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ApplyPricingRuleRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ApplyPricingRuleRequest) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *ApplyPricingRuleRequest) GetSourceType() v2.PricingRuleApplySourceType {
	if x != nil {
		return x.SourceType
	}
	return v2.PricingRuleApplySourceType(0)
}

func (x *ApplyPricingRuleRequest) GetPetDetails() []*v21.PetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// apply pricing rule response
type ApplyPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v21.PetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *ApplyPricingRuleResponse) Reset() {
	*x = ApplyPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyPricingRuleResponse) ProtoMessage() {}

func (x *ApplyPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*ApplyPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP(), []int{5}
}

func (x *ApplyPricingRuleResponse) GetPetDetails() []*v21.PetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

var File_moego_service_appointment_v2_pricing_rule_apply_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x3a, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd2, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x0b, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x17, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x30, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22,
	0x71, 0x0a, 0x31, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xc7, 0x02, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x72, 0x0a, 0x18,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x32, 0x85, 0x04, 0x0a, 0x17, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x96, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xcc, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x55, 0x73, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x32, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescData = file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDesc
)

func file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDescData
}

var file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_service_appointment_v2_pricing_rule_apply_service_proto_goTypes = []interface{}{
	(*ListPricingRuleApplyLogRequest)(nil),                    // 0: moego.service.appointment.v2.ListPricingRuleApplyLogRequest
	(*ListPricingRuleApplyLogResponse)(nil),                   // 1: moego.service.appointment.v2.ListPricingRuleApplyLogResponse
	(*UpdateUpcomingAppointmentUsingPricingRuleRequest)(nil),  // 2: moego.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleRequest
	(*UpdateUpcomingAppointmentUsingPricingRuleResponse)(nil), // 3: moego.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse
	(*ApplyPricingRuleRequest)(nil),                           // 4: moego.service.appointment.v2.ApplyPricingRuleRequest
	(*ApplyPricingRuleResponse)(nil),                          // 5: moego.service.appointment.v2.ApplyPricingRuleResponse
	(v2.PricingRuleApplySourceType)(0),                        // 6: moego.models.appointment.v2.PricingRuleApplySourceType
	(*v2.PricingRuleApplyLogModel)(nil),                       // 7: moego.models.appointment.v2.PricingRuleApplyLogModel
	(*v21.PetDetailCalculateDef)(nil),                         // 8: moego.models.offering.v2.PetDetailCalculateDef
	(*v21.PetDetailCalculateResultDef)(nil),                   // 9: moego.models.offering.v2.PetDetailCalculateResultDef
}
var file_moego_service_appointment_v2_pricing_rule_apply_service_proto_depIdxs = []int32{
	6, // 0: moego.service.appointment.v2.ListPricingRuleApplyLogRequest.source_type:type_name -> moego.models.appointment.v2.PricingRuleApplySourceType
	7, // 1: moego.service.appointment.v2.ListPricingRuleApplyLogResponse.pricing_rule_apply_logs:type_name -> moego.models.appointment.v2.PricingRuleApplyLogModel
	6, // 2: moego.service.appointment.v2.ApplyPricingRuleRequest.source_type:type_name -> moego.models.appointment.v2.PricingRuleApplySourceType
	8, // 3: moego.service.appointment.v2.ApplyPricingRuleRequest.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateDef
	9, // 4: moego.service.appointment.v2.ApplyPricingRuleResponse.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateResultDef
	0, // 5: moego.service.appointment.v2.PricingRuleApplyService.ListPricingRuleApplyLog:input_type -> moego.service.appointment.v2.ListPricingRuleApplyLogRequest
	2, // 6: moego.service.appointment.v2.PricingRuleApplyService.UpdateUpcomingAppointmentUsingPricingRule:input_type -> moego.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleRequest
	4, // 7: moego.service.appointment.v2.PricingRuleApplyService.ApplyPricingRule:input_type -> moego.service.appointment.v2.ApplyPricingRuleRequest
	1, // 8: moego.service.appointment.v2.PricingRuleApplyService.ListPricingRuleApplyLog:output_type -> moego.service.appointment.v2.ListPricingRuleApplyLogResponse
	3, // 9: moego.service.appointment.v2.PricingRuleApplyService.UpdateUpcomingAppointmentUsingPricingRule:output_type -> moego.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse
	5, // 10: moego.service.appointment.v2.PricingRuleApplyService.ApplyPricingRule:output_type -> moego.service.appointment.v2.ApplyPricingRuleResponse
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v2_pricing_rule_apply_service_proto_init() }
func file_moego_service_appointment_v2_pricing_rule_apply_service_proto_init() {
	if File_moego_service_appointment_v2_pricing_rule_apply_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleApplyLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleApplyLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingAppointmentUsingPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpcomingAppointmentUsingPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v2_pricing_rule_apply_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v2_pricing_rule_apply_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v2_pricing_rule_apply_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v2_pricing_rule_apply_service_proto = out.File
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_rawDesc = nil
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_goTypes = nil
	file_moego_service_appointment_v2_pricing_rule_apply_service_proto_depIdxs = nil
}
