// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/fulfillment/v1/group_class_attendance_service.proto

package fulfillmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassAttendanceServiceClient is the client API for GroupClassAttendanceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassAttendanceServiceClient interface {
	// List group class attendance
	ListAttendances(ctx context.Context, in *ListAttendancesRequest, opts ...grpc.CallOption) (*ListAttendancesResponse, error)
}

type groupClassAttendanceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassAttendanceServiceClient(cc grpc.ClientConnInterface) GroupClassAttendanceServiceClient {
	return &groupClassAttendanceServiceClient{cc}
}

func (c *groupClassAttendanceServiceClient) ListAttendances(ctx context.Context, in *ListAttendancesRequest, opts ...grpc.CallOption) (*ListAttendancesResponse, error) {
	out := new(ListAttendancesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.GroupClassAttendanceService/ListAttendances", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupClassAttendanceServiceServer is the server API for GroupClassAttendanceService service.
// All implementations must embed UnimplementedGroupClassAttendanceServiceServer
// for forward compatibility
type GroupClassAttendanceServiceServer interface {
	// List group class attendance
	ListAttendances(context.Context, *ListAttendancesRequest) (*ListAttendancesResponse, error)
	mustEmbedUnimplementedGroupClassAttendanceServiceServer()
}

// UnimplementedGroupClassAttendanceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassAttendanceServiceServer struct {
}

func (UnimplementedGroupClassAttendanceServiceServer) ListAttendances(context.Context, *ListAttendancesRequest) (*ListAttendancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAttendances not implemented")
}
func (UnimplementedGroupClassAttendanceServiceServer) mustEmbedUnimplementedGroupClassAttendanceServiceServer() {
}

// UnsafeGroupClassAttendanceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassAttendanceServiceServer will
// result in compilation errors.
type UnsafeGroupClassAttendanceServiceServer interface {
	mustEmbedUnimplementedGroupClassAttendanceServiceServer()
}

func RegisterGroupClassAttendanceServiceServer(s grpc.ServiceRegistrar, srv GroupClassAttendanceServiceServer) {
	s.RegisterService(&GroupClassAttendanceService_ServiceDesc, srv)
}

func _GroupClassAttendanceService_ListAttendances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAttendancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassAttendanceServiceServer).ListAttendances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.GroupClassAttendanceService/ListAttendances",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassAttendanceServiceServer).ListAttendances(ctx, req.(*ListAttendancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupClassAttendanceService_ServiceDesc is the grpc.ServiceDesc for GroupClassAttendanceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassAttendanceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.fulfillment.v1.GroupClassAttendanceService",
	HandlerType: (*GroupClassAttendanceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAttendances",
			Handler:    _GroupClassAttendanceService_ListAttendances_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/fulfillment/v1/group_class_attendance_service.proto",
}
