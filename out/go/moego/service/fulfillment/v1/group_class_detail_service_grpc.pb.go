// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/fulfillment/v1/group_class_detail_service.proto

package fulfillmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassDetailServiceClient is the client API for GroupClassDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassDetailServiceClient interface {
	// list group_class_detail
	ListGroupClassDetails(ctx context.Context, in *ListGroupClassDetailsRequest, opts ...grpc.CallOption) (*ListGroupClassDetailsResponse, error)
	// List pets enrolled in a group class instance
	ListEnrolledPets(ctx context.Context, in *ListEnrolledPetsRequest, opts ...grpc.CallOption) (*ListEnrolledPetsResponse, error)
	// List uncheck in pets
	ListUncheckInPets(ctx context.Context, in *ListUncheckInPetsRequest, opts ...grpc.CallOption) (*ListUncheckInPetsResponse, error)
	// Check in group class session
	// It will be retrieved within the current business day
	CheckInGroupClassSession(ctx context.Context, in *CheckInGroupClassSessionRequest, opts ...grpc.CallOption) (*CheckInGroupClassSessionResponse, error)
}

type groupClassDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassDetailServiceClient(cc grpc.ClientConnInterface) GroupClassDetailServiceClient {
	return &groupClassDetailServiceClient{cc}
}

func (c *groupClassDetailServiceClient) ListGroupClassDetails(ctx context.Context, in *ListGroupClassDetailsRequest, opts ...grpc.CallOption) (*ListGroupClassDetailsResponse, error) {
	out := new(ListGroupClassDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.GroupClassDetailService/ListGroupClassDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassDetailServiceClient) ListEnrolledPets(ctx context.Context, in *ListEnrolledPetsRequest, opts ...grpc.CallOption) (*ListEnrolledPetsResponse, error) {
	out := new(ListEnrolledPetsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.GroupClassDetailService/ListEnrolledPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassDetailServiceClient) ListUncheckInPets(ctx context.Context, in *ListUncheckInPetsRequest, opts ...grpc.CallOption) (*ListUncheckInPetsResponse, error) {
	out := new(ListUncheckInPetsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.GroupClassDetailService/ListUncheckInPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassDetailServiceClient) CheckInGroupClassSession(ctx context.Context, in *CheckInGroupClassSessionRequest, opts ...grpc.CallOption) (*CheckInGroupClassSessionResponse, error) {
	out := new(CheckInGroupClassSessionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.fulfillment.v1.GroupClassDetailService/CheckInGroupClassSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupClassDetailServiceServer is the server API for GroupClassDetailService service.
// All implementations must embed UnimplementedGroupClassDetailServiceServer
// for forward compatibility
type GroupClassDetailServiceServer interface {
	// list group_class_detail
	ListGroupClassDetails(context.Context, *ListGroupClassDetailsRequest) (*ListGroupClassDetailsResponse, error)
	// List pets enrolled in a group class instance
	ListEnrolledPets(context.Context, *ListEnrolledPetsRequest) (*ListEnrolledPetsResponse, error)
	// List uncheck in pets
	ListUncheckInPets(context.Context, *ListUncheckInPetsRequest) (*ListUncheckInPetsResponse, error)
	// Check in group class session
	// It will be retrieved within the current business day
	CheckInGroupClassSession(context.Context, *CheckInGroupClassSessionRequest) (*CheckInGroupClassSessionResponse, error)
	mustEmbedUnimplementedGroupClassDetailServiceServer()
}

// UnimplementedGroupClassDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassDetailServiceServer struct {
}

func (UnimplementedGroupClassDetailServiceServer) ListGroupClassDetails(context.Context, *ListGroupClassDetailsRequest) (*ListGroupClassDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroupClassDetails not implemented")
}
func (UnimplementedGroupClassDetailServiceServer) ListEnrolledPets(context.Context, *ListEnrolledPetsRequest) (*ListEnrolledPetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnrolledPets not implemented")
}
func (UnimplementedGroupClassDetailServiceServer) ListUncheckInPets(context.Context, *ListUncheckInPetsRequest) (*ListUncheckInPetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUncheckInPets not implemented")
}
func (UnimplementedGroupClassDetailServiceServer) CheckInGroupClassSession(context.Context, *CheckInGroupClassSessionRequest) (*CheckInGroupClassSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckInGroupClassSession not implemented")
}
func (UnimplementedGroupClassDetailServiceServer) mustEmbedUnimplementedGroupClassDetailServiceServer() {
}

// UnsafeGroupClassDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassDetailServiceServer will
// result in compilation errors.
type UnsafeGroupClassDetailServiceServer interface {
	mustEmbedUnimplementedGroupClassDetailServiceServer()
}

func RegisterGroupClassDetailServiceServer(s grpc.ServiceRegistrar, srv GroupClassDetailServiceServer) {
	s.RegisterService(&GroupClassDetailService_ServiceDesc, srv)
}

func _GroupClassDetailService_ListGroupClassDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGroupClassDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassDetailServiceServer).ListGroupClassDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.GroupClassDetailService/ListGroupClassDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassDetailServiceServer).ListGroupClassDetails(ctx, req.(*ListGroupClassDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassDetailService_ListEnrolledPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnrolledPetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassDetailServiceServer).ListEnrolledPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.GroupClassDetailService/ListEnrolledPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassDetailServiceServer).ListEnrolledPets(ctx, req.(*ListEnrolledPetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassDetailService_ListUncheckInPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUncheckInPetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassDetailServiceServer).ListUncheckInPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.GroupClassDetailService/ListUncheckInPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassDetailServiceServer).ListUncheckInPets(ctx, req.(*ListUncheckInPetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassDetailService_CheckInGroupClassSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInGroupClassSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassDetailServiceServer).CheckInGroupClassSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.fulfillment.v1.GroupClassDetailService/CheckInGroupClassSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassDetailServiceServer).CheckInGroupClassSession(ctx, req.(*CheckInGroupClassSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupClassDetailService_ServiceDesc is the grpc.ServiceDesc for GroupClassDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.fulfillment.v1.GroupClassDetailService",
	HandlerType: (*GroupClassDetailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListGroupClassDetails",
			Handler:    _GroupClassDetailService_ListGroupClassDetails_Handler,
		},
		{
			MethodName: "ListEnrolledPets",
			Handler:    _GroupClassDetailService_ListEnrolledPets_Handler,
		},
		{
			MethodName: "ListUncheckInPets",
			Handler:    _GroupClassDetailService_ListUncheckInPets_Handler,
		},
		{
			MethodName: "CheckInGroupClassSession",
			Handler:    _GroupClassDetailService_CheckInGroupClassSession_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/fulfillment/v1/group_class_detail_service.proto",
}
