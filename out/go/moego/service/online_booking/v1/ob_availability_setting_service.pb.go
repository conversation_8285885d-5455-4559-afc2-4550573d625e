// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/ob_availability_setting_service.proto

package onlinebookingsvcpb

import (
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get boarding service availability setting request
type GetBoardingServiceAvailabilitySettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
}

func (x *GetBoardingServiceAvailabilitySettingRequest) Reset() {
	*x = GetBoardingServiceAvailabilitySettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBoardingServiceAvailabilitySettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBoardingServiceAvailabilitySettingRequest) ProtoMessage() {}

func (x *GetBoardingServiceAvailabilitySettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBoardingServiceAvailabilitySettingRequest.ProtoReflect.Descriptor instead.
func (*GetBoardingServiceAvailabilitySettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetBoardingServiceAvailabilitySettingRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// get boarding service availability setting response
type GetBoardingServiceAvailabilitySettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the boarding service availability setting
	BoardingServiceAvailabilitySetting *v11.BoardingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=boarding_service_availability_setting,json=boardingServiceAvailabilitySetting,proto3" json:"boarding_service_availability_setting,omitempty"`
}

func (x *GetBoardingServiceAvailabilitySettingResponse) Reset() {
	*x = GetBoardingServiceAvailabilitySettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBoardingServiceAvailabilitySettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBoardingServiceAvailabilitySettingResponse) ProtoMessage() {}

func (x *GetBoardingServiceAvailabilitySettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBoardingServiceAvailabilitySettingResponse.ProtoReflect.Descriptor instead.
func (*GetBoardingServiceAvailabilitySettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetBoardingServiceAvailabilitySettingResponse) GetBoardingServiceAvailabilitySetting() *v11.BoardingServiceAvailabilityModel {
	if x != nil {
		return x.BoardingServiceAvailabilitySetting
	}
	return nil
}

// update boarding service availability setting request
type UpdateBoardingServiceAvailabilitySettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// the boarding service availability setting
	BoardingServiceAvailabilitySetting *v11.BoardingServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=boarding_service_availability_setting,json=boardingServiceAvailabilitySetting,proto3" json:"boarding_service_availability_setting,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) Reset() {
	*x = UpdateBoardingServiceAvailabilitySettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingServiceAvailabilitySettingRequest) ProtoMessage() {}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingServiceAvailabilitySettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateBoardingServiceAvailabilitySettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) GetBoardingServiceAvailabilitySetting() *v11.BoardingServiceAvailabilityUpdateDef {
	if x != nil {
		return x.BoardingServiceAvailabilitySetting
	}
	return nil
}

func (x *UpdateBoardingServiceAvailabilitySettingRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// update boarding service availability setting response
type UpdateBoardingServiceAvailabilitySettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the boarding service availability setting
	BoardingServiceAvailabilitySetting *v11.BoardingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=boarding_service_availability_setting,json=boardingServiceAvailabilitySetting,proto3" json:"boarding_service_availability_setting,omitempty"`
}

func (x *UpdateBoardingServiceAvailabilitySettingResponse) Reset() {
	*x = UpdateBoardingServiceAvailabilitySettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingServiceAvailabilitySettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingServiceAvailabilitySettingResponse) ProtoMessage() {}

func (x *UpdateBoardingServiceAvailabilitySettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingServiceAvailabilitySettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateBoardingServiceAvailabilitySettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBoardingServiceAvailabilitySettingResponse) GetBoardingServiceAvailabilitySetting() *v11.BoardingServiceAvailabilityModel {
	if x != nil {
		return x.BoardingServiceAvailabilitySetting
	}
	return nil
}

// get daycare service availability setting request
type GetDaycareServiceAvailabilitySettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
}

func (x *GetDaycareServiceAvailabilitySettingRequest) Reset() {
	*x = GetDaycareServiceAvailabilitySettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaycareServiceAvailabilitySettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaycareServiceAvailabilitySettingRequest) ProtoMessage() {}

func (x *GetDaycareServiceAvailabilitySettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaycareServiceAvailabilitySettingRequest.ProtoReflect.Descriptor instead.
func (*GetDaycareServiceAvailabilitySettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetDaycareServiceAvailabilitySettingRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// get daycare service availability setting response
type GetDaycareServiceAvailabilitySettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the daycare service availability setting
	DaycareServiceAvailabilitySetting *v11.DaycareServiceAvailabilityModel `protobuf:"bytes,1,opt,name=daycare_service_availability_setting,json=daycareServiceAvailabilitySetting,proto3" json:"daycare_service_availability_setting,omitempty"`
}

func (x *GetDaycareServiceAvailabilitySettingResponse) Reset() {
	*x = GetDaycareServiceAvailabilitySettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDaycareServiceAvailabilitySettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDaycareServiceAvailabilitySettingResponse) ProtoMessage() {}

func (x *GetDaycareServiceAvailabilitySettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDaycareServiceAvailabilitySettingResponse.ProtoReflect.Descriptor instead.
func (*GetDaycareServiceAvailabilitySettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetDaycareServiceAvailabilitySettingResponse) GetDaycareServiceAvailabilitySetting() *v11.DaycareServiceAvailabilityModel {
	if x != nil {
		return x.DaycareServiceAvailabilitySetting
	}
	return nil
}

// update daycare service availability setting request
type UpdateDaycareServiceAvailabilitySettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// the daycare service availability setting
	DaycareServiceAvailabilitySetting *v11.DaycareServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=daycare_service_availability_setting,json=daycareServiceAvailabilitySetting,proto3" json:"daycare_service_availability_setting,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) Reset() {
	*x = UpdateDaycareServiceAvailabilitySettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDaycareServiceAvailabilitySettingRequest) ProtoMessage() {}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDaycareServiceAvailabilitySettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateDaycareServiceAvailabilitySettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) GetDaycareServiceAvailabilitySetting() *v11.DaycareServiceAvailabilityUpdateDef {
	if x != nil {
		return x.DaycareServiceAvailabilitySetting
	}
	return nil
}

func (x *UpdateDaycareServiceAvailabilitySettingRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// update daycare service availability setting response
type UpdateDaycareServiceAvailabilitySettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the daycare service availability setting
	DaycareServiceAvailabilitySetting *v11.DaycareServiceAvailabilityModel `protobuf:"bytes,1,opt,name=daycare_service_availability_setting,json=daycareServiceAvailabilitySetting,proto3" json:"daycare_service_availability_setting,omitempty"`
}

func (x *UpdateDaycareServiceAvailabilitySettingResponse) Reset() {
	*x = UpdateDaycareServiceAvailabilitySettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDaycareServiceAvailabilitySettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDaycareServiceAvailabilitySettingResponse) ProtoMessage() {}

func (x *UpdateDaycareServiceAvailabilitySettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDaycareServiceAvailabilitySettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateDaycareServiceAvailabilitySettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateDaycareServiceAvailabilitySettingResponse) GetDaycareServiceAvailabilitySetting() *v11.DaycareServiceAvailabilityModel {
	if x != nil {
		return x.DaycareServiceAvailabilitySetting
	}
	return nil
}

// query available pet type request
type QueryAvailablePetTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *QueryAvailablePetTypeRequest) Reset() {
	*x = QueryAvailablePetTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAvailablePetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAvailablePetTypeRequest) ProtoMessage() {}

func (x *QueryAvailablePetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAvailablePetTypeRequest.ProtoReflect.Descriptor instead.
func (*QueryAvailablePetTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{8}
}

func (x *QueryAvailablePetTypeRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *QueryAvailablePetTypeRequest) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

// query available pet type response
type QueryAvailablePetTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available pet type
	AvailablePetType []v13.PetType `protobuf:"varint,1,rep,packed,name=available_pet_type,json=availablePetType,proto3,enum=moego.models.customer.v1.PetType" json:"available_pet_type,omitempty"`
}

func (x *QueryAvailablePetTypeResponse) Reset() {
	*x = QueryAvailablePetTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAvailablePetTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAvailablePetTypeResponse) ProtoMessage() {}

func (x *QueryAvailablePetTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAvailablePetTypeResponse.ProtoReflect.Descriptor instead.
func (*QueryAvailablePetTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{9}
}

func (x *QueryAvailablePetTypeResponse) GetAvailablePetType() []v13.PetType {
	if x != nil {
		return x.AvailablePetType
	}
	return nil
}

// query available booking date range request
type QueryAvailableBookingDateRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *QueryAvailableBookingDateRangeRequest) Reset() {
	*x = QueryAvailableBookingDateRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAvailableBookingDateRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAvailableBookingDateRangeRequest) ProtoMessage() {}

func (x *QueryAvailableBookingDateRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAvailableBookingDateRangeRequest.ProtoReflect.Descriptor instead.
func (*QueryAvailableBookingDateRangeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{10}
}

func (x *QueryAvailableBookingDateRangeRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *QueryAvailableBookingDateRangeRequest) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

// query available booking date range response
type QueryAvailableBookingDateRangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// from date
	FromDate *date.Date `protobuf:"bytes,1,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// to date
	ToDate *date.Date `protobuf:"bytes,2,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
}

func (x *QueryAvailableBookingDateRangeResponse) Reset() {
	*x = QueryAvailableBookingDateRangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAvailableBookingDateRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAvailableBookingDateRangeResponse) ProtoMessage() {}

func (x *QueryAvailableBookingDateRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAvailableBookingDateRangeResponse.ProtoReflect.Descriptor instead.
func (*QueryAvailableBookingDateRangeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{11}
}

func (x *QueryAvailableBookingDateRangeResponse) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *QueryAvailableBookingDateRangeResponse) GetToDate() *date.Date {
	if x != nil {
		return x.ToDate
	}
	return nil
}

// get evaluation service availability request
type GetEvaluationServiceAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
}

func (x *GetEvaluationServiceAvailabilityRequest) Reset() {
	*x = GetEvaluationServiceAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationServiceAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationServiceAvailabilityRequest) ProtoMessage() {}

func (x *GetEvaluationServiceAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationServiceAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*GetEvaluationServiceAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetEvaluationServiceAvailabilityRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// get evaluation service availability response
type GetEvaluationServiceAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation service availability
	Availability *v11.EvaluationServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *GetEvaluationServiceAvailabilityResponse) Reset() {
	*x = GetEvaluationServiceAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationServiceAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationServiceAvailabilityResponse) ProtoMessage() {}

func (x *GetEvaluationServiceAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationServiceAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*GetEvaluationServiceAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetEvaluationServiceAvailabilityResponse) GetAvailability() *v11.EvaluationServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update evaluation service availability request
type UpdateEvaluationServiceAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// evaluation service availability
	Availability *v11.EvaluationServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=availability,proto3" json:"availability,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *UpdateEvaluationServiceAvailabilityRequest) Reset() {
	*x = UpdateEvaluationServiceAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationServiceAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationServiceAvailabilityRequest) ProtoMessage() {}

func (x *UpdateEvaluationServiceAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationServiceAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationServiceAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateEvaluationServiceAvailabilityRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateEvaluationServiceAvailabilityRequest) GetAvailability() *v11.EvaluationServiceAvailabilityUpdateDef {
	if x != nil {
		return x.Availability
	}
	return nil
}

func (x *UpdateEvaluationServiceAvailabilityRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// update evaluation service availability response
type UpdateEvaluationServiceAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation service availability
	Availability *v11.EvaluationServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateEvaluationServiceAvailabilityResponse) Reset() {
	*x = UpdateEvaluationServiceAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationServiceAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationServiceAvailabilityResponse) ProtoMessage() {}

func (x *UpdateEvaluationServiceAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationServiceAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationServiceAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateEvaluationServiceAvailabilityResponse) GetAvailability() *v11.EvaluationServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// get grooming service availability request
type GetGroomingServiceAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
}

func (x *GetGroomingServiceAvailabilityRequest) Reset() {
	*x = GetGroomingServiceAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingServiceAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingServiceAvailabilityRequest) ProtoMessage() {}

func (x *GetGroomingServiceAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingServiceAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*GetGroomingServiceAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetGroomingServiceAvailabilityRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// get grooming service availability response
type GetGroomingServiceAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming service availability
	Availability *v11.GroomingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *GetGroomingServiceAvailabilityResponse) Reset() {
	*x = GetGroomingServiceAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroomingServiceAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingServiceAvailabilityResponse) ProtoMessage() {}

func (x *GetGroomingServiceAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingServiceAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*GetGroomingServiceAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetGroomingServiceAvailabilityResponse) GetAvailability() *v11.GroomingServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// update grooming service availability request
type UpdateGroomingServiceAvailabilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// grooming service availability
	Availability *v11.GroomingServiceAvailabilityUpdateDef `protobuf:"bytes,2,opt,name=availability,proto3" json:"availability,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *UpdateGroomingServiceAvailabilityRequest) Reset() {
	*x = UpdateGroomingServiceAvailabilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroomingServiceAvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroomingServiceAvailabilityRequest) ProtoMessage() {}

func (x *UpdateGroomingServiceAvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroomingServiceAvailabilityRequest.ProtoReflect.Descriptor instead.
func (*UpdateGroomingServiceAvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateGroomingServiceAvailabilityRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateGroomingServiceAvailabilityRequest) GetAvailability() *v11.GroomingServiceAvailabilityUpdateDef {
	if x != nil {
		return x.Availability
	}
	return nil
}

func (x *UpdateGroomingServiceAvailabilityRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// update grooming service availability response
type UpdateGroomingServiceAvailabilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming service availability
	Availability *v11.GroomingServiceAvailabilityModel `protobuf:"bytes,1,opt,name=availability,proto3" json:"availability,omitempty"`
}

func (x *UpdateGroomingServiceAvailabilityResponse) Reset() {
	*x = UpdateGroomingServiceAvailabilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGroomingServiceAvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroomingServiceAvailabilityResponse) ProtoMessage() {}

func (x *UpdateGroomingServiceAvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroomingServiceAvailabilityResponse.ProtoReflect.Descriptor instead.
func (*UpdateGroomingServiceAvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateGroomingServiceAvailabilityResponse) GetAvailability() *v11.GroomingServiceAvailabilityModel {
	if x != nil {
		return x.Availability
	}
	return nil
}

// get accepted customer setting request
// deprecated by pc since /2025/6/20 use OBAvailableDateTimeService.GetAvailableDateTime instead.
type ListAvailableBookingTimeRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item types
	ServiceItemType []v12.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *ListAvailableBookingTimeRangeRequest) Reset() {
	*x = ListAvailableBookingTimeRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAvailableBookingTimeRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableBookingTimeRangeRequest) ProtoMessage() {}

func (x *ListAvailableBookingTimeRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableBookingTimeRangeRequest.ProtoReflect.Descriptor instead.
func (*ListAvailableBookingTimeRangeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListAvailableBookingTimeRangeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAvailableBookingTimeRangeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAvailableBookingTimeRangeRequest) GetServiceItemType() []v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return nil
}

// get accepted customer setting response
type ListAvailableBookingTimeRangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time ranges
	TimeRanges []*ListAvailableBookingTimeRangeResponse_TimeRange `protobuf:"bytes,1,rep,name=time_ranges,json=timeRanges,proto3" json:"time_ranges,omitempty"`
}

func (x *ListAvailableBookingTimeRangeResponse) Reset() {
	*x = ListAvailableBookingTimeRangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAvailableBookingTimeRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableBookingTimeRangeResponse) ProtoMessage() {}

func (x *ListAvailableBookingTimeRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableBookingTimeRangeResponse.ProtoReflect.Descriptor instead.
func (*ListAvailableBookingTimeRangeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListAvailableBookingTimeRangeResponse) GetTimeRanges() []*ListAvailableBookingTimeRangeResponse_TimeRange {
	if x != nil {
		return x.TimeRanges
	}
	return nil
}

// get accepted customer setting request
type ListAcceptedCustomerSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item types
	ServiceItemTypes []v12.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ListAcceptedCustomerSettingRequest) Reset() {
	*x = ListAcceptedCustomerSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAcceptedCustomerSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAcceptedCustomerSettingRequest) ProtoMessage() {}

func (x *ListAcceptedCustomerSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAcceptedCustomerSettingRequest.ProtoReflect.Descriptor instead.
func (*ListAcceptedCustomerSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListAcceptedCustomerSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAcceptedCustomerSettingRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAcceptedCustomerSettingRequest) GetServiceItemTypes() []v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// get accepted customer setting response
type ListAcceptedCustomerSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted customer types
	AcceptCustomerTypes []*ListAcceptedCustomerSettingResponse_AcceptCustomerType `protobuf:"bytes,1,rep,name=accept_customer_types,json=acceptCustomerTypes,proto3" json:"accept_customer_types,omitempty"`
}

func (x *ListAcceptedCustomerSettingResponse) Reset() {
	*x = ListAcceptedCustomerSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAcceptedCustomerSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAcceptedCustomerSettingResponse) ProtoMessage() {}

func (x *ListAcceptedCustomerSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAcceptedCustomerSettingResponse.ProtoReflect.Descriptor instead.
func (*ListAcceptedCustomerSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListAcceptedCustomerSettingResponse) GetAcceptCustomerTypes() []*ListAcceptedCustomerSettingResponse_AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerTypes
	}
	return nil
}

// update accepted customer setting request
type UpdateAcceptedCustomerSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the tenant id
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// accept customer type
	AcceptCustomerType v11.AcceptCustomerType `protobuf:"varint,2,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
	// service item types
	ServiceItemTypes []v12.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *UpdateAcceptedCustomerSettingRequest) Reset() {
	*x = UpdateAcceptedCustomerSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAcceptedCustomerSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAcceptedCustomerSettingRequest) ProtoMessage() {}

func (x *UpdateAcceptedCustomerSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAcceptedCustomerSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateAcceptedCustomerSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateAcceptedCustomerSettingRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateAcceptedCustomerSettingRequest) GetAcceptCustomerType() v11.AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return v11.AcceptCustomerType(0)
}

func (x *UpdateAcceptedCustomerSettingRequest) GetServiceItemTypes() []v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *UpdateAcceptedCustomerSettingRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// update accepted customer setting response
type UpdateAcceptedCustomerSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAcceptedCustomerSettingResponse) Reset() {
	*x = UpdateAcceptedCustomerSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAcceptedCustomerSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAcceptedCustomerSettingResponse) ProtoMessage() {}

func (x *UpdateAcceptedCustomerSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAcceptedCustomerSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateAcceptedCustomerSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{25}
}

// List arrival pick up time overrides request
type ListArrivalPickUpTimeOverridesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business ids, optional
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// service item types, optional
	ServiceItemTypes []v12.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,8,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListArrivalPickUpTimeOverridesRequest) Reset() {
	*x = ListArrivalPickUpTimeOverridesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArrivalPickUpTimeOverridesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArrivalPickUpTimeOverridesRequest) ProtoMessage() {}

func (x *ListArrivalPickUpTimeOverridesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArrivalPickUpTimeOverridesRequest.ProtoReflect.Descriptor instead.
func (*ListArrivalPickUpTimeOverridesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListArrivalPickUpTimeOverridesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListArrivalPickUpTimeOverridesRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListArrivalPickUpTimeOverridesRequest) GetServiceItemTypes() []v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListArrivalPickUpTimeOverridesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// List arrival pick up time overrides  response
type ListArrivalPickUpTimeOverridesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival/pick up time overrides
	Overrides []*v11.ArrivalPickUpTimeOverrideModel `protobuf:"bytes,1,rep,name=overrides,proto3" json:"overrides,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListArrivalPickUpTimeOverridesResponse) Reset() {
	*x = ListArrivalPickUpTimeOverridesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListArrivalPickUpTimeOverridesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListArrivalPickUpTimeOverridesResponse) ProtoMessage() {}

func (x *ListArrivalPickUpTimeOverridesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListArrivalPickUpTimeOverridesResponse.ProtoReflect.Descriptor instead.
func (*ListArrivalPickUpTimeOverridesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListArrivalPickUpTimeOverridesResponse) GetOverrides() []*v11.ArrivalPickUpTimeOverrideModel {
	if x != nil {
		return x.Overrides
	}
	return nil
}

func (x *ListArrivalPickUpTimeOverridesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// batch create arrival pick up time override request
type BatchCreateArrivalPickUpTimeOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// arrival/pick up time overrides
	Overrides []*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef `protobuf:"bytes,4,rep,name=overrides,proto3" json:"overrides,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideRequest) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{28}
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest) GetOverrides() []*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef {
	if x != nil {
		return x.Overrides
	}
	return nil
}

// batch create arrival pick up time override response
type BatchCreateArrivalPickUpTimeOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival/pick up time overrides
	Overrides []*v11.ArrivalPickUpTimeOverrideModel `protobuf:"bytes,1,rep,name=overrides,proto3" json:"overrides,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideResponse) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideResponse) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{29}
}

func (x *BatchCreateArrivalPickUpTimeOverrideResponse) GetOverrides() []*v11.ArrivalPickUpTimeOverrideModel {
	if x != nil {
		return x.Overrides
	}
	return nil
}

// batch delete arrival pick up time override request
type BatchDeleteArrivalPickUpTimeOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival/pick up time override ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *BatchDeleteArrivalPickUpTimeOverrideRequest) Reset() {
	*x = BatchDeleteArrivalPickUpTimeOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteArrivalPickUpTimeOverrideRequest) ProtoMessage() {}

func (x *BatchDeleteArrivalPickUpTimeOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteArrivalPickUpTimeOverrideRequest.ProtoReflect.Descriptor instead.
func (*BatchDeleteArrivalPickUpTimeOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{30}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchDeleteArrivalPickUpTimeOverrideRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// batch delete arrival pick up time override response
type BatchDeleteArrivalPickUpTimeOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteArrivalPickUpTimeOverrideResponse) Reset() {
	*x = BatchDeleteArrivalPickUpTimeOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteArrivalPickUpTimeOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteArrivalPickUpTimeOverrideResponse) ProtoMessage() {}

func (x *BatchDeleteArrivalPickUpTimeOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteArrivalPickUpTimeOverrideResponse.ProtoReflect.Descriptor instead.
func (*BatchDeleteArrivalPickUpTimeOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{31}
}

// batch update arrival pick up time override request
type BatchUpdateArrivalPickUpTimeOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival time overrides
	Overrides []*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef `protobuf:"bytes,1,rep,name=overrides,proto3" json:"overrides,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideRequest) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{32}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest) GetOverrides() []*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef {
	if x != nil {
		return x.Overrides
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// batch update arrival pick up time override response
type BatchUpdateArrivalPickUpTimeOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival/pick up time overrides
	Overrides []*v11.ArrivalPickUpTimeOverrideModel `protobuf:"bytes,1,rep,name=overrides,proto3" json:"overrides,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResponse) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideResponse) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{33}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideResponse) GetOverrides() []*v11.ArrivalPickUpTimeOverrideModel {
	if x != nil {
		return x.Overrides
	}
	return nil
}

// List capacity overrides request
type ListCapacityOverridesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *ListCapacityOverridesRequest) Reset() {
	*x = ListCapacityOverridesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCapacityOverridesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCapacityOverridesRequest) ProtoMessage() {}

func (x *ListCapacityOverridesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCapacityOverridesRequest.ProtoReflect.Descriptor instead.
func (*ListCapacityOverridesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{34}
}

func (x *ListCapacityOverridesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCapacityOverridesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListCapacityOverridesRequest) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

// List capacity overrides response
type ListCapacityOverridesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v11.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *ListCapacityOverridesResponse) Reset() {
	*x = ListCapacityOverridesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCapacityOverridesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCapacityOverridesResponse) ProtoMessage() {}

func (x *ListCapacityOverridesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCapacityOverridesResponse.ProtoReflect.Descriptor instead.
func (*ListCapacityOverridesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{35}
}

func (x *ListCapacityOverridesResponse) GetCapacityOverrides() []*v11.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// create capacity override request
type CreateCapacityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// capacity override
	CapacityOverride *v11.CapacityOverrideDef `protobuf:"bytes,4,opt,name=capacity_override,json=capacityOverride,proto3" json:"capacity_override,omitempty"`
}

func (x *CreateCapacityOverrideRequest) Reset() {
	*x = CreateCapacityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCapacityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCapacityOverrideRequest) ProtoMessage() {}

func (x *CreateCapacityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCapacityOverrideRequest.ProtoReflect.Descriptor instead.
func (*CreateCapacityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{36}
}

func (x *CreateCapacityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCapacityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCapacityOverrideRequest) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

func (x *CreateCapacityOverrideRequest) GetCapacityOverride() *v11.CapacityOverrideDef {
	if x != nil {
		return x.CapacityOverride
	}
	return nil
}

// create capacity override response
type CreateCapacityOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v11.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *CreateCapacityOverrideResponse) Reset() {
	*x = CreateCapacityOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCapacityOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCapacityOverrideResponse) ProtoMessage() {}

func (x *CreateCapacityOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCapacityOverrideResponse.ProtoReflect.Descriptor instead.
func (*CreateCapacityOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{37}
}

func (x *CreateCapacityOverrideResponse) GetCapacityOverrides() []*v11.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// delete capacity override request
type DeleteCapacityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// capacity override id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteCapacityOverrideRequest) Reset() {
	*x = DeleteCapacityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCapacityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCapacityOverrideRequest) ProtoMessage() {}

func (x *DeleteCapacityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCapacityOverrideRequest.ProtoReflect.Descriptor instead.
func (*DeleteCapacityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteCapacityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeleteCapacityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteCapacityOverrideRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete capacity override response
type DeleteCapacityOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v11.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *DeleteCapacityOverrideResponse) Reset() {
	*x = DeleteCapacityOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCapacityOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCapacityOverrideResponse) ProtoMessage() {}

func (x *DeleteCapacityOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCapacityOverrideResponse.ProtoReflect.Descriptor instead.
func (*DeleteCapacityOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteCapacityOverrideResponse) GetCapacityOverrides() []*v11.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// update capacity override request
type UpdateCapacityOverrideRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, optional
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// capacity override
	CapacityOverride *v11.CapacityOverrideDef `protobuf:"bytes,3,opt,name=capacity_override,json=capacityOverride,proto3" json:"capacity_override,omitempty"`
}

func (x *UpdateCapacityOverrideRequest) Reset() {
	*x = UpdateCapacityOverrideRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityOverrideRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityOverrideRequest) ProtoMessage() {}

func (x *UpdateCapacityOverrideRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityOverrideRequest.ProtoReflect.Descriptor instead.
func (*UpdateCapacityOverrideRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{40}
}

func (x *UpdateCapacityOverrideRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateCapacityOverrideRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateCapacityOverrideRequest) GetCapacityOverride() *v11.CapacityOverrideDef {
	if x != nil {
		return x.CapacityOverride
	}
	return nil
}

// update capacity override response
type UpdateCapacityOverrideResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// capacity overrides
	CapacityOverrides []*v11.CapacityOverrideModel `protobuf:"bytes,1,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *UpdateCapacityOverrideResponse) Reset() {
	*x = UpdateCapacityOverrideResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityOverrideResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityOverrideResponse) ProtoMessage() {}

func (x *UpdateCapacityOverrideResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityOverrideResponse.ProtoReflect.Descriptor instead.
func (*UpdateCapacityOverrideResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{41}
}

func (x *UpdateCapacityOverrideResponse) GetCapacityOverrides() []*v11.CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// time range
type ListAvailableBookingTimeRangeResponse_TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *v11.ArrivalPickUpTimeDef `protobuf:"bytes,2,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
}

func (x *ListAvailableBookingTimeRangeResponse_TimeRange) Reset() {
	*x = ListAvailableBookingTimeRangeResponse_TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAvailableBookingTimeRangeResponse_TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableBookingTimeRangeResponse_TimeRange) ProtoMessage() {}

func (x *ListAvailableBookingTimeRangeResponse_TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableBookingTimeRangeResponse_TimeRange.ProtoReflect.Descriptor instead.
func (*ListAvailableBookingTimeRangeResponse_TimeRange) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{21, 0}
}

func (x *ListAvailableBookingTimeRangeResponse_TimeRange) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

func (x *ListAvailableBookingTimeRangeResponse_TimeRange) GetArrivalPickUpTimeRange() *v11.ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

// accept customer type
type ListAcceptedCustomerSettingResponse_AcceptCustomerType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v12.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// accepted customer type
	AcceptCustomerType v11.AcceptCustomerType `protobuf:"varint,2,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
}

func (x *ListAcceptedCustomerSettingResponse_AcceptCustomerType) Reset() {
	*x = ListAcceptedCustomerSettingResponse_AcceptCustomerType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAcceptedCustomerSettingResponse_AcceptCustomerType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAcceptedCustomerSettingResponse_AcceptCustomerType) ProtoMessage() {}

func (x *ListAcceptedCustomerSettingResponse_AcceptCustomerType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAcceptedCustomerSettingResponse_AcceptCustomerType.ProtoReflect.Descriptor instead.
func (*ListAcceptedCustomerSettingResponse_AcceptCustomerType) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{23, 0}
}

func (x *ListAcceptedCustomerSettingResponse_AcceptCustomerType) GetServiceItemType() v12.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v12.ServiceItemType(0)
}

func (x *ListAcceptedCustomerSettingResponse_AcceptCustomerType) GetAcceptCustomerType() v11.AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return v11.AcceptCustomerType(0)
}

// arrival/pick up time override request
type BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival time/pick up time
	Type v11.TimeRangeType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.online_booking.v1.TimeRangeType" json:"type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// time range
	DayTimeRanges []*v11.DayTimeRangeDef `protobuf:"bytes,5,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) Reset() {
	*x = BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) ProtoMessage() {}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef.ProtoReflect.Descriptor instead.
func (*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{28, 0}
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) GetType() v11.TimeRangeType {
	if x != nil {
		return x.Type
	}
	return v11.TimeRangeType(0)
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef) GetDayTimeRanges() []*v11.DayTimeRangeDef {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

// arrival/pick up time override update request
type BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// is available
	IsAvailable *bool `protobuf:"varint,4,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// day time range list
	DayTimeRanges *v11.DayTimeRangeDefList `protobuf:"bytes,5,opt,name=day_time_ranges,json=dayTimeRanges,proto3,oneof" json:"day_time_ranges,omitempty"`
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) Reset() {
	*x = BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) ProtoMessage() {}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef.ProtoReflect.Descriptor instead.
func (*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef) GetDayTimeRanges() *v11.DayTimeRangeDefList {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

var File_moego_service_online_booking_v1_ob_availability_setting_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDesc = []byte{
	0x0a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x76, 0x0a, 0x2c, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x25, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x22, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xd3, 0x02, 0x0a, 0x2f, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46,
	0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0xa1, 0x01, 0x0a, 0x25, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x22, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x22, 0xc8, 0x01, 0x0a, 0x30, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x25, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x22, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x75, 0x0a, 0x2b, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x22, 0xc1, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x24, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x21, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xcf, 0x02, 0x0a, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x12, 0x9e, 0x01, 0x0a, 0x24, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x21, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x2f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a,
	0x24, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x21, 0x64, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22,
	0xc7, 0x01, 0x0a, 0x1c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x70, 0x0a, 0x1d, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x12, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd0, 0x01, 0x0a, 0x25,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x5f, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x84,
	0x01, 0x0a, 0x26, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x74, 0x6f, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x06, 0x74,
	0x6f, 0x44, 0x61, 0x74, 0x65, 0x22, 0x71, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x22, 0x92, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x96, 0x02,
	0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x12, 0x6a, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x66, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x2b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x6f,
	0x0a, 0x25, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x22,
	0x8e, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0c, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x22, 0x92, 0x02, 0x0a, 0x28, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a,
	0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x68, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x66, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x91, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xe0, 0x01, 0x0a, 0x24, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92,
	0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf1, 0x02, 0x0a,
	0x25, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x1a, 0xd4, 0x01, 0x0a, 0x09, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x70,
	0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75,
	0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x22, 0xe0, 0x01, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x22, 0x87, 0x03, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x15,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0xd1, 0x01, 0x0a, 0x12, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x64, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x86, 0x03,
	0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x70,
	0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x12, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x6e, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x92, 0x01, 0x0f, 0x08,
	0x01, 0x10, 0x0a, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x27, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xc7, 0x02, 0x0a, 0x25, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08,
	0x00, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xde, 0x01, 0x0a, 0x26, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x85, 0x05, 0x0a, 0x2b, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x74, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x56, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x1a, 0xaa, 0x02, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x41, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x57, 0x0a, 0x0f, 0x64, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x2c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x22, 0x90, 0x01, 0x0a, 0x2b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13,
	0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x2c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xbb, 0x04, 0x0a, 0x2b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x1a, 0xd9, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02,
	0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x60, 0x0a, 0x0f, 0x64, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x03,
	0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x2c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x22, 0xd3, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22,
	0xc0, 0x02, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6a, 0x0a, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x10, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x1d,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x12, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x22, 0xdd, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x10, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x22, 0x86, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x32, 0xb3, 0x1e, 0x0a, 0x1c, 0x4f,
	0x42, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xc8, 0x01, 0x0a, 0x25,
	0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xd1, 0x01, 0x0a, 0x28, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xc5, 0x01, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0xce, 0x01, 0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb3,
	0x01, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0xb9, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xc2, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb3, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xbc, 0x01, 0x0a, 0x21,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x12, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb0, 0x01, 0x0a, 0x1d, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x45, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xaa, 0x01,
	0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x43, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb0, 0x01, 0x0a, 0x1d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x65, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb3, 0x01,
	0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63,
	0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73,
	0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69,
	0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xc5, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x4c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xc5, 0x01, 0x0a, 0x24,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x12, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xc5, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x4c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12,
	0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x3e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescData = file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_moego_service_online_booking_v1_ob_availability_setting_service_proto_goTypes = []interface{}{
	(*GetBoardingServiceAvailabilitySettingRequest)(nil),           // 0: moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest
	(*GetBoardingServiceAvailabilitySettingResponse)(nil),          // 1: moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingResponse
	(*UpdateBoardingServiceAvailabilitySettingRequest)(nil),        // 2: moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest
	(*UpdateBoardingServiceAvailabilitySettingResponse)(nil),       // 3: moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingResponse
	(*GetDaycareServiceAvailabilitySettingRequest)(nil),            // 4: moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest
	(*GetDaycareServiceAvailabilitySettingResponse)(nil),           // 5: moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingResponse
	(*UpdateDaycareServiceAvailabilitySettingRequest)(nil),         // 6: moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest
	(*UpdateDaycareServiceAvailabilitySettingResponse)(nil),        // 7: moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingResponse
	(*QueryAvailablePetTypeRequest)(nil),                           // 8: moego.service.online_booking.v1.QueryAvailablePetTypeRequest
	(*QueryAvailablePetTypeResponse)(nil),                          // 9: moego.service.online_booking.v1.QueryAvailablePetTypeResponse
	(*QueryAvailableBookingDateRangeRequest)(nil),                  // 10: moego.service.online_booking.v1.QueryAvailableBookingDateRangeRequest
	(*QueryAvailableBookingDateRangeResponse)(nil),                 // 11: moego.service.online_booking.v1.QueryAvailableBookingDateRangeResponse
	(*GetEvaluationServiceAvailabilityRequest)(nil),                // 12: moego.service.online_booking.v1.GetEvaluationServiceAvailabilityRequest
	(*GetEvaluationServiceAvailabilityResponse)(nil),               // 13: moego.service.online_booking.v1.GetEvaluationServiceAvailabilityResponse
	(*UpdateEvaluationServiceAvailabilityRequest)(nil),             // 14: moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest
	(*UpdateEvaluationServiceAvailabilityResponse)(nil),            // 15: moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityResponse
	(*GetGroomingServiceAvailabilityRequest)(nil),                  // 16: moego.service.online_booking.v1.GetGroomingServiceAvailabilityRequest
	(*GetGroomingServiceAvailabilityResponse)(nil),                 // 17: moego.service.online_booking.v1.GetGroomingServiceAvailabilityResponse
	(*UpdateGroomingServiceAvailabilityRequest)(nil),               // 18: moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest
	(*UpdateGroomingServiceAvailabilityResponse)(nil),              // 19: moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityResponse
	(*ListAvailableBookingTimeRangeRequest)(nil),                   // 20: moego.service.online_booking.v1.ListAvailableBookingTimeRangeRequest
	(*ListAvailableBookingTimeRangeResponse)(nil),                  // 21: moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse
	(*ListAcceptedCustomerSettingRequest)(nil),                     // 22: moego.service.online_booking.v1.ListAcceptedCustomerSettingRequest
	(*ListAcceptedCustomerSettingResponse)(nil),                    // 23: moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse
	(*UpdateAcceptedCustomerSettingRequest)(nil),                   // 24: moego.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest
	(*UpdateAcceptedCustomerSettingResponse)(nil),                  // 25: moego.service.online_booking.v1.UpdateAcceptedCustomerSettingResponse
	(*ListArrivalPickUpTimeOverridesRequest)(nil),                  // 26: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest
	(*ListArrivalPickUpTimeOverridesResponse)(nil),                 // 27: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesResponse
	(*BatchCreateArrivalPickUpTimeOverrideRequest)(nil),            // 28: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest
	(*BatchCreateArrivalPickUpTimeOverrideResponse)(nil),           // 29: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResponse
	(*BatchDeleteArrivalPickUpTimeOverrideRequest)(nil),            // 30: moego.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideRequest
	(*BatchDeleteArrivalPickUpTimeOverrideResponse)(nil),           // 31: moego.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResponse
	(*BatchUpdateArrivalPickUpTimeOverrideRequest)(nil),            // 32: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest
	(*BatchUpdateArrivalPickUpTimeOverrideResponse)(nil),           // 33: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResponse
	(*ListCapacityOverridesRequest)(nil),                           // 34: moego.service.online_booking.v1.ListCapacityOverridesRequest
	(*ListCapacityOverridesResponse)(nil),                          // 35: moego.service.online_booking.v1.ListCapacityOverridesResponse
	(*CreateCapacityOverrideRequest)(nil),                          // 36: moego.service.online_booking.v1.CreateCapacityOverrideRequest
	(*CreateCapacityOverrideResponse)(nil),                         // 37: moego.service.online_booking.v1.CreateCapacityOverrideResponse
	(*DeleteCapacityOverrideRequest)(nil),                          // 38: moego.service.online_booking.v1.DeleteCapacityOverrideRequest
	(*DeleteCapacityOverrideResponse)(nil),                         // 39: moego.service.online_booking.v1.DeleteCapacityOverrideResponse
	(*UpdateCapacityOverrideRequest)(nil),                          // 40: moego.service.online_booking.v1.UpdateCapacityOverrideRequest
	(*UpdateCapacityOverrideResponse)(nil),                         // 41: moego.service.online_booking.v1.UpdateCapacityOverrideResponse
	(*ListAvailableBookingTimeRangeResponse_TimeRange)(nil),        // 42: moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse.TimeRange
	(*ListAcceptedCustomerSettingResponse_AcceptCustomerType)(nil), // 43: moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse.AcceptCustomerType
	(*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef)(nil),  // 44: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef
	(*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef)(nil),  // 45: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef
	(*v1.Tenant)(nil), // 46: moego.models.organization.v1.Tenant
	(*v11.BoardingServiceAvailabilityModel)(nil),       // 47: moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	(*v11.BoardingServiceAvailabilityUpdateDef)(nil),   // 48: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef
	(*v11.DaycareServiceAvailabilityModel)(nil),        // 49: moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	(*v11.DaycareServiceAvailabilityUpdateDef)(nil),    // 50: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef
	(v12.ServiceItemType)(0),                           // 51: moego.models.offering.v1.ServiceItemType
	(v13.PetType)(0),                                   // 52: moego.models.customer.v1.PetType
	(*date.Date)(nil),                                  // 53: google.type.Date
	(*v11.EvaluationServiceAvailabilityModel)(nil),     // 54: moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	(*v11.EvaluationServiceAvailabilityUpdateDef)(nil), // 55: moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef
	(*v11.GroomingServiceAvailabilityModel)(nil),       // 56: moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	(*v11.GroomingServiceAvailabilityUpdateDef)(nil),   // 57: moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef
	(v11.AcceptCustomerType)(0),                        // 58: moego.models.online_booking.v1.AcceptCustomerType
	(*v2.PaginationRequest)(nil),                       // 59: moego.utils.v2.PaginationRequest
	(*v11.ArrivalPickUpTimeOverrideModel)(nil),         // 60: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel
	(*v2.PaginationResponse)(nil),                      // 61: moego.utils.v2.PaginationResponse
	(*v11.CapacityOverrideModel)(nil),                  // 62: moego.models.online_booking.v1.CapacityOverrideModel
	(*v11.CapacityOverrideDef)(nil),                    // 63: moego.models.online_booking.v1.CapacityOverrideDef
	(*v11.ArrivalPickUpTimeDef)(nil),                   // 64: moego.models.online_booking.v1.ArrivalPickUpTimeDef
	(v11.TimeRangeType)(0),                             // 65: moego.models.online_booking.v1.TimeRangeType
	(*v11.DayTimeRangeDef)(nil),                        // 66: moego.models.online_booking.v1.DayTimeRangeDef
	(*v11.DayTimeRangeDefList)(nil),                    // 67: moego.models.online_booking.v1.DayTimeRangeDefList
}
var file_moego_service_online_booking_v1_ob_availability_setting_service_proto_depIdxs = []int32{
	46, // 0: moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	47, // 1: moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingResponse.boarding_service_availability_setting:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	46, // 2: moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	48, // 3: moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest.boarding_service_availability_setting:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef
	47, // 4: moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingResponse.boarding_service_availability_setting:type_name -> moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	46, // 5: moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	49, // 6: moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingResponse.daycare_service_availability_setting:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	46, // 7: moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	50, // 8: moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest.daycare_service_availability_setting:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef
	49, // 9: moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingResponse.daycare_service_availability_setting:type_name -> moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	46, // 10: moego.service.online_booking.v1.QueryAvailablePetTypeRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	51, // 11: moego.service.online_booking.v1.QueryAvailablePetTypeRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	52, // 12: moego.service.online_booking.v1.QueryAvailablePetTypeResponse.available_pet_type:type_name -> moego.models.customer.v1.PetType
	46, // 13: moego.service.online_booking.v1.QueryAvailableBookingDateRangeRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	51, // 14: moego.service.online_booking.v1.QueryAvailableBookingDateRangeRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	53, // 15: moego.service.online_booking.v1.QueryAvailableBookingDateRangeResponse.from_date:type_name -> google.type.Date
	53, // 16: moego.service.online_booking.v1.QueryAvailableBookingDateRangeResponse.to_date:type_name -> google.type.Date
	46, // 17: moego.service.online_booking.v1.GetEvaluationServiceAvailabilityRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	54, // 18: moego.service.online_booking.v1.GetEvaluationServiceAvailabilityResponse.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	46, // 19: moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	55, // 20: moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef
	54, // 21: moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityResponse.availability:type_name -> moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	46, // 22: moego.service.online_booking.v1.GetGroomingServiceAvailabilityRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	56, // 23: moego.service.online_booking.v1.GetGroomingServiceAvailabilityResponse.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	46, // 24: moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	57, // 25: moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef
	56, // 26: moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityResponse.availability:type_name -> moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	51, // 27: moego.service.online_booking.v1.ListAvailableBookingTimeRangeRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	42, // 28: moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse.time_ranges:type_name -> moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse.TimeRange
	51, // 29: moego.service.online_booking.v1.ListAcceptedCustomerSettingRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	43, // 30: moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse.accept_customer_types:type_name -> moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse.AcceptCustomerType
	46, // 31: moego.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	58, // 32: moego.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	51, // 33: moego.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	51, // 34: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	59, // 35: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	60, // 36: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesResponse.overrides:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel
	61, // 37: moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	51, // 38: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	44, // 39: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.overrides:type_name -> moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef
	60, // 40: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResponse.overrides:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel
	45, // 41: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.overrides:type_name -> moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef
	60, // 42: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResponse.overrides:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel
	51, // 43: moego.service.online_booking.v1.ListCapacityOverridesRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	62, // 44: moego.service.online_booking.v1.ListCapacityOverridesResponse.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	51, // 45: moego.service.online_booking.v1.CreateCapacityOverrideRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	63, // 46: moego.service.online_booking.v1.CreateCapacityOverrideRequest.capacity_override:type_name -> moego.models.online_booking.v1.CapacityOverrideDef
	62, // 47: moego.service.online_booking.v1.CreateCapacityOverrideResponse.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	62, // 48: moego.service.online_booking.v1.DeleteCapacityOverrideResponse.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	63, // 49: moego.service.online_booking.v1.UpdateCapacityOverrideRequest.capacity_override:type_name -> moego.models.online_booking.v1.CapacityOverrideDef
	62, // 50: moego.service.online_booking.v1.UpdateCapacityOverrideResponse.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	51, // 51: moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse.TimeRange.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	64, // 52: moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse.TimeRange.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	51, // 53: moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse.AcceptCustomerType.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	58, // 54: moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse.AcceptCustomerType.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	65, // 55: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.type:type_name -> moego.models.online_booking.v1.TimeRangeType
	53, // 56: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.start_date:type_name -> google.type.Date
	53, // 57: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.end_date:type_name -> google.type.Date
	66, // 58: moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	53, // 59: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef.start_date:type_name -> google.type.Date
	53, // 60: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef.end_date:type_name -> google.type.Date
	67, // 61: moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDefList
	0,  // 62: moego.service.online_booking.v1.OBAvailabilitySettingService.GetBoardingServiceAvailabilitySetting:input_type -> moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest
	2,  // 63: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateBoardingServiceAvailabilitySetting:input_type -> moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest
	4,  // 64: moego.service.online_booking.v1.OBAvailabilitySettingService.GetDaycareServiceAvailabilitySetting:input_type -> moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest
	6,  // 65: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateDaycareServiceAvailabilitySetting:input_type -> moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest
	8,  // 66: moego.service.online_booking.v1.OBAvailabilitySettingService.QueryAvailablePetType:input_type -> moego.service.online_booking.v1.QueryAvailablePetTypeRequest
	10, // 67: moego.service.online_booking.v1.OBAvailabilitySettingService.QueryAvailableBookingDateRange:input_type -> moego.service.online_booking.v1.QueryAvailableBookingDateRangeRequest
	12, // 68: moego.service.online_booking.v1.OBAvailabilitySettingService.GetEvaluationServiceAvailability:input_type -> moego.service.online_booking.v1.GetEvaluationServiceAvailabilityRequest
	14, // 69: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateEvaluationServiceAvailability:input_type -> moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest
	16, // 70: moego.service.online_booking.v1.OBAvailabilitySettingService.GetGroomingServiceAvailability:input_type -> moego.service.online_booking.v1.GetGroomingServiceAvailabilityRequest
	18, // 71: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateGroomingServiceAvailability:input_type -> moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest
	20, // 72: moego.service.online_booking.v1.OBAvailabilitySettingService.ListAvailableBookingTimeRange:input_type -> moego.service.online_booking.v1.ListAvailableBookingTimeRangeRequest
	22, // 73: moego.service.online_booking.v1.OBAvailabilitySettingService.ListAcceptedCustomerSetting:input_type -> moego.service.online_booking.v1.ListAcceptedCustomerSettingRequest
	24, // 74: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateAcceptedCustomerSetting:input_type -> moego.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest
	26, // 75: moego.service.online_booking.v1.OBAvailabilitySettingService.ListArrivalPickUpTimeOverrides:input_type -> moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest
	28, // 76: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchCreateArrivalPickUpTimeOverride:input_type -> moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest
	30, // 77: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchDeleteArrivalPickUpTimeOverride:input_type -> moego.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideRequest
	32, // 78: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchUpdateArrivalPickUpTimeOverride:input_type -> moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest
	34, // 79: moego.service.online_booking.v1.OBAvailabilitySettingService.ListCapacityOverrides:input_type -> moego.service.online_booking.v1.ListCapacityOverridesRequest
	36, // 80: moego.service.online_booking.v1.OBAvailabilitySettingService.CreateCapacityOverride:input_type -> moego.service.online_booking.v1.CreateCapacityOverrideRequest
	38, // 81: moego.service.online_booking.v1.OBAvailabilitySettingService.DeleteCapacityOverride:input_type -> moego.service.online_booking.v1.DeleteCapacityOverrideRequest
	40, // 82: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateCapacityOverride:input_type -> moego.service.online_booking.v1.UpdateCapacityOverrideRequest
	1,  // 83: moego.service.online_booking.v1.OBAvailabilitySettingService.GetBoardingServiceAvailabilitySetting:output_type -> moego.service.online_booking.v1.GetBoardingServiceAvailabilitySettingResponse
	3,  // 84: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateBoardingServiceAvailabilitySetting:output_type -> moego.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingResponse
	5,  // 85: moego.service.online_booking.v1.OBAvailabilitySettingService.GetDaycareServiceAvailabilitySetting:output_type -> moego.service.online_booking.v1.GetDaycareServiceAvailabilitySettingResponse
	7,  // 86: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateDaycareServiceAvailabilitySetting:output_type -> moego.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingResponse
	9,  // 87: moego.service.online_booking.v1.OBAvailabilitySettingService.QueryAvailablePetType:output_type -> moego.service.online_booking.v1.QueryAvailablePetTypeResponse
	11, // 88: moego.service.online_booking.v1.OBAvailabilitySettingService.QueryAvailableBookingDateRange:output_type -> moego.service.online_booking.v1.QueryAvailableBookingDateRangeResponse
	13, // 89: moego.service.online_booking.v1.OBAvailabilitySettingService.GetEvaluationServiceAvailability:output_type -> moego.service.online_booking.v1.GetEvaluationServiceAvailabilityResponse
	15, // 90: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateEvaluationServiceAvailability:output_type -> moego.service.online_booking.v1.UpdateEvaluationServiceAvailabilityResponse
	17, // 91: moego.service.online_booking.v1.OBAvailabilitySettingService.GetGroomingServiceAvailability:output_type -> moego.service.online_booking.v1.GetGroomingServiceAvailabilityResponse
	19, // 92: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateGroomingServiceAvailability:output_type -> moego.service.online_booking.v1.UpdateGroomingServiceAvailabilityResponse
	21, // 93: moego.service.online_booking.v1.OBAvailabilitySettingService.ListAvailableBookingTimeRange:output_type -> moego.service.online_booking.v1.ListAvailableBookingTimeRangeResponse
	23, // 94: moego.service.online_booking.v1.OBAvailabilitySettingService.ListAcceptedCustomerSetting:output_type -> moego.service.online_booking.v1.ListAcceptedCustomerSettingResponse
	25, // 95: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateAcceptedCustomerSetting:output_type -> moego.service.online_booking.v1.UpdateAcceptedCustomerSettingResponse
	27, // 96: moego.service.online_booking.v1.OBAvailabilitySettingService.ListArrivalPickUpTimeOverrides:output_type -> moego.service.online_booking.v1.ListArrivalPickUpTimeOverridesResponse
	29, // 97: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchCreateArrivalPickUpTimeOverride:output_type -> moego.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResponse
	31, // 98: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchDeleteArrivalPickUpTimeOverride:output_type -> moego.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResponse
	33, // 99: moego.service.online_booking.v1.OBAvailabilitySettingService.BatchUpdateArrivalPickUpTimeOverride:output_type -> moego.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResponse
	35, // 100: moego.service.online_booking.v1.OBAvailabilitySettingService.ListCapacityOverrides:output_type -> moego.service.online_booking.v1.ListCapacityOverridesResponse
	37, // 101: moego.service.online_booking.v1.OBAvailabilitySettingService.CreateCapacityOverride:output_type -> moego.service.online_booking.v1.CreateCapacityOverrideResponse
	39, // 102: moego.service.online_booking.v1.OBAvailabilitySettingService.DeleteCapacityOverride:output_type -> moego.service.online_booking.v1.DeleteCapacityOverrideResponse
	41, // 103: moego.service.online_booking.v1.OBAvailabilitySettingService.UpdateCapacityOverride:output_type -> moego.service.online_booking.v1.UpdateCapacityOverrideResponse
	83, // [83:104] is the sub-list for method output_type
	62, // [62:83] is the sub-list for method input_type
	62, // [62:62] is the sub-list for extension type_name
	62, // [62:62] is the sub-list for extension extendee
	0,  // [0:62] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_ob_availability_setting_service_proto_init() }
func file_moego_service_online_booking_v1_ob_availability_setting_service_proto_init() {
	if File_moego_service_online_booking_v1_ob_availability_setting_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBoardingServiceAvailabilitySettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBoardingServiceAvailabilitySettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingServiceAvailabilitySettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingServiceAvailabilitySettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaycareServiceAvailabilitySettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDaycareServiceAvailabilitySettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDaycareServiceAvailabilitySettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDaycareServiceAvailabilitySettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAvailablePetTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAvailablePetTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAvailableBookingDateRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAvailableBookingDateRangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationServiceAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationServiceAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationServiceAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationServiceAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingServiceAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroomingServiceAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroomingServiceAvailabilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGroomingServiceAvailabilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAvailableBookingTimeRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAvailableBookingTimeRangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAcceptedCustomerSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAcceptedCustomerSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAcceptedCustomerSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAcceptedCustomerSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArrivalPickUpTimeOverridesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListArrivalPickUpTimeOverridesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteArrivalPickUpTimeOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteArrivalPickUpTimeOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCapacityOverridesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCapacityOverridesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCapacityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCapacityOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCapacityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCapacityOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityOverrideRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityOverrideResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAvailableBookingTimeRangeResponse_TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAcceptedCustomerSettingResponse_AcceptCustomerType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateArrivalPickUpTimeOverrideRequest_CreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateArrivalPickUpTimeOverrideRequest_UpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[27].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes[45].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_ob_availability_setting_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_ob_availability_setting_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_ob_availability_setting_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_ob_availability_setting_service_proto = out.File
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_ob_availability_setting_service_proto_depIdxs = nil
}
