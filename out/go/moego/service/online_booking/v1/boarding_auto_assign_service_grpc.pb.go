// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/boarding_auto_assign_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BoardingAutoAssignServiceClient is the client API for BoardingAutoAssignService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BoardingAutoAssignServiceClient interface {
	// List boarding auto assign records by booking request ids.
	ListBoardingAutoAssignByBookingRequestIds(ctx context.Context, in *ListBoardingAutoAssignByBookingRequestIdsRequest, opts ...grpc.CallOption) (*ListBoardingAutoAssignByBookingRequestIdsResponse, error)
	// Get boarding auto assign record by service detail id.
	GetBoardingAutoAssignByServiceDetailId(ctx context.Context, in *GetBoardingAutoAssignByServiceDetailIdRequest, opts ...grpc.CallOption) (*GetBoardingAutoAssignByServiceDetailIdResponse, error)
	// List boarding auto assign records by service detail ids.
	ListBoardingAutoAssignByServiceDetailIds(ctx context.Context, in *ListBoardingAutoAssignByServiceDetailIdsRequest, opts ...grpc.CallOption) (*ListBoardingAutoAssignByServiceDetailIdsResponse, error)
}

type boardingAutoAssignServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBoardingAutoAssignServiceClient(cc grpc.ClientConnInterface) BoardingAutoAssignServiceClient {
	return &boardingAutoAssignServiceClient{cc}
}

func (c *boardingAutoAssignServiceClient) ListBoardingAutoAssignByBookingRequestIds(ctx context.Context, in *ListBoardingAutoAssignByBookingRequestIdsRequest, opts ...grpc.CallOption) (*ListBoardingAutoAssignByBookingRequestIdsResponse, error) {
	out := new(ListBoardingAutoAssignByBookingRequestIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BoardingAutoAssignService/ListBoardingAutoAssignByBookingRequestIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *boardingAutoAssignServiceClient) GetBoardingAutoAssignByServiceDetailId(ctx context.Context, in *GetBoardingAutoAssignByServiceDetailIdRequest, opts ...grpc.CallOption) (*GetBoardingAutoAssignByServiceDetailIdResponse, error) {
	out := new(GetBoardingAutoAssignByServiceDetailIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BoardingAutoAssignService/GetBoardingAutoAssignByServiceDetailId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *boardingAutoAssignServiceClient) ListBoardingAutoAssignByServiceDetailIds(ctx context.Context, in *ListBoardingAutoAssignByServiceDetailIdsRequest, opts ...grpc.CallOption) (*ListBoardingAutoAssignByServiceDetailIdsResponse, error) {
	out := new(ListBoardingAutoAssignByServiceDetailIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BoardingAutoAssignService/ListBoardingAutoAssignByServiceDetailIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BoardingAutoAssignServiceServer is the server API for BoardingAutoAssignService service.
// All implementations must embed UnimplementedBoardingAutoAssignServiceServer
// for forward compatibility
type BoardingAutoAssignServiceServer interface {
	// List boarding auto assign records by booking request ids.
	ListBoardingAutoAssignByBookingRequestIds(context.Context, *ListBoardingAutoAssignByBookingRequestIdsRequest) (*ListBoardingAutoAssignByBookingRequestIdsResponse, error)
	// Get boarding auto assign record by service detail id.
	GetBoardingAutoAssignByServiceDetailId(context.Context, *GetBoardingAutoAssignByServiceDetailIdRequest) (*GetBoardingAutoAssignByServiceDetailIdResponse, error)
	// List boarding auto assign records by service detail ids.
	ListBoardingAutoAssignByServiceDetailIds(context.Context, *ListBoardingAutoAssignByServiceDetailIdsRequest) (*ListBoardingAutoAssignByServiceDetailIdsResponse, error)
	mustEmbedUnimplementedBoardingAutoAssignServiceServer()
}

// UnimplementedBoardingAutoAssignServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBoardingAutoAssignServiceServer struct {
}

func (UnimplementedBoardingAutoAssignServiceServer) ListBoardingAutoAssignByBookingRequestIds(context.Context, *ListBoardingAutoAssignByBookingRequestIdsRequest) (*ListBoardingAutoAssignByBookingRequestIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBoardingAutoAssignByBookingRequestIds not implemented")
}
func (UnimplementedBoardingAutoAssignServiceServer) GetBoardingAutoAssignByServiceDetailId(context.Context, *GetBoardingAutoAssignByServiceDetailIdRequest) (*GetBoardingAutoAssignByServiceDetailIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBoardingAutoAssignByServiceDetailId not implemented")
}
func (UnimplementedBoardingAutoAssignServiceServer) ListBoardingAutoAssignByServiceDetailIds(context.Context, *ListBoardingAutoAssignByServiceDetailIdsRequest) (*ListBoardingAutoAssignByServiceDetailIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBoardingAutoAssignByServiceDetailIds not implemented")
}
func (UnimplementedBoardingAutoAssignServiceServer) mustEmbedUnimplementedBoardingAutoAssignServiceServer() {
}

// UnsafeBoardingAutoAssignServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BoardingAutoAssignServiceServer will
// result in compilation errors.
type UnsafeBoardingAutoAssignServiceServer interface {
	mustEmbedUnimplementedBoardingAutoAssignServiceServer()
}

func RegisterBoardingAutoAssignServiceServer(s grpc.ServiceRegistrar, srv BoardingAutoAssignServiceServer) {
	s.RegisterService(&BoardingAutoAssignService_ServiceDesc, srv)
}

func _BoardingAutoAssignService_ListBoardingAutoAssignByBookingRequestIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBoardingAutoAssignByBookingRequestIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BoardingAutoAssignServiceServer).ListBoardingAutoAssignByBookingRequestIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BoardingAutoAssignService/ListBoardingAutoAssignByBookingRequestIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BoardingAutoAssignServiceServer).ListBoardingAutoAssignByBookingRequestIds(ctx, req.(*ListBoardingAutoAssignByBookingRequestIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BoardingAutoAssignService_GetBoardingAutoAssignByServiceDetailId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoardingAutoAssignByServiceDetailIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BoardingAutoAssignServiceServer).GetBoardingAutoAssignByServiceDetailId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BoardingAutoAssignService/GetBoardingAutoAssignByServiceDetailId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BoardingAutoAssignServiceServer).GetBoardingAutoAssignByServiceDetailId(ctx, req.(*GetBoardingAutoAssignByServiceDetailIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BoardingAutoAssignService_ListBoardingAutoAssignByServiceDetailIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBoardingAutoAssignByServiceDetailIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BoardingAutoAssignServiceServer).ListBoardingAutoAssignByServiceDetailIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BoardingAutoAssignService/ListBoardingAutoAssignByServiceDetailIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BoardingAutoAssignServiceServer).ListBoardingAutoAssignByServiceDetailIds(ctx, req.(*ListBoardingAutoAssignByServiceDetailIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BoardingAutoAssignService_ServiceDesc is the grpc.ServiceDesc for BoardingAutoAssignService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BoardingAutoAssignService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.BoardingAutoAssignService",
	HandlerType: (*BoardingAutoAssignServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBoardingAutoAssignByBookingRequestIds",
			Handler:    _BoardingAutoAssignService_ListBoardingAutoAssignByBookingRequestIds_Handler,
		},
		{
			MethodName: "GetBoardingAutoAssignByServiceDetailId",
			Handler:    _BoardingAutoAssignService_GetBoardingAutoAssignByServiceDetailId_Handler,
		},
		{
			MethodName: "ListBoardingAutoAssignByServiceDetailIds",
			Handler:    _BoardingAutoAssignService_ListBoardingAutoAssignByServiceDetailIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/boarding_auto_assign_service.proto",
}
