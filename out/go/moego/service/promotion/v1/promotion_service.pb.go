// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/promotion/v1/promotion_service.proto

package promotionpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// recommend coupons request
type RecommendCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// targets 优惠券目标项目列表
	Targets []*v1.CouponApplicationTarget `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// search condition
	SearchCondition *v1.CouponSearchCondition `protobuf:"bytes,3,opt,name=search_condition,json=searchCondition,proto3" json:"search_condition,omitempty"`
}

func (x *RecommendCouponsRequest) Reset() {
	*x = RecommendCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendCouponsRequest) ProtoMessage() {}

func (x *RecommendCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendCouponsRequest.ProtoReflect.Descriptor instead.
func (*RecommendCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{0}
}

func (x *RecommendCouponsRequest) GetTargets() []*v1.CouponApplicationTarget {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *RecommendCouponsRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *RecommendCouponsRequest) GetSearchCondition() *v1.CouponSearchCondition {
	if x != nil {
		return x.SearchCondition
	}
	return nil
}

// recommend coupons response
type RecommendCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recommended coupons 推荐的优惠券
	RecommendedCoupons []*v1.CouponUsage `protobuf:"bytes,1,rep,name=recommended_coupons,json=recommendedCoupons,proto3" json:"recommended_coupons,omitempty"`
	// all valid coupons 所有可用的优惠券
	AvailableCoupons []*v1.Coupon `protobuf:"bytes,2,rep,name=available_coupons,json=availableCoupons,proto3" json:"available_coupons,omitempty"`
	// estimated_discount_amount 预估优惠金额
	EstimatedDiscountAmount *money.Money `protobuf:"bytes,3,opt,name=estimated_discount_amount,json=estimatedDiscountAmount,proto3" json:"estimated_discount_amount,omitempty"`
	// unavailable_coupons 不可用的优惠券
	UnavailableCoupons []*v1.Coupon `protobuf:"bytes,4,rep,name=unavailable_coupons,json=unavailableCoupons,proto3" json:"unavailable_coupons,omitempty"`
}

func (x *RecommendCouponsResponse) Reset() {
	*x = RecommendCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendCouponsResponse) ProtoMessage() {}

func (x *RecommendCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendCouponsResponse.ProtoReflect.Descriptor instead.
func (*RecommendCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{1}
}

func (x *RecommendCouponsResponse) GetRecommendedCoupons() []*v1.CouponUsage {
	if x != nil {
		return x.RecommendedCoupons
	}
	return nil
}

func (x *RecommendCouponsResponse) GetAvailableCoupons() []*v1.Coupon {
	if x != nil {
		return x.AvailableCoupons
	}
	return nil
}

func (x *RecommendCouponsResponse) GetEstimatedDiscountAmount() *money.Money {
	if x != nil {
		return x.EstimatedDiscountAmount
	}
	return nil
}

func (x *RecommendCouponsResponse) GetUnavailableCoupons() []*v1.Coupon {
	if x != nil {
		return x.UnavailableCoupons
	}
	return nil
}

// preview coupons request
type PreviewCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// targets 优惠券目标项目列表
	CouponUsages []*v1.CouponUsage `protobuf:"bytes,1,rep,name=coupon_usages,json=couponUsages,proto3" json:"coupon_usages,omitempty"`
	// customer_id 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *PreviewCouponsRequest) Reset() {
	*x = PreviewCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCouponsRequest) ProtoMessage() {}

func (x *PreviewCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCouponsRequest.ProtoReflect.Descriptor instead.
func (*PreviewCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{2}
}

func (x *PreviewCouponsRequest) GetCouponUsages() []*v1.CouponUsage {
	if x != nil {
		return x.CouponUsages
	}
	return nil
}

func (x *PreviewCouponsRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// PreviewCouponsResponse 预览优惠券响应
type PreviewCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deductions 目标抵扣结果列表
	Deductions []*v1.TargetDeduction `protobuf:"bytes,1,rep,name=deductions,proto3" json:"deductions,omitempty"`
	// total_discount_amount 总优惠金额
	TotalDiscountAmount *money.Money `protobuf:"bytes,2,opt,name=total_discount_amount,json=totalDiscountAmount,proto3" json:"total_discount_amount,omitempty"`
}

func (x *PreviewCouponsResponse) Reset() {
	*x = PreviewCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewCouponsResponse) ProtoMessage() {}

func (x *PreviewCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewCouponsResponse.ProtoReflect.Descriptor instead.
func (*PreviewCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{3}
}

func (x *PreviewCouponsResponse) GetDeductions() []*v1.TargetDeduction {
	if x != nil {
		return x.Deductions
	}
	return nil
}

func (x *PreviewCouponsResponse) GetTotalDiscountAmount() *money.Money {
	if x != nil {
		return x.TotalDiscountAmount
	}
	return nil
}

// redeem coupons request
type RedeemCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// coupon redeem details
	Redeems []*v1.CouponRedeem `protobuf:"bytes,2,rep,name=redeems,proto3" json:"redeems,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// scene 场景
	//
	// Types that are assignable to RedeemScene:
	//
	//	*RedeemCouponsRequest_AppointmentId
	RedeemScene isRedeemCouponsRequest_RedeemScene `protobuf_oneof:"redeem_scene"`
	// 订单收入 -- discount 核销使用
	OrderSales *money.Money `protobuf:"bytes,15,opt,name=order_sales,json=orderSales,proto3" json:"order_sales,omitempty"`
}

func (x *RedeemCouponsRequest) Reset() {
	*x = RedeemCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemCouponsRequest) ProtoMessage() {}

func (x *RedeemCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemCouponsRequest.ProtoReflect.Descriptor instead.
func (*RedeemCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{4}
}

func (x *RedeemCouponsRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RedeemCouponsRequest) GetRedeems() []*v1.CouponRedeem {
	if x != nil {
		return x.Redeems
	}
	return nil
}

func (x *RedeemCouponsRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (m *RedeemCouponsRequest) GetRedeemScene() isRedeemCouponsRequest_RedeemScene {
	if m != nil {
		return m.RedeemScene
	}
	return nil
}

func (x *RedeemCouponsRequest) GetAppointmentId() int64 {
	if x, ok := x.GetRedeemScene().(*RedeemCouponsRequest_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *RedeemCouponsRequest) GetOrderSales() *money.Money {
	if x != nil {
		return x.OrderSales
	}
	return nil
}

type isRedeemCouponsRequest_RedeemScene interface {
	isRedeemCouponsRequest_RedeemScene()
}

type RedeemCouponsRequest_AppointmentId struct {
	// 订单场景
	AppointmentId int64 `protobuf:"varint,4,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

func (*RedeemCouponsRequest_AppointmentId) isRedeemCouponsRequest_RedeemScene() {}

// redeem coupons response
type RedeemCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RedeemCouponsResponse) Reset() {
	*x = RedeemCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemCouponsResponse) ProtoMessage() {}

func (x *RedeemCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemCouponsResponse.ProtoReflect.Descriptor instead.
func (*RedeemCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{5}
}

// refund coupons request
type RefundCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 幂等 key
	IdempotenceKey string `protobuf:"bytes,1,opt,name=idempotence_key,json=idempotenceKey,proto3" json:"idempotence_key,omitempty"`
	// 需要退款的核销记录
	Revisions []*v1.CouponRevision `protobuf:"bytes,2,rep,name=revisions,proto3" json:"revisions,omitempty"`
}

func (x *RefundCouponsRequest) Reset() {
	*x = RefundCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundCouponsRequest) ProtoMessage() {}

func (x *RefundCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundCouponsRequest.ProtoReflect.Descriptor instead.
func (*RefundCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{6}
}

func (x *RefundCouponsRequest) GetIdempotenceKey() string {
	if x != nil {
		return x.IdempotenceKey
	}
	return ""
}

func (x *RefundCouponsRequest) GetRevisions() []*v1.CouponRevision {
	if x != nil {
		return x.Revisions
	}
	return nil
}

// refund coupons response
type RefundCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 生成的退款记录
	Revisions []*v1.CouponRevision `protobuf:"bytes,1,rep,name=revisions,proto3" json:"revisions,omitempty"`
}

func (x *RefundCouponsResponse) Reset() {
	*x = RefundCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundCouponsResponse) ProtoMessage() {}

func (x *RefundCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundCouponsResponse.ProtoReflect.Descriptor instead.
func (*RefundCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{7}
}

func (x *RefundCouponsResponse) GetRevisions() []*v1.CouponRevision {
	if x != nil {
		return x.Revisions
	}
	return nil
}

// search coupons request
type SearchCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// search condition
	SearchCondition *v1.CouponSearchCondition `protobuf:"bytes,1,opt,name=search_condition,json=searchCondition,proto3" json:"search_condition,omitempty"`
}

func (x *SearchCouponsRequest) Reset() {
	*x = SearchCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponsRequest) ProtoMessage() {}

func (x *SearchCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponsRequest.ProtoReflect.Descriptor instead.
func (*SearchCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{8}
}

func (x *SearchCouponsRequest) GetSearchCondition() *v1.CouponSearchCondition {
	if x != nil {
		return x.SearchCondition
	}
	return nil
}

// search coupons response
type SearchCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 搜索结果
	Coupons []*v1.Coupon `protobuf:"bytes,1,rep,name=coupons,proto3" json:"coupons,omitempty"`
}

func (x *SearchCouponsResponse) Reset() {
	*x = SearchCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponsResponse) ProtoMessage() {}

func (x *SearchCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_promotion_v1_promotion_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponsResponse.ProtoReflect.Descriptor instead.
func (*SearchCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP(), []int{9}
}

func (x *SearchCouponsResponse) GetCoupons() []*v1.Coupon {
	if x != nil {
		return x.Coupons
	}
	return nil
}

var File_moego_service_promotion_v1_promotion_service_proto protoreflect.FileDescriptor

var file_moego_service_promotion_v1_promotion_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5, 0x01, 0x0a,
	0x17, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x02, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x57, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x12, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x11, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x19, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x17, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x52, 0x0a, 0x13, 0x75, 0x6e,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x12, 0x75, 0x6e, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x22, 0x85,
	0x01, 0x0a, 0x15, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0xac, 0x01, 0x0a, 0x16, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a,
	0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x07, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x73, 0x61, 0x6c, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x17, 0x0a, 0x15, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65,
	0x6e, 0x63, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x60, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x73, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x10, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x54, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x3b, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x32, 0xf6, 0x04, 0x0a,
	0x10, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x7f, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x79, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a,
	0x0d, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a,
	0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x83, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_promotion_v1_promotion_service_proto_rawDescOnce sync.Once
	file_moego_service_promotion_v1_promotion_service_proto_rawDescData = file_moego_service_promotion_v1_promotion_service_proto_rawDesc
)

func file_moego_service_promotion_v1_promotion_service_proto_rawDescGZIP() []byte {
	file_moego_service_promotion_v1_promotion_service_proto_rawDescOnce.Do(func() {
		file_moego_service_promotion_v1_promotion_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_promotion_v1_promotion_service_proto_rawDescData)
	})
	return file_moego_service_promotion_v1_promotion_service_proto_rawDescData
}

var file_moego_service_promotion_v1_promotion_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_promotion_v1_promotion_service_proto_goTypes = []interface{}{
	(*RecommendCouponsRequest)(nil),    // 0: moego.service.promotion.v1.RecommendCouponsRequest
	(*RecommendCouponsResponse)(nil),   // 1: moego.service.promotion.v1.RecommendCouponsResponse
	(*PreviewCouponsRequest)(nil),      // 2: moego.service.promotion.v1.PreviewCouponsRequest
	(*PreviewCouponsResponse)(nil),     // 3: moego.service.promotion.v1.PreviewCouponsResponse
	(*RedeemCouponsRequest)(nil),       // 4: moego.service.promotion.v1.RedeemCouponsRequest
	(*RedeemCouponsResponse)(nil),      // 5: moego.service.promotion.v1.RedeemCouponsResponse
	(*RefundCouponsRequest)(nil),       // 6: moego.service.promotion.v1.RefundCouponsRequest
	(*RefundCouponsResponse)(nil),      // 7: moego.service.promotion.v1.RefundCouponsResponse
	(*SearchCouponsRequest)(nil),       // 8: moego.service.promotion.v1.SearchCouponsRequest
	(*SearchCouponsResponse)(nil),      // 9: moego.service.promotion.v1.SearchCouponsResponse
	(*v1.CouponApplicationTarget)(nil), // 10: moego.models.promotion.v1.CouponApplicationTarget
	(*v1.CouponSearchCondition)(nil),   // 11: moego.models.promotion.v1.CouponSearchCondition
	(*v1.CouponUsage)(nil),             // 12: moego.models.promotion.v1.CouponUsage
	(*v1.Coupon)(nil),                  // 13: moego.models.promotion.v1.Coupon
	(*money.Money)(nil),                // 14: google.type.Money
	(*v1.TargetDeduction)(nil),         // 15: moego.models.promotion.v1.TargetDeduction
	(*v1.CouponRedeem)(nil),            // 16: moego.models.promotion.v1.CouponRedeem
	(*v1.CouponRevision)(nil),          // 17: moego.models.promotion.v1.CouponRevision
}
var file_moego_service_promotion_v1_promotion_service_proto_depIdxs = []int32{
	10, // 0: moego.service.promotion.v1.RecommendCouponsRequest.targets:type_name -> moego.models.promotion.v1.CouponApplicationTarget
	11, // 1: moego.service.promotion.v1.RecommendCouponsRequest.search_condition:type_name -> moego.models.promotion.v1.CouponSearchCondition
	12, // 2: moego.service.promotion.v1.RecommendCouponsResponse.recommended_coupons:type_name -> moego.models.promotion.v1.CouponUsage
	13, // 3: moego.service.promotion.v1.RecommendCouponsResponse.available_coupons:type_name -> moego.models.promotion.v1.Coupon
	14, // 4: moego.service.promotion.v1.RecommendCouponsResponse.estimated_discount_amount:type_name -> google.type.Money
	13, // 5: moego.service.promotion.v1.RecommendCouponsResponse.unavailable_coupons:type_name -> moego.models.promotion.v1.Coupon
	12, // 6: moego.service.promotion.v1.PreviewCouponsRequest.coupon_usages:type_name -> moego.models.promotion.v1.CouponUsage
	15, // 7: moego.service.promotion.v1.PreviewCouponsResponse.deductions:type_name -> moego.models.promotion.v1.TargetDeduction
	14, // 8: moego.service.promotion.v1.PreviewCouponsResponse.total_discount_amount:type_name -> google.type.Money
	16, // 9: moego.service.promotion.v1.RedeemCouponsRequest.redeems:type_name -> moego.models.promotion.v1.CouponRedeem
	14, // 10: moego.service.promotion.v1.RedeemCouponsRequest.order_sales:type_name -> google.type.Money
	17, // 11: moego.service.promotion.v1.RefundCouponsRequest.revisions:type_name -> moego.models.promotion.v1.CouponRevision
	17, // 12: moego.service.promotion.v1.RefundCouponsResponse.revisions:type_name -> moego.models.promotion.v1.CouponRevision
	11, // 13: moego.service.promotion.v1.SearchCouponsRequest.search_condition:type_name -> moego.models.promotion.v1.CouponSearchCondition
	13, // 14: moego.service.promotion.v1.SearchCouponsResponse.coupons:type_name -> moego.models.promotion.v1.Coupon
	0,  // 15: moego.service.promotion.v1.PromotionService.RecommendCoupons:input_type -> moego.service.promotion.v1.RecommendCouponsRequest
	2,  // 16: moego.service.promotion.v1.PromotionService.PreviewCoupons:input_type -> moego.service.promotion.v1.PreviewCouponsRequest
	4,  // 17: moego.service.promotion.v1.PromotionService.RedeemCoupons:input_type -> moego.service.promotion.v1.RedeemCouponsRequest
	6,  // 18: moego.service.promotion.v1.PromotionService.RefundCoupons:input_type -> moego.service.promotion.v1.RefundCouponsRequest
	8,  // 19: moego.service.promotion.v1.PromotionService.SearchCoupons:input_type -> moego.service.promotion.v1.SearchCouponsRequest
	1,  // 20: moego.service.promotion.v1.PromotionService.RecommendCoupons:output_type -> moego.service.promotion.v1.RecommendCouponsResponse
	3,  // 21: moego.service.promotion.v1.PromotionService.PreviewCoupons:output_type -> moego.service.promotion.v1.PreviewCouponsResponse
	5,  // 22: moego.service.promotion.v1.PromotionService.RedeemCoupons:output_type -> moego.service.promotion.v1.RedeemCouponsResponse
	7,  // 23: moego.service.promotion.v1.PromotionService.RefundCoupons:output_type -> moego.service.promotion.v1.RefundCouponsResponse
	9,  // 24: moego.service.promotion.v1.PromotionService.SearchCoupons:output_type -> moego.service.promotion.v1.SearchCouponsResponse
	20, // [20:25] is the sub-list for method output_type
	15, // [15:20] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_service_promotion_v1_promotion_service_proto_init() }
func file_moego_service_promotion_v1_promotion_service_proto_init() {
	if File_moego_service_promotion_v1_promotion_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_promotion_v1_promotion_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_promotion_v1_promotion_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*RedeemCouponsRequest_AppointmentId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_promotion_v1_promotion_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_promotion_v1_promotion_service_proto_goTypes,
		DependencyIndexes: file_moego_service_promotion_v1_promotion_service_proto_depIdxs,
		MessageInfos:      file_moego_service_promotion_v1_promotion_service_proto_msgTypes,
	}.Build()
	File_moego_service_promotion_v1_promotion_service_proto = out.File
	file_moego_service_promotion_v1_promotion_service_proto_rawDesc = nil
	file_moego_service_promotion_v1_promotion_service_proto_goTypes = nil
	file_moego_service_promotion_v1_promotion_service_proto_depIdxs = nil
}
