// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_initialization_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerInitializationServiceClient is the client API for BusinessCustomerInitializationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerInitializationServiceClient interface {
	// Initialize business customer module for a tenant (company).
	// If `init_setting` is set, the following settings will be initialized:
	// - customer referral source
	// - customer tag
	// - default preferred frequency
	// - pet type
	// - pet breed
	// - pet behavior
	// - pet code
	// - pet coat type
	// - pet fixed
	// - pet size
	// - pet vaccine
	// If `copy_from_tenant` is set, these settings will be copied from it.
	// Otherwise, these settings will be copied from platform template, and in this case `weight_unit` should be set to
	// help initialize pet size.
	//
	// If `init_demo_profile` is set, the following data will be initialized:
	// - a customer named Demo Profile with a main contact and a primary address
	// - a note for Demo Profile, created by given `staff_id`
	// - two pets for Demo Profile, named Mini and Max
	// - a note for each pet, created by given `staff_id`
	// - bind three tags to Demo Profile, if there are enough tags initialized
	// - bind two pet codes to each pet, if there are enough pet codes initialized
	// - add a vaccine record to each pet, if there are enough vaccines initialized
	InitializeBusinessCustomerModule(ctx context.Context, in *InitializeBusinessCustomerModuleRequest, opts ...grpc.CallOption) (*InitializeBusinessCustomerModuleResponse, error)
}

type businessCustomerInitializationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerInitializationServiceClient(cc grpc.ClientConnInterface) BusinessCustomerInitializationServiceClient {
	return &businessCustomerInitializationServiceClient{cc}
}

func (c *businessCustomerInitializationServiceClient) InitializeBusinessCustomerModule(ctx context.Context, in *InitializeBusinessCustomerModuleRequest, opts ...grpc.CallOption) (*InitializeBusinessCustomerModuleResponse, error) {
	out := new(InitializeBusinessCustomerModuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerInitializationService/InitializeBusinessCustomerModule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerInitializationServiceServer is the server API for BusinessCustomerInitializationService service.
// All implementations must embed UnimplementedBusinessCustomerInitializationServiceServer
// for forward compatibility
type BusinessCustomerInitializationServiceServer interface {
	// Initialize business customer module for a tenant (company).
	// If `init_setting` is set, the following settings will be initialized:
	// - customer referral source
	// - customer tag
	// - default preferred frequency
	// - pet type
	// - pet breed
	// - pet behavior
	// - pet code
	// - pet coat type
	// - pet fixed
	// - pet size
	// - pet vaccine
	// If `copy_from_tenant` is set, these settings will be copied from it.
	// Otherwise, these settings will be copied from platform template, and in this case `weight_unit` should be set to
	// help initialize pet size.
	//
	// If `init_demo_profile` is set, the following data will be initialized:
	// - a customer named Demo Profile with a main contact and a primary address
	// - a note for Demo Profile, created by given `staff_id`
	// - two pets for Demo Profile, named Mini and Max
	// - a note for each pet, created by given `staff_id`
	// - bind three tags to Demo Profile, if there are enough tags initialized
	// - bind two pet codes to each pet, if there are enough pet codes initialized
	// - add a vaccine record to each pet, if there are enough vaccines initialized
	InitializeBusinessCustomerModule(context.Context, *InitializeBusinessCustomerModuleRequest) (*InitializeBusinessCustomerModuleResponse, error)
	mustEmbedUnimplementedBusinessCustomerInitializationServiceServer()
}

// UnimplementedBusinessCustomerInitializationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerInitializationServiceServer struct {
}

func (UnimplementedBusinessCustomerInitializationServiceServer) InitializeBusinessCustomerModule(context.Context, *InitializeBusinessCustomerModuleRequest) (*InitializeBusinessCustomerModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitializeBusinessCustomerModule not implemented")
}
func (UnimplementedBusinessCustomerInitializationServiceServer) mustEmbedUnimplementedBusinessCustomerInitializationServiceServer() {
}

// UnsafeBusinessCustomerInitializationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerInitializationServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerInitializationServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerInitializationServiceServer()
}

func RegisterBusinessCustomerInitializationServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerInitializationServiceServer) {
	s.RegisterService(&BusinessCustomerInitializationService_ServiceDesc, srv)
}

func _BusinessCustomerInitializationService_InitializeBusinessCustomerModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitializeBusinessCustomerModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerInitializationServiceServer).InitializeBusinessCustomerModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerInitializationService/InitializeBusinessCustomerModule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerInitializationServiceServer).InitializeBusinessCustomerModule(ctx, req.(*InitializeBusinessCustomerModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerInitializationService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerInitializationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerInitializationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerInitializationService",
	HandlerType: (*BusinessCustomerInitializationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitializeBusinessCustomerModule",
			Handler:    _BusinessCustomerInitializationService_InitializeBusinessCustomerModule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_initialization_service.proto",
}
