// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/sms/v1/business_sms_setting_service.proto

package smssvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// one busines sms setting query request
type BusinessSmsSettingOneQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *BusinessSmsSettingOneQueryRequest) Reset() {
	*x = BusinessSmsSettingOneQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingOneQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingOneQueryRequest) ProtoMessage() {}

func (x *BusinessSmsSettingOneQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingOneQueryRequest.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingOneQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessSmsSettingOneQueryRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// busines sms setting response
type BusinessSmsSettingOneQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting model
	Setting *v1.BusinessSmsSettingModel `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *BusinessSmsSettingOneQueryResponse) Reset() {
	*x = BusinessSmsSettingOneQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingOneQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingOneQueryResponse) ProtoMessage() {}

func (x *BusinessSmsSettingOneQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingOneQueryResponse.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingOneQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessSmsSettingOneQueryResponse) GetSetting() *v1.BusinessSmsSettingModel {
	if x != nil {
		return x.Setting
	}
	return nil
}

// multi busines sms setting query request
type BusinessSmsSettingMultiQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *BusinessSmsSettingMultiQueryRequest) Reset() {
	*x = BusinessSmsSettingMultiQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingMultiQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingMultiQueryRequest) ProtoMessage() {}

func (x *BusinessSmsSettingMultiQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingMultiQueryRequest.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingMultiQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessSmsSettingMultiQueryRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// multi busines sms setting response
type BusinessSmsSettingMultiQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting model map
	Settings []*v1.BusinessSmsSettingModel `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty"`
}

func (x *BusinessSmsSettingMultiQueryResponse) Reset() {
	*x = BusinessSmsSettingMultiQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingMultiQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingMultiQueryResponse) ProtoMessage() {}

func (x *BusinessSmsSettingMultiQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingMultiQueryResponse.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingMultiQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessSmsSettingMultiQueryResponse) GetSettings() []*v1.BusinessSmsSettingModel {
	if x != nil {
		return x.Settings
	}
	return nil
}

// multi busines sms setting query request
type BusinessSmsSettingMultiQueryByNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	Number string `protobuf:"bytes,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *BusinessSmsSettingMultiQueryByNumberRequest) Reset() {
	*x = BusinessSmsSettingMultiQueryByNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingMultiQueryByNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingMultiQueryByNumberRequest) ProtoMessage() {}

func (x *BusinessSmsSettingMultiQueryByNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingMultiQueryByNumberRequest.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingMultiQueryByNumberRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{4}
}

func (x *BusinessSmsSettingMultiQueryByNumberRequest) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

// multi busines sms setting response
type BusinessSmsSettingMultiQueryByNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting model map
	Settings []*v1.BusinessSmsSettingModel `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty"`
}

func (x *BusinessSmsSettingMultiQueryByNumberResponse) Reset() {
	*x = BusinessSmsSettingMultiQueryByNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingMultiQueryByNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingMultiQueryByNumberResponse) ProtoMessage() {}

func (x *BusinessSmsSettingMultiQueryByNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingMultiQueryByNumberResponse.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingMultiQueryByNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{5}
}

func (x *BusinessSmsSettingMultiQueryByNumberResponse) GetSettings() []*v1.BusinessSmsSettingModel {
	if x != nil {
		return x.Settings
	}
	return nil
}

// business sms setting update request
type BusinessSmsSettingUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business call handle type
	CallHandleType *int32 `protobuf:"varint,6,opt,name=call_handle_type,json=callHandleType,proto3,oneof" json:"call_handle_type,omitempty"`
	// business call phone number
	CallPhoneNumber *string `protobuf:"bytes,7,opt,name=call_phone_number,json=callPhoneNumber,proto3,oneof" json:"call_phone_number,omitempty"`
	// business call reply type
	CallReplyType *int32 `protobuf:"varint,8,opt,name=call_reply_type,json=callReplyType,proto3,oneof" json:"call_reply_type,omitempty"`
	// business reply message body
	ReplyMessage *string `protobuf:"bytes,9,opt,name=reply_message,json=replyMessage,proto3,oneof" json:"reply_message,omitempty"`
	// twilio number
	TwilioNumber *string `protobuf:"bytes,10,opt,name=twilio_number,json=twilioNumber,proto3,oneof" json:"twilio_number,omitempty"`
}

func (x *BusinessSmsSettingUpdateRequest) Reset() {
	*x = BusinessSmsSettingUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingUpdateRequest) ProtoMessage() {}

func (x *BusinessSmsSettingUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingUpdateRequest.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingUpdateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{6}
}

func (x *BusinessSmsSettingUpdateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessSmsSettingUpdateRequest) GetCallHandleType() int32 {
	if x != nil && x.CallHandleType != nil {
		return *x.CallHandleType
	}
	return 0
}

func (x *BusinessSmsSettingUpdateRequest) GetCallPhoneNumber() string {
	if x != nil && x.CallPhoneNumber != nil {
		return *x.CallPhoneNumber
	}
	return ""
}

func (x *BusinessSmsSettingUpdateRequest) GetCallReplyType() int32 {
	if x != nil && x.CallReplyType != nil {
		return *x.CallReplyType
	}
	return 0
}

func (x *BusinessSmsSettingUpdateRequest) GetReplyMessage() string {
	if x != nil && x.ReplyMessage != nil {
		return *x.ReplyMessage
	}
	return ""
}

func (x *BusinessSmsSettingUpdateRequest) GetTwilioNumber() string {
	if x != nil && x.TwilioNumber != nil {
		return *x.TwilioNumber
	}
	return ""
}

// busines sms setting response
type BusinessSmsSettingUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BusinessSmsSettingUpdateResponse) Reset() {
	*x = BusinessSmsSettingUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingUpdateResponse) ProtoMessage() {}

func (x *BusinessSmsSettingUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingUpdateResponse.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingUpdateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{7}
}

// assign phone number to business request
type InitBusinessSmsSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// is create sub
	InitType v1.InitTypeEnum `protobuf:"varint,2,opt,name=init_type,json=initType,proto3,enum=moego.models.sms.v1.InitTypeEnum" json:"init_type,omitempty"`
}

func (x *InitBusinessSmsSettingRequest) Reset() {
	*x = InitBusinessSmsSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitBusinessSmsSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitBusinessSmsSettingRequest) ProtoMessage() {}

func (x *InitBusinessSmsSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitBusinessSmsSettingRequest.ProtoReflect.Descriptor instead.
func (*InitBusinessSmsSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{8}
}

func (x *InitBusinessSmsSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *InitBusinessSmsSettingRequest) GetInitType() v1.InitTypeEnum {
	if x != nil {
		return x.InitType
	}
	return v1.InitTypeEnum(0)
}

// assign response void
type InitBusinessSmsSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InitBusinessSmsSettingResponse) Reset() {
	*x = InitBusinessSmsSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitBusinessSmsSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitBusinessSmsSettingResponse) ProtoMessage() {}

func (x *InitBusinessSmsSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitBusinessSmsSettingResponse.ProtoReflect.Descriptor instead.
func (*InitBusinessSmsSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{9}
}

// recycling number empty request
type RecyclingNumberTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RecyclingNumberTaskRequest) Reset() {
	*x = RecyclingNumberTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclingNumberTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclingNumberTaskRequest) ProtoMessage() {}

func (x *RecyclingNumberTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclingNumberTaskRequest.ProtoReflect.Descriptor instead.
func (*RecyclingNumberTaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{10}
}

// recycling number empty response
type RecyclingNumberTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RecyclingNumberTaskResponse) Reset() {
	*x = RecyclingNumberTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclingNumberTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclingNumberTaskResponse) ProtoMessage() {}

func (x *RecyclingNumberTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclingNumberTaskResponse.ProtoReflect.Descriptor instead.
func (*RecyclingNumberTaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP(), []int{11}
}

var File_moego_service_sms_v1_business_sms_setting_service_proto protoreflect.FileDescriptor

var file_moego_service_sms_v1_business_sms_setting_service_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x6d, 0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x1a,
	0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6d, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x4d, 0x0a, 0x21, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0x6c, 0x0a, 0x22, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x58, 0x0a,
	0x23, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92,
	0x01, 0x08, 0x08, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x70, 0x0a, 0x24, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x48, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x4e, 0x0a, 0x2b, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x32, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x78, 0x0a, 0x2c, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0xcf, 0x03, 0x0a, 0x1f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x3f, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x1a, 0x0b, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x48, 0x00, 0x52,
	0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x01, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x0f,
	0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x1a, 0x0b, 0x20, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x48, 0x02, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x72,
	0x65, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xe8, 0x07, 0x48, 0x03, 0x52, 0x0c,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x31, 0x0a, 0x0d, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48,
	0x04, 0x52, 0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x22, 0x0a, 0x20, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x1d, 0x49, 0x6e,
	0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x09, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x20, 0x0a,
	0x1e, 0x49, 0x6e, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1c, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1d, 0x0a,
	0x1b, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x83, 0x07, 0x0a,
	0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x1a, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x4f, 0x6e, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x6e, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a,
	0x1c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x24, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x83, 0x01, 0x0a, 0x16, 0x49, 0x6e, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53,
	0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c,
	0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x69, 0x6e, 0x67, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x69, 0x6e, 0x67,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x74, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b,
	0x73, 0x6d, 0x73, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescOnce sync.Once
	file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescData = file_moego_service_sms_v1_business_sms_setting_service_proto_rawDesc
)

func file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescGZIP() []byte {
	file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescOnce.Do(func() {
		file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescData)
	})
	return file_moego_service_sms_v1_business_sms_setting_service_proto_rawDescData
}

var file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_service_sms_v1_business_sms_setting_service_proto_goTypes = []interface{}{
	(*BusinessSmsSettingOneQueryRequest)(nil),            // 0: moego.service.sms.v1.BusinessSmsSettingOneQueryRequest
	(*BusinessSmsSettingOneQueryResponse)(nil),           // 1: moego.service.sms.v1.BusinessSmsSettingOneQueryResponse
	(*BusinessSmsSettingMultiQueryRequest)(nil),          // 2: moego.service.sms.v1.BusinessSmsSettingMultiQueryRequest
	(*BusinessSmsSettingMultiQueryResponse)(nil),         // 3: moego.service.sms.v1.BusinessSmsSettingMultiQueryResponse
	(*BusinessSmsSettingMultiQueryByNumberRequest)(nil),  // 4: moego.service.sms.v1.BusinessSmsSettingMultiQueryByNumberRequest
	(*BusinessSmsSettingMultiQueryByNumberResponse)(nil), // 5: moego.service.sms.v1.BusinessSmsSettingMultiQueryByNumberResponse
	(*BusinessSmsSettingUpdateRequest)(nil),              // 6: moego.service.sms.v1.BusinessSmsSettingUpdateRequest
	(*BusinessSmsSettingUpdateResponse)(nil),             // 7: moego.service.sms.v1.BusinessSmsSettingUpdateResponse
	(*InitBusinessSmsSettingRequest)(nil),                // 8: moego.service.sms.v1.InitBusinessSmsSettingRequest
	(*InitBusinessSmsSettingResponse)(nil),               // 9: moego.service.sms.v1.InitBusinessSmsSettingResponse
	(*RecyclingNumberTaskRequest)(nil),                   // 10: moego.service.sms.v1.RecyclingNumberTaskRequest
	(*RecyclingNumberTaskResponse)(nil),                  // 11: moego.service.sms.v1.RecyclingNumberTaskResponse
	(*v1.BusinessSmsSettingModel)(nil),                   // 12: moego.models.sms.v1.BusinessSmsSettingModel
	(v1.InitTypeEnum)(0),                                 // 13: moego.models.sms.v1.InitTypeEnum
}
var file_moego_service_sms_v1_business_sms_setting_service_proto_depIdxs = []int32{
	12, // 0: moego.service.sms.v1.BusinessSmsSettingOneQueryResponse.setting:type_name -> moego.models.sms.v1.BusinessSmsSettingModel
	12, // 1: moego.service.sms.v1.BusinessSmsSettingMultiQueryResponse.settings:type_name -> moego.models.sms.v1.BusinessSmsSettingModel
	12, // 2: moego.service.sms.v1.BusinessSmsSettingMultiQueryByNumberResponse.settings:type_name -> moego.models.sms.v1.BusinessSmsSettingModel
	13, // 3: moego.service.sms.v1.InitBusinessSmsSettingRequest.init_type:type_name -> moego.models.sms.v1.InitTypeEnum
	0,  // 4: moego.service.sms.v1.BusinessSmsSettingService.QueryOneBusinessSmsSetting:input_type -> moego.service.sms.v1.BusinessSmsSettingOneQueryRequest
	2,  // 5: moego.service.sms.v1.BusinessSmsSettingService.QueryMultiBusinessSmsSetting:input_type -> moego.service.sms.v1.BusinessSmsSettingMultiQueryRequest
	4,  // 6: moego.service.sms.v1.BusinessSmsSettingService.QueryMultiBusinessSmsSettingByNumber:input_type -> moego.service.sms.v1.BusinessSmsSettingMultiQueryByNumberRequest
	6,  // 7: moego.service.sms.v1.BusinessSmsSettingService.UpdateBusinessSmsSetting:input_type -> moego.service.sms.v1.BusinessSmsSettingUpdateRequest
	8,  // 8: moego.service.sms.v1.BusinessSmsSettingService.InitBusinessSmsSetting:input_type -> moego.service.sms.v1.InitBusinessSmsSettingRequest
	10, // 9: moego.service.sms.v1.BusinessSmsSettingService.RecyclingNumberTask:input_type -> moego.service.sms.v1.RecyclingNumberTaskRequest
	1,  // 10: moego.service.sms.v1.BusinessSmsSettingService.QueryOneBusinessSmsSetting:output_type -> moego.service.sms.v1.BusinessSmsSettingOneQueryResponse
	3,  // 11: moego.service.sms.v1.BusinessSmsSettingService.QueryMultiBusinessSmsSetting:output_type -> moego.service.sms.v1.BusinessSmsSettingMultiQueryResponse
	5,  // 12: moego.service.sms.v1.BusinessSmsSettingService.QueryMultiBusinessSmsSettingByNumber:output_type -> moego.service.sms.v1.BusinessSmsSettingMultiQueryByNumberResponse
	7,  // 13: moego.service.sms.v1.BusinessSmsSettingService.UpdateBusinessSmsSetting:output_type -> moego.service.sms.v1.BusinessSmsSettingUpdateResponse
	9,  // 14: moego.service.sms.v1.BusinessSmsSettingService.InitBusinessSmsSetting:output_type -> moego.service.sms.v1.InitBusinessSmsSettingResponse
	11, // 15: moego.service.sms.v1.BusinessSmsSettingService.RecyclingNumberTask:output_type -> moego.service.sms.v1.RecyclingNumberTaskResponse
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_sms_v1_business_sms_setting_service_proto_init() }
func file_moego_service_sms_v1_business_sms_setting_service_proto_init() {
	if File_moego_service_sms_v1_business_sms_setting_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingOneQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingOneQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingMultiQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingMultiQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingMultiQueryByNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingMultiQueryByNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitBusinessSmsSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitBusinessSmsSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclingNumberTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclingNumberTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_sms_v1_business_sms_setting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_sms_v1_business_sms_setting_service_proto_goTypes,
		DependencyIndexes: file_moego_service_sms_v1_business_sms_setting_service_proto_depIdxs,
		MessageInfos:      file_moego_service_sms_v1_business_sms_setting_service_proto_msgTypes,
	}.Build()
	File_moego_service_sms_v1_business_sms_setting_service_proto = out.File
	file_moego_service_sms_v1_business_sms_setting_service_proto_rawDesc = nil
	file_moego_service_sms_v1_business_sms_setting_service_proto_goTypes = nil
	file_moego_service_sms_v1_business_sms_setting_service_proto_depIdxs = nil
}
