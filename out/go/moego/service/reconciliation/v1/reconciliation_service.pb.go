// @since 2024-07-24 10:22:15
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/reconciliation/v1/reconciliation_service.proto

package reconciliationsvcpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// execute type
type ExecuteReconciliationRequest_Type int32

const (
	// unknown
	ExecuteReconciliationRequest_TYPE_UNSPECIFIED ExecuteReconciliationRequest_Type = 0
	// order, execute order unilateral reconciliation
	ExecuteReconciliationRequest_TYPE_ORDER_UNILATERAL ExecuteReconciliationRequest_Type = 1
	// payment, execute payment unilateral reconciliation
	ExecuteReconciliationRequest_TYPE_PAYMENT_UNILATERAL ExecuteReconciliationRequest_Type = 2
	// split payment
	ExecuteReconciliationRequest_TYPE_SPLIT_PAYMENT_UNILATERAL ExecuteReconciliationRequest_Type = 3
	// split payment reversal reconciliation
	ExecuteReconciliationRequest_TYPE_SPLIT_PAYMENT_REVERSAL_UNILATERAL ExecuteReconciliationRequest_Type = 4
	// refund reconciliation
	ExecuteReconciliationRequest_TYPE_REFUND_UNILATERAL ExecuteReconciliationRequest_Type = 5
)

// Enum value maps for ExecuteReconciliationRequest_Type.
var (
	ExecuteReconciliationRequest_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TYPE_ORDER_UNILATERAL",
		2: "TYPE_PAYMENT_UNILATERAL",
		3: "TYPE_SPLIT_PAYMENT_UNILATERAL",
		4: "TYPE_SPLIT_PAYMENT_REVERSAL_UNILATERAL",
		5: "TYPE_REFUND_UNILATERAL",
	}
	ExecuteReconciliationRequest_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":                       0,
		"TYPE_ORDER_UNILATERAL":                  1,
		"TYPE_PAYMENT_UNILATERAL":                2,
		"TYPE_SPLIT_PAYMENT_UNILATERAL":          3,
		"TYPE_SPLIT_PAYMENT_REVERSAL_UNILATERAL": 4,
		"TYPE_REFUND_UNILATERAL":                 5,
	}
)

func (x ExecuteReconciliationRequest_Type) Enum() *ExecuteReconciliationRequest_Type {
	p := new(ExecuteReconciliationRequest_Type)
	*p = x
	return p
}

func (x ExecuteReconciliationRequest_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecuteReconciliationRequest_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_reconciliation_v1_reconciliation_service_proto_enumTypes[0].Descriptor()
}

func (ExecuteReconciliationRequest_Type) Type() protoreflect.EnumType {
	return &file_moego_service_reconciliation_v1_reconciliation_service_proto_enumTypes[0]
}

func (x ExecuteReconciliationRequest_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecuteReconciliationRequest_Type.Descriptor instead.
func (ExecuteReconciliationRequest_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescGZIP(), []int{0, 0}
}

// the execute reconciliation request
type ExecuteReconciliationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reconciliation type
	Type ExecuteReconciliationRequest_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.service.reconciliation.v1.ExecuteReconciliationRequest_Type" json:"type,omitempty"`
	// start time
	// the reconciliation task will reconcile the data from ${start_time} to ${end_time}
	//
	// if the ${start_time} is not set, default value is 10 minutes ago
	StartTime *int64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// end time
	// if the ${end_time} is not set, default value is now
	//
	// the ${end_time} must be greater than ${start_time},
	// if ${end_time} less than ${start_time}, both parameters will be set to default values
	// that is from 10 minutes ago until now
	EndTime *int64 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *ExecuteReconciliationRequest) Reset() {
	*x = ExecuteReconciliationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteReconciliationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteReconciliationRequest) ProtoMessage() {}

func (x *ExecuteReconciliationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteReconciliationRequest.ProtoReflect.Descriptor instead.
func (*ExecuteReconciliationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescGZIP(), []int{0}
}

func (x *ExecuteReconciliationRequest) GetType() ExecuteReconciliationRequest_Type {
	if x != nil {
		return x.Type
	}
	return ExecuteReconciliationRequest_TYPE_UNSPECIFIED
}

func (x *ExecuteReconciliationRequest) GetStartTime() int64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *ExecuteReconciliationRequest) GetEndTime() int64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

var File_moego_service_reconciliation_v1_reconciliation_service_proto protoreflect.FileDescriptor

var file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65,
	0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98, 0x03, 0x0a,
	0x1c, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x22, 0xbf, 0x01, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x49, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x02,
	0x12, 0x21, 0x0a, 0x1d, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x41,
	0x4c, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x4c, 0x49,
	0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53,
	0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x49, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x04, 0x12,
	0x1a, 0x0a, 0x16, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x55,
	0x4e, 0x49, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x05, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x32, 0x87, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x63, 0x6f,
	0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x6e, 0x0a, 0x15, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6e,
	0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x42, 0x95, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x68, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescOnce sync.Once
	file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescData = file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDesc
)

func file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescGZIP() []byte {
	file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescOnce.Do(func() {
		file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescData)
	})
	return file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDescData
}

var file_moego_service_reconciliation_v1_reconciliation_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_service_reconciliation_v1_reconciliation_service_proto_goTypes = []interface{}{
	(ExecuteReconciliationRequest_Type)(0), // 0: moego.service.reconciliation.v1.ExecuteReconciliationRequest.Type
	(*ExecuteReconciliationRequest)(nil),   // 1: moego.service.reconciliation.v1.ExecuteReconciliationRequest
	(*emptypb.Empty)(nil),                  // 2: google.protobuf.Empty
}
var file_moego_service_reconciliation_v1_reconciliation_service_proto_depIdxs = []int32{
	0, // 0: moego.service.reconciliation.v1.ExecuteReconciliationRequest.type:type_name -> moego.service.reconciliation.v1.ExecuteReconciliationRequest.Type
	1, // 1: moego.service.reconciliation.v1.ReconciliationService.ExecuteReconciliation:input_type -> moego.service.reconciliation.v1.ExecuteReconciliationRequest
	2, // 2: moego.service.reconciliation.v1.ReconciliationService.ExecuteReconciliation:output_type -> google.protobuf.Empty
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_reconciliation_v1_reconciliation_service_proto_init() }
func file_moego_service_reconciliation_v1_reconciliation_service_proto_init() {
	if File_moego_service_reconciliation_v1_reconciliation_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteReconciliationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_reconciliation_v1_reconciliation_service_proto_goTypes,
		DependencyIndexes: file_moego_service_reconciliation_v1_reconciliation_service_proto_depIdxs,
		EnumInfos:         file_moego_service_reconciliation_v1_reconciliation_service_proto_enumTypes,
		MessageInfos:      file_moego_service_reconciliation_v1_reconciliation_service_proto_msgTypes,
	}.Build()
	File_moego_service_reconciliation_v1_reconciliation_service_proto = out.File
	file_moego_service_reconciliation_v1_reconciliation_service_proto_rawDesc = nil
	file_moego_service_reconciliation_v1_reconciliation_service_proto_goTypes = nil
	file_moego_service_reconciliation_v1_reconciliation_service_proto_depIdxs = nil
}
