// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/open_platform/v1/webhook_service.proto

package openplatformsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/open_platform/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateWebhookRequest
type CreateWebhookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id is the ID of the enterprise owning the webhook.
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// client_id is the ID of the associated client.
	ClientId string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// endpoint_url is the URL where the webhook will be delivered.
	EndpointUrl string `protobuf:"bytes,3,opt,name=endpoint_url,json=endpointUrl,proto3" json:"endpoint_url,omitempty"`
	// event_types is the list of event types this webhook subscribes to.
	// If it is empty, no events will be subscribed to
	EventTypes []v1.EventType `protobuf:"varint,4,rep,packed,name=event_types,json=eventTypes,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_types,omitempty"`
	// List of organizations the webhook is subscribed to.
	// If empty, the webhook is subscribed to all organizations.
	Organizations []*v11.Organization `protobuf:"bytes,5,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// secret_token is an optional HMAC token used to sign payloads.
	SecretToken *string `protobuf:"bytes,6,opt,name=secret_token,json=secretToken,proto3,oneof" json:"secret_token,omitempty"`
	// content_type specifies the Content-Type header when delivering payloads.
	ContentType v11.Webhook_ContentType `protobuf:"varint,7,opt,name=content_type,json=contentType,proto3,enum=moego.models.open_platform.v1.Webhook_ContentType" json:"content_type,omitempty"`
	// is_active indicates whether the webhook is currently active.
	IsActive *bool `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// Whether to verify SSL certificates when delivering payloads.
	// Default: true (recommended for security)
	VerifySsl *bool `protobuf:"varint,9,opt,name=verify_ssl,json=verifySsl,proto3,oneof" json:"verify_ssl,omitempty"`
	// Custom HTTP headers to include when delivering payloads.
	Headers map[string]*v11.Webhook_HeaderValues `protobuf:"bytes,10,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CreateWebhookRequest) Reset() {
	*x = CreateWebhookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWebhookRequest) ProtoMessage() {}

func (x *CreateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWebhookRequest.ProtoReflect.Descriptor instead.
func (*CreateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWebhookRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateWebhookRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CreateWebhookRequest) GetEndpointUrl() string {
	if x != nil {
		return x.EndpointUrl
	}
	return ""
}

func (x *CreateWebhookRequest) GetEventTypes() []v1.EventType {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

func (x *CreateWebhookRequest) GetOrganizations() []*v11.Organization {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *CreateWebhookRequest) GetSecretToken() string {
	if x != nil && x.SecretToken != nil {
		return *x.SecretToken
	}
	return ""
}

func (x *CreateWebhookRequest) GetContentType() v11.Webhook_ContentType {
	if x != nil {
		return x.ContentType
	}
	return v11.Webhook_ContentType(0)
}

func (x *CreateWebhookRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *CreateWebhookRequest) GetVerifySsl() bool {
	if x != nil && x.VerifySsl != nil {
		return *x.VerifySsl
	}
	return false
}

func (x *CreateWebhookRequest) GetHeaders() map[string]*v11.Webhook_HeaderValues {
	if x != nil {
		return x.Headers
	}
	return nil
}

// CreateWebhookResponse
type CreateWebhookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhook is the created webhook configuration.
	Webhook *v11.Webhook `protobuf:"bytes,1,opt,name=webhook,proto3" json:"webhook,omitempty"`
}

func (x *CreateWebhookResponse) Reset() {
	*x = CreateWebhookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWebhookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWebhookResponse) ProtoMessage() {}

func (x *CreateWebhookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWebhookResponse.ProtoReflect.Descriptor instead.
func (*CreateWebhookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateWebhookResponse) GetWebhook() *v11.Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

// GetWebhookRequest
type GetWebhookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id is the unique identifier of the webhook.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetWebhookRequest) Reset() {
	*x = GetWebhookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookRequest) ProtoMessage() {}

func (x *GetWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetWebhookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetWebhookResponse
type GetWebhookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhook is the retrieved webhook configuration.
	Webhook *v11.Webhook `protobuf:"bytes,1,opt,name=webhook,proto3" json:"webhook,omitempty"`
}

func (x *GetWebhookResponse) Reset() {
	*x = GetWebhookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookResponse) ProtoMessage() {}

func (x *GetWebhookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookResponse.ProtoReflect.Descriptor instead.
func (*GetWebhookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetWebhookResponse) GetWebhook() *v11.Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

// UpdateWebhookRequest
type UpdateWebhookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id is the unique identifier of the webhook.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// endpoint_url is the URL where the webhook will be delivered.
	EndpointUrl string `protobuf:"bytes,2,opt,name=endpoint_url,json=endpointUrl,proto3" json:"endpoint_url,omitempty"`
	// event_types is the list of event types this webhook subscribes to.
	EventTypes []v1.EventType `protobuf:"varint,3,rep,packed,name=event_types,json=eventTypes,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_types,omitempty"`
	// List of organizations the webhook is subscribed to.
	// If empty, the webhook is subscribed to all organizations.
	Organizations []*v11.Organization `protobuf:"bytes,4,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// secret_token is an optional HMAC token used to sign payloads.
	SecretToken *string `protobuf:"bytes,5,opt,name=secret_token,json=secretToken,proto3,oneof" json:"secret_token,omitempty"`
	// content_type specifies the Content-Type header when delivering payloads.
	ContentType v11.Webhook_ContentType `protobuf:"varint,6,opt,name=content_type,json=contentType,proto3,enum=moego.models.open_platform.v1.Webhook_ContentType" json:"content_type,omitempty"`
	// is_active indicates whether the webhook is currently active.
	IsActive *bool `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// Whether to verify SSL certificates when delivering payloads.
	// Default: true (recommended for security)
	VerifySsl *bool `protobuf:"varint,8,opt,name=verify_ssl,json=verifySsl,proto3,oneof" json:"verify_ssl,omitempty"`
	// Custom HTTP headers to include when delivering payloads.
	Headers map[string]*v11.Webhook_HeaderValues `protobuf:"bytes,9,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateWebhookRequest) Reset() {
	*x = UpdateWebhookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookRequest) ProtoMessage() {}

func (x *UpdateWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookRequest.ProtoReflect.Descriptor instead.
func (*UpdateWebhookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWebhookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateWebhookRequest) GetEndpointUrl() string {
	if x != nil {
		return x.EndpointUrl
	}
	return ""
}

func (x *UpdateWebhookRequest) GetEventTypes() []v1.EventType {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

func (x *UpdateWebhookRequest) GetOrganizations() []*v11.Organization {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *UpdateWebhookRequest) GetSecretToken() string {
	if x != nil && x.SecretToken != nil {
		return *x.SecretToken
	}
	return ""
}

func (x *UpdateWebhookRequest) GetContentType() v11.Webhook_ContentType {
	if x != nil {
		return x.ContentType
	}
	return v11.Webhook_ContentType(0)
}

func (x *UpdateWebhookRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateWebhookRequest) GetVerifySsl() bool {
	if x != nil && x.VerifySsl != nil {
		return *x.VerifySsl
	}
	return false
}

func (x *UpdateWebhookRequest) GetHeaders() map[string]*v11.Webhook_HeaderValues {
	if x != nil {
		return x.Headers
	}
	return nil
}

// UpdateWebhookResponse
type UpdateWebhookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhook is the updated webhook configuration.
	Webhook *v11.Webhook `protobuf:"bytes,1,opt,name=webhook,proto3" json:"webhook,omitempty"`
}

func (x *UpdateWebhookResponse) Reset() {
	*x = UpdateWebhookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWebhookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookResponse) ProtoMessage() {}

func (x *UpdateWebhookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookResponse.ProtoReflect.Descriptor instead.
func (*UpdateWebhookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWebhookResponse) GetWebhook() *v11.Webhook {
	if x != nil {
		return x.Webhook
	}
	return nil
}

// DeleteWebhookRequest
type DeleteWebhookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id is the unique identifier of the webhook.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteWebhookRequest) Reset() {
	*x = DeleteWebhookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWebhookRequest) ProtoMessage() {}

func (x *DeleteWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWebhookRequest.ProtoReflect.Descriptor instead.
func (*DeleteWebhookRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteWebhookRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteWebhookResponse
type DeleteWebhookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteWebhookResponse) Reset() {
	*x = DeleteWebhookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWebhookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWebhookResponse) ProtoMessage() {}

func (x *DeleteWebhookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWebhookResponse.ProtoReflect.Descriptor instead.
func (*DeleteWebhookResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{7}
}

// ListWebhooksRequest
type ListWebhooksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id filters webhooks by enterprise.
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// client_id filters webhooks by client.
	ClientId *string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`
	// filter options for webhooks
	Filter *ListWebhooksRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// order by
	OrderBy *v2.OrderBy `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// pagination options
	Pagination *v2.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWebhooksRequest) Reset() {
	*x = ListWebhooksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksRequest) ProtoMessage() {}

func (x *ListWebhooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListWebhooksRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListWebhooksRequest) GetClientId() string {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return ""
}

func (x *ListWebhooksRequest) GetFilter() *ListWebhooksRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListWebhooksRequest) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ListWebhooksRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListWebhooksResponse
type ListWebhooksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhooks is the list of matching webhook configurations.
	Webhooks []*v11.Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
	// pagination metadata
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWebhooksResponse) Reset() {
	*x = ListWebhooksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksResponse) ProtoMessage() {}

func (x *ListWebhooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListWebhooksResponse) GetWebhooks() []*v11.Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

func (x *ListWebhooksResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListWebhooksByEventRequest defines the request for listing webhooks by event name.
type ListWebhooksByEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id filters webhooks by enterprise.
	EnterpriseId *int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// event_type is the event type to filter webhooks.
	EventType v1.EventType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_type,omitempty"`
}

func (x *ListWebhooksByEventRequest) Reset() {
	*x = ListWebhooksByEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksByEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksByEventRequest) ProtoMessage() {}

func (x *ListWebhooksByEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksByEventRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksByEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListWebhooksByEventRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ListWebhooksByEventRequest) GetEventType() v1.EventType {
	if x != nil {
		return x.EventType
	}
	return v1.EventType(0)
}

// ListWebhooksByEventResponse defines the response for listing webhooks by event name.
type ListWebhooksByEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhooks is the list of matching webhook configurations.
	Webhooks []*v11.Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
}

func (x *ListWebhooksByEventResponse) Reset() {
	*x = ListWebhooksByEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksByEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksByEventResponse) ProtoMessage() {}

func (x *ListWebhooksByEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksByEventResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksByEventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListWebhooksByEventResponse) GetWebhooks() []*v11.Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

// ListWebhooksByClientRequest defines the request for listing webhooks by client ID.
type ListWebhooksByClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id filters webhooks by enterprise.
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// client_id is the ID of the associated client.
	ClientId string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *ListWebhooksByClientRequest) Reset() {
	*x = ListWebhooksByClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksByClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksByClientRequest) ProtoMessage() {}

func (x *ListWebhooksByClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksByClientRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksByClientRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListWebhooksByClientRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListWebhooksByClientRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

// ListWebhooksByClientResponse defines the response for listing webhooks by client ID.
type ListWebhooksByClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhooks is the list of matching webhook configurations.
	Webhooks []*v11.Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
}

func (x *ListWebhooksByClientResponse) Reset() {
	*x = ListWebhooksByClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksByClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksByClientResponse) ProtoMessage() {}

func (x *ListWebhooksByClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksByClientResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksByClientResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListWebhooksByClientResponse) GetWebhooks() []*v11.Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

// GetWebhookDeliveryRequest
type GetWebhookDeliveryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id is the unique identifier of the webhook event delivery.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetWebhookDeliveryRequest) Reset() {
	*x = GetWebhookDeliveryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookDeliveryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookDeliveryRequest) ProtoMessage() {}

func (x *GetWebhookDeliveryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookDeliveryRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookDeliveryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetWebhookDeliveryRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetWebhookDeliveryResponse
type GetWebhookDeliveryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delivery is the retrieved webhook event delivery log.
	Delivery *v11.WebhookDelivery `protobuf:"bytes,1,opt,name=delivery,proto3" json:"delivery,omitempty"`
}

func (x *GetWebhookDeliveryResponse) Reset() {
	*x = GetWebhookDeliveryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookDeliveryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookDeliveryResponse) ProtoMessage() {}

func (x *GetWebhookDeliveryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookDeliveryResponse.ProtoReflect.Descriptor instead.
func (*GetWebhookDeliveryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetWebhookDeliveryResponse) GetDelivery() *v11.WebhookDelivery {
	if x != nil {
		return x.Delivery
	}
	return nil
}

// ListWebhookDeliveriesRequest
type ListWebhookDeliveriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhook_id filters deliveries by the webhook.
	WebhookId int64 `protobuf:"varint,1,opt,name=webhook_id,json=webhookId,proto3" json:"webhook_id,omitempty"`
	// filter options for deliveries
	Filter *ListWebhookDeliveriesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// order by field
	OrderBy *v2.OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// pagination options
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWebhookDeliveriesRequest) Reset() {
	*x = ListWebhookDeliveriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhookDeliveriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhookDeliveriesRequest) ProtoMessage() {}

func (x *ListWebhookDeliveriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhookDeliveriesRequest.ProtoReflect.Descriptor instead.
func (*ListWebhookDeliveriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListWebhookDeliveriesRequest) GetWebhookId() int64 {
	if x != nil {
		return x.WebhookId
	}
	return 0
}

func (x *ListWebhookDeliveriesRequest) GetFilter() *ListWebhookDeliveriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListWebhookDeliveriesRequest) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *ListWebhookDeliveriesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListWebhookDeliveriesResponse
type ListWebhookDeliveriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deliveries is the list of matching webhook event deliveries.
	Deliveries []*v11.WebhookDelivery `protobuf:"bytes,1,rep,name=deliveries,proto3" json:"deliveries,omitempty"`
	// pagination metadata
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWebhookDeliveriesResponse) Reset() {
	*x = ListWebhookDeliveriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhookDeliveriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhookDeliveriesResponse) ProtoMessage() {}

func (x *ListWebhookDeliveriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhookDeliveriesResponse.ProtoReflect.Descriptor instead.
func (*ListWebhookDeliveriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListWebhookDeliveriesResponse) GetDeliveries() []*v11.WebhookDelivery {
	if x != nil {
		return x.Deliveries
	}
	return nil
}

func (x *ListWebhookDeliveriesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// BatchUpsertWebhookDeliveriesRequest defines the request for bulk creation or update of webhook deliveries.
type BatchUpsertWebhookDeliveriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of webhook deliveries to upsert.
	Deliveries []*v11.WebhookDelivery `protobuf:"bytes,1,rep,name=deliveries,proto3" json:"deliveries,omitempty"`
}

func (x *BatchUpsertWebhookDeliveriesRequest) Reset() {
	*x = BatchUpsertWebhookDeliveriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertWebhookDeliveriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertWebhookDeliveriesRequest) ProtoMessage() {}

func (x *BatchUpsertWebhookDeliveriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertWebhookDeliveriesRequest.ProtoReflect.Descriptor instead.
func (*BatchUpsertWebhookDeliveriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{18}
}

func (x *BatchUpsertWebhookDeliveriesRequest) GetDeliveries() []*v11.WebhookDelivery {
	if x != nil {
		return x.Deliveries
	}
	return nil
}

// BatchUpsertWebhookDeliveriesResponse returns the result of a batch operation.
type BatchUpsertWebhookDeliveriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of successfully processed deliveries.
	SuccessCount int32 `protobuf:"varint,1,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`
	// Optional list of errors (if any).
	Errors []string `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
	// List of deliveries that were processed successfully.
	Deliveries []*v11.WebhookDelivery `protobuf:"bytes,3,rep,name=deliveries,proto3" json:"deliveries,omitempty"`
}

func (x *BatchUpsertWebhookDeliveriesResponse) Reset() {
	*x = BatchUpsertWebhookDeliveriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertWebhookDeliveriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertWebhookDeliveriesResponse) ProtoMessage() {}

func (x *BatchUpsertWebhookDeliveriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertWebhookDeliveriesResponse.ProtoReflect.Descriptor instead.
func (*BatchUpsertWebhookDeliveriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{19}
}

func (x *BatchUpsertWebhookDeliveriesResponse) GetSuccessCount() int32 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchUpsertWebhookDeliveriesResponse) GetErrors() []string {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *BatchUpsertWebhookDeliveriesResponse) GetDeliveries() []*v11.WebhookDelivery {
	if x != nil {
		return x.Deliveries
	}
	return nil
}

// GetWebhookQuotaRequest
type GetWebhookQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id filters quota by the client.
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *GetWebhookQuotaRequest) Reset() {
	*x = GetWebhookQuotaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookQuotaRequest) ProtoMessage() {}

func (x *GetWebhookQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookQuotaRequest.ProtoReflect.Descriptor instead.
func (*GetWebhookQuotaRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetWebhookQuotaRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

// GetWebhookQuotaResponse
type GetWebhookQuotaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quota is the retrieved webhook quota configuration.
	Quota *v11.WebhookQuotaConfig `protobuf:"bytes,1,opt,name=quota,proto3" json:"quota,omitempty"`
}

func (x *GetWebhookQuotaResponse) Reset() {
	*x = GetWebhookQuotaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebhookQuotaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebhookQuotaResponse) ProtoMessage() {}

func (x *GetWebhookQuotaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebhookQuotaResponse.ProtoReflect.Descriptor instead.
func (*GetWebhookQuotaResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetWebhookQuotaResponse) GetQuota() *v11.WebhookQuotaConfig {
	if x != nil {
		return x.Quota
	}
	return nil
}

// UpdateWebhookQuotaRequest
type UpdateWebhookQuotaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id filters quota by the client.
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// Maximum number of webhooks allowed for this client.
	MaxWebhooks int32 `protobuf:"varint,2,opt,name=max_webhooks,json=maxWebhooks,proto3" json:"max_webhooks,omitempty"`
	// Maximum number of days to retain delivery logs for this client.
	MaxDeliveryRetentionDays int32 `protobuf:"varint,3,opt,name=max_delivery_retention_days,json=maxDeliveryRetentionDays,proto3" json:"max_delivery_retention_days,omitempty"`
	// Maximum number of push events allowed per minute for this client.
	MaxPushPerMinute int32 `protobuf:"varint,4,opt,name=max_push_per_minute,json=maxPushPerMinute,proto3" json:"max_push_per_minute,omitempty"`
	// Maximum number of push events allowed per day for this client.
	MaxPushPerDay int32 `protobuf:"varint,5,opt,name=max_push_per_day,json=maxPushPerDay,proto3" json:"max_push_per_day,omitempty"`
	// Maximum number of push events allowed per month for this client.
	MaxPushPerMonth int32 `protobuf:"varint,6,opt,name=max_push_per_month,json=maxPushPerMonth,proto3" json:"max_push_per_month,omitempty"`
}

func (x *UpdateWebhookQuotaRequest) Reset() {
	*x = UpdateWebhookQuotaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWebhookQuotaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookQuotaRequest) ProtoMessage() {}

func (x *UpdateWebhookQuotaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookQuotaRequest.ProtoReflect.Descriptor instead.
func (*UpdateWebhookQuotaRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateWebhookQuotaRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *UpdateWebhookQuotaRequest) GetMaxWebhooks() int32 {
	if x != nil {
		return x.MaxWebhooks
	}
	return 0
}

func (x *UpdateWebhookQuotaRequest) GetMaxDeliveryRetentionDays() int32 {
	if x != nil {
		return x.MaxDeliveryRetentionDays
	}
	return 0
}

func (x *UpdateWebhookQuotaRequest) GetMaxPushPerMinute() int32 {
	if x != nil {
		return x.MaxPushPerMinute
	}
	return 0
}

func (x *UpdateWebhookQuotaRequest) GetMaxPushPerDay() int32 {
	if x != nil {
		return x.MaxPushPerDay
	}
	return 0
}

func (x *UpdateWebhookQuotaRequest) GetMaxPushPerMonth() int32 {
	if x != nil {
		return x.MaxPushPerMonth
	}
	return 0
}

// UpdateWebhookQuotaResponse
type UpdateWebhookQuotaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quota is the retrieved webhook quota configuration.
	Quota *v11.WebhookQuotaConfig `protobuf:"bytes,1,opt,name=quota,proto3" json:"quota,omitempty"`
}

func (x *UpdateWebhookQuotaResponse) Reset() {
	*x = UpdateWebhookQuotaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWebhookQuotaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebhookQuotaResponse) ProtoMessage() {}

func (x *UpdateWebhookQuotaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebhookQuotaResponse.ProtoReflect.Descriptor instead.
func (*UpdateWebhookQuotaResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateWebhookQuotaResponse) GetQuota() *v11.WebhookQuotaConfig {
	if x != nil {
		return x.Quota
	}
	return nil
}

// ListWebhooksAndQuotasRequest
type ListWebhooksAndQuotasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise_id filters webhooks by enterprise.
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// client_ids filters webhooks by client.
	ClientIds []string `protobuf:"bytes,2,rep,name=client_ids,json=clientIds,proto3" json:"client_ids,omitempty"`
}

func (x *ListWebhooksAndQuotasRequest) Reset() {
	*x = ListWebhooksAndQuotasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksAndQuotasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksAndQuotasRequest) ProtoMessage() {}

func (x *ListWebhooksAndQuotasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksAndQuotasRequest.ProtoReflect.Descriptor instead.
func (*ListWebhooksAndQuotasRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListWebhooksAndQuotasRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListWebhooksAndQuotasRequest) GetClientIds() []string {
	if x != nil {
		return x.ClientIds
	}
	return nil
}

// ListWebhooksAndQuotasResponse
type ListWebhooksAndQuotasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// webhooks is the list of webhooks.
	Webhooks []*v11.Webhook `protobuf:"bytes,1,rep,name=webhooks,proto3" json:"webhooks,omitempty"`
	// quotas is the list of webhook quotas.
	Quotas []*v11.WebhookQuotaConfig `protobuf:"bytes,2,rep,name=quotas,proto3" json:"quotas,omitempty"`
}

func (x *ListWebhooksAndQuotasResponse) Reset() {
	*x = ListWebhooksAndQuotasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksAndQuotasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksAndQuotasResponse) ProtoMessage() {}

func (x *ListWebhooksAndQuotasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksAndQuotasResponse.ProtoReflect.Descriptor instead.
func (*ListWebhooksAndQuotasResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListWebhooksAndQuotasResponse) GetWebhooks() []*v11.Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

func (x *ListWebhooksAndQuotasResponse) GetQuotas() []*v11.WebhookQuotaConfig {
	if x != nil {
		return x.Quotas
	}
	return nil
}

// Filter
type ListWebhooksRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by active status
	IsActive *bool `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// filter by event types (corrected validation)
	EventTypes []v1.EventType `protobuf:"varint,2,rep,packed,name=event_types,json=eventTypes,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_types,omitempty"`
	// filter by created time
	CreatedTime *interval.Interval `protobuf:"bytes,3,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// filter by updated time
	UpdatedTime *interval.Interval `protobuf:"bytes,4,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
}

func (x *ListWebhooksRequest_Filter) Reset() {
	*x = ListWebhooksRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhooksRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhooksRequest_Filter) ProtoMessage() {}

func (x *ListWebhooksRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhooksRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListWebhooksRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListWebhooksRequest_Filter) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *ListWebhooksRequest_Filter) GetEventTypes() []v1.EventType {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

func (x *ListWebhooksRequest_Filter) GetCreatedTime() *interval.Interval {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

func (x *ListWebhooksRequest_Filter) GetUpdatedTime() *interval.Interval {
	if x != nil {
		return x.UpdatedTime
	}
	return nil
}

// Filter defines advanced filtering options
type ListWebhookDeliveriesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by event types (corrected validation)
	EventTypes []v1.EventType `protobuf:"varint,1,rep,packed,name=event_types,json=eventTypes,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_types,omitempty"`
	// filter by success status
	Success *bool `protobuf:"varint,2,opt,name=success,proto3,oneof" json:"success,omitempty"`
	// filter by delivery time range
	DeliveryTime *interval.Interval `protobuf:"bytes,3,opt,name=delivery_time,json=deliveryTime,proto3" json:"delivery_time,omitempty"`
}

func (x *ListWebhookDeliveriesRequest_Filter) Reset() {
	*x = ListWebhookDeliveriesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWebhookDeliveriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWebhookDeliveriesRequest_Filter) ProtoMessage() {}

func (x *ListWebhookDeliveriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWebhookDeliveriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListWebhookDeliveriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListWebhookDeliveriesRequest_Filter) GetEventTypes() []v1.EventType {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

func (x *ListWebhookDeliveriesRequest_Filter) GetSuccess() bool {
	if x != nil && x.Success != nil {
		return *x.Success
	}
	return false
}

func (x *ListWebhookDeliveriesRequest_Filter) GetDeliveryTime() *interval.Interval {
	if x != nil {
		return x.DeliveryTime
	}
	return nil
}

var File_moego_service_open_platform_v1_webhook_service_proto protoreflect.FileDescriptor

var file_moego_service_open_platform_v1_webhook_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xfc, 0x05, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x0c, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x88, 0x01, 0x01, 0x52, 0x0b, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x4f, 0x0a, 0x0b, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26,
	0x0a, 0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x01, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x22, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x73, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x09, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x73, 0x6c,
	0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x1a, 0x6f, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x73, 0x6c, 0x22,
	0x59, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x52, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x22, 0x2c, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x56, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x22, 0xc1, 0x05, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2b, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x88,
	0x01, 0x01, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12,
	0x4f, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x51, 0x0a, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73,
	0x73, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x09, 0x76, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x53, 0x73, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x6f, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x5f, 0x73, 0x73, 0x6c, 0x22, 0x59, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x22,
	0x2f, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf0, 0x04, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x02,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x84, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x0b,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08,
	0x00, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38,
	0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0x9e, 0x01, 0x0a,
	0x14, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52,
	0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb0, 0x01,
	0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0c,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x4d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x22, 0x61, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73,
	0x42, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x42, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x73, 0x22, 0x71, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x73, 0x42, 0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x52, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x22, 0x34, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x68, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a,
	0x0a, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x52, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x22, 0x86, 0x04, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x77,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x48, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x41,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0xc7, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x0b,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08,
	0x00, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x22, 0xb3, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x86, 0x01, 0x0a, 0x23, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x5f, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x24, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x0a, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x22, 0x35, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x62, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x71, 0x75,
	0x6f, 0x74, 0x61, 0x22, 0x9f, 0x02, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6d, 0x61, 0x78, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79, 0x73,
	0x12, 0x2d, 0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x72,
	0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d,
	0x61, 0x78, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12,
	0x27, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x50, 0x75,
	0x73, 0x68, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f,
	0x70, 0x75, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x72,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0x65, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x22, 0x6b, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x41, 0x6e, 0x64, 0x51,
	0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x1d, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x77,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x12,
	0x49, 0x0a, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x32, 0x9a, 0x0e, 0x0a, 0x0e, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a,
	0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7c, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42,
	0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x42, 0x79, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xa9, 0x01, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x94, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x73, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x41, 0x6e, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x91, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x65, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_open_platform_v1_webhook_service_proto_rawDescOnce sync.Once
	file_moego_service_open_platform_v1_webhook_service_proto_rawDescData = file_moego_service_open_platform_v1_webhook_service_proto_rawDesc
)

func file_moego_service_open_platform_v1_webhook_service_proto_rawDescGZIP() []byte {
	file_moego_service_open_platform_v1_webhook_service_proto_rawDescOnce.Do(func() {
		file_moego_service_open_platform_v1_webhook_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_open_platform_v1_webhook_service_proto_rawDescData)
	})
	return file_moego_service_open_platform_v1_webhook_service_proto_rawDescData
}

var file_moego_service_open_platform_v1_webhook_service_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_moego_service_open_platform_v1_webhook_service_proto_goTypes = []interface{}{
	(*CreateWebhookRequest)(nil),                 // 0: moego.service.open_platform.v1.CreateWebhookRequest
	(*CreateWebhookResponse)(nil),                // 1: moego.service.open_platform.v1.CreateWebhookResponse
	(*GetWebhookRequest)(nil),                    // 2: moego.service.open_platform.v1.GetWebhookRequest
	(*GetWebhookResponse)(nil),                   // 3: moego.service.open_platform.v1.GetWebhookResponse
	(*UpdateWebhookRequest)(nil),                 // 4: moego.service.open_platform.v1.UpdateWebhookRequest
	(*UpdateWebhookResponse)(nil),                // 5: moego.service.open_platform.v1.UpdateWebhookResponse
	(*DeleteWebhookRequest)(nil),                 // 6: moego.service.open_platform.v1.DeleteWebhookRequest
	(*DeleteWebhookResponse)(nil),                // 7: moego.service.open_platform.v1.DeleteWebhookResponse
	(*ListWebhooksRequest)(nil),                  // 8: moego.service.open_platform.v1.ListWebhooksRequest
	(*ListWebhooksResponse)(nil),                 // 9: moego.service.open_platform.v1.ListWebhooksResponse
	(*ListWebhooksByEventRequest)(nil),           // 10: moego.service.open_platform.v1.ListWebhooksByEventRequest
	(*ListWebhooksByEventResponse)(nil),          // 11: moego.service.open_platform.v1.ListWebhooksByEventResponse
	(*ListWebhooksByClientRequest)(nil),          // 12: moego.service.open_platform.v1.ListWebhooksByClientRequest
	(*ListWebhooksByClientResponse)(nil),         // 13: moego.service.open_platform.v1.ListWebhooksByClientResponse
	(*GetWebhookDeliveryRequest)(nil),            // 14: moego.service.open_platform.v1.GetWebhookDeliveryRequest
	(*GetWebhookDeliveryResponse)(nil),           // 15: moego.service.open_platform.v1.GetWebhookDeliveryResponse
	(*ListWebhookDeliveriesRequest)(nil),         // 16: moego.service.open_platform.v1.ListWebhookDeliveriesRequest
	(*ListWebhookDeliveriesResponse)(nil),        // 17: moego.service.open_platform.v1.ListWebhookDeliveriesResponse
	(*BatchUpsertWebhookDeliveriesRequest)(nil),  // 18: moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesRequest
	(*BatchUpsertWebhookDeliveriesResponse)(nil), // 19: moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesResponse
	(*GetWebhookQuotaRequest)(nil),               // 20: moego.service.open_platform.v1.GetWebhookQuotaRequest
	(*GetWebhookQuotaResponse)(nil),              // 21: moego.service.open_platform.v1.GetWebhookQuotaResponse
	(*UpdateWebhookQuotaRequest)(nil),            // 22: moego.service.open_platform.v1.UpdateWebhookQuotaRequest
	(*UpdateWebhookQuotaResponse)(nil),           // 23: moego.service.open_platform.v1.UpdateWebhookQuotaResponse
	(*ListWebhooksAndQuotasRequest)(nil),         // 24: moego.service.open_platform.v1.ListWebhooksAndQuotasRequest
	(*ListWebhooksAndQuotasResponse)(nil),        // 25: moego.service.open_platform.v1.ListWebhooksAndQuotasResponse
	nil,                                          // 26: moego.service.open_platform.v1.CreateWebhookRequest.HeadersEntry
	nil,                                          // 27: moego.service.open_platform.v1.UpdateWebhookRequest.HeadersEntry
	(*ListWebhooksRequest_Filter)(nil),           // 28: moego.service.open_platform.v1.ListWebhooksRequest.Filter
	(*ListWebhookDeliveriesRequest_Filter)(nil),  // 29: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.Filter
	(v1.EventType)(0),                            // 30: moego.models.event_bus.v1.EventType
	(*v11.Organization)(nil),                     // 31: moego.models.open_platform.v1.Organization
	(v11.Webhook_ContentType)(0),                 // 32: moego.models.open_platform.v1.Webhook.ContentType
	(*v11.Webhook)(nil),                          // 33: moego.models.open_platform.v1.Webhook
	(*v2.OrderBy)(nil),                           // 34: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),                 // 35: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                // 36: moego.utils.v2.PaginationResponse
	(*v11.WebhookDelivery)(nil),                  // 37: moego.models.open_platform.v1.WebhookDelivery
	(*v11.WebhookQuotaConfig)(nil),               // 38: moego.models.open_platform.v1.WebhookQuotaConfig
	(*v11.Webhook_HeaderValues)(nil),             // 39: moego.models.open_platform.v1.Webhook.HeaderValues
	(*interval.Interval)(nil),                    // 40: google.type.Interval
}
var file_moego_service_open_platform_v1_webhook_service_proto_depIdxs = []int32{
	30, // 0: moego.service.open_platform.v1.CreateWebhookRequest.event_types:type_name -> moego.models.event_bus.v1.EventType
	31, // 1: moego.service.open_platform.v1.CreateWebhookRequest.organizations:type_name -> moego.models.open_platform.v1.Organization
	32, // 2: moego.service.open_platform.v1.CreateWebhookRequest.content_type:type_name -> moego.models.open_platform.v1.Webhook.ContentType
	26, // 3: moego.service.open_platform.v1.CreateWebhookRequest.headers:type_name -> moego.service.open_platform.v1.CreateWebhookRequest.HeadersEntry
	33, // 4: moego.service.open_platform.v1.CreateWebhookResponse.webhook:type_name -> moego.models.open_platform.v1.Webhook
	33, // 5: moego.service.open_platform.v1.GetWebhookResponse.webhook:type_name -> moego.models.open_platform.v1.Webhook
	30, // 6: moego.service.open_platform.v1.UpdateWebhookRequest.event_types:type_name -> moego.models.event_bus.v1.EventType
	31, // 7: moego.service.open_platform.v1.UpdateWebhookRequest.organizations:type_name -> moego.models.open_platform.v1.Organization
	32, // 8: moego.service.open_platform.v1.UpdateWebhookRequest.content_type:type_name -> moego.models.open_platform.v1.Webhook.ContentType
	27, // 9: moego.service.open_platform.v1.UpdateWebhookRequest.headers:type_name -> moego.service.open_platform.v1.UpdateWebhookRequest.HeadersEntry
	33, // 10: moego.service.open_platform.v1.UpdateWebhookResponse.webhook:type_name -> moego.models.open_platform.v1.Webhook
	28, // 11: moego.service.open_platform.v1.ListWebhooksRequest.filter:type_name -> moego.service.open_platform.v1.ListWebhooksRequest.Filter
	34, // 12: moego.service.open_platform.v1.ListWebhooksRequest.order_by:type_name -> moego.utils.v2.OrderBy
	35, // 13: moego.service.open_platform.v1.ListWebhooksRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	33, // 14: moego.service.open_platform.v1.ListWebhooksResponse.webhooks:type_name -> moego.models.open_platform.v1.Webhook
	36, // 15: moego.service.open_platform.v1.ListWebhooksResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	30, // 16: moego.service.open_platform.v1.ListWebhooksByEventRequest.event_type:type_name -> moego.models.event_bus.v1.EventType
	33, // 17: moego.service.open_platform.v1.ListWebhooksByEventResponse.webhooks:type_name -> moego.models.open_platform.v1.Webhook
	33, // 18: moego.service.open_platform.v1.ListWebhooksByClientResponse.webhooks:type_name -> moego.models.open_platform.v1.Webhook
	37, // 19: moego.service.open_platform.v1.GetWebhookDeliveryResponse.delivery:type_name -> moego.models.open_platform.v1.WebhookDelivery
	29, // 20: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.filter:type_name -> moego.service.open_platform.v1.ListWebhookDeliveriesRequest.Filter
	34, // 21: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.order_by:type_name -> moego.utils.v2.OrderBy
	35, // 22: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	37, // 23: moego.service.open_platform.v1.ListWebhookDeliveriesResponse.deliveries:type_name -> moego.models.open_platform.v1.WebhookDelivery
	36, // 24: moego.service.open_platform.v1.ListWebhookDeliveriesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	37, // 25: moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesRequest.deliveries:type_name -> moego.models.open_platform.v1.WebhookDelivery
	37, // 26: moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesResponse.deliveries:type_name -> moego.models.open_platform.v1.WebhookDelivery
	38, // 27: moego.service.open_platform.v1.GetWebhookQuotaResponse.quota:type_name -> moego.models.open_platform.v1.WebhookQuotaConfig
	38, // 28: moego.service.open_platform.v1.UpdateWebhookQuotaResponse.quota:type_name -> moego.models.open_platform.v1.WebhookQuotaConfig
	33, // 29: moego.service.open_platform.v1.ListWebhooksAndQuotasResponse.webhooks:type_name -> moego.models.open_platform.v1.Webhook
	38, // 30: moego.service.open_platform.v1.ListWebhooksAndQuotasResponse.quotas:type_name -> moego.models.open_platform.v1.WebhookQuotaConfig
	39, // 31: moego.service.open_platform.v1.CreateWebhookRequest.HeadersEntry.value:type_name -> moego.models.open_platform.v1.Webhook.HeaderValues
	39, // 32: moego.service.open_platform.v1.UpdateWebhookRequest.HeadersEntry.value:type_name -> moego.models.open_platform.v1.Webhook.HeaderValues
	30, // 33: moego.service.open_platform.v1.ListWebhooksRequest.Filter.event_types:type_name -> moego.models.event_bus.v1.EventType
	40, // 34: moego.service.open_platform.v1.ListWebhooksRequest.Filter.created_time:type_name -> google.type.Interval
	40, // 35: moego.service.open_platform.v1.ListWebhooksRequest.Filter.updated_time:type_name -> google.type.Interval
	30, // 36: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.Filter.event_types:type_name -> moego.models.event_bus.v1.EventType
	40, // 37: moego.service.open_platform.v1.ListWebhookDeliveriesRequest.Filter.delivery_time:type_name -> google.type.Interval
	0,  // 38: moego.service.open_platform.v1.WebhookService.CreateWebhook:input_type -> moego.service.open_platform.v1.CreateWebhookRequest
	2,  // 39: moego.service.open_platform.v1.WebhookService.GetWebhook:input_type -> moego.service.open_platform.v1.GetWebhookRequest
	4,  // 40: moego.service.open_platform.v1.WebhookService.UpdateWebhook:input_type -> moego.service.open_platform.v1.UpdateWebhookRequest
	6,  // 41: moego.service.open_platform.v1.WebhookService.DeleteWebhook:input_type -> moego.service.open_platform.v1.DeleteWebhookRequest
	8,  // 42: moego.service.open_platform.v1.WebhookService.ListWebhooks:input_type -> moego.service.open_platform.v1.ListWebhooksRequest
	10, // 43: moego.service.open_platform.v1.WebhookService.ListWebhooksByEvent:input_type -> moego.service.open_platform.v1.ListWebhooksByEventRequest
	12, // 44: moego.service.open_platform.v1.WebhookService.ListWebhooksByClient:input_type -> moego.service.open_platform.v1.ListWebhooksByClientRequest
	14, // 45: moego.service.open_platform.v1.WebhookService.GetWebhookDelivery:input_type -> moego.service.open_platform.v1.GetWebhookDeliveryRequest
	16, // 46: moego.service.open_platform.v1.WebhookService.ListWebhookDeliveries:input_type -> moego.service.open_platform.v1.ListWebhookDeliveriesRequest
	18, // 47: moego.service.open_platform.v1.WebhookService.BatchUpsertWebhookDeliveries:input_type -> moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesRequest
	20, // 48: moego.service.open_platform.v1.WebhookService.GetWebhookQuota:input_type -> moego.service.open_platform.v1.GetWebhookQuotaRequest
	22, // 49: moego.service.open_platform.v1.WebhookService.UpdateWebhookQuota:input_type -> moego.service.open_platform.v1.UpdateWebhookQuotaRequest
	24, // 50: moego.service.open_platform.v1.WebhookService.ListWebhooksAndQuotas:input_type -> moego.service.open_platform.v1.ListWebhooksAndQuotasRequest
	1,  // 51: moego.service.open_platform.v1.WebhookService.CreateWebhook:output_type -> moego.service.open_platform.v1.CreateWebhookResponse
	3,  // 52: moego.service.open_platform.v1.WebhookService.GetWebhook:output_type -> moego.service.open_platform.v1.GetWebhookResponse
	5,  // 53: moego.service.open_platform.v1.WebhookService.UpdateWebhook:output_type -> moego.service.open_platform.v1.UpdateWebhookResponse
	7,  // 54: moego.service.open_platform.v1.WebhookService.DeleteWebhook:output_type -> moego.service.open_platform.v1.DeleteWebhookResponse
	9,  // 55: moego.service.open_platform.v1.WebhookService.ListWebhooks:output_type -> moego.service.open_platform.v1.ListWebhooksResponse
	11, // 56: moego.service.open_platform.v1.WebhookService.ListWebhooksByEvent:output_type -> moego.service.open_platform.v1.ListWebhooksByEventResponse
	13, // 57: moego.service.open_platform.v1.WebhookService.ListWebhooksByClient:output_type -> moego.service.open_platform.v1.ListWebhooksByClientResponse
	15, // 58: moego.service.open_platform.v1.WebhookService.GetWebhookDelivery:output_type -> moego.service.open_platform.v1.GetWebhookDeliveryResponse
	17, // 59: moego.service.open_platform.v1.WebhookService.ListWebhookDeliveries:output_type -> moego.service.open_platform.v1.ListWebhookDeliveriesResponse
	19, // 60: moego.service.open_platform.v1.WebhookService.BatchUpsertWebhookDeliveries:output_type -> moego.service.open_platform.v1.BatchUpsertWebhookDeliveriesResponse
	21, // 61: moego.service.open_platform.v1.WebhookService.GetWebhookQuota:output_type -> moego.service.open_platform.v1.GetWebhookQuotaResponse
	23, // 62: moego.service.open_platform.v1.WebhookService.UpdateWebhookQuota:output_type -> moego.service.open_platform.v1.UpdateWebhookQuotaResponse
	25, // 63: moego.service.open_platform.v1.WebhookService.ListWebhooksAndQuotas:output_type -> moego.service.open_platform.v1.ListWebhooksAndQuotasResponse
	51, // [51:64] is the sub-list for method output_type
	38, // [38:51] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_moego_service_open_platform_v1_webhook_service_proto_init() }
func file_moego_service_open_platform_v1_webhook_service_proto_init() {
	if File_moego_service_open_platform_v1_webhook_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWebhookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWebhookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWebhookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWebhookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWebhookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWebhookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksByEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksByEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksByClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksByClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookDeliveryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookDeliveryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhookDeliveriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhookDeliveriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertWebhookDeliveriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertWebhookDeliveriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookQuotaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebhookQuotaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWebhookQuotaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWebhookQuotaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksAndQuotasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksAndQuotasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhooksRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWebhookDeliveriesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_service_open_platform_v1_webhook_service_proto_msgTypes[29].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_open_platform_v1_webhook_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_open_platform_v1_webhook_service_proto_goTypes,
		DependencyIndexes: file_moego_service_open_platform_v1_webhook_service_proto_depIdxs,
		MessageInfos:      file_moego_service_open_platform_v1_webhook_service_proto_msgTypes,
	}.Build()
	File_moego_service_open_platform_v1_webhook_service_proto = out.File
	file_moego_service_open_platform_v1_webhook_service_proto_rawDesc = nil
	file_moego_service_open_platform_v1_webhook_service_proto_goTypes = nil
	file_moego_service_open_platform_v1_webhook_service_proto_depIdxs = nil
}
