// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/open_platform/v1/oauth_service.proto

package openplatformsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/open_platform/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AuthenticateRequest
type AuthenticateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// credential is the API key or OAuth2.0 access token.
	//
	// Types that are assignable to Credential:
	//
	//	*AuthenticateRequest_Apikey
	//	*AuthenticateRequest_Oauth
	//	*AuthenticateRequest_Code
	Credential isAuthenticateRequest_Credential `protobuf_oneof:"credential"`
	// session info
	SessionInfo *SessionInfo `protobuf:"bytes,100,opt,name=session_info,json=sessionInfo,proto3" json:"session_info,omitempty"`
}

func (x *AuthenticateRequest) Reset() {
	*x = AuthenticateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateRequest) ProtoMessage() {}

func (x *AuthenticateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateRequest.ProtoReflect.Descriptor instead.
func (*AuthenticateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{0}
}

func (m *AuthenticateRequest) GetCredential() isAuthenticateRequest_Credential {
	if m != nil {
		return m.Credential
	}
	return nil
}

func (x *AuthenticateRequest) GetApikey() string {
	if x, ok := x.GetCredential().(*AuthenticateRequest_Apikey); ok {
		return x.Apikey
	}
	return ""
}

func (x *AuthenticateRequest) GetOauth() *OAuthCredential {
	if x, ok := x.GetCredential().(*AuthenticateRequest_Oauth); ok {
		return x.Oauth
	}
	return nil
}

func (x *AuthenticateRequest) GetCode() string {
	if x, ok := x.GetCredential().(*AuthenticateRequest_Code); ok {
		return x.Code
	}
	return ""
}

func (x *AuthenticateRequest) GetSessionInfo() *SessionInfo {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

type isAuthenticateRequest_Credential interface {
	isAuthenticateRequest_Credential()
}

type AuthenticateRequest_Apikey struct {
	// apikey
	Apikey string `protobuf:"bytes,1,opt,name=apikey,proto3,oneof"`
}

type AuthenticateRequest_Oauth struct {
	// oauth credential
	Oauth *OAuthCredential `protobuf:"bytes,3,opt,name=oauth,proto3,oneof"`
}

type AuthenticateRequest_Code struct {
	// oauth2 callback code
	Code string `protobuf:"bytes,4,opt,name=code,proto3,oneof"`
}

func (*AuthenticateRequest_Apikey) isAuthenticateRequest_Credential() {}

func (*AuthenticateRequest_Oauth) isAuthenticateRequest_Credential() {}

func (*AuthenticateRequest_Code) isAuthenticateRequest_Credential() {}

// Session Info
type SessionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company_id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business_id
	BusinessId int64 `protobuf:"varint,11,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// account_id
	AccountId int64 `protobuf:"varint,12,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{1}
}

func (x *SessionInfo) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SessionInfo) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SessionInfo) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

// OAuthCredential
type OAuthCredential struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id is the unique identifier of the client.
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// open_id is the unique identifier of the user.
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// access_token is the OAuth2.0 access token.
	AccessToken string `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *OAuthCredential) Reset() {
	*x = OAuthCredential{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OAuthCredential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuthCredential) ProtoMessage() {}

func (x *OAuthCredential) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuthCredential.ProtoReflect.Descriptor instead.
func (*OAuthCredential) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{2}
}

func (x *OAuthCredential) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OAuthCredential) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *OAuthCredential) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

// AuthenticateResponse
type AuthenticateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user is the user information.
	User *v1.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// scopes is the list of scopes that the user has.
	Scopes []string `protobuf:"bytes,2,rep,name=scopes,proto3" json:"scopes,omitempty"`
	// restrictions is the restrictions of the Credential.
	Restrictions *v1.Restrictions `protobuf:"bytes,3,opt,name=restrictions,proto3" json:"restrictions,omitempty"`
}

func (x *AuthenticateResponse) Reset() {
	*x = AuthenticateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateResponse) ProtoMessage() {}

func (x *AuthenticateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateResponse.ProtoReflect.Descriptor instead.
func (*AuthenticateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{3}
}

func (x *AuthenticateResponse) GetUser() *v1.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *AuthenticateResponse) GetScopes() []string {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *AuthenticateResponse) GetRestrictions() *v1.Restrictions {
	if x != nil {
		return x.Restrictions
	}
	return nil
}

// CreateAPIKeyRequest
type CreateAPIKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name is the name of the API key.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// restrictions is the restrictions of the API key.
	Restrictions *v1.Restrictions `protobuf:"bytes,2,opt,name=restrictions,proto3,oneof" json:"restrictions,omitempty"`
	// enterprise_id is the unique identifier of the enterprise.
	// 临时参数，仅用于内部初始化数据
	EnterpriseId int64 `protobuf:"varint,10,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *CreateAPIKeyRequest) Reset() {
	*x = CreateAPIKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAPIKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAPIKeyRequest) ProtoMessage() {}

func (x *CreateAPIKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAPIKeyRequest.ProtoReflect.Descriptor instead.
func (*CreateAPIKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAPIKeyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAPIKeyRequest) GetRestrictions() *v1.Restrictions {
	if x != nil {
		return x.Restrictions
	}
	return nil
}

func (x *CreateAPIKeyRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// CreateAPIKeyResponse
type CreateAPIKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// api_key is the created API key.
	ApiKey *v1.APIKey `protobuf:"bytes,1,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
}

func (x *CreateAPIKeyResponse) Reset() {
	*x = CreateAPIKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAPIKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAPIKeyResponse) ProtoMessage() {}

func (x *CreateAPIKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAPIKeyResponse.ProtoReflect.Descriptor instead.
func (*CreateAPIKeyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAPIKeyResponse) GetApiKey() *v1.APIKey {
	if x != nil {
		return x.ApiKey
	}
	return nil
}

// DeleteAPIKeyRequest
type DeleteAPIKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id is the unique identifier of the API key.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteAPIKeyRequest) Reset() {
	*x = DeleteAPIKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAPIKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAPIKeyRequest) ProtoMessage() {}

func (x *DeleteAPIKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAPIKeyRequest.ProtoReflect.Descriptor instead.
func (*DeleteAPIKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteAPIKeyRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// DeleteAPIKeyResponse
type DeleteAPIKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteAPIKeyResponse) Reset() {
	*x = DeleteAPIKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAPIKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAPIKeyResponse) ProtoMessage() {}

func (x *DeleteAPIKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAPIKeyResponse.ProtoReflect.Descriptor instead.
func (*DeleteAPIKeyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP(), []int{7}
}

var File_moego_service_open_platform_v1_oauth_service_proto protoreflect.FileDescriptor

var file_moego_service_open_platform_v1_oauth_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xff, 0x01, 0x0a, 0x13, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01,
	0x18, 0x40, 0xd0, 0x01, 0x01, 0x48, 0x00, 0x52, 0x06, 0x61, 0x70, 0x69, 0x6b, 0x65, 0x79, 0x12,
	0x47, 0x0a, 0x05, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x41, 0x75, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x48,
	0x00, 0x52, 0x05, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x4e,
	0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x6c, 0x0a, 0x0b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x8f, 0x01, 0x0a, 0x0f, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x20, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42,
	0x09, 0x72, 0x07, 0x10, 0x01, 0x18, 0x20, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0x18, 0x80, 0x02, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0xb8, 0x01, 0x0a, 0x14, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0c, 0x72,
	0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc9, 0x01, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x65, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x56, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3e, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79,
	0x22, 0x30, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x20, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x50, 0x49, 0x4b,
	0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x85, 0x03, 0x0a, 0x0c, 0x4f,
	0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7b, 0x0a, 0x0c, 0x41,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x50, 0x49, 0x4b, 0x65, 0x79, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x50, 0x49,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x91, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x65, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_open_platform_v1_oauth_service_proto_rawDescOnce sync.Once
	file_moego_service_open_platform_v1_oauth_service_proto_rawDescData = file_moego_service_open_platform_v1_oauth_service_proto_rawDesc
)

func file_moego_service_open_platform_v1_oauth_service_proto_rawDescGZIP() []byte {
	file_moego_service_open_platform_v1_oauth_service_proto_rawDescOnce.Do(func() {
		file_moego_service_open_platform_v1_oauth_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_open_platform_v1_oauth_service_proto_rawDescData)
	})
	return file_moego_service_open_platform_v1_oauth_service_proto_rawDescData
}

var file_moego_service_open_platform_v1_oauth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_open_platform_v1_oauth_service_proto_goTypes = []interface{}{
	(*AuthenticateRequest)(nil),  // 0: moego.service.open_platform.v1.AuthenticateRequest
	(*SessionInfo)(nil),          // 1: moego.service.open_platform.v1.SessionInfo
	(*OAuthCredential)(nil),      // 2: moego.service.open_platform.v1.OAuthCredential
	(*AuthenticateResponse)(nil), // 3: moego.service.open_platform.v1.AuthenticateResponse
	(*CreateAPIKeyRequest)(nil),  // 4: moego.service.open_platform.v1.CreateAPIKeyRequest
	(*CreateAPIKeyResponse)(nil), // 5: moego.service.open_platform.v1.CreateAPIKeyResponse
	(*DeleteAPIKeyRequest)(nil),  // 6: moego.service.open_platform.v1.DeleteAPIKeyRequest
	(*DeleteAPIKeyResponse)(nil), // 7: moego.service.open_platform.v1.DeleteAPIKeyResponse
	(*v1.User)(nil),              // 8: moego.models.open_platform.v1.User
	(*v1.Restrictions)(nil),      // 9: moego.models.open_platform.v1.Restrictions
	(*v1.APIKey)(nil),            // 10: moego.models.open_platform.v1.APIKey
}
var file_moego_service_open_platform_v1_oauth_service_proto_depIdxs = []int32{
	2,  // 0: moego.service.open_platform.v1.AuthenticateRequest.oauth:type_name -> moego.service.open_platform.v1.OAuthCredential
	1,  // 1: moego.service.open_platform.v1.AuthenticateRequest.session_info:type_name -> moego.service.open_platform.v1.SessionInfo
	8,  // 2: moego.service.open_platform.v1.AuthenticateResponse.user:type_name -> moego.models.open_platform.v1.User
	9,  // 3: moego.service.open_platform.v1.AuthenticateResponse.restrictions:type_name -> moego.models.open_platform.v1.Restrictions
	9,  // 4: moego.service.open_platform.v1.CreateAPIKeyRequest.restrictions:type_name -> moego.models.open_platform.v1.Restrictions
	10, // 5: moego.service.open_platform.v1.CreateAPIKeyResponse.api_key:type_name -> moego.models.open_platform.v1.APIKey
	0,  // 6: moego.service.open_platform.v1.OAuthService.Authenticate:input_type -> moego.service.open_platform.v1.AuthenticateRequest
	4,  // 7: moego.service.open_platform.v1.OAuthService.CreateAPIKey:input_type -> moego.service.open_platform.v1.CreateAPIKeyRequest
	6,  // 8: moego.service.open_platform.v1.OAuthService.DeleteAPIKey:input_type -> moego.service.open_platform.v1.DeleteAPIKeyRequest
	3,  // 9: moego.service.open_platform.v1.OAuthService.Authenticate:output_type -> moego.service.open_platform.v1.AuthenticateResponse
	5,  // 10: moego.service.open_platform.v1.OAuthService.CreateAPIKey:output_type -> moego.service.open_platform.v1.CreateAPIKeyResponse
	7,  // 11: moego.service.open_platform.v1.OAuthService.DeleteAPIKey:output_type -> moego.service.open_platform.v1.DeleteAPIKeyResponse
	9,  // [9:12] is the sub-list for method output_type
	6,  // [6:9] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_service_open_platform_v1_oauth_service_proto_init() }
func file_moego_service_open_platform_v1_oauth_service_proto_init() {
	if File_moego_service_open_platform_v1_oauth_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OAuthCredential); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAPIKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAPIKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAPIKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAPIKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AuthenticateRequest_Apikey)(nil),
		(*AuthenticateRequest_Oauth)(nil),
		(*AuthenticateRequest_Code)(nil),
	}
	file_moego_service_open_platform_v1_oauth_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_open_platform_v1_oauth_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_open_platform_v1_oauth_service_proto_goTypes,
		DependencyIndexes: file_moego_service_open_platform_v1_oauth_service_proto_depIdxs,
		MessageInfos:      file_moego_service_open_platform_v1_oauth_service_proto_msgTypes,
	}.Build()
	File_moego_service_open_platform_v1_oauth_service_proto = out.File
	file_moego_service_open_platform_v1_oauth_service_proto_rawDesc = nil
	file_moego_service_open_platform_v1_oauth_service_proto_goTypes = nil
	file_moego_service_open_platform_v1_oauth_service_proto_depIdxs = nil
}
