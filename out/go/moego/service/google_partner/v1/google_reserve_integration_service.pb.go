// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/google_partner/v1/google_reserve_integration_service.proto

package googlepartnersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/google_partner/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update Google reserve integration request param
type UpdateGoogleReserveIntegrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int32 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable
	Enabled *bool `protobuf:"varint,2,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	// status
	Status *v1.GoogleReserveIntegrationStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.google_partner.v1.GoogleReserveIntegrationStatus,oneof" json:"status,omitempty"`
}

func (x *UpdateGoogleReserveIntegrationRequest) Reset() {
	*x = UpdateGoogleReserveIntegrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGoogleReserveIntegrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGoogleReserveIntegrationRequest) ProtoMessage() {}

func (x *UpdateGoogleReserveIntegrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGoogleReserveIntegrationRequest.ProtoReflect.Descriptor instead.
func (*UpdateGoogleReserveIntegrationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateGoogleReserveIntegrationRequest) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateGoogleReserveIntegrationRequest) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *UpdateGoogleReserveIntegrationRequest) GetStatus() v1.GoogleReserveIntegrationStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.GoogleReserveIntegrationStatus(0)
}

// get Google reserve integration request param
type GetGoogleReserveIntegrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int32 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetGoogleReserveIntegrationRequest) Reset() {
	*x = GetGoogleReserveIntegrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoogleReserveIntegrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoogleReserveIntegrationRequest) ProtoMessage() {}

func (x *GetGoogleReserveIntegrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoogleReserveIntegrationRequest.ProtoReflect.Descriptor instead.
func (*GetGoogleReserveIntegrationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetGoogleReserveIntegrationRequest) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// insert Google reserve integration request param
type InsertGoogleReserveIntegrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int32 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable
	Enabled *bool `protobuf:"varint,2,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	// status
	Status *v1.GoogleReserveIntegrationStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.google_partner.v1.GoogleReserveIntegrationStatus,oneof" json:"status,omitempty"`
}

func (x *InsertGoogleReserveIntegrationRequest) Reset() {
	*x = InsertGoogleReserveIntegrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsertGoogleReserveIntegrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertGoogleReserveIntegrationRequest) ProtoMessage() {}

func (x *InsertGoogleReserveIntegrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertGoogleReserveIntegrationRequest.ProtoReflect.Descriptor instead.
func (*InsertGoogleReserveIntegrationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{2}
}

func (x *InsertGoogleReserveIntegrationRequest) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *InsertGoogleReserveIntegrationRequest) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *InsertGoogleReserveIntegrationRequest) GetStatus() v1.GoogleReserveIntegrationStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.GoogleReserveIntegrationStatus(0)
}

// refresh Google reserve integration status request param
type RefreshGoogleReserveIntegrationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshGoogleReserveIntegrationStatusRequest) Reset() {
	*x = RefreshGoogleReserveIntegrationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshGoogleReserveIntegrationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshGoogleReserveIntegrationStatusRequest) ProtoMessage() {}

func (x *RefreshGoogleReserveIntegrationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshGoogleReserveIntegrationStatusRequest.ProtoReflect.Descriptor instead.
func (*RefreshGoogleReserveIntegrationStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{3}
}

// refresh Google reserve integration status response
type RefreshGoogleReserveIntegrationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unmatched business ids
	UnmatchedBusinessIds []int32 `protobuf:"varint,1,rep,packed,name=unmatched_business_ids,json=unmatchedBusinessIds,proto3" json:"unmatched_business_ids,omitempty"`
	// matched business ids
	MatchedBusinessIds []int32 `protobuf:"varint,2,rep,packed,name=matched_business_ids,json=matchedBusinessIds,proto3" json:"matched_business_ids,omitempty"`
}

func (x *RefreshGoogleReserveIntegrationStatusResponse) Reset() {
	*x = RefreshGoogleReserveIntegrationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshGoogleReserveIntegrationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshGoogleReserveIntegrationStatusResponse) ProtoMessage() {}

func (x *RefreshGoogleReserveIntegrationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshGoogleReserveIntegrationStatusResponse.ProtoReflect.Descriptor instead.
func (*RefreshGoogleReserveIntegrationStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{4}
}

func (x *RefreshGoogleReserveIntegrationStatusResponse) GetUnmatchedBusinessIds() []int32 {
	if x != nil {
		return x.UnmatchedBusinessIds
	}
	return nil
}

func (x *RefreshGoogleReserveIntegrationStatusResponse) GetMatchedBusinessIds() []int32 {
	if x != nil {
		return x.MatchedBusinessIds
	}
	return nil
}

// list Google reserve integration response
type ListGoogleReserveIntegrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Google reserve integration list
	Models []*v1.GoogleReserveIntegrationModel `protobuf:"bytes,1,rep,name=models,proto3" json:"models,omitempty"`
}

func (x *ListGoogleReserveIntegrationResponse) Reset() {
	*x = ListGoogleReserveIntegrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGoogleReserveIntegrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGoogleReserveIntegrationResponse) ProtoMessage() {}

func (x *ListGoogleReserveIntegrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGoogleReserveIntegrationResponse.ProtoReflect.Descriptor instead.
func (*ListGoogleReserveIntegrationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListGoogleReserveIntegrationResponse) GetModels() []*v1.GoogleReserveIntegrationModel {
	if x != nil {
		return x.Models
	}
	return nil
}

// delete Google reserve integration request param
type DeleteGoogleReserveIntegrationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId []int32 `protobuf:"varint,1,rep,packed,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *DeleteGoogleReserveIntegrationRequest) Reset() {
	*x = DeleteGoogleReserveIntegrationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGoogleReserveIntegrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGoogleReserveIntegrationRequest) ProtoMessage() {}

func (x *DeleteGoogleReserveIntegrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGoogleReserveIntegrationRequest.ProtoReflect.Descriptor instead.
func (*DeleteGoogleReserveIntegrationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteGoogleReserveIntegrationRequest) GetBusinessId() []int32 {
	if x != nil {
		return x.BusinessId
	}
	return nil
}

// delete Google reserve integration response
type DeleteGoogleReserveIntegrationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// count
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *DeleteGoogleReserveIntegrationResponse) Reset() {
	*x = DeleteGoogleReserveIntegrationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteGoogleReserveIntegrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGoogleReserveIntegrationResponse) ProtoMessage() {}

func (x *DeleteGoogleReserveIntegrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGoogleReserveIntegrationResponse.ProtoReflect.Descriptor instead.
func (*DeleteGoogleReserveIntegrationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteGoogleReserveIntegrationResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_moego_service_google_partner_v1_google_reserve_integration_service_proto protoreflect.FileDescriptor

var file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDesc = []byte{
	0x0a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f,
	0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x46, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe4, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4e, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xe4, 0x01, 0x0a, 0x25, 0x49, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2e,
	0x0a, 0x2c, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x97,
	0x01, 0x0a, 0x2d, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x34, 0x0a, 0x16, 0x75, 0x6e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x14, 0x75, 0x6e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x7d, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x55, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x22, 0x56, 0x0a, 0x25, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0x3e, 0x0a, 0x26, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x32,
	0xa1, 0x08, 0x0a, 0x1f, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x00, 0x12, 0xa9, 0x01, 0x0a, 0x1e, 0x49, 0x6e,
	0x73, 0x65, 0x72, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6e, 0x73, 0x65, 0x72, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x22, 0x00, 0x12, 0xa9, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22,
	0x00, 0x12, 0xc8, 0x01, 0x0a, 0x25, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb3, 0x01,
	0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x67, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescOnce sync.Once
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescData = file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDesc
)

func file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescGZIP() []byte {
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescOnce.Do(func() {
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescData)
	})
	return file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDescData
}

var file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_google_partner_v1_google_reserve_integration_service_proto_goTypes = []interface{}{
	(*UpdateGoogleReserveIntegrationRequest)(nil),         // 0: moego.service.google_partner.v1.UpdateGoogleReserveIntegrationRequest
	(*GetGoogleReserveIntegrationRequest)(nil),            // 1: moego.service.google_partner.v1.GetGoogleReserveIntegrationRequest
	(*InsertGoogleReserveIntegrationRequest)(nil),         // 2: moego.service.google_partner.v1.InsertGoogleReserveIntegrationRequest
	(*RefreshGoogleReserveIntegrationStatusRequest)(nil),  // 3: moego.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusRequest
	(*RefreshGoogleReserveIntegrationStatusResponse)(nil), // 4: moego.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusResponse
	(*ListGoogleReserveIntegrationResponse)(nil),          // 5: moego.service.google_partner.v1.ListGoogleReserveIntegrationResponse
	(*DeleteGoogleReserveIntegrationRequest)(nil),         // 6: moego.service.google_partner.v1.DeleteGoogleReserveIntegrationRequest
	(*DeleteGoogleReserveIntegrationResponse)(nil),        // 7: moego.service.google_partner.v1.DeleteGoogleReserveIntegrationResponse
	(v1.GoogleReserveIntegrationStatus)(0),                // 8: moego.models.google_partner.v1.GoogleReserveIntegrationStatus
	(*v1.GoogleReserveIntegrationModel)(nil),              // 9: moego.models.google_partner.v1.GoogleReserveIntegrationModel
	(*emptypb.Empty)(nil),                                 // 10: google.protobuf.Empty
}
var file_moego_service_google_partner_v1_google_reserve_integration_service_proto_depIdxs = []int32{
	8,  // 0: moego.service.google_partner.v1.UpdateGoogleReserveIntegrationRequest.status:type_name -> moego.models.google_partner.v1.GoogleReserveIntegrationStatus
	8,  // 1: moego.service.google_partner.v1.InsertGoogleReserveIntegrationRequest.status:type_name -> moego.models.google_partner.v1.GoogleReserveIntegrationStatus
	9,  // 2: moego.service.google_partner.v1.ListGoogleReserveIntegrationResponse.models:type_name -> moego.models.google_partner.v1.GoogleReserveIntegrationModel
	1,  // 3: moego.service.google_partner.v1.GoogleReserveIntegrationService.GetGoogleReserveIntegration:input_type -> moego.service.google_partner.v1.GetGoogleReserveIntegrationRequest
	2,  // 4: moego.service.google_partner.v1.GoogleReserveIntegrationService.InsertGoogleReserveIntegration:input_type -> moego.service.google_partner.v1.InsertGoogleReserveIntegrationRequest
	0,  // 5: moego.service.google_partner.v1.GoogleReserveIntegrationService.UpdateGoogleReserveIntegration:input_type -> moego.service.google_partner.v1.UpdateGoogleReserveIntegrationRequest
	3,  // 6: moego.service.google_partner.v1.GoogleReserveIntegrationService.RefreshGoogleReserveIntegrationStatus:input_type -> moego.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusRequest
	10, // 7: moego.service.google_partner.v1.GoogleReserveIntegrationService.ListGoogleReserveIntegration:input_type -> google.protobuf.Empty
	6,  // 8: moego.service.google_partner.v1.GoogleReserveIntegrationService.DeleteGoogleReserveIntegration:input_type -> moego.service.google_partner.v1.DeleteGoogleReserveIntegrationRequest
	9,  // 9: moego.service.google_partner.v1.GoogleReserveIntegrationService.GetGoogleReserveIntegration:output_type -> moego.models.google_partner.v1.GoogleReserveIntegrationModel
	9,  // 10: moego.service.google_partner.v1.GoogleReserveIntegrationService.InsertGoogleReserveIntegration:output_type -> moego.models.google_partner.v1.GoogleReserveIntegrationModel
	9,  // 11: moego.service.google_partner.v1.GoogleReserveIntegrationService.UpdateGoogleReserveIntegration:output_type -> moego.models.google_partner.v1.GoogleReserveIntegrationModel
	4,  // 12: moego.service.google_partner.v1.GoogleReserveIntegrationService.RefreshGoogleReserveIntegrationStatus:output_type -> moego.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusResponse
	5,  // 13: moego.service.google_partner.v1.GoogleReserveIntegrationService.ListGoogleReserveIntegration:output_type -> moego.service.google_partner.v1.ListGoogleReserveIntegrationResponse
	7,  // 14: moego.service.google_partner.v1.GoogleReserveIntegrationService.DeleteGoogleReserveIntegration:output_type -> moego.service.google_partner.v1.DeleteGoogleReserveIntegrationResponse
	9,  // [9:15] is the sub-list for method output_type
	3,  // [3:9] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_google_partner_v1_google_reserve_integration_service_proto_init() }
func file_moego_service_google_partner_v1_google_reserve_integration_service_proto_init() {
	if File_moego_service_google_partner_v1_google_reserve_integration_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGoogleReserveIntegrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoogleReserveIntegrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsertGoogleReserveIntegrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshGoogleReserveIntegrationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshGoogleReserveIntegrationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGoogleReserveIntegrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGoogleReserveIntegrationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteGoogleReserveIntegrationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_google_partner_v1_google_reserve_integration_service_proto_goTypes,
		DependencyIndexes: file_moego_service_google_partner_v1_google_reserve_integration_service_proto_depIdxs,
		MessageInfos:      file_moego_service_google_partner_v1_google_reserve_integration_service_proto_msgTypes,
	}.Build()
	File_moego_service_google_partner_v1_google_reserve_integration_service_proto = out.File
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_rawDesc = nil
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_goTypes = nil
	file_moego_service_google_partner_v1_google_reserve_integration_service_proto_depIdxs = nil
}
