// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/ai_assistant/v1/business_conversation_service.proto

package aiassistantsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessConversationServiceClient is the client API for BusinessConversationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessConversationServiceClient interface {
	// create a business conversation
	CreateConversation(ctx context.Context, in *CreateBusinessConversationRequest, opts ...grpc.CallOption) (*CreateBusinessConversationResponse, error)
	// close the business conversation
	CloseConversation(ctx context.Context, in *CloseBusinessConversationRequest, opts ...grpc.CallOption) (*CloseBusinessConversationResponse, error)
	// pose a question and ask for an answer
	Ask(ctx context.Context, in *AskRequest, opts ...grpc.CallOption) (*AskResponse, error)
	// get a rephrase answer for the question
	RephraseAnswer(ctx context.Context, in *RephraseAnswerRequest, opts ...grpc.CallOption) (*RephraseAnswerResponse, error)
	// adopt an answer
	AdoptAnswer(ctx context.Context, in *AdoptAnswerRequest, opts ...grpc.CallOption) (*AdoptAnswerResponse, error)
	// adopt an answer
	SendAnswer(ctx context.Context, in *SendAnswerRequest, opts ...grpc.CallOption) (*SendAnswerResponse, error)
	// get conversation questions
	GetConversationQuestions(ctx context.Context, in *GetConversationQuestionsRequest, opts ...grpc.CallOption) (*GetConversationQuestionsResponse, error)
	// describe business conversation
	DescribeConversations(ctx context.Context, in *DescribeBusinessConversationsRequest, opts ...grpc.CallOption) (*DescribeBusinessConversationsResponse, error)
	// describe business conversation summaries
	DescribeConversationSummaries(ctx context.Context, in *DescribeBusinessConversationSummariesRequest, opts ...grpc.CallOption) (*DescribeBusinessConversationSummariesResponse, error)
	// describe system summaries
	DescribeSystemSummaries(ctx context.Context, in *DescribeSystemSummariesRequest, opts ...grpc.CallOption) (*DescribeSystemSummariesResponse, error)
}

type businessConversationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessConversationServiceClient(cc grpc.ClientConnInterface) BusinessConversationServiceClient {
	return &businessConversationServiceClient{cc}
}

func (c *businessConversationServiceClient) CreateConversation(ctx context.Context, in *CreateBusinessConversationRequest, opts ...grpc.CallOption) (*CreateBusinessConversationResponse, error) {
	out := new(CreateBusinessConversationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/CreateConversation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) CloseConversation(ctx context.Context, in *CloseBusinessConversationRequest, opts ...grpc.CallOption) (*CloseBusinessConversationResponse, error) {
	out := new(CloseBusinessConversationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/CloseConversation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) Ask(ctx context.Context, in *AskRequest, opts ...grpc.CallOption) (*AskResponse, error) {
	out := new(AskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/Ask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) RephraseAnswer(ctx context.Context, in *RephraseAnswerRequest, opts ...grpc.CallOption) (*RephraseAnswerResponse, error) {
	out := new(RephraseAnswerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/RephraseAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) AdoptAnswer(ctx context.Context, in *AdoptAnswerRequest, opts ...grpc.CallOption) (*AdoptAnswerResponse, error) {
	out := new(AdoptAnswerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/AdoptAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) SendAnswer(ctx context.Context, in *SendAnswerRequest, opts ...grpc.CallOption) (*SendAnswerResponse, error) {
	out := new(SendAnswerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/SendAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) GetConversationQuestions(ctx context.Context, in *GetConversationQuestionsRequest, opts ...grpc.CallOption) (*GetConversationQuestionsResponse, error) {
	out := new(GetConversationQuestionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/GetConversationQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) DescribeConversations(ctx context.Context, in *DescribeBusinessConversationsRequest, opts ...grpc.CallOption) (*DescribeBusinessConversationsResponse, error) {
	out := new(DescribeBusinessConversationsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeConversations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) DescribeConversationSummaries(ctx context.Context, in *DescribeBusinessConversationSummariesRequest, opts ...grpc.CallOption) (*DescribeBusinessConversationSummariesResponse, error) {
	out := new(DescribeBusinessConversationSummariesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeConversationSummaries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) DescribeSystemSummaries(ctx context.Context, in *DescribeSystemSummariesRequest, opts ...grpc.CallOption) (*DescribeSystemSummariesResponse, error) {
	out := new(DescribeSystemSummariesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeSystemSummaries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessConversationServiceServer is the server API for BusinessConversationService service.
// All implementations must embed UnimplementedBusinessConversationServiceServer
// for forward compatibility
type BusinessConversationServiceServer interface {
	// create a business conversation
	CreateConversation(context.Context, *CreateBusinessConversationRequest) (*CreateBusinessConversationResponse, error)
	// close the business conversation
	CloseConversation(context.Context, *CloseBusinessConversationRequest) (*CloseBusinessConversationResponse, error)
	// pose a question and ask for an answer
	Ask(context.Context, *AskRequest) (*AskResponse, error)
	// get a rephrase answer for the question
	RephraseAnswer(context.Context, *RephraseAnswerRequest) (*RephraseAnswerResponse, error)
	// adopt an answer
	AdoptAnswer(context.Context, *AdoptAnswerRequest) (*AdoptAnswerResponse, error)
	// adopt an answer
	SendAnswer(context.Context, *SendAnswerRequest) (*SendAnswerResponse, error)
	// get conversation questions
	GetConversationQuestions(context.Context, *GetConversationQuestionsRequest) (*GetConversationQuestionsResponse, error)
	// describe business conversation
	DescribeConversations(context.Context, *DescribeBusinessConversationsRequest) (*DescribeBusinessConversationsResponse, error)
	// describe business conversation summaries
	DescribeConversationSummaries(context.Context, *DescribeBusinessConversationSummariesRequest) (*DescribeBusinessConversationSummariesResponse, error)
	// describe system summaries
	DescribeSystemSummaries(context.Context, *DescribeSystemSummariesRequest) (*DescribeSystemSummariesResponse, error)
	mustEmbedUnimplementedBusinessConversationServiceServer()
}

// UnimplementedBusinessConversationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessConversationServiceServer struct {
}

func (UnimplementedBusinessConversationServiceServer) CreateConversation(context.Context, *CreateBusinessConversationRequest) (*CreateBusinessConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConversation not implemented")
}
func (UnimplementedBusinessConversationServiceServer) CloseConversation(context.Context, *CloseBusinessConversationRequest) (*CloseBusinessConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseConversation not implemented")
}
func (UnimplementedBusinessConversationServiceServer) Ask(context.Context, *AskRequest) (*AskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ask not implemented")
}
func (UnimplementedBusinessConversationServiceServer) RephraseAnswer(context.Context, *RephraseAnswerRequest) (*RephraseAnswerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RephraseAnswer not implemented")
}
func (UnimplementedBusinessConversationServiceServer) AdoptAnswer(context.Context, *AdoptAnswerRequest) (*AdoptAnswerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdoptAnswer not implemented")
}
func (UnimplementedBusinessConversationServiceServer) SendAnswer(context.Context, *SendAnswerRequest) (*SendAnswerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendAnswer not implemented")
}
func (UnimplementedBusinessConversationServiceServer) GetConversationQuestions(context.Context, *GetConversationQuestionsRequest) (*GetConversationQuestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversationQuestions not implemented")
}
func (UnimplementedBusinessConversationServiceServer) DescribeConversations(context.Context, *DescribeBusinessConversationsRequest) (*DescribeBusinessConversationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeConversations not implemented")
}
func (UnimplementedBusinessConversationServiceServer) DescribeConversationSummaries(context.Context, *DescribeBusinessConversationSummariesRequest) (*DescribeBusinessConversationSummariesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeConversationSummaries not implemented")
}
func (UnimplementedBusinessConversationServiceServer) DescribeSystemSummaries(context.Context, *DescribeSystemSummariesRequest) (*DescribeSystemSummariesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeSystemSummaries not implemented")
}
func (UnimplementedBusinessConversationServiceServer) mustEmbedUnimplementedBusinessConversationServiceServer() {
}

// UnsafeBusinessConversationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessConversationServiceServer will
// result in compilation errors.
type UnsafeBusinessConversationServiceServer interface {
	mustEmbedUnimplementedBusinessConversationServiceServer()
}

func RegisterBusinessConversationServiceServer(s grpc.ServiceRegistrar, srv BusinessConversationServiceServer) {
	s.RegisterService(&BusinessConversationService_ServiceDesc, srv)
}

func _BusinessConversationService_CreateConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBusinessConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).CreateConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/CreateConversation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).CreateConversation(ctx, req.(*CreateBusinessConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_CloseConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseBusinessConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).CloseConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/CloseConversation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).CloseConversation(ctx, req.(*CloseBusinessConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_Ask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).Ask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/Ask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).Ask(ctx, req.(*AskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_RephraseAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RephraseAnswerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).RephraseAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/RephraseAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).RephraseAnswer(ctx, req.(*RephraseAnswerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_AdoptAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdoptAnswerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).AdoptAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/AdoptAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).AdoptAnswer(ctx, req.(*AdoptAnswerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_SendAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAnswerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).SendAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/SendAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).SendAnswer(ctx, req.(*SendAnswerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_GetConversationQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversationQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).GetConversationQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/GetConversationQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).GetConversationQuestions(ctx, req.(*GetConversationQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_DescribeConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeBusinessConversationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeConversations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeConversations(ctx, req.(*DescribeBusinessConversationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_DescribeConversationSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeBusinessConversationSummariesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeConversationSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeConversationSummaries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeConversationSummaries(ctx, req.(*DescribeBusinessConversationSummariesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_DescribeSystemSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeSystemSummariesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeSystemSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ai_assistant.v1.BusinessConversationService/DescribeSystemSummaries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeSystemSummaries(ctx, req.(*DescribeSystemSummariesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessConversationService_ServiceDesc is the grpc.ServiceDesc for BusinessConversationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessConversationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.ai_assistant.v1.BusinessConversationService",
	HandlerType: (*BusinessConversationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateConversation",
			Handler:    _BusinessConversationService_CreateConversation_Handler,
		},
		{
			MethodName: "CloseConversation",
			Handler:    _BusinessConversationService_CloseConversation_Handler,
		},
		{
			MethodName: "Ask",
			Handler:    _BusinessConversationService_Ask_Handler,
		},
		{
			MethodName: "RephraseAnswer",
			Handler:    _BusinessConversationService_RephraseAnswer_Handler,
		},
		{
			MethodName: "AdoptAnswer",
			Handler:    _BusinessConversationService_AdoptAnswer_Handler,
		},
		{
			MethodName: "SendAnswer",
			Handler:    _BusinessConversationService_SendAnswer_Handler,
		},
		{
			MethodName: "GetConversationQuestions",
			Handler:    _BusinessConversationService_GetConversationQuestions_Handler,
		},
		{
			MethodName: "DescribeConversations",
			Handler:    _BusinessConversationService_DescribeConversations_Handler,
		},
		{
			MethodName: "DescribeConversationSummaries",
			Handler:    _BusinessConversationService_DescribeConversationSummaries_Handler,
		},
		{
			MethodName: "DescribeSystemSummaries",
			Handler:    _BusinessConversationService_DescribeSystemSummaries_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/ai_assistant/v1/business_conversation_service.proto",
}
