// @since 2023-06-29 16:13:41
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/ai_assistant/v1/business_conversation_service.proto

package aiassistantsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create business conversation request
type CreateBusinessConversationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// prompt. If provided, the conversation will start based on this prompt
	Prompt string `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// scenario
	Scenario string `protobuf:"bytes,5,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// variables
	Variables map[string]string `protobuf:"bytes,4,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CreateBusinessConversationRequest) Reset() {
	*x = CreateBusinessConversationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBusinessConversationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBusinessConversationRequest) ProtoMessage() {}

func (x *CreateBusinessConversationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBusinessConversationRequest.ProtoReflect.Descriptor instead.
func (*CreateBusinessConversationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBusinessConversationRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBusinessConversationRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateBusinessConversationRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *CreateBusinessConversationRequest) GetScenario() string {
	if x != nil {
		return x.Scenario
	}
	return ""
}

func (x *CreateBusinessConversationRequest) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

// create business conversation response
type CreateBusinessConversationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
}

func (x *CreateBusinessConversationResponse) Reset() {
	*x = CreateBusinessConversationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBusinessConversationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBusinessConversationResponse) ProtoMessage() {}

func (x *CreateBusinessConversationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBusinessConversationResponse.ProtoReflect.Descriptor instead.
func (*CreateBusinessConversationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBusinessConversationResponse) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

// close business conversation request
type CloseBusinessConversationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CloseBusinessConversationRequest) Reset() {
	*x = CloseBusinessConversationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseBusinessConversationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseBusinessConversationRequest) ProtoMessage() {}

func (x *CloseBusinessConversationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseBusinessConversationRequest.ProtoReflect.Descriptor instead.
func (*CloseBusinessConversationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{2}
}

func (x *CloseBusinessConversationRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *CloseBusinessConversationRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// close business conversation response
type CloseBusinessConversationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseBusinessConversationResponse) Reset() {
	*x = CloseBusinessConversationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseBusinessConversationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseBusinessConversationResponse) ProtoMessage() {}

func (x *CloseBusinessConversationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseBusinessConversationResponse.ProtoReflect.Descriptor instead.
func (*CloseBusinessConversationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{3}
}

// ask request
type AskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// sender id (staff id)
	SenderId int64 `protobuf:"varint,2,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// question
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
}

func (x *AskRequest) Reset() {
	*x = AskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskRequest) ProtoMessage() {}

func (x *AskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskRequest.ProtoReflect.Descriptor instead.
func (*AskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{4}
}

func (x *AskRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *AskRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AskRequest) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *AskRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

// ask response
type AskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// question id
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// answer
	Answer string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *AskResponse) Reset() {
	*x = AskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskResponse) ProtoMessage() {}

func (x *AskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskResponse.ProtoReflect.Descriptor instead.
func (*AskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{5}
}

func (x *AskResponse) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *AskResponse) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// rephrase answer request
type RephraseAnswerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
}

func (x *RephraseAnswerRequest) Reset() {
	*x = RephraseAnswerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RephraseAnswerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RephraseAnswerRequest) ProtoMessage() {}

func (x *RephraseAnswerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RephraseAnswerRequest.ProtoReflect.Descriptor instead.
func (*RephraseAnswerRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{6}
}

func (x *RephraseAnswerRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *RephraseAnswerRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *RephraseAnswerRequest) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

// rephrase answer response
type RephraseAnswerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// new question id, different from the one in RephraseAnswerRequest
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// answer
	Answer string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *RephraseAnswerResponse) Reset() {
	*x = RephraseAnswerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RephraseAnswerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RephraseAnswerResponse) ProtoMessage() {}

func (x *RephraseAnswerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RephraseAnswerResponse.ProtoReflect.Descriptor instead.
func (*RephraseAnswerResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{7}
}

func (x *RephraseAnswerResponse) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *RephraseAnswerResponse) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// adopt answer request
type AdoptAnswerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
}

func (x *AdoptAnswerRequest) Reset() {
	*x = AdoptAnswerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdoptAnswerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdoptAnswerRequest) ProtoMessage() {}

func (x *AdoptAnswerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdoptAnswerRequest.ProtoReflect.Descriptor instead.
func (*AdoptAnswerRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{8}
}

func (x *AdoptAnswerRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AdoptAnswerRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *AdoptAnswerRequest) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

// adopt answer request
type SendAnswerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// the message id
	MessageId int64 `protobuf:"varint,3,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
}

func (x *SendAnswerRequest) Reset() {
	*x = SendAnswerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAnswerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAnswerRequest) ProtoMessage() {}

func (x *SendAnswerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAnswerRequest.ProtoReflect.Descriptor instead.
func (*SendAnswerRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{9}
}

func (x *SendAnswerRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SendAnswerRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *SendAnswerRequest) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *SendAnswerRequest) GetMessageId() int64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

// adopt answer response
type AdoptAnswerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AdoptAnswerResponse) Reset() {
	*x = AdoptAnswerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdoptAnswerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdoptAnswerResponse) ProtoMessage() {}

func (x *AdoptAnswerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdoptAnswerResponse.ProtoReflect.Descriptor instead.
func (*AdoptAnswerResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{10}
}

// adopt answer response
type SendAnswerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendAnswerResponse) Reset() {
	*x = SendAnswerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAnswerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAnswerResponse) ProtoMessage() {}

func (x *SendAnswerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAnswerResponse.ProtoReflect.Descriptor instead.
func (*SendAnswerResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{11}
}

// get conversation questions request
type GetConversationQuestionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
}

func (x *GetConversationQuestionsRequest) Reset() {
	*x = GetConversationQuestionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConversationQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationQuestionsRequest) ProtoMessage() {}

func (x *GetConversationQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationQuestionsRequest.ProtoReflect.Descriptor instead.
func (*GetConversationQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetConversationQuestionsRequest) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

// get conversation questions response
type GetConversationQuestionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// questions
	Questions []*v1.BusinessConversationQuestionModel `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *GetConversationQuestionsResponse) Reset() {
	*x = GetConversationQuestionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConversationQuestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationQuestionsResponse) ProtoMessage() {}

func (x *GetConversationQuestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationQuestionsResponse.ProtoReflect.Descriptor instead.
func (*GetConversationQuestionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetConversationQuestionsResponse) GetQuestions() []*v1.BusinessConversationQuestionModel {
	if x != nil {
		return x.Questions
	}
	return nil
}

// describe business conversations request
type DescribeBusinessConversationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeBusinessConversationsRequest) Reset() {
	*x = DescribeBusinessConversationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationsRequest) ProtoMessage() {}

func (x *DescribeBusinessConversationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationsRequest.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{14}
}

func (x *DescribeBusinessConversationsRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *DescribeBusinessConversationsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe business conversations response
type DescribeBusinessConversationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversations
	Conversations []*v1.BusinessConversationModel `protobuf:"bytes,1,rep,name=conversations,proto3" json:"conversations,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeBusinessConversationsResponse) Reset() {
	*x = DescribeBusinessConversationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationsResponse) ProtoMessage() {}

func (x *DescribeBusinessConversationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationsResponse.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{15}
}

func (x *DescribeBusinessConversationsResponse) GetConversations() []*v1.BusinessConversationModel {
	if x != nil {
		return x.Conversations
	}
	return nil
}

func (x *DescribeBusinessConversationsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe business conversation summaries request
type DescribeBusinessConversationSummariesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// order by, field name should be in BusinessConversationSummaryModel
	OrderBy *v2.OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
}

func (x *DescribeBusinessConversationSummariesRequest) Reset() {
	*x = DescribeBusinessConversationSummariesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationSummariesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationSummariesRequest) ProtoMessage() {}

func (x *DescribeBusinessConversationSummariesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationSummariesRequest.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationSummariesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{16}
}

func (x *DescribeBusinessConversationSummariesRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *DescribeBusinessConversationSummariesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *DescribeBusinessConversationSummariesRequest) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

// describe business conversation summaries response
type DescribeBusinessConversationSummariesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// summaries
	Summaries []*v1.BusinessConversationSummaryModel `protobuf:"bytes,1,rep,name=summaries,proto3" json:"summaries,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeBusinessConversationSummariesResponse) Reset() {
	*x = DescribeBusinessConversationSummariesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationSummariesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationSummariesResponse) ProtoMessage() {}

func (x *DescribeBusinessConversationSummariesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationSummariesResponse.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationSummariesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{17}
}

func (x *DescribeBusinessConversationSummariesResponse) GetSummaries() []*v1.BusinessConversationSummaryModel {
	if x != nil {
		return x.Summaries
	}
	return nil
}

func (x *DescribeBusinessConversationSummariesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe system summaries request
type DescribeSystemSummariesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DescribeSystemSummariesRequest) Reset() {
	*x = DescribeSystemSummariesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSystemSummariesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSystemSummariesRequest) ProtoMessage() {}

func (x *DescribeSystemSummariesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSystemSummariesRequest.ProtoReflect.Descriptor instead.
func (*DescribeSystemSummariesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{18}
}

// describe system summaries response
type DescribeSystemSummariesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business count
	BusinessCount int32 `protobuf:"varint,1,opt,name=business_count,json=businessCount,proto3" json:"business_count,omitempty"`
	// conversation count
	ConversationCount int32 `protobuf:"varint,2,opt,name=conversation_count,json=conversationCount,proto3" json:"conversation_count,omitempty"`
	// nonempty conversation count
	NonemptyConversationCount int32 `protobuf:"varint,9,opt,name=nonempty_conversation_count,json=nonemptyConversationCount,proto3" json:"nonempty_conversation_count,omitempty"`
	// adopted conversation count
	AdoptedConversationCount int32 `protobuf:"varint,3,opt,name=adopted_conversation_count,json=adoptedConversationCount,proto3" json:"adopted_conversation_count,omitempty"`
	// question count
	QuestionCount int32 `protobuf:"varint,4,opt,name=question_count,json=questionCount,proto3" json:"question_count,omitempty"`
	// total cost
	TotalCost float64 `protobuf:"fixed64,5,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	// total input token
	TotalInputToken int32 `protobuf:"varint,6,opt,name=total_input_token,json=totalInputToken,proto3" json:"total_input_token,omitempty"`
	// total output token
	TotalOutputToken int32 `protobuf:"varint,7,opt,name=total_output_token,json=totalOutputToken,proto3" json:"total_output_token,omitempty"`
	// total token
	TotalToken int32 `protobuf:"varint,8,opt,name=total_token,json=totalToken,proto3" json:"total_token,omitempty"`
}

func (x *DescribeSystemSummariesResponse) Reset() {
	*x = DescribeSystemSummariesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSystemSummariesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSystemSummariesResponse) ProtoMessage() {}

func (x *DescribeSystemSummariesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSystemSummariesResponse.ProtoReflect.Descriptor instead.
func (*DescribeSystemSummariesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP(), []int{19}
}

func (x *DescribeSystemSummariesResponse) GetBusinessCount() int32 {
	if x != nil {
		return x.BusinessCount
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetConversationCount() int32 {
	if x != nil {
		return x.ConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetNonemptyConversationCount() int32 {
	if x != nil {
		return x.NonemptyConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetAdoptedConversationCount() int32 {
	if x != nil {
		return x.AdoptedConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetQuestionCount() int32 {
	if x != nil {
		return x.QuestionCount
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetTotalInputToken() int32 {
	if x != nil {
		return x.TotalInputToken
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetTotalOutputToken() int32 {
	if x != nil {
		return x.TotalOutputToken
	}
	return 0
}

func (x *DescribeSystemSummariesResponse) GetTotalToken() int32 {
	if x != nil {
		return x.TotalToken
	}
	return 0
}

var File_moego_service_ai_assistant_v1_business_conversation_service_proto protoreflect.FileDescriptor

var file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xca, 0x02, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x40, 0x52, 0x06, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12,
	0x6d, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x1a, 0x3c,
	0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4d, 0x0a, 0x22,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x20, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x23, 0x0a, 0x21, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x99,
	0x01, 0x0a, 0x0a, 0x41, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x40,
	0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x46, 0x0a, 0x0b, 0x41, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x68, 0x72,
	0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22, 0x7f, 0x0a, 0x12, 0x41, 0x64,
	0x6f, 0x70, 0x74, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x11,
	0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x41,
	0x64, 0x6f, 0x70, 0x74, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x09, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x24, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x25, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xed, 0x01, 0x0a, 0x2c, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x01, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0xd1, 0x01, 0x0a, 0x2d, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x20, 0x0a, 0x1e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb6, 0x03,
	0x0a, 0x1f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x1b, 0x6e, 0x6f, 0x6e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x6e, 0x6f,
	0x6e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x64, 0x6f, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x61, 0x64, 0x6f,
	0x70, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xb3, 0x0b, 0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x11, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x03, 0x41,
	0x73, 0x6b, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x52, 0x65, 0x70,
	0x68, 0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x68,
	0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x0b, 0x41, 0x64, 0x6f, 0x70,
	0x74, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6f, 0x70, 0x74, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6f, 0x70, 0x74,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71,
	0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x9b, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa2, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xba, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x98, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8e, 0x01, 0x0a,
	0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescOnce sync.Once
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescData = file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDesc
)

func file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescGZIP() []byte {
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescOnce.Do(func() {
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescData)
	})
	return file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDescData
}

var file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_moego_service_ai_assistant_v1_business_conversation_service_proto_goTypes = []interface{}{
	(*CreateBusinessConversationRequest)(nil),             // 0: moego.service.ai_assistant.v1.CreateBusinessConversationRequest
	(*CreateBusinessConversationResponse)(nil),            // 1: moego.service.ai_assistant.v1.CreateBusinessConversationResponse
	(*CloseBusinessConversationRequest)(nil),              // 2: moego.service.ai_assistant.v1.CloseBusinessConversationRequest
	(*CloseBusinessConversationResponse)(nil),             // 3: moego.service.ai_assistant.v1.CloseBusinessConversationResponse
	(*AskRequest)(nil),                                    // 4: moego.service.ai_assistant.v1.AskRequest
	(*AskResponse)(nil),                                   // 5: moego.service.ai_assistant.v1.AskResponse
	(*RephraseAnswerRequest)(nil),                         // 6: moego.service.ai_assistant.v1.RephraseAnswerRequest
	(*RephraseAnswerResponse)(nil),                        // 7: moego.service.ai_assistant.v1.RephraseAnswerResponse
	(*AdoptAnswerRequest)(nil),                            // 8: moego.service.ai_assistant.v1.AdoptAnswerRequest
	(*SendAnswerRequest)(nil),                             // 9: moego.service.ai_assistant.v1.SendAnswerRequest
	(*AdoptAnswerResponse)(nil),                           // 10: moego.service.ai_assistant.v1.AdoptAnswerResponse
	(*SendAnswerResponse)(nil),                            // 11: moego.service.ai_assistant.v1.SendAnswerResponse
	(*GetConversationQuestionsRequest)(nil),               // 12: moego.service.ai_assistant.v1.GetConversationQuestionsRequest
	(*GetConversationQuestionsResponse)(nil),              // 13: moego.service.ai_assistant.v1.GetConversationQuestionsResponse
	(*DescribeBusinessConversationsRequest)(nil),          // 14: moego.service.ai_assistant.v1.DescribeBusinessConversationsRequest
	(*DescribeBusinessConversationsResponse)(nil),         // 15: moego.service.ai_assistant.v1.DescribeBusinessConversationsResponse
	(*DescribeBusinessConversationSummariesRequest)(nil),  // 16: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesRequest
	(*DescribeBusinessConversationSummariesResponse)(nil), // 17: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesResponse
	(*DescribeSystemSummariesRequest)(nil),                // 18: moego.service.ai_assistant.v1.DescribeSystemSummariesRequest
	(*DescribeSystemSummariesResponse)(nil),               // 19: moego.service.ai_assistant.v1.DescribeSystemSummariesResponse
	nil,                                                   // 20: moego.service.ai_assistant.v1.CreateBusinessConversationRequest.VariablesEntry
	(*v1.BusinessConversationQuestionModel)(nil),          // 21: moego.models.ai_assistant.v1.BusinessConversationQuestionModel
	(*v2.PaginationRequest)(nil),                          // 22: moego.utils.v2.PaginationRequest
	(*v1.BusinessConversationModel)(nil),                  // 23: moego.models.ai_assistant.v1.BusinessConversationModel
	(*v2.PaginationResponse)(nil),                         // 24: moego.utils.v2.PaginationResponse
	(*v2.OrderBy)(nil),                                    // 25: moego.utils.v2.OrderBy
	(*v1.BusinessConversationSummaryModel)(nil),           // 26: moego.models.ai_assistant.v1.BusinessConversationSummaryModel
}
var file_moego_service_ai_assistant_v1_business_conversation_service_proto_depIdxs = []int32{
	20, // 0: moego.service.ai_assistant.v1.CreateBusinessConversationRequest.variables:type_name -> moego.service.ai_assistant.v1.CreateBusinessConversationRequest.VariablesEntry
	21, // 1: moego.service.ai_assistant.v1.GetConversationQuestionsResponse.questions:type_name -> moego.models.ai_assistant.v1.BusinessConversationQuestionModel
	22, // 2: moego.service.ai_assistant.v1.DescribeBusinessConversationsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	23, // 3: moego.service.ai_assistant.v1.DescribeBusinessConversationsResponse.conversations:type_name -> moego.models.ai_assistant.v1.BusinessConversationModel
	24, // 4: moego.service.ai_assistant.v1.DescribeBusinessConversationsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	22, // 5: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	25, // 6: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesRequest.order_by:type_name -> moego.utils.v2.OrderBy
	26, // 7: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesResponse.summaries:type_name -> moego.models.ai_assistant.v1.BusinessConversationSummaryModel
	24, // 8: moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 9: moego.service.ai_assistant.v1.BusinessConversationService.CreateConversation:input_type -> moego.service.ai_assistant.v1.CreateBusinessConversationRequest
	2,  // 10: moego.service.ai_assistant.v1.BusinessConversationService.CloseConversation:input_type -> moego.service.ai_assistant.v1.CloseBusinessConversationRequest
	4,  // 11: moego.service.ai_assistant.v1.BusinessConversationService.Ask:input_type -> moego.service.ai_assistant.v1.AskRequest
	6,  // 12: moego.service.ai_assistant.v1.BusinessConversationService.RephraseAnswer:input_type -> moego.service.ai_assistant.v1.RephraseAnswerRequest
	8,  // 13: moego.service.ai_assistant.v1.BusinessConversationService.AdoptAnswer:input_type -> moego.service.ai_assistant.v1.AdoptAnswerRequest
	9,  // 14: moego.service.ai_assistant.v1.BusinessConversationService.SendAnswer:input_type -> moego.service.ai_assistant.v1.SendAnswerRequest
	12, // 15: moego.service.ai_assistant.v1.BusinessConversationService.GetConversationQuestions:input_type -> moego.service.ai_assistant.v1.GetConversationQuestionsRequest
	14, // 16: moego.service.ai_assistant.v1.BusinessConversationService.DescribeConversations:input_type -> moego.service.ai_assistant.v1.DescribeBusinessConversationsRequest
	16, // 17: moego.service.ai_assistant.v1.BusinessConversationService.DescribeConversationSummaries:input_type -> moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesRequest
	18, // 18: moego.service.ai_assistant.v1.BusinessConversationService.DescribeSystemSummaries:input_type -> moego.service.ai_assistant.v1.DescribeSystemSummariesRequest
	1,  // 19: moego.service.ai_assistant.v1.BusinessConversationService.CreateConversation:output_type -> moego.service.ai_assistant.v1.CreateBusinessConversationResponse
	3,  // 20: moego.service.ai_assistant.v1.BusinessConversationService.CloseConversation:output_type -> moego.service.ai_assistant.v1.CloseBusinessConversationResponse
	5,  // 21: moego.service.ai_assistant.v1.BusinessConversationService.Ask:output_type -> moego.service.ai_assistant.v1.AskResponse
	7,  // 22: moego.service.ai_assistant.v1.BusinessConversationService.RephraseAnswer:output_type -> moego.service.ai_assistant.v1.RephraseAnswerResponse
	10, // 23: moego.service.ai_assistant.v1.BusinessConversationService.AdoptAnswer:output_type -> moego.service.ai_assistant.v1.AdoptAnswerResponse
	11, // 24: moego.service.ai_assistant.v1.BusinessConversationService.SendAnswer:output_type -> moego.service.ai_assistant.v1.SendAnswerResponse
	13, // 25: moego.service.ai_assistant.v1.BusinessConversationService.GetConversationQuestions:output_type -> moego.service.ai_assistant.v1.GetConversationQuestionsResponse
	15, // 26: moego.service.ai_assistant.v1.BusinessConversationService.DescribeConversations:output_type -> moego.service.ai_assistant.v1.DescribeBusinessConversationsResponse
	17, // 27: moego.service.ai_assistant.v1.BusinessConversationService.DescribeConversationSummaries:output_type -> moego.service.ai_assistant.v1.DescribeBusinessConversationSummariesResponse
	19, // 28: moego.service.ai_assistant.v1.BusinessConversationService.DescribeSystemSummaries:output_type -> moego.service.ai_assistant.v1.DescribeSystemSummariesResponse
	19, // [19:29] is the sub-list for method output_type
	9,  // [9:19] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_service_ai_assistant_v1_business_conversation_service_proto_init() }
func file_moego_service_ai_assistant_v1_business_conversation_service_proto_init() {
	if File_moego_service_ai_assistant_v1_business_conversation_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBusinessConversationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBusinessConversationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseBusinessConversationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseBusinessConversationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RephraseAnswerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RephraseAnswerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdoptAnswerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAnswerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdoptAnswerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAnswerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConversationQuestionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConversationQuestionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationSummariesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationSummariesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSystemSummariesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSystemSummariesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_ai_assistant_v1_business_conversation_service_proto_goTypes,
		DependencyIndexes: file_moego_service_ai_assistant_v1_business_conversation_service_proto_depIdxs,
		MessageInfos:      file_moego_service_ai_assistant_v1_business_conversation_service_proto_msgTypes,
	}.Build()
	File_moego_service_ai_assistant_v1_business_conversation_service_proto = out.File
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_rawDesc = nil
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_goTypes = nil
	file_moego_service_ai_assistant_v1_business_conversation_service_proto_depIdxs = nil
}
