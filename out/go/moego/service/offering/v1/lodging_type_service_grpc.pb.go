// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/lodging_type_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// LodgingTypeServiceClient is the client API for LodgingTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LodgingTypeServiceClient interface {
	// create lodging type
	CreateLodgingType(ctx context.Context, in *CreateLodgingTypeRequest, opts ...grpc.CallOption) (*CreateLodgingTypeResponse, error)
	// update lodging type
	UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeRequest, opts ...grpc.CallOption) (*UpdateLodgingTypeResponse, error)
	// delete lodging type
	DeleteLodgingType(ctx context.Context, in *DeleteLodgingTypeRequest, opts ...grpc.CallOption) (*DeleteLodgingTypeResponse, error)
	// get lodging type list
	GetLodgingTypeList(ctx context.Context, in *GetLodgingTypeListRequest, opts ...grpc.CallOption) (*GetLodgingTypeListResponse, error)
	// mget
	MGetLodgingType(ctx context.Context, in *MGetLodgingTypeRequest, opts ...grpc.CallOption) (*MGetLodgingTypeResponse, error)
	// sort lodging type by ids
	SortLodgingTypeByIds(ctx context.Context, in *SortLodgingTypeByIdsRequest, opts ...grpc.CallOption) (*SortLodgingTypeByIdsResponse, error)
}

type lodgingTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLodgingTypeServiceClient(cc grpc.ClientConnInterface) LodgingTypeServiceClient {
	return &lodgingTypeServiceClient{cc}
}

func (c *lodgingTypeServiceClient) CreateLodgingType(ctx context.Context, in *CreateLodgingTypeRequest, opts ...grpc.CallOption) (*CreateLodgingTypeResponse, error) {
	out := new(CreateLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/CreateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingTypeServiceClient) UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeRequest, opts ...grpc.CallOption) (*UpdateLodgingTypeResponse, error) {
	out := new(UpdateLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/UpdateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingTypeServiceClient) DeleteLodgingType(ctx context.Context, in *DeleteLodgingTypeRequest, opts ...grpc.CallOption) (*DeleteLodgingTypeResponse, error) {
	out := new(DeleteLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/DeleteLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingTypeServiceClient) GetLodgingTypeList(ctx context.Context, in *GetLodgingTypeListRequest, opts ...grpc.CallOption) (*GetLodgingTypeListResponse, error) {
	out := new(GetLodgingTypeListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/GetLodgingTypeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingTypeServiceClient) MGetLodgingType(ctx context.Context, in *MGetLodgingTypeRequest, opts ...grpc.CallOption) (*MGetLodgingTypeResponse, error) {
	out := new(MGetLodgingTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/MGetLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lodgingTypeServiceClient) SortLodgingTypeByIds(ctx context.Context, in *SortLodgingTypeByIdsRequest, opts ...grpc.CallOption) (*SortLodgingTypeByIdsResponse, error) {
	out := new(SortLodgingTypeByIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.LodgingTypeService/SortLodgingTypeByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LodgingTypeServiceServer is the server API for LodgingTypeService service.
// All implementations must embed UnimplementedLodgingTypeServiceServer
// for forward compatibility
type LodgingTypeServiceServer interface {
	// create lodging type
	CreateLodgingType(context.Context, *CreateLodgingTypeRequest) (*CreateLodgingTypeResponse, error)
	// update lodging type
	UpdateLodgingType(context.Context, *UpdateLodgingTypeRequest) (*UpdateLodgingTypeResponse, error)
	// delete lodging type
	DeleteLodgingType(context.Context, *DeleteLodgingTypeRequest) (*DeleteLodgingTypeResponse, error)
	// get lodging type list
	GetLodgingTypeList(context.Context, *GetLodgingTypeListRequest) (*GetLodgingTypeListResponse, error)
	// mget
	MGetLodgingType(context.Context, *MGetLodgingTypeRequest) (*MGetLodgingTypeResponse, error)
	// sort lodging type by ids
	SortLodgingTypeByIds(context.Context, *SortLodgingTypeByIdsRequest) (*SortLodgingTypeByIdsResponse, error)
	mustEmbedUnimplementedLodgingTypeServiceServer()
}

// UnimplementedLodgingTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLodgingTypeServiceServer struct {
}

func (UnimplementedLodgingTypeServiceServer) CreateLodgingType(context.Context, *CreateLodgingTypeRequest) (*CreateLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLodgingType not implemented")
}
func (UnimplementedLodgingTypeServiceServer) UpdateLodgingType(context.Context, *UpdateLodgingTypeRequest) (*UpdateLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLodgingType not implemented")
}
func (UnimplementedLodgingTypeServiceServer) DeleteLodgingType(context.Context, *DeleteLodgingTypeRequest) (*DeleteLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLodgingType not implemented")
}
func (UnimplementedLodgingTypeServiceServer) GetLodgingTypeList(context.Context, *GetLodgingTypeListRequest) (*GetLodgingTypeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingTypeList not implemented")
}
func (UnimplementedLodgingTypeServiceServer) MGetLodgingType(context.Context, *MGetLodgingTypeRequest) (*MGetLodgingTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MGetLodgingType not implemented")
}
func (UnimplementedLodgingTypeServiceServer) SortLodgingTypeByIds(context.Context, *SortLodgingTypeByIdsRequest) (*SortLodgingTypeByIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortLodgingTypeByIds not implemented")
}
func (UnimplementedLodgingTypeServiceServer) mustEmbedUnimplementedLodgingTypeServiceServer() {}

// UnsafeLodgingTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LodgingTypeServiceServer will
// result in compilation errors.
type UnsafeLodgingTypeServiceServer interface {
	mustEmbedUnimplementedLodgingTypeServiceServer()
}

func RegisterLodgingTypeServiceServer(s grpc.ServiceRegistrar, srv LodgingTypeServiceServer) {
	s.RegisterService(&LodgingTypeService_ServiceDesc, srv)
}

func _LodgingTypeService_CreateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).CreateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/CreateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).CreateLodgingType(ctx, req.(*CreateLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingTypeService_UpdateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).UpdateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/UpdateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).UpdateLodgingType(ctx, req.(*UpdateLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingTypeService_DeleteLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).DeleteLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/DeleteLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).DeleteLodgingType(ctx, req.(*DeleteLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingTypeService_GetLodgingTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingTypeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).GetLodgingTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/GetLodgingTypeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).GetLodgingTypeList(ctx, req.(*GetLodgingTypeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingTypeService_MGetLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MGetLodgingTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).MGetLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/MGetLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).MGetLodgingType(ctx, req.(*MGetLodgingTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LodgingTypeService_SortLodgingTypeByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortLodgingTypeByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LodgingTypeServiceServer).SortLodgingTypeByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.LodgingTypeService/SortLodgingTypeByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LodgingTypeServiceServer).SortLodgingTypeByIds(ctx, req.(*SortLodgingTypeByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LodgingTypeService_ServiceDesc is the grpc.ServiceDesc for LodgingTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LodgingTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.LodgingTypeService",
	HandlerType: (*LodgingTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLodgingType",
			Handler:    _LodgingTypeService_CreateLodgingType_Handler,
		},
		{
			MethodName: "UpdateLodgingType",
			Handler:    _LodgingTypeService_UpdateLodgingType_Handler,
		},
		{
			MethodName: "DeleteLodgingType",
			Handler:    _LodgingTypeService_DeleteLodgingType_Handler,
		},
		{
			MethodName: "GetLodgingTypeList",
			Handler:    _LodgingTypeService_GetLodgingTypeList_Handler,
		},
		{
			MethodName: "MGetLodgingType",
			Handler:    _LodgingTypeService_MGetLodgingType_Handler,
		},
		{
			MethodName: "SortLodgingTypeByIds",
			Handler:    _LodgingTypeService_SortLodgingTypeByIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/lodging_type_service.proto",
}
