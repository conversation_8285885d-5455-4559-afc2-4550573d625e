// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v2/pricing_rule_service.proto

package offeringsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// upsert pricing rule request
type UpsertPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing_rule def
	PricingRuleDef *v2.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_def,json=pricingRuleDef,proto3" json:"pricing_rule_def,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *UpsertPricingRuleRequest) Reset() {
	*x = UpsertPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleRequest) ProtoMessage() {}

func (x *UpsertPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertPricingRuleRequest) GetPricingRuleDef() *v2.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleDef
	}
	return nil
}

func (x *UpsertPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpsertPricingRuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// upsert pricing rule response
type UpsertPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created pricing_rule
	PricingRule *v2.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpsertPricingRuleResponse) Reset() {
	*x = UpsertPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleResponse) ProtoMessage() {}

func (x *UpsertPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertPricingRuleResponse) GetPricingRule() *v2.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// get pricing rule request
type GetPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetPricingRuleRequest) Reset() {
	*x = GetPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleRequest) ProtoMessage() {}

func (x *GetPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*GetPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPricingRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get pricing rule response
type GetPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule
	PricingRule *v2.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *GetPricingRuleResponse) Reset() {
	*x = GetPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleResponse) ProtoMessage() {}

func (x *GetPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*GetPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPricingRuleResponse) GetPricingRule() *v2.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// list pricing rule request
type ListPricingRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v2.ListPricingRuleFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListPricingRulesRequest) Reset() {
	*x = ListPricingRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesRequest) ProtoMessage() {}

func (x *ListPricingRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesRequest.ProtoReflect.Descriptor instead.
func (*ListPricingRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListPricingRulesRequest) GetFilter() *v2.ListPricingRuleFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPricingRulesRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListPricingRulesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// list pricing rule response
type ListPricingRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule list
	PricingRules []*v2.PricingRule `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
	// pagination
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesResponse) Reset() {
	*x = ListPricingRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResponse) ProtoMessage() {}

func (x *ListPricingRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResponse.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListPricingRulesResponse) GetPricingRules() []*v2.PricingRule {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

func (x *ListPricingRulesResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// calculate pricing rule request
type CalculatePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PetDetailCalculateDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,3,opt,name=setting,proto3,oneof" json:"setting,omitempty"`
	// calculate for preview
	IsPreview bool `protobuf:"varint,4,opt,name=is_preview,json=isPreview,proto3" json:"is_preview,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,9,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CalculatePricingRuleRequest) Reset() {
	*x = CalculatePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleRequest) ProtoMessage() {}

func (x *CalculatePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{6}
}

func (x *CalculatePricingRuleRequest) GetPetDetails() []*v2.PetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleRequest) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *CalculatePricingRuleRequest) GetIsPreview() bool {
	if x != nil {
		return x.IsPreview
	}
	return false
}

func (x *CalculatePricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// calculate pricing rule response
type CalculatePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// formula for preview calculation
	Formula *string `protobuf:"bytes,2,opt,name=formula,proto3,oneof" json:"formula,omitempty"`
}

func (x *CalculatePricingRuleResponse) Reset() {
	*x = CalculatePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleResponse) ProtoMessage() {}

func (x *CalculatePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{7}
}

func (x *CalculatePricingRuleResponse) GetPetDetails() []*v2.PetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleResponse) GetFormula() string {
	if x != nil && x.Formula != nil {
		return *x.Formula
	}
	return ""
}

// delete pricing rule request
type DeletePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *DeletePricingRuleRequest) Reset() {
	*x = DeletePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleRequest) ProtoMessage() {}

func (x *DeletePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePricingRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeletePricingRuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// delete pricing rule response
type DeletePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePricingRuleResponse) Reset() {
	*x = DeletePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleResponse) ProtoMessage() {}

func (x *DeletePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{9}
}

// get discount setting params
type GetDiscountSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetDiscountSettingRequest) Reset() {
	*x = GetDiscountSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountSettingRequest) ProtoMessage() {}

func (x *GetDiscountSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountSettingRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetDiscountSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get discount setting result
type GetDiscountSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *GetDiscountSettingResponse) Reset() {
	*x = GetDiscountSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountSettingResponse) ProtoMessage() {}

func (x *GetDiscountSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountSettingResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetDiscountSettingResponse) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

// update discount setting params
type UpdateDiscountSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *UpdateDiscountSettingRequest) Reset() {
	*x = UpdateDiscountSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountSettingRequest) ProtoMessage() {}

func (x *UpdateDiscountSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateDiscountSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateDiscountSettingRequest) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateDiscountSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateDiscountSettingRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// update discount setting result
type UpdateDiscountSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *UpdateDiscountSettingResponse) Reset() {
	*x = UpdateDiscountSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountSettingResponse) ProtoMessage() {}

func (x *UpdateDiscountSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateDiscountSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateDiscountSettingResponse) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

var File_moego_service_offering_v2_pricing_rule_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v2_pricing_rule_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xca, 0x01, 0x0a, 0x18, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x62, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x58,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0xcd, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0xaa, 0x01, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x99, 0x02, 0x0a, 0x1b, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x0b, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4b, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xa1, 0x01, 0x0a, 0x1c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44,
	0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d,
	0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x22, 0x7f, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x22, 0xbc, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x22, 0x67, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x66, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x32, 0xa3, 0x07, 0x0a, 0x12, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x7e, 0x0a, 0x11, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x75, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7e, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x81, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_offering_v2_pricing_rule_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v2_pricing_rule_service_proto_rawDescData = file_moego_service_offering_v2_pricing_rule_service_proto_rawDesc
)

func file_moego_service_offering_v2_pricing_rule_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v2_pricing_rule_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v2_pricing_rule_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v2_pricing_rule_service_proto_rawDescData)
	})
	return file_moego_service_offering_v2_pricing_rule_service_proto_rawDescData
}

var file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_service_offering_v2_pricing_rule_service_proto_goTypes = []interface{}{
	(*UpsertPricingRuleRequest)(nil),       // 0: moego.service.offering.v2.UpsertPricingRuleRequest
	(*UpsertPricingRuleResponse)(nil),      // 1: moego.service.offering.v2.UpsertPricingRuleResponse
	(*GetPricingRuleRequest)(nil),          // 2: moego.service.offering.v2.GetPricingRuleRequest
	(*GetPricingRuleResponse)(nil),         // 3: moego.service.offering.v2.GetPricingRuleResponse
	(*ListPricingRulesRequest)(nil),        // 4: moego.service.offering.v2.ListPricingRulesRequest
	(*ListPricingRulesResponse)(nil),       // 5: moego.service.offering.v2.ListPricingRulesResponse
	(*CalculatePricingRuleRequest)(nil),    // 6: moego.service.offering.v2.CalculatePricingRuleRequest
	(*CalculatePricingRuleResponse)(nil),   // 7: moego.service.offering.v2.CalculatePricingRuleResponse
	(*DeletePricingRuleRequest)(nil),       // 8: moego.service.offering.v2.DeletePricingRuleRequest
	(*DeletePricingRuleResponse)(nil),      // 9: moego.service.offering.v2.DeletePricingRuleResponse
	(*GetDiscountSettingRequest)(nil),      // 10: moego.service.offering.v2.GetDiscountSettingRequest
	(*GetDiscountSettingResponse)(nil),     // 11: moego.service.offering.v2.GetDiscountSettingResponse
	(*UpdateDiscountSettingRequest)(nil),   // 12: moego.service.offering.v2.UpdateDiscountSettingRequest
	(*UpdateDiscountSettingResponse)(nil),  // 13: moego.service.offering.v2.UpdateDiscountSettingResponse
	(*v2.PricingRuleUpsertDef)(nil),        // 14: moego.models.offering.v2.PricingRuleUpsertDef
	(*v2.PricingRule)(nil),                 // 15: moego.models.offering.v2.PricingRule
	(*v2.ListPricingRuleFilter)(nil),       // 16: moego.models.offering.v2.ListPricingRuleFilter
	(*v21.PaginationRequest)(nil),          // 17: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),         // 18: moego.utils.v2.PaginationResponse
	(*v2.PetDetailCalculateDef)(nil),       // 19: moego.models.offering.v2.PetDetailCalculateDef
	(*v2.DiscountSettingDef)(nil),          // 20: moego.models.offering.v2.DiscountSettingDef
	(*v2.PetDetailCalculateResultDef)(nil), // 21: moego.models.offering.v2.PetDetailCalculateResultDef
}
var file_moego_service_offering_v2_pricing_rule_service_proto_depIdxs = []int32{
	14, // 0: moego.service.offering.v2.UpsertPricingRuleRequest.pricing_rule_def:type_name -> moego.models.offering.v2.PricingRuleUpsertDef
	15, // 1: moego.service.offering.v2.UpsertPricingRuleResponse.pricing_rule:type_name -> moego.models.offering.v2.PricingRule
	15, // 2: moego.service.offering.v2.GetPricingRuleResponse.pricing_rule:type_name -> moego.models.offering.v2.PricingRule
	16, // 3: moego.service.offering.v2.ListPricingRulesRequest.filter:type_name -> moego.models.offering.v2.ListPricingRuleFilter
	17, // 4: moego.service.offering.v2.ListPricingRulesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	15, // 5: moego.service.offering.v2.ListPricingRulesResponse.pricing_rules:type_name -> moego.models.offering.v2.PricingRule
	18, // 6: moego.service.offering.v2.ListPricingRulesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	19, // 7: moego.service.offering.v2.CalculatePricingRuleRequest.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateDef
	20, // 8: moego.service.offering.v2.CalculatePricingRuleRequest.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	21, // 9: moego.service.offering.v2.CalculatePricingRuleResponse.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateResultDef
	20, // 10: moego.service.offering.v2.GetDiscountSettingResponse.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	20, // 11: moego.service.offering.v2.UpdateDiscountSettingRequest.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	20, // 12: moego.service.offering.v2.UpdateDiscountSettingResponse.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	0,  // 13: moego.service.offering.v2.PricingRuleService.UpsertPricingRule:input_type -> moego.service.offering.v2.UpsertPricingRuleRequest
	2,  // 14: moego.service.offering.v2.PricingRuleService.GetPricingRule:input_type -> moego.service.offering.v2.GetPricingRuleRequest
	4,  // 15: moego.service.offering.v2.PricingRuleService.ListPricingRules:input_type -> moego.service.offering.v2.ListPricingRulesRequest
	6,  // 16: moego.service.offering.v2.PricingRuleService.CalculatePricingRule:input_type -> moego.service.offering.v2.CalculatePricingRuleRequest
	8,  // 17: moego.service.offering.v2.PricingRuleService.DeletePricingRule:input_type -> moego.service.offering.v2.DeletePricingRuleRequest
	10, // 18: moego.service.offering.v2.PricingRuleService.GetDiscountSetting:input_type -> moego.service.offering.v2.GetDiscountSettingRequest
	12, // 19: moego.service.offering.v2.PricingRuleService.UpdateDiscountSetting:input_type -> moego.service.offering.v2.UpdateDiscountSettingRequest
	1,  // 20: moego.service.offering.v2.PricingRuleService.UpsertPricingRule:output_type -> moego.service.offering.v2.UpsertPricingRuleResponse
	3,  // 21: moego.service.offering.v2.PricingRuleService.GetPricingRule:output_type -> moego.service.offering.v2.GetPricingRuleResponse
	5,  // 22: moego.service.offering.v2.PricingRuleService.ListPricingRules:output_type -> moego.service.offering.v2.ListPricingRulesResponse
	7,  // 23: moego.service.offering.v2.PricingRuleService.CalculatePricingRule:output_type -> moego.service.offering.v2.CalculatePricingRuleResponse
	9,  // 24: moego.service.offering.v2.PricingRuleService.DeletePricingRule:output_type -> moego.service.offering.v2.DeletePricingRuleResponse
	11, // 25: moego.service.offering.v2.PricingRuleService.GetDiscountSetting:output_type -> moego.service.offering.v2.GetDiscountSettingResponse
	13, // 26: moego.service.offering.v2.PricingRuleService.UpdateDiscountSetting:output_type -> moego.service.offering.v2.UpdateDiscountSettingResponse
	20, // [20:27] is the sub-list for method output_type
	13, // [13:20] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v2_pricing_rule_service_proto_init() }
func file_moego_service_offering_v2_pricing_rule_service_proto_init() {
	if File_moego_service_offering_v2_pricing_rule_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v2_pricing_rule_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v2_pricing_rule_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v2_pricing_rule_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v2_pricing_rule_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v2_pricing_rule_service_proto = out.File
	file_moego_service_offering_v2_pricing_rule_service_proto_rawDesc = nil
	file_moego_service_offering_v2_pricing_rule_service_proto_goTypes = nil
	file_moego_service_offering_v2_pricing_rule_service_proto_depIdxs = nil
}
