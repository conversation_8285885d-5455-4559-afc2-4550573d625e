// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/notification/v1/notification_service.proto

package notificationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NotificationServiceClient is the client API for NotificationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotificationServiceClient interface {
	// Create inbox notification
	CreateInboxNotification(ctx context.Context, in *CreateInboxNotificationRequest, opts ...grpc.CallOption) (*CreateInboxNotificationResponse, error)
	// Read inbox notification
	ReadInboxNotification(ctx context.Context, in *ReadInboxNotificationRequest, opts ...grpc.CallOption) (*ReadInboxNotificationResponse, error)
	// Delete inbox notification
	DeleteInboxNotification(ctx context.Context, in *DeleteInboxNotificationRequest, opts ...grpc.CallOption) (*DeleteInboxNotificationResponse, error)
	// Get notification list
	GetNotificationList(ctx context.Context, in *GetNotificationListRequest, opts ...grpc.CallOption) (*GetNotificationListResponse, error)
}

type notificationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationServiceClient(cc grpc.ClientConnInterface) NotificationServiceClient {
	return &notificationServiceClient{cc}
}

func (c *notificationServiceClient) CreateInboxNotification(ctx context.Context, in *CreateInboxNotificationRequest, opts ...grpc.CallOption) (*CreateInboxNotificationResponse, error) {
	out := new(CreateInboxNotificationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.NotificationService/CreateInboxNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) ReadInboxNotification(ctx context.Context, in *ReadInboxNotificationRequest, opts ...grpc.CallOption) (*ReadInboxNotificationResponse, error) {
	out := new(ReadInboxNotificationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.NotificationService/ReadInboxNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) DeleteInboxNotification(ctx context.Context, in *DeleteInboxNotificationRequest, opts ...grpc.CallOption) (*DeleteInboxNotificationResponse, error) {
	out := new(DeleteInboxNotificationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.NotificationService/DeleteInboxNotification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetNotificationList(ctx context.Context, in *GetNotificationListRequest, opts ...grpc.CallOption) (*GetNotificationListResponse, error) {
	out := new(GetNotificationListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.notification.v1.NotificationService/GetNotificationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationServiceServer is the server API for NotificationService service.
// All implementations must embed UnimplementedNotificationServiceServer
// for forward compatibility
type NotificationServiceServer interface {
	// Create inbox notification
	CreateInboxNotification(context.Context, *CreateInboxNotificationRequest) (*CreateInboxNotificationResponse, error)
	// Read inbox notification
	ReadInboxNotification(context.Context, *ReadInboxNotificationRequest) (*ReadInboxNotificationResponse, error)
	// Delete inbox notification
	DeleteInboxNotification(context.Context, *DeleteInboxNotificationRequest) (*DeleteInboxNotificationResponse, error)
	// Get notification list
	GetNotificationList(context.Context, *GetNotificationListRequest) (*GetNotificationListResponse, error)
	mustEmbedUnimplementedNotificationServiceServer()
}

// UnimplementedNotificationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNotificationServiceServer struct {
}

func (UnimplementedNotificationServiceServer) CreateInboxNotification(context.Context, *CreateInboxNotificationRequest) (*CreateInboxNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInboxNotification not implemented")
}
func (UnimplementedNotificationServiceServer) ReadInboxNotification(context.Context, *ReadInboxNotificationRequest) (*ReadInboxNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadInboxNotification not implemented")
}
func (UnimplementedNotificationServiceServer) DeleteInboxNotification(context.Context, *DeleteInboxNotificationRequest) (*DeleteInboxNotificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInboxNotification not implemented")
}
func (UnimplementedNotificationServiceServer) GetNotificationList(context.Context, *GetNotificationListRequest) (*GetNotificationListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotificationList not implemented")
}
func (UnimplementedNotificationServiceServer) mustEmbedUnimplementedNotificationServiceServer() {}

// UnsafeNotificationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationServiceServer will
// result in compilation errors.
type UnsafeNotificationServiceServer interface {
	mustEmbedUnimplementedNotificationServiceServer()
}

func RegisterNotificationServiceServer(s grpc.ServiceRegistrar, srv NotificationServiceServer) {
	s.RegisterService(&NotificationService_ServiceDesc, srv)
}

func _NotificationService_CreateInboxNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInboxNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).CreateInboxNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.NotificationService/CreateInboxNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).CreateInboxNotification(ctx, req.(*CreateInboxNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_ReadInboxNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadInboxNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).ReadInboxNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.NotificationService/ReadInboxNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).ReadInboxNotification(ctx, req.(*ReadInboxNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_DeleteInboxNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInboxNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).DeleteInboxNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.NotificationService/DeleteInboxNotification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).DeleteInboxNotification(ctx, req.(*DeleteInboxNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetNotificationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetNotificationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.notification.v1.NotificationService/GetNotificationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetNotificationList(ctx, req.(*GetNotificationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationService_ServiceDesc is the grpc.ServiceDesc for NotificationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.notification.v1.NotificationService",
	HandlerType: (*NotificationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInboxNotification",
			Handler:    _NotificationService_CreateInboxNotification_Handler,
		},
		{
			MethodName: "ReadInboxNotification",
			Handler:    _NotificationService_ReadInboxNotification_Handler,
		},
		{
			MethodName: "DeleteInboxNotification",
			Handler:    _NotificationService_DeleteInboxNotification_Handler,
		},
		{
			MethodName: "GetNotificationList",
			Handler:    _NotificationService_GetNotificationList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/notification/v1/notification_service.proto",
}
