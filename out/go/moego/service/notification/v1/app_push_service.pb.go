// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/notification/v1/app_push_service.proto

package notificationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// register device request
type RefreshDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// push token
	PushToken string `protobuf:"bytes,1,opt,name=push_token,json=pushToken,proto3" json:"push_token,omitempty"`
	// device type
	DeviceType v1.DeviceType `protobuf:"varint,2,opt,name=device_type,json=deviceType,proto3,enum=moego.models.notification.v1.DeviceType" json:"device_type,omitempty"`
	// account id
	AccountId *int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// source
	Source v1.PushTokenSource `protobuf:"varint,4,opt,name=source,proto3,enum=moego.models.notification.v1.PushTokenSource" json:"source,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,6,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *RefreshDeviceRequest) Reset() {
	*x = RefreshDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDeviceRequest) ProtoMessage() {}

func (x *RefreshDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDeviceRequest.ProtoReflect.Descriptor instead.
func (*RefreshDeviceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{0}
}

func (x *RefreshDeviceRequest) GetPushToken() string {
	if x != nil {
		return x.PushToken
	}
	return ""
}

func (x *RefreshDeviceRequest) GetDeviceType() v1.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return v1.DeviceType(0)
}

func (x *RefreshDeviceRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *RefreshDeviceRequest) GetSource() v1.PushTokenSource {
	if x != nil {
		return x.Source
	}
	return v1.PushTokenSource(0)
}

func (x *RefreshDeviceRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *RefreshDeviceRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// register device response
type RefreshDeviceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is online
	IsOnline bool `protobuf:"varint,1,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
}

func (x *RefreshDeviceResponse) Reset() {
	*x = RefreshDeviceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDeviceResponse) ProtoMessage() {}

func (x *RefreshDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDeviceResponse.ProtoReflect.Descriptor instead.
func (*RefreshDeviceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{1}
}

func (x *RefreshDeviceResponse) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

// create app push request
type CreateAppPushRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type
	Type v1.NotificationType `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.notification.v1.NotificationType" json:"type,omitempty"`
	// extra info
	Extra *v1.NotificationExtraDef `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	// app push
	AppPush *v1.AppPushDef `protobuf:"bytes,6,opt,name=app_push,json=appPush,proto3" json:"app_push,omitempty"`
}

func (x *CreateAppPushRequest) Reset() {
	*x = CreateAppPushRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppPushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppPushRequest) ProtoMessage() {}

func (x *CreateAppPushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppPushRequest.ProtoReflect.Descriptor instead.
func (*CreateAppPushRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAppPushRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateAppPushRequest) GetType() v1.NotificationType {
	if x != nil {
		return x.Type
	}
	return v1.NotificationType(0)
}

func (x *CreateAppPushRequest) GetExtra() *v1.NotificationExtraDef {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *CreateAppPushRequest) GetAppPush() *v1.AppPushDef {
	if x != nil {
		return x.AppPush
	}
	return nil
}

// create app push response
type CreateAppPushResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// push successes
	PushSuccesses int64 `protobuf:"varint,1,opt,name=push_successes,json=pushSuccesses,proto3" json:"push_successes,omitempty"`
}

func (x *CreateAppPushResponse) Reset() {
	*x = CreateAppPushResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppPushResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppPushResponse) ProtoMessage() {}

func (x *CreateAppPushResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppPushResponse.ProtoReflect.Descriptor instead.
func (*CreateAppPushResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAppPushResponse) GetPushSuccesses() int64 {
	if x != nil {
		return x.PushSuccesses
	}
	return 0
}

// batch crate app push request
type BatchCreateAppPushRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*BatchCreateAppPushRequest_ByAccount
	//	*BatchCreateAppPushRequest_ByCompany
	//	*BatchCreateAppPushRequest_ByBusiness
	Identifier isBatchCreateAppPushRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *BatchCreateAppPushRequest) Reset() {
	*x = BatchCreateAppPushRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateAppPushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateAppPushRequest) ProtoMessage() {}

func (x *BatchCreateAppPushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateAppPushRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateAppPushRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{4}
}

func (m *BatchCreateAppPushRequest) GetIdentifier() isBatchCreateAppPushRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *BatchCreateAppPushRequest) GetByAccount() *MultiAccountDef {
	if x, ok := x.GetIdentifier().(*BatchCreateAppPushRequest_ByAccount); ok {
		return x.ByAccount
	}
	return nil
}

func (x *BatchCreateAppPushRequest) GetByCompany() *MultiCompanyDef {
	if x, ok := x.GetIdentifier().(*BatchCreateAppPushRequest_ByCompany); ok {
		return x.ByCompany
	}
	return nil
}

func (x *BatchCreateAppPushRequest) GetByBusiness() *MultiBusinessDef {
	if x, ok := x.GetIdentifier().(*BatchCreateAppPushRequest_ByBusiness); ok {
		return x.ByBusiness
	}
	return nil
}

type isBatchCreateAppPushRequest_Identifier interface {
	isBatchCreateAppPushRequest_Identifier()
}

type BatchCreateAppPushRequest_ByAccount struct {
	// by account
	ByAccount *MultiAccountDef `protobuf:"bytes,1,opt,name=by_account,json=byAccount,proto3,oneof"`
}

type BatchCreateAppPushRequest_ByCompany struct {
	// by company
	ByCompany *MultiCompanyDef `protobuf:"bytes,2,opt,name=by_company,json=byCompany,proto3,oneof"`
}

type BatchCreateAppPushRequest_ByBusiness struct {
	// by business
	ByBusiness *MultiBusinessDef `protobuf:"bytes,3,opt,name=by_business,json=byBusiness,proto3,oneof"`
}

func (*BatchCreateAppPushRequest_ByAccount) isBatchCreateAppPushRequest_Identifier() {}

func (*BatchCreateAppPushRequest_ByCompany) isBatchCreateAppPushRequest_Identifier() {}

func (*BatchCreateAppPushRequest_ByBusiness) isBatchCreateAppPushRequest_Identifier() {}

// multi account def
type MultiAccountDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multi account ids
	AccountIds []int64 `protobuf:"varint,1,rep,packed,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
}

func (x *MultiAccountDef) Reset() {
	*x = MultiAccountDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiAccountDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiAccountDef) ProtoMessage() {}

func (x *MultiAccountDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiAccountDef.ProtoReflect.Descriptor instead.
func (*MultiAccountDef) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{5}
}

func (x *MultiAccountDef) GetAccountIds() []int64 {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

// multi company def
type MultiCompanyDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multi company ids
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *MultiCompanyDef) Reset() {
	*x = MultiCompanyDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiCompanyDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiCompanyDef) ProtoMessage() {}

func (x *MultiCompanyDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiCompanyDef.ProtoReflect.Descriptor instead.
func (*MultiCompanyDef) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{6}
}

func (x *MultiCompanyDef) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// multi business def
type MultiBusinessDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multi business ids
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *MultiBusinessDef) Reset() {
	*x = MultiBusinessDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiBusinessDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiBusinessDef) ProtoMessage() {}

func (x *MultiBusinessDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiBusinessDef.ProtoReflect.Descriptor instead.
func (*MultiBusinessDef) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{7}
}

func (x *MultiBusinessDef) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// batch create app push response
type BatchCreateAppPushResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchCreateAppPushResponse) Reset() {
	*x = BatchCreateAppPushResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateAppPushResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateAppPushResponse) ProtoMessage() {}

func (x *BatchCreateAppPushResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateAppPushResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateAppPushResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{8}
}

// refresh branded app notification config request
type RefreshBrandedAppNotificationConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshBrandedAppNotificationConfigRequest) Reset() {
	*x = RefreshBrandedAppNotificationConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshBrandedAppNotificationConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshBrandedAppNotificationConfigRequest) ProtoMessage() {}

func (x *RefreshBrandedAppNotificationConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshBrandedAppNotificationConfigRequest.ProtoReflect.Descriptor instead.
func (*RefreshBrandedAppNotificationConfigRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{9}
}

// refresh branded app notification config response
type RefreshBrandedAppNotificationConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshBrandedAppNotificationConfigResponse) Reset() {
	*x = RefreshBrandedAppNotificationConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshBrandedAppNotificationConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshBrandedAppNotificationConfigResponse) ProtoMessage() {}

func (x *RefreshBrandedAppNotificationConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshBrandedAppNotificationConfigResponse.ProtoReflect.Descriptor instead.
func (*RefreshBrandedAppNotificationConfigResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{10}
}

// list push token request
type ListPushTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListPushTokenRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPushTokenRequest) Reset() {
	*x = ListPushTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPushTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPushTokenRequest) ProtoMessage() {}

func (x *ListPushTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPushTokenRequest.ProtoReflect.Descriptor instead.
func (*ListPushTokenRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListPushTokenRequest) GetFilter() *ListPushTokenRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list push token response
type ListPushTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// push tokens
	PushTokens []*v1.PushTokenModel `protobuf:"bytes,1,rep,name=push_tokens,json=pushTokens,proto3" json:"push_tokens,omitempty"`
}

func (x *ListPushTokenResponse) Reset() {
	*x = ListPushTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPushTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPushTokenResponse) ProtoMessage() {}

func (x *ListPushTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPushTokenResponse.ProtoReflect.Descriptor instead.
func (*ListPushTokenResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListPushTokenResponse) GetPushTokens() []*v1.PushTokenModel {
	if x != nil {
		return x.PushTokens
	}
	return nil
}

// filter
type ListPushTokenRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountIds []int64 `protobuf:"varint,1,rep,packed,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
	// source
	Sources []v1.PushTokenSource `protobuf:"varint,2,rep,packed,name=sources,proto3,enum=moego.models.notification.v1.PushTokenSource" json:"sources,omitempty"`
}

func (x *ListPushTokenRequest_Filter) Reset() {
	*x = ListPushTokenRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPushTokenRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPushTokenRequest_Filter) ProtoMessage() {}

func (x *ListPushTokenRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_app_push_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPushTokenRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPushTokenRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ListPushTokenRequest_Filter) GetAccountIds() []int64 {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

func (x *ListPushTokenRequest_Filter) GetSources() []v1.PushTokenSource {
	if x != nil {
		return x.Sources
	}
	return nil
}

var File_moego_service_notification_v1_app_push_service_proto protoreflect.FileDescriptor

var file_moego_service_notification_v1_app_push_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73,
	0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x03,
	0x0a, 0x14, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x73, 0x68,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x49, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x34, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x22, 0x9b,
	0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x65, 0x66,
	0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x43, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x75, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68,
	0x44, 0x65, 0x66, 0x52, 0x07, 0x61, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x22, 0x47, 0x0a, 0x15,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x0e, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0d, 0x70, 0x75, 0x73, 0x68, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x0a, 0x62, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x09, 0x62, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0a, 0x62, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x09, 0x62, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x52, 0x0a, 0x0b, 0x62, 0x79, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x62,
	0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x42, 0x0a, 0x0f,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12,
	0x2f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x00, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x22, 0x42, 0x0a, 0x0f, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x44, 0x65, 0x66, 0x12, 0x2f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08,
	0x08, 0x00, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x22, 0x45, 0x0a, 0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x00, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x1c, 0x0a, 0x1a, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x0a, 0x2a, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2d, 0x0a, 0x2b, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfc, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x52, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x1a, 0x8f, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2d,
	0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x56, 0x0a,
	0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0d, 0xfa,
	0x42, 0x0a, 0x92, 0x01, 0x07, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x66, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x73,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d,
	0x0a, 0x0b, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x32, 0xcf, 0x05,
	0x0a, 0x0e, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x7a, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x0d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xbc, 0x01, 0x0a, 0x23, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x49, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x42,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x73, 0x68, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_notification_v1_app_push_service_proto_rawDescOnce sync.Once
	file_moego_service_notification_v1_app_push_service_proto_rawDescData = file_moego_service_notification_v1_app_push_service_proto_rawDesc
)

func file_moego_service_notification_v1_app_push_service_proto_rawDescGZIP() []byte {
	file_moego_service_notification_v1_app_push_service_proto_rawDescOnce.Do(func() {
		file_moego_service_notification_v1_app_push_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_notification_v1_app_push_service_proto_rawDescData)
	})
	return file_moego_service_notification_v1_app_push_service_proto_rawDescData
}

var file_moego_service_notification_v1_app_push_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_service_notification_v1_app_push_service_proto_goTypes = []interface{}{
	(*RefreshDeviceRequest)(nil),                        // 0: moego.service.notification.v1.RefreshDeviceRequest
	(*RefreshDeviceResponse)(nil),                       // 1: moego.service.notification.v1.RefreshDeviceResponse
	(*CreateAppPushRequest)(nil),                        // 2: moego.service.notification.v1.CreateAppPushRequest
	(*CreateAppPushResponse)(nil),                       // 3: moego.service.notification.v1.CreateAppPushResponse
	(*BatchCreateAppPushRequest)(nil),                   // 4: moego.service.notification.v1.BatchCreateAppPushRequest
	(*MultiAccountDef)(nil),                             // 5: moego.service.notification.v1.MultiAccountDef
	(*MultiCompanyDef)(nil),                             // 6: moego.service.notification.v1.MultiCompanyDef
	(*MultiBusinessDef)(nil),                            // 7: moego.service.notification.v1.MultiBusinessDef
	(*BatchCreateAppPushResponse)(nil),                  // 8: moego.service.notification.v1.BatchCreateAppPushResponse
	(*RefreshBrandedAppNotificationConfigRequest)(nil),  // 9: moego.service.notification.v1.RefreshBrandedAppNotificationConfigRequest
	(*RefreshBrandedAppNotificationConfigResponse)(nil), // 10: moego.service.notification.v1.RefreshBrandedAppNotificationConfigResponse
	(*ListPushTokenRequest)(nil),                        // 11: moego.service.notification.v1.ListPushTokenRequest
	(*ListPushTokenResponse)(nil),                       // 12: moego.service.notification.v1.ListPushTokenResponse
	(*ListPushTokenRequest_Filter)(nil),                 // 13: moego.service.notification.v1.ListPushTokenRequest.Filter
	(v1.DeviceType)(0),                                  // 14: moego.models.notification.v1.DeviceType
	(v1.PushTokenSource)(0),                             // 15: moego.models.notification.v1.PushTokenSource
	(v1.NotificationType)(0),                            // 16: moego.models.notification.v1.NotificationType
	(*v1.NotificationExtraDef)(nil),                     // 17: moego.models.notification.v1.NotificationExtraDef
	(*v1.AppPushDef)(nil),                               // 18: moego.models.notification.v1.AppPushDef
	(*v1.PushTokenModel)(nil),                           // 19: moego.models.notification.v1.PushTokenModel
}
var file_moego_service_notification_v1_app_push_service_proto_depIdxs = []int32{
	14, // 0: moego.service.notification.v1.RefreshDeviceRequest.device_type:type_name -> moego.models.notification.v1.DeviceType
	15, // 1: moego.service.notification.v1.RefreshDeviceRequest.source:type_name -> moego.models.notification.v1.PushTokenSource
	16, // 2: moego.service.notification.v1.CreateAppPushRequest.type:type_name -> moego.models.notification.v1.NotificationType
	17, // 3: moego.service.notification.v1.CreateAppPushRequest.extra:type_name -> moego.models.notification.v1.NotificationExtraDef
	18, // 4: moego.service.notification.v1.CreateAppPushRequest.app_push:type_name -> moego.models.notification.v1.AppPushDef
	5,  // 5: moego.service.notification.v1.BatchCreateAppPushRequest.by_account:type_name -> moego.service.notification.v1.MultiAccountDef
	6,  // 6: moego.service.notification.v1.BatchCreateAppPushRequest.by_company:type_name -> moego.service.notification.v1.MultiCompanyDef
	7,  // 7: moego.service.notification.v1.BatchCreateAppPushRequest.by_business:type_name -> moego.service.notification.v1.MultiBusinessDef
	13, // 8: moego.service.notification.v1.ListPushTokenRequest.filter:type_name -> moego.service.notification.v1.ListPushTokenRequest.Filter
	19, // 9: moego.service.notification.v1.ListPushTokenResponse.push_tokens:type_name -> moego.models.notification.v1.PushTokenModel
	15, // 10: moego.service.notification.v1.ListPushTokenRequest.Filter.sources:type_name -> moego.models.notification.v1.PushTokenSource
	0,  // 11: moego.service.notification.v1.AppPushService.RefreshDevice:input_type -> moego.service.notification.v1.RefreshDeviceRequest
	2,  // 12: moego.service.notification.v1.AppPushService.CreateAppPush:input_type -> moego.service.notification.v1.CreateAppPushRequest
	4,  // 13: moego.service.notification.v1.AppPushService.BatchCreateAppPush:input_type -> moego.service.notification.v1.BatchCreateAppPushRequest
	9,  // 14: moego.service.notification.v1.AppPushService.RefreshBrandedAppNotificationConfig:input_type -> moego.service.notification.v1.RefreshBrandedAppNotificationConfigRequest
	11, // 15: moego.service.notification.v1.AppPushService.ListPushToken:input_type -> moego.service.notification.v1.ListPushTokenRequest
	1,  // 16: moego.service.notification.v1.AppPushService.RefreshDevice:output_type -> moego.service.notification.v1.RefreshDeviceResponse
	3,  // 17: moego.service.notification.v1.AppPushService.CreateAppPush:output_type -> moego.service.notification.v1.CreateAppPushResponse
	8,  // 18: moego.service.notification.v1.AppPushService.BatchCreateAppPush:output_type -> moego.service.notification.v1.BatchCreateAppPushResponse
	10, // 19: moego.service.notification.v1.AppPushService.RefreshBrandedAppNotificationConfig:output_type -> moego.service.notification.v1.RefreshBrandedAppNotificationConfigResponse
	12, // 20: moego.service.notification.v1.AppPushService.ListPushToken:output_type -> moego.service.notification.v1.ListPushTokenResponse
	16, // [16:21] is the sub-list for method output_type
	11, // [11:16] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_service_notification_v1_app_push_service_proto_init() }
func file_moego_service_notification_v1_app_push_service_proto_init() {
	if File_moego_service_notification_v1_app_push_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDeviceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppPushRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppPushResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateAppPushRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiAccountDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiCompanyDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MultiBusinessDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateAppPushResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshBrandedAppNotificationConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshBrandedAppNotificationConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPushTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPushTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_app_push_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPushTokenRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_notification_v1_app_push_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_notification_v1_app_push_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*BatchCreateAppPushRequest_ByAccount)(nil),
		(*BatchCreateAppPushRequest_ByCompany)(nil),
		(*BatchCreateAppPushRequest_ByBusiness)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_notification_v1_app_push_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_notification_v1_app_push_service_proto_goTypes,
		DependencyIndexes: file_moego_service_notification_v1_app_push_service_proto_depIdxs,
		MessageInfos:      file_moego_service_notification_v1_app_push_service_proto_msgTypes,
	}.Build()
	File_moego_service_notification_v1_app_push_service_proto = out.File
	file_moego_service_notification_v1_app_push_service_proto_rawDesc = nil
	file_moego_service_notification_v1_app_push_service_proto_goTypes = nil
	file_moego_service_notification_v1_app_push_service_proto_depIdxs = nil
}
