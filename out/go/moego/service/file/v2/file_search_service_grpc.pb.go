// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/file/v2/file_search_service.proto

package filesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FileSearchServiceClient is the client API for FileSearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileSearchServiceClient interface {
	// DescribeFiles
	DescribeFiles(ctx context.Context, in *DescribeFilesRequest, opts ...grpc.CallOption) (*DescribeFilesResponse, error)
}

type fileSearchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileSearchServiceClient(cc grpc.ClientConnInterface) FileSearchServiceClient {
	return &fileSearchServiceClient{cc}
}

func (c *fileSearchServiceClient) DescribeFiles(ctx context.Context, in *DescribeFilesRequest, opts ...grpc.CallOption) (*DescribeFilesResponse, error) {
	out := new(DescribeFilesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileSearchService/DescribeFiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileSearchServiceServer is the server API for FileSearchService service.
// All implementations must embed UnimplementedFileSearchServiceServer
// for forward compatibility
type FileSearchServiceServer interface {
	// DescribeFiles
	DescribeFiles(context.Context, *DescribeFilesRequest) (*DescribeFilesResponse, error)
	mustEmbedUnimplementedFileSearchServiceServer()
}

// UnimplementedFileSearchServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFileSearchServiceServer struct {
}

func (UnimplementedFileSearchServiceServer) DescribeFiles(context.Context, *DescribeFilesRequest) (*DescribeFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeFiles not implemented")
}
func (UnimplementedFileSearchServiceServer) mustEmbedUnimplementedFileSearchServiceServer() {}

// UnsafeFileSearchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileSearchServiceServer will
// result in compilation errors.
type UnsafeFileSearchServiceServer interface {
	mustEmbedUnimplementedFileSearchServiceServer()
}

func RegisterFileSearchServiceServer(s grpc.ServiceRegistrar, srv FileSearchServiceServer) {
	s.RegisterService(&FileSearchService_ServiceDesc, srv)
}

func _FileSearchService_DescribeFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileSearchServiceServer).DescribeFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileSearchService/DescribeFiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileSearchServiceServer).DescribeFiles(ctx, req.(*DescribeFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileSearchService_ServiceDesc is the grpc.ServiceDesc for FileSearchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileSearchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.file.v2.FileSearchService",
	HandlerType: (*FileSearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeFiles",
			Handler:    _FileSearchService_DescribeFiles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/file/v2/file_search_service.proto",
}
