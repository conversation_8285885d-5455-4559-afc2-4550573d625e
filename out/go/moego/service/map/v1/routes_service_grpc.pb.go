// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/map/v1/routes_service.proto

package mapsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RoutesServiceClient is the client API for RoutesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoutesServiceClient interface {
	// query routes
	QueryRoutes(ctx context.Context, in *QueryRoutesRequest, opts ...grpc.CallOption) (*QueryRoutesResponse, error)
	// query routes by point
	// This interface uses the following parameters by default:
	//   - TravelMode: DRIVING
	//   - PolylineEncoding: ENCODED_POLYLINE
	//
	// Note: Call this API will give priority to getting the value from the cache.
	// If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
	QueryRouteByPoint(ctx context.Context, in *QueryRouteByPointRequest, opts ...grpc.CallOption) (*QueryRoutesResponse, error)
	// query route matrix
	QueryRouteMatrix(ctx context.Context, in *QueryRouteMatrixRequest, opts ...grpc.CallOption) (*QueryRouteMatrixResponse, error)
	// query route matrix by points
	// This interface uses the following parameters by default:
	//   - TravelMode: DRIVING
	//
	// Note: Call this API will give priority to getting the value from the cache.
	// If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
	QueryRouteMatrixByPoints(ctx context.Context, in *QueryRouteMatrixByPointsRequest, opts ...grpc.CallOption) (*QueryRouteMatrixResponse, error)
}

type routesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRoutesServiceClient(cc grpc.ClientConnInterface) RoutesServiceClient {
	return &routesServiceClient{cc}
}

func (c *routesServiceClient) QueryRoutes(ctx context.Context, in *QueryRoutesRequest, opts ...grpc.CallOption) (*QueryRoutesResponse, error) {
	out := new(QueryRoutesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.RoutesService/QueryRoutes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *routesServiceClient) QueryRouteByPoint(ctx context.Context, in *QueryRouteByPointRequest, opts ...grpc.CallOption) (*QueryRoutesResponse, error) {
	out := new(QueryRoutesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.RoutesService/QueryRouteByPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *routesServiceClient) QueryRouteMatrix(ctx context.Context, in *QueryRouteMatrixRequest, opts ...grpc.CallOption) (*QueryRouteMatrixResponse, error) {
	out := new(QueryRouteMatrixResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.RoutesService/QueryRouteMatrix", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *routesServiceClient) QueryRouteMatrixByPoints(ctx context.Context, in *QueryRouteMatrixByPointsRequest, opts ...grpc.CallOption) (*QueryRouteMatrixResponse, error) {
	out := new(QueryRouteMatrixResponse)
	err := c.cc.Invoke(ctx, "/moego.service.map.v1.RoutesService/QueryRouteMatrixByPoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoutesServiceServer is the server API for RoutesService service.
// All implementations must embed UnimplementedRoutesServiceServer
// for forward compatibility
type RoutesServiceServer interface {
	// query routes
	QueryRoutes(context.Context, *QueryRoutesRequest) (*QueryRoutesResponse, error)
	// query routes by point
	// This interface uses the following parameters by default:
	//   - TravelMode: DRIVING
	//   - PolylineEncoding: ENCODED_POLYLINE
	//
	// Note: Call this API will give priority to getting the value from the cache.
	// If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
	QueryRouteByPoint(context.Context, *QueryRouteByPointRequest) (*QueryRoutesResponse, error)
	// query route matrix
	QueryRouteMatrix(context.Context, *QueryRouteMatrixRequest) (*QueryRouteMatrixResponse, error)
	// query route matrix by points
	// This interface uses the following parameters by default:
	//   - TravelMode: DRIVING
	//
	// Note: Call this API will give priority to getting the value from the cache.
	// If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
	QueryRouteMatrixByPoints(context.Context, *QueryRouteMatrixByPointsRequest) (*QueryRouteMatrixResponse, error)
	mustEmbedUnimplementedRoutesServiceServer()
}

// UnimplementedRoutesServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRoutesServiceServer struct {
}

func (UnimplementedRoutesServiceServer) QueryRoutes(context.Context, *QueryRoutesRequest) (*QueryRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRoutes not implemented")
}
func (UnimplementedRoutesServiceServer) QueryRouteByPoint(context.Context, *QueryRouteByPointRequest) (*QueryRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRouteByPoint not implemented")
}
func (UnimplementedRoutesServiceServer) QueryRouteMatrix(context.Context, *QueryRouteMatrixRequest) (*QueryRouteMatrixResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRouteMatrix not implemented")
}
func (UnimplementedRoutesServiceServer) QueryRouteMatrixByPoints(context.Context, *QueryRouteMatrixByPointsRequest) (*QueryRouteMatrixResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRouteMatrixByPoints not implemented")
}
func (UnimplementedRoutesServiceServer) mustEmbedUnimplementedRoutesServiceServer() {}

// UnsafeRoutesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoutesServiceServer will
// result in compilation errors.
type UnsafeRoutesServiceServer interface {
	mustEmbedUnimplementedRoutesServiceServer()
}

func RegisterRoutesServiceServer(s grpc.ServiceRegistrar, srv RoutesServiceServer) {
	s.RegisterService(&RoutesService_ServiceDesc, srv)
}

func _RoutesService_QueryRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoutesServiceServer).QueryRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.RoutesService/QueryRoutes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoutesServiceServer).QueryRoutes(ctx, req.(*QueryRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoutesService_QueryRouteByPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRouteByPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoutesServiceServer).QueryRouteByPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.RoutesService/QueryRouteByPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoutesServiceServer).QueryRouteByPoint(ctx, req.(*QueryRouteByPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoutesService_QueryRouteMatrix_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRouteMatrixRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoutesServiceServer).QueryRouteMatrix(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.RoutesService/QueryRouteMatrix",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoutesServiceServer).QueryRouteMatrix(ctx, req.(*QueryRouteMatrixRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoutesService_QueryRouteMatrixByPoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRouteMatrixByPointsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoutesServiceServer).QueryRouteMatrixByPoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.map.v1.RoutesService/QueryRouteMatrixByPoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoutesServiceServer).QueryRouteMatrixByPoints(ctx, req.(*QueryRouteMatrixByPointsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RoutesService_ServiceDesc is the grpc.ServiceDesc for RoutesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoutesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.map.v1.RoutesService",
	HandlerType: (*RoutesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRoutes",
			Handler:    _RoutesService_QueryRoutes_Handler,
		},
		{
			MethodName: "QueryRouteByPoint",
			Handler:    _RoutesService_QueryRouteByPoint_Handler,
		},
		{
			MethodName: "QueryRouteMatrix",
			Handler:    _RoutesService_QueryRouteMatrix_Handler,
		},
		{
			MethodName: "QueryRouteMatrixByPoints",
			Handler:    _RoutesService_QueryRouteMatrixByPoints_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/map/v1/routes_service.proto",
}
