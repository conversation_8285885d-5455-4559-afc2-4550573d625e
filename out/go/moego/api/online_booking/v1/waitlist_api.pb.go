// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/waitlist_api.proto

package onlinebookingapipb

import (
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create waitlist params
type CreateWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Pet services
	Services []*v1.Service `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
	// comment
	Comment *string `protobuf:"bytes,4,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date, format: yyyy-mm-dd
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *CreateWaitlistParams) Reset() {
	*x = CreateWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWaitlistParams) ProtoMessage() {}

func (x *CreateWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWaitlistParams.ProtoReflect.Descriptor instead.
func (*CreateWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWaitlistParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateWaitlistParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateWaitlistParams) GetServices() []*v1.Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *CreateWaitlistParams) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *CreateWaitlistParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *CreateWaitlistParams) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

// create waitlist result
type CreateWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist id
	WaitlistId int64 `protobuf:"varint,1,opt,name=waitlist_id,json=waitlistId,proto3" json:"waitlist_id,omitempty"`
}

func (x *CreateWaitlistResult) Reset() {
	*x = CreateWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWaitlistResult) ProtoMessage() {}

func (x *CreateWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWaitlistResult.ProtoReflect.Descriptor instead.
func (*CreateWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateWaitlistResult) GetWaitlistId() int64 {
	if x != nil {
		return x.WaitlistId
	}
	return 0
}

// update waitlist params
type UpdateWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist id
	WaitlistId int64 `protobuf:"varint,1,opt,name=waitlist_id,json=waitlistId,proto3" json:"waitlist_id,omitempty"`
	// Pet services
	Services []*v1.Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// comment
	Comment *string `protobuf:"bytes,3,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate *string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date, format: yyyy-mm-dd
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *UpdateWaitlistParams) Reset() {
	*x = UpdateWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWaitlistParams) ProtoMessage() {}

func (x *UpdateWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWaitlistParams.ProtoReflect.Descriptor instead.
func (*UpdateWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateWaitlistParams) GetWaitlistId() int64 {
	if x != nil {
		return x.WaitlistId
	}
	return 0
}

func (x *UpdateWaitlistParams) GetServices() []*v1.Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *UpdateWaitlistParams) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *UpdateWaitlistParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *UpdateWaitlistParams) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

// update waitlist result
type UpdateWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateWaitlistResult) Reset() {
	*x = UpdateWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWaitlistResult) ProtoMessage() {}

func (x *UpdateWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWaitlistResult.ProtoReflect.Descriptor instead.
func (*UpdateWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{3}
}

// delete waitlist params
type DeleteWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist id
	WaitlistId int64 `protobuf:"varint,1,opt,name=waitlist_id,json=waitlistId,proto3" json:"waitlist_id,omitempty"`
}

func (x *DeleteWaitlistParams) Reset() {
	*x = DeleteWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWaitlistParams) ProtoMessage() {}

func (x *DeleteWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWaitlistParams.ProtoReflect.Descriptor instead.
func (*DeleteWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteWaitlistParams) GetWaitlistId() int64 {
	if x != nil {
		return x.WaitlistId
	}
	return 0
}

// Delete waitlist result
type DeleteWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteWaitlistResult) Reset() {
	*x = DeleteWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWaitlistResult) ProtoMessage() {}

func (x *DeleteWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWaitlistResult.ProtoReflect.Descriptor instead.
func (*DeleteWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{5}
}

// get waitlist
type GetWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist id
	WaitlistId int64 `protobuf:"varint,1,opt,name=waitlist_id,json=waitlistId,proto3" json:"waitlist_id,omitempty"`
}

func (x *GetWaitlistParams) Reset() {
	*x = GetWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWaitlistParams) ProtoMessage() {}

func (x *GetWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWaitlistParams.ProtoReflect.Descriptor instead.
func (*GetWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetWaitlistParams) GetWaitlistId() int64 {
	if x != nil {
		return x.WaitlistId
	}
	return 0
}

// get waitlist result
type GetWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist
	Waitlist *WaitlistView `protobuf:"bytes,1,opt,name=waitlist,proto3" json:"waitlist,omitempty"`
}

func (x *GetWaitlistResult) Reset() {
	*x = GetWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWaitlistResult) ProtoMessage() {}

func (x *GetWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWaitlistResult.ProtoReflect.Descriptor instead.
func (*GetWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetWaitlistResult) GetWaitlist() *WaitlistView {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// list waitlist params
type ListWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business_id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service type 只能选 2 3
	ServiceType v11.ServiceItemType `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_type,omitempty"`
	// filter
	Filters *ListWaitlistParams_Filters `protobuf:"bytes,3,opt,name=filters,proto3,oneof" json:"filters,omitempty"`
	// order by
	OrderBys []*v2.OrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// waitlist order by price desc
	OrderPriceDesc *bool `protobuf:"varint,5,opt,name=order_price_desc,json=orderPriceDesc,proto3,oneof" json:"order_price_desc,omitempty"`
	// Pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,20,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWaitlistParams) Reset() {
	*x = ListWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWaitlistParams) ProtoMessage() {}

func (x *ListWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWaitlistParams.ProtoReflect.Descriptor instead.
func (*ListWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListWaitlistParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListWaitlistParams) GetServiceType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceItemType(0)
}

func (x *ListWaitlistParams) GetFilters() *ListWaitlistParams_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ListWaitlistParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ListWaitlistParams) GetOrderPriceDesc() bool {
	if x != nil && x.OrderPriceDesc != nil {
		return *x.OrderPriceDesc
	}
	return false
}

func (x *ListWaitlistParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list waitlist result
type ListWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist
	Waitlists []*WaitlistView `protobuf:"bytes,1,rep,name=waitlists,proto3" json:"waitlists,omitempty"`
	// Pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListWaitlistResult) Reset() {
	*x = ListWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWaitlistResult) ProtoMessage() {}

func (x *ListWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWaitlistResult.ProtoReflect.Descriptor instead.
func (*ListWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListWaitlistResult) GetWaitlists() []*WaitlistView {
	if x != nil {
		return x.Waitlists
	}
	return nil
}

func (x *ListWaitlistResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// waitlist view
type WaitlistView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist
	Waitlist *v1.BookingRequestModel `protobuf:"bytes,1,opt,name=waitlist,proto3" json:"waitlist,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// is expired
	IsExpired bool `protobuf:"varint,3,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	// client info
	Client *WaitlistView_CustomerInfo `protobuf:"bytes,4,opt,name=client,proto3" json:"client,omitempty"`
	// pet info
	Pets []*WaitlistView_PetInfo `protobuf:"bytes,5,rep,name=pets,proto3" json:"pets,omitempty"`
	// create by
	CreateBy *WaitlistView_CreateBy `protobuf:"bytes,6,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`
	// total price
	TotalPrice float64 `protobuf:"fixed64,7,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// membership
	MembershipSubscriptions *v12.MembershipSubscriptionListModel `protobuf:"bytes,8,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3,oneof" json:"membership_subscriptions,omitempty"`
}

func (x *WaitlistView) Reset() {
	*x = WaitlistView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistView) ProtoMessage() {}

func (x *WaitlistView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistView.ProtoReflect.Descriptor instead.
func (*WaitlistView) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{10}
}

func (x *WaitlistView) GetWaitlist() *v1.BookingRequestModel {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

func (x *WaitlistView) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *WaitlistView) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *WaitlistView) GetClient() *WaitlistView_CustomerInfo {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *WaitlistView) GetPets() []*WaitlistView_PetInfo {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *WaitlistView) GetCreateBy() *WaitlistView_CreateBy {
	if x != nil {
		return x.CreateBy
	}
	return nil
}

func (x *WaitlistView) GetTotalPrice() float64 {
	if x != nil {
		return x.TotalPrice
	}
	return 0
}

func (x *WaitlistView) GetMembershipSubscriptions() *v12.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// book waitlist params
type BookWaitlistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// waitlist id
	WaitlistId int64 `protobuf:"varint,1,opt,name=waitlist_id,json=waitlistId,proto3" json:"waitlist_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// appointment
	Appointment *v13.AppointmentCreateDef `protobuf:"bytes,3,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// 下面两个 def，是复用的 createAppointment
	// Pet to services mapping
	PetDetails []*v13.PetDetailDef `protobuf:"bytes,4,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Appointment notes, contains ticket comments and alert notes
	// Ticket comments: For this appointment. Private to business only
	// Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
	Notes []*v13.AppointmentNoteCreateDef `protobuf:"bytes,5,rep,name=notes,proto3" json:"notes,omitempty"`
}

func (x *BookWaitlistParams) Reset() {
	*x = BookWaitlistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookWaitlistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookWaitlistParams) ProtoMessage() {}

func (x *BookWaitlistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookWaitlistParams.ProtoReflect.Descriptor instead.
func (*BookWaitlistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{11}
}

func (x *BookWaitlistParams) GetWaitlistId() int64 {
	if x != nil {
		return x.WaitlistId
	}
	return 0
}

func (x *BookWaitlistParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BookWaitlistParams) GetAppointment() *v13.AppointmentCreateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *BookWaitlistParams) GetPetDetails() []*v13.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *BookWaitlistParams) GetNotes() []*v13.AppointmentNoteCreateDef {
	if x != nil {
		return x.Notes
	}
	return nil
}

// book waitlist result
type BookWaitlistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *BookWaitlistResult) Reset() {
	*x = BookWaitlistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookWaitlistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookWaitlistResult) ProtoMessage() {}

func (x *BookWaitlistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookWaitlistResult.ProtoReflect.Descriptor instead.
func (*BookWaitlistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{12}
}

func (x *BookWaitlistResult) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// filter
type ListWaitlistParams_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key word
	Keyword *string `protobuf:"bytes,1,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// created from ob
	IsFromOb *bool `protobuf:"varint,4,opt,name=is_from_ob,json=isFromOb,proto3,oneof" json:"is_from_ob,omitempty"`
	// is expired
	IsExpired *bool `protobuf:"varint,5,opt,name=is_expired,json=isExpired,proto3,oneof" json:"is_expired,omitempty"`
	// is available
	IsAvailable *bool `protobuf:"varint,6,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
}

func (x *ListWaitlistParams_Filters) Reset() {
	*x = ListWaitlistParams_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWaitlistParams_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWaitlistParams_Filters) ProtoMessage() {}

func (x *ListWaitlistParams_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWaitlistParams_Filters.ProtoReflect.Descriptor instead.
func (*ListWaitlistParams_Filters) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListWaitlistParams_Filters) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *ListWaitlistParams_Filters) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListWaitlistParams_Filters) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListWaitlistParams_Filters) GetIsFromOb() bool {
	if x != nil && x.IsFromOb != nil {
		return *x.IsFromOb
	}
	return false
}

func (x *ListWaitlistParams_Filters) GetIsExpired() bool {
	if x != nil && x.IsExpired != nil {
		return *x.IsExpired
	}
	return false
}

func (x *ListWaitlistParams_Filters) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

// create by
type WaitlistView_CreateBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lastName
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// firstName
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// avatarPath
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *WaitlistView_CreateBy) Reset() {
	*x = WaitlistView_CreateBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistView_CreateBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistView_CreateBy) ProtoMessage() {}

func (x *WaitlistView_CreateBy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistView_CreateBy.ProtoReflect.Descriptor instead.
func (*WaitlistView_CreateBy) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *WaitlistView_CreateBy) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WaitlistView_CreateBy) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *WaitlistView_CreateBy) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *WaitlistView_CreateBy) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

// client information
type WaitlistView_CustomerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// client's last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// client's first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// client label color
	ClientColor string `protobuf:"bytes,4,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// path to client's avatar image
	AvatarPath string `protobuf:"bytes,5,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// client's phone number
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,7,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
}

func (x *WaitlistView_CustomerInfo) Reset() {
	*x = WaitlistView_CustomerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistView_CustomerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistView_CustomerInfo) ProtoMessage() {}

func (x *WaitlistView_CustomerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistView_CustomerInfo.ProtoReflect.Descriptor instead.
func (*WaitlistView_CustomerInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{10, 1}
}

func (x *WaitlistView_CustomerInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WaitlistView_CustomerInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *WaitlistView_CustomerInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *WaitlistView_CustomerInfo) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *WaitlistView_CustomerInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *WaitlistView_CustomerInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *WaitlistView_CustomerInfo) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

// service information
type WaitlistView_ServiceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service ID
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,3,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *WaitlistView_ServiceInfo) Reset() {
	*x = WaitlistView_ServiceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistView_ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistView_ServiceInfo) ProtoMessage() {}

func (x *WaitlistView_ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistView_ServiceInfo.ProtoReflect.Descriptor instead.
func (*WaitlistView_ServiceInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{10, 2}
}

func (x *WaitlistView_ServiceInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *WaitlistView_ServiceInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *WaitlistView_ServiceInfo) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *WaitlistView_ServiceInfo) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// pet information
type WaitlistView_PetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// breed of the pet
	Breed string `protobuf:"bytes,3,opt,name=breed,proto3" json:"breed,omitempty"`
	// type ID of the pet (e.g., dog, cat)
	PetTypeId int32 `protobuf:"varint,4,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// list of services associated with the pet
	Services []*WaitlistView_ServiceInfo `protobuf:"bytes,5,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *WaitlistView_PetInfo) Reset() {
	*x = WaitlistView_PetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaitlistView_PetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaitlistView_PetInfo) ProtoMessage() {}

func (x *WaitlistView_PetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaitlistView_PetInfo.ProtoReflect.Descriptor instead.
func (*WaitlistView_PetInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP(), []int{10, 3}
}

func (x *WaitlistView_PetInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *WaitlistView_PetInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *WaitlistView_PetInfo) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *WaitlistView_PetInfo) GetPetTypeId() int32 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *WaitlistView_PetInfo) GetServices() []*WaitlistView_ServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_moego_api_online_booking_v1_waitlist_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_waitlist_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x4d, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64,
	0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x37,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x22, 0xc8, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x02, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x40, 0x0a, 0x14, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x22, 0x16, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x3d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45, 0x0a, 0x08, 0x77, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0xa9, 0x06, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x5a, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x82, 0x01, 0x06, 0x10, 0x01, 0x18, 0x02, 0x18, 0x03, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x2d, 0x0a, 0x10, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xe1, 0x02, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x08, 0x69, 0x73, 0x46, 0x72,
	0x6f, 0x6d, 0x4f, 0x62, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x09, 0x69,
	0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x05, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x69, 0x73, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x62, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69,
	0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x22, 0xa1, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x09, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xc8, 0x0a, 0x0a, 0x0c, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x4f, 0x0a, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x12, 0x4e, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x54, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74,
	0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x79, 0x48, 0x00, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x7b, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x01, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x77,
	0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x1a, 0xe0, 0x01, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x1a, 0xcb, 0x01, 0x0a, 0x0b, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xc4, 0x01, 0x0a, 0x07, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0b,
	0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x42, 0x1b, 0x0a,
	0x19, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x86, 0x03, 0x0a, 0x12, 0x42,
	0x6f, 0x6f, 0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64,
	0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6e, 0x6f,
	0x74, 0x65, 0x73, 0x22, 0x3b, 0x0a, 0x12, 0x42, 0x6f, 0x6f, 0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x32, 0xcc, 0x05, 0x0a, 0x0f, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x69,
	0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0c, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a,
	0x0c, 0x42, 0x6f, 0x6f, 0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42,
	0x8c, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_waitlist_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_waitlist_api_proto_rawDescData = file_moego_api_online_booking_v1_waitlist_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_waitlist_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_waitlist_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_waitlist_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_waitlist_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_waitlist_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_moego_api_online_booking_v1_waitlist_api_proto_goTypes = []interface{}{
	(*CreateWaitlistParams)(nil),                // 0: moego.api.online_booking.v1.CreateWaitlistParams
	(*CreateWaitlistResult)(nil),                // 1: moego.api.online_booking.v1.CreateWaitlistResult
	(*UpdateWaitlistParams)(nil),                // 2: moego.api.online_booking.v1.UpdateWaitlistParams
	(*UpdateWaitlistResult)(nil),                // 3: moego.api.online_booking.v1.UpdateWaitlistResult
	(*DeleteWaitlistParams)(nil),                // 4: moego.api.online_booking.v1.DeleteWaitlistParams
	(*DeleteWaitlistResult)(nil),                // 5: moego.api.online_booking.v1.DeleteWaitlistResult
	(*GetWaitlistParams)(nil),                   // 6: moego.api.online_booking.v1.GetWaitlistParams
	(*GetWaitlistResult)(nil),                   // 7: moego.api.online_booking.v1.GetWaitlistResult
	(*ListWaitlistParams)(nil),                  // 8: moego.api.online_booking.v1.ListWaitlistParams
	(*ListWaitlistResult)(nil),                  // 9: moego.api.online_booking.v1.ListWaitlistResult
	(*WaitlistView)(nil),                        // 10: moego.api.online_booking.v1.WaitlistView
	(*BookWaitlistParams)(nil),                  // 11: moego.api.online_booking.v1.BookWaitlistParams
	(*BookWaitlistResult)(nil),                  // 12: moego.api.online_booking.v1.BookWaitlistResult
	(*ListWaitlistParams_Filters)(nil),          // 13: moego.api.online_booking.v1.ListWaitlistParams.Filters
	(*WaitlistView_CreateBy)(nil),               // 14: moego.api.online_booking.v1.WaitlistView.CreateBy
	(*WaitlistView_CustomerInfo)(nil),           // 15: moego.api.online_booking.v1.WaitlistView.CustomerInfo
	(*WaitlistView_ServiceInfo)(nil),            // 16: moego.api.online_booking.v1.WaitlistView.ServiceInfo
	(*WaitlistView_PetInfo)(nil),                // 17: moego.api.online_booking.v1.WaitlistView.PetInfo
	(*v1.Service)(nil),                          // 18: moego.models.online_booking.v1.Service
	(v11.ServiceItemType)(0),                    // 19: moego.models.offering.v1.ServiceItemType
	(*v2.OrderBy)(nil),                          // 20: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),                // 21: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),               // 22: moego.utils.v2.PaginationResponse
	(*v1.BookingRequestModel)(nil),              // 23: moego.models.online_booking.v1.BookingRequestModel
	(*v12.MembershipSubscriptionListModel)(nil), // 24: moego.models.membership.v1.MembershipSubscriptionListModel
	(*v13.AppointmentCreateDef)(nil),            // 25: moego.models.appointment.v1.AppointmentCreateDef
	(*v13.PetDetailDef)(nil),                    // 26: moego.models.appointment.v1.PetDetailDef
	(*v13.AppointmentNoteCreateDef)(nil),        // 27: moego.models.appointment.v1.AppointmentNoteCreateDef
	(*date.Date)(nil),                           // 28: google.type.Date
}
var file_moego_api_online_booking_v1_waitlist_api_proto_depIdxs = []int32{
	18, // 0: moego.api.online_booking.v1.CreateWaitlistParams.services:type_name -> moego.models.online_booking.v1.Service
	18, // 1: moego.api.online_booking.v1.UpdateWaitlistParams.services:type_name -> moego.models.online_booking.v1.Service
	10, // 2: moego.api.online_booking.v1.GetWaitlistResult.waitlist:type_name -> moego.api.online_booking.v1.WaitlistView
	19, // 3: moego.api.online_booking.v1.ListWaitlistParams.service_type:type_name -> moego.models.offering.v1.ServiceItemType
	13, // 4: moego.api.online_booking.v1.ListWaitlistParams.filters:type_name -> moego.api.online_booking.v1.ListWaitlistParams.Filters
	20, // 5: moego.api.online_booking.v1.ListWaitlistParams.order_bys:type_name -> moego.utils.v2.OrderBy
	21, // 6: moego.api.online_booking.v1.ListWaitlistParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	10, // 7: moego.api.online_booking.v1.ListWaitlistResult.waitlists:type_name -> moego.api.online_booking.v1.WaitlistView
	22, // 8: moego.api.online_booking.v1.ListWaitlistResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	23, // 9: moego.api.online_booking.v1.WaitlistView.waitlist:type_name -> moego.models.online_booking.v1.BookingRequestModel
	15, // 10: moego.api.online_booking.v1.WaitlistView.client:type_name -> moego.api.online_booking.v1.WaitlistView.CustomerInfo
	17, // 11: moego.api.online_booking.v1.WaitlistView.pets:type_name -> moego.api.online_booking.v1.WaitlistView.PetInfo
	14, // 12: moego.api.online_booking.v1.WaitlistView.create_by:type_name -> moego.api.online_booking.v1.WaitlistView.CreateBy
	24, // 13: moego.api.online_booking.v1.WaitlistView.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	25, // 14: moego.api.online_booking.v1.BookWaitlistParams.appointment:type_name -> moego.models.appointment.v1.AppointmentCreateDef
	26, // 15: moego.api.online_booking.v1.BookWaitlistParams.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	27, // 16: moego.api.online_booking.v1.BookWaitlistParams.notes:type_name -> moego.models.appointment.v1.AppointmentNoteCreateDef
	28, // 17: moego.api.online_booking.v1.ListWaitlistParams.Filters.start_date:type_name -> google.type.Date
	28, // 18: moego.api.online_booking.v1.ListWaitlistParams.Filters.end_date:type_name -> google.type.Date
	19, // 19: moego.api.online_booking.v1.WaitlistView.ServiceInfo.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	16, // 20: moego.api.online_booking.v1.WaitlistView.PetInfo.services:type_name -> moego.api.online_booking.v1.WaitlistView.ServiceInfo
	0,  // 21: moego.api.online_booking.v1.WaitlistService.CreateWaitlist:input_type -> moego.api.online_booking.v1.CreateWaitlistParams
	2,  // 22: moego.api.online_booking.v1.WaitlistService.UpdateWaitlist:input_type -> moego.api.online_booking.v1.UpdateWaitlistParams
	4,  // 23: moego.api.online_booking.v1.WaitlistService.DeleteWaitlist:input_type -> moego.api.online_booking.v1.DeleteWaitlistParams
	6,  // 24: moego.api.online_booking.v1.WaitlistService.GetWaitlist:input_type -> moego.api.online_booking.v1.GetWaitlistParams
	8,  // 25: moego.api.online_booking.v1.WaitlistService.ListWaitlist:input_type -> moego.api.online_booking.v1.ListWaitlistParams
	11, // 26: moego.api.online_booking.v1.WaitlistService.BookWaitlist:input_type -> moego.api.online_booking.v1.BookWaitlistParams
	1,  // 27: moego.api.online_booking.v1.WaitlistService.CreateWaitlist:output_type -> moego.api.online_booking.v1.CreateWaitlistResult
	3,  // 28: moego.api.online_booking.v1.WaitlistService.UpdateWaitlist:output_type -> moego.api.online_booking.v1.UpdateWaitlistResult
	5,  // 29: moego.api.online_booking.v1.WaitlistService.DeleteWaitlist:output_type -> moego.api.online_booking.v1.DeleteWaitlistResult
	7,  // 30: moego.api.online_booking.v1.WaitlistService.GetWaitlist:output_type -> moego.api.online_booking.v1.GetWaitlistResult
	9,  // 31: moego.api.online_booking.v1.WaitlistService.ListWaitlist:output_type -> moego.api.online_booking.v1.ListWaitlistResult
	12, // 32: moego.api.online_booking.v1.WaitlistService.BookWaitlist:output_type -> moego.api.online_booking.v1.BookWaitlistResult
	27, // [27:33] is the sub-list for method output_type
	21, // [21:27] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_waitlist_api_proto_init() }
func file_moego_api_online_booking_v1_waitlist_api_proto_init() {
	if File_moego_api_online_booking_v1_waitlist_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookWaitlistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookWaitlistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWaitlistParams_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistView_CreateBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistView_CustomerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistView_ServiceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaitlistView_PetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_waitlist_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_waitlist_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_waitlist_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_waitlist_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_waitlist_api_proto = out.File
	file_moego_api_online_booking_v1_waitlist_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_waitlist_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_waitlist_api_proto_depIdxs = nil
}
