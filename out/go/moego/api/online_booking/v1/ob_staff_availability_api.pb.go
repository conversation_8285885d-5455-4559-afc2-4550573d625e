// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_staff_availability_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetStaffAvailabilityParams
type GetStaffAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id list, staff will init when staff_id no exist
	StaffIdList []int64 `protobuf:"varint,1,rep,packed,name=staff_id_list,json=staffIdList,proto3" json:"staff_id_list,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// availability type
	AvailabilityType *v1.AvailabilityType `protobuf:"varint,4,opt,name=availability_type,json=availabilityType,proto3,enum=moego.models.organization.v1.AvailabilityType,oneof" json:"availability_type,omitempty"`
}

func (x *GetStaffAvailabilityParams) Reset() {
	*x = GetStaffAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityParams) ProtoMessage() {}

func (x *GetStaffAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityParams.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetStaffAvailabilityParams) GetStaffIdList() []int64 {
	if x != nil {
		return x.StaffIdList
	}
	return nil
}

func (x *GetStaffAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffAvailabilityParams) GetAvailabilityType() v1.AvailabilityType {
	if x != nil && x.AvailabilityType != nil {
		return *x.AvailabilityType
	}
	return v1.AvailabilityType(0)
}

// StaffAvailabilityResult
type GetStaffAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffAvailabilityList []*v1.StaffAvailability `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
	// staff availability info
	StaffList []*StaffAvailabilityInfo `protobuf:"bytes,2,rep,name=staff_list,json=staffList,proto3" json:"staff_list,omitempty"`
}

func (x *GetStaffAvailabilityResult) Reset() {
	*x = GetStaffAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffAvailabilityResult) ProtoMessage() {}

func (x *GetStaffAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffAvailabilityResult.ProtoReflect.Descriptor instead.
func (*GetStaffAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetStaffAvailabilityResult) GetStaffAvailabilityList() []*v1.StaffAvailability {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

func (x *GetStaffAvailabilityResult) GetStaffList() []*StaffAvailabilityInfo {
	if x != nil {
		return x.StaffList
	}
	return nil
}

// StaffAvailabilityInfo
type StaffAvailabilityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// schedule type
	ScheduleType v1.ScheduleType `protobuf:"varint,4,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// weeks availability, key: "firstWeek", "secondWeek", "thirdWeek", "forthWeek"
	Weeks map[string]*WeekAvailability `protobuf:"bytes,5,rep,name=weeks,proto3" json:"weeks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StaffAvailabilityInfo) Reset() {
	*x = StaffAvailabilityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAvailabilityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAvailabilityInfo) ProtoMessage() {}

func (x *StaffAvailabilityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAvailabilityInfo.ProtoReflect.Descriptor instead.
func (*StaffAvailabilityInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{2}
}

func (x *StaffAvailabilityInfo) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffAvailabilityInfo) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *StaffAvailabilityInfo) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *StaffAvailabilityInfo) GetScheduleType() v1.ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return v1.ScheduleType(0)
}

func (x *StaffAvailabilityInfo) GetWeeks() map[string]*WeekAvailability {
	if x != nil {
		return x.Weeks
	}
	return nil
}

// WeekAvailability
type WeekAvailability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// by slot, key: "sunday", "monday", ..., "saturday"
	Days map[string]*v1.SlotAvailabilityDay `protobuf:"bytes,1,rep,name=days,proto3" json:"days,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// by time, key: "sunday", "monday", ..., "saturday"
	ByTimeDays map[string]*v1.TimeAvailabilityDayDef `protobuf:"bytes,2,rep,name=by_time_days,json=byTimeDays,proto3" json:"by_time_days,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WeekAvailability) Reset() {
	*x = WeekAvailability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeekAvailability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekAvailability) ProtoMessage() {}

func (x *WeekAvailability) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekAvailability.ProtoReflect.Descriptor instead.
func (*WeekAvailability) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{3}
}

func (x *WeekAvailability) GetDays() map[string]*v1.SlotAvailabilityDay {
	if x != nil {
		return x.Days
	}
	return nil
}

func (x *WeekAvailability) GetByTimeDays() map[string]*v1.TimeAvailabilityDayDef {
	if x != nil {
		return x.ByTimeDays
	}
	return nil
}

// UpdateStaffAvailabilityParams
type UpdateStaffAvailabilityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff available list
	StaffAvailabilityList []*v1.StaffAvailabilityDef `protobuf:"bytes,1,rep,name=staff_availability_list,json=staffAvailabilityList,proto3" json:"staff_availability_list,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *UpdateStaffAvailabilityParams) Reset() {
	*x = UpdateStaffAvailabilityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityParams) ProtoMessage() {}

func (x *UpdateStaffAvailabilityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityParams.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateStaffAvailabilityParams) GetStaffAvailabilityList() []*v1.StaffAvailabilityDef {
	if x != nil {
		return x.StaffAvailabilityList
	}
	return nil
}

func (x *UpdateStaffAvailabilityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// UpdateStaffAvailabilityResult
type UpdateStaffAvailabilityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStaffAvailabilityResult) Reset() {
	*x = UpdateStaffAvailabilityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffAvailabilityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffAvailabilityResult) ProtoMessage() {}

func (x *UpdateStaffAvailabilityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffAvailabilityResult.ProtoReflect.Descriptor instead.
func (*UpdateStaffAvailabilityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP(), []int{5}
}

var File_moego_api_online_booking_v1_ob_staff_availability_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x39, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x6c, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x51, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x83, 0x03, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x05, 0x77, 0x65,
	0x65, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x57, 0x65,
	0x65, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x1a,
	0x67, 0x0a, 0x0a, 0x57, 0x65, 0x65, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x65,
	0x6b, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa1, 0x03, 0x0a, 0x10, 0x57, 0x65, 0x65,
	0x6b, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x4b, 0x0a,
	0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x61, 0x79, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x12, 0x5f, 0x0a, 0x0c, 0x62, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x65, 0x65, 0x6b, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e,
	0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x62, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x79, 0x73, 0x1a, 0x6a, 0x0a, 0x09, 0x44,
	0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x73, 0x0a, 0x0f, 0x42, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x61, 0x79, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4a, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x44, 0x65,
	0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbf, 0x01, 0x0a,
	0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x74,
	0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x15, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x1f,
	0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32,
	0xbb, 0x02, 0x0a, 0x1a, 0x4f, 0x42, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x17, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8c, 0x01,
	0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_api_online_booking_v1_ob_staff_availability_api_proto_goTypes = []interface{}{
	(*GetStaffAvailabilityParams)(nil),    // 0: moego.api.online_booking.v1.GetStaffAvailabilityParams
	(*GetStaffAvailabilityResult)(nil),    // 1: moego.api.online_booking.v1.GetStaffAvailabilityResult
	(*StaffAvailabilityInfo)(nil),         // 2: moego.api.online_booking.v1.StaffAvailabilityInfo
	(*WeekAvailability)(nil),              // 3: moego.api.online_booking.v1.WeekAvailability
	(*UpdateStaffAvailabilityParams)(nil), // 4: moego.api.online_booking.v1.UpdateStaffAvailabilityParams
	(*UpdateStaffAvailabilityResult)(nil), // 5: moego.api.online_booking.v1.UpdateStaffAvailabilityResult
	nil,                                   // 6: moego.api.online_booking.v1.StaffAvailabilityInfo.WeeksEntry
	nil,                                   // 7: moego.api.online_booking.v1.WeekAvailability.DaysEntry
	nil,                                   // 8: moego.api.online_booking.v1.WeekAvailability.ByTimeDaysEntry
	(v1.AvailabilityType)(0),              // 9: moego.models.organization.v1.AvailabilityType
	(*v1.StaffAvailability)(nil),          // 10: moego.models.organization.v1.StaffAvailability
	(v1.ScheduleType)(0),                  // 11: moego.models.organization.v1.ScheduleType
	(*v1.StaffAvailabilityDef)(nil),       // 12: moego.models.organization.v1.StaffAvailabilityDef
	(*v1.SlotAvailabilityDay)(nil),        // 13: moego.models.organization.v1.SlotAvailabilityDay
	(*v1.TimeAvailabilityDayDef)(nil),     // 14: moego.models.organization.v1.TimeAvailabilityDayDef
}
var file_moego_api_online_booking_v1_ob_staff_availability_api_proto_depIdxs = []int32{
	9,  // 0: moego.api.online_booking.v1.GetStaffAvailabilityParams.availability_type:type_name -> moego.models.organization.v1.AvailabilityType
	10, // 1: moego.api.online_booking.v1.GetStaffAvailabilityResult.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailability
	2,  // 2: moego.api.online_booking.v1.GetStaffAvailabilityResult.staff_list:type_name -> moego.api.online_booking.v1.StaffAvailabilityInfo
	11, // 3: moego.api.online_booking.v1.StaffAvailabilityInfo.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	6,  // 4: moego.api.online_booking.v1.StaffAvailabilityInfo.weeks:type_name -> moego.api.online_booking.v1.StaffAvailabilityInfo.WeeksEntry
	7,  // 5: moego.api.online_booking.v1.WeekAvailability.days:type_name -> moego.api.online_booking.v1.WeekAvailability.DaysEntry
	8,  // 6: moego.api.online_booking.v1.WeekAvailability.by_time_days:type_name -> moego.api.online_booking.v1.WeekAvailability.ByTimeDaysEntry
	12, // 7: moego.api.online_booking.v1.UpdateStaffAvailabilityParams.staff_availability_list:type_name -> moego.models.organization.v1.StaffAvailabilityDef
	3,  // 8: moego.api.online_booking.v1.StaffAvailabilityInfo.WeeksEntry.value:type_name -> moego.api.online_booking.v1.WeekAvailability
	13, // 9: moego.api.online_booking.v1.WeekAvailability.DaysEntry.value:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	14, // 10: moego.api.online_booking.v1.WeekAvailability.ByTimeDaysEntry.value:type_name -> moego.models.organization.v1.TimeAvailabilityDayDef
	0,  // 11: moego.api.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailability:input_type -> moego.api.online_booking.v1.GetStaffAvailabilityParams
	4,  // 12: moego.api.online_booking.v1.OBStaffAvailabilityService.UpdateStaffAvailability:input_type -> moego.api.online_booking.v1.UpdateStaffAvailabilityParams
	1,  // 13: moego.api.online_booking.v1.OBStaffAvailabilityService.GetStaffAvailability:output_type -> moego.api.online_booking.v1.GetStaffAvailabilityResult
	5,  // 14: moego.api.online_booking.v1.OBStaffAvailabilityService.UpdateStaffAvailability:output_type -> moego.api.online_booking.v1.UpdateStaffAvailabilityResult
	13, // [13:15] is the sub-list for method output_type
	11, // [11:13] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_staff_availability_api_proto_init() }
func file_moego_api_online_booking_v1_ob_staff_availability_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_staff_availability_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAvailabilityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeekAvailability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffAvailabilityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_staff_availability_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_staff_availability_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_staff_availability_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_staff_availability_api_proto = out.File
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_staff_availability_api_proto_depIdxs = nil
}
