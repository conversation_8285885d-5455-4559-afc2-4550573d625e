// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/order/v1/service_charge_company_api.proto

package orderapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServiceChargeCompanyServiceClient is the client API for ServiceChargeCompanyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceChargeCompanyServiceClient interface {
	// get service charge list
	GetCompanyServiceChargeList(ctx context.Context, in *GetCompanyServiceChargeListParams, opts ...grpc.CallOption) (*GetCompanyServiceChargeListResult, error)
	// add service charge
	AddCompanyServiceCharge(ctx context.Context, in *AddCompanyServiceChargeParams, opts ...grpc.CallOption) (*v1.ServiceCharge, error)
	// update service charge
	UpdateCompanyServiceCharge(ctx context.Context, in *UpdateCompanyServiceChargeParams, opts ...grpc.CallOption) (*v1.ServiceCharge, error)
	// list associated food source
	ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceParams, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResult, error)
}

type serviceChargeCompanyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceChargeCompanyServiceClient(cc grpc.ClientConnInterface) ServiceChargeCompanyServiceClient {
	return &serviceChargeCompanyServiceClient{cc}
}

func (c *serviceChargeCompanyServiceClient) GetCompanyServiceChargeList(ctx context.Context, in *GetCompanyServiceChargeListParams, opts ...grpc.CallOption) (*GetCompanyServiceChargeListResult, error) {
	out := new(GetCompanyServiceChargeListResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.ServiceChargeCompanyService/GetCompanyServiceChargeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeCompanyServiceClient) AddCompanyServiceCharge(ctx context.Context, in *AddCompanyServiceChargeParams, opts ...grpc.CallOption) (*v1.ServiceCharge, error) {
	out := new(v1.ServiceCharge)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.ServiceChargeCompanyService/AddCompanyServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeCompanyServiceClient) UpdateCompanyServiceCharge(ctx context.Context, in *UpdateCompanyServiceChargeParams, opts ...grpc.CallOption) (*v1.ServiceCharge, error) {
	out := new(v1.ServiceCharge)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.ServiceChargeCompanyService/UpdateCompanyServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceChargeCompanyServiceClient) ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceParams, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResult, error) {
	out := new(ListSurchargeAssociatedFoodSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.ServiceChargeCompanyService/ListSurchargeAssociatedFoodSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceChargeCompanyServiceServer is the server API for ServiceChargeCompanyService service.
// All implementations must embed UnimplementedServiceChargeCompanyServiceServer
// for forward compatibility
type ServiceChargeCompanyServiceServer interface {
	// get service charge list
	GetCompanyServiceChargeList(context.Context, *GetCompanyServiceChargeListParams) (*GetCompanyServiceChargeListResult, error)
	// add service charge
	AddCompanyServiceCharge(context.Context, *AddCompanyServiceChargeParams) (*v1.ServiceCharge, error)
	// update service charge
	UpdateCompanyServiceCharge(context.Context, *UpdateCompanyServiceChargeParams) (*v1.ServiceCharge, error)
	// list associated food source
	ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceParams) (*ListSurchargeAssociatedFoodSourceResult, error)
	mustEmbedUnimplementedServiceChargeCompanyServiceServer()
}

// UnimplementedServiceChargeCompanyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceChargeCompanyServiceServer struct {
}

func (UnimplementedServiceChargeCompanyServiceServer) GetCompanyServiceChargeList(context.Context, *GetCompanyServiceChargeListParams) (*GetCompanyServiceChargeListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyServiceChargeList not implemented")
}
func (UnimplementedServiceChargeCompanyServiceServer) AddCompanyServiceCharge(context.Context, *AddCompanyServiceChargeParams) (*v1.ServiceCharge, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCompanyServiceCharge not implemented")
}
func (UnimplementedServiceChargeCompanyServiceServer) UpdateCompanyServiceCharge(context.Context, *UpdateCompanyServiceChargeParams) (*v1.ServiceCharge, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompanyServiceCharge not implemented")
}
func (UnimplementedServiceChargeCompanyServiceServer) ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceParams) (*ListSurchargeAssociatedFoodSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSurchargeAssociatedFoodSource not implemented")
}
func (UnimplementedServiceChargeCompanyServiceServer) mustEmbedUnimplementedServiceChargeCompanyServiceServer() {
}

// UnsafeServiceChargeCompanyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceChargeCompanyServiceServer will
// result in compilation errors.
type UnsafeServiceChargeCompanyServiceServer interface {
	mustEmbedUnimplementedServiceChargeCompanyServiceServer()
}

func RegisterServiceChargeCompanyServiceServer(s grpc.ServiceRegistrar, srv ServiceChargeCompanyServiceServer) {
	s.RegisterService(&ServiceChargeCompanyService_ServiceDesc, srv)
}

func _ServiceChargeCompanyService_GetCompanyServiceChargeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyServiceChargeListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeCompanyServiceServer).GetCompanyServiceChargeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.ServiceChargeCompanyService/GetCompanyServiceChargeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeCompanyServiceServer).GetCompanyServiceChargeList(ctx, req.(*GetCompanyServiceChargeListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeCompanyService_AddCompanyServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCompanyServiceChargeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeCompanyServiceServer).AddCompanyServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.ServiceChargeCompanyService/AddCompanyServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeCompanyServiceServer).AddCompanyServiceCharge(ctx, req.(*AddCompanyServiceChargeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeCompanyService_UpdateCompanyServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyServiceChargeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeCompanyServiceServer).UpdateCompanyServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.ServiceChargeCompanyService/UpdateCompanyServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeCompanyServiceServer).UpdateCompanyServiceCharge(ctx, req.(*UpdateCompanyServiceChargeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceChargeCompanyService_ListSurchargeAssociatedFoodSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSurchargeAssociatedFoodSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceChargeCompanyServiceServer).ListSurchargeAssociatedFoodSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.ServiceChargeCompanyService/ListSurchargeAssociatedFoodSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceChargeCompanyServiceServer).ListSurchargeAssociatedFoodSource(ctx, req.(*ListSurchargeAssociatedFoodSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceChargeCompanyService_ServiceDesc is the grpc.ServiceDesc for ServiceChargeCompanyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceChargeCompanyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.order.v1.ServiceChargeCompanyService",
	HandlerType: (*ServiceChargeCompanyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCompanyServiceChargeList",
			Handler:    _ServiceChargeCompanyService_GetCompanyServiceChargeList_Handler,
		},
		{
			MethodName: "AddCompanyServiceCharge",
			Handler:    _ServiceChargeCompanyService_AddCompanyServiceCharge_Handler,
		},
		{
			MethodName: "UpdateCompanyServiceCharge",
			Handler:    _ServiceChargeCompanyService_UpdateCompanyServiceCharge_Handler,
		},
		{
			MethodName: "ListSurchargeAssociatedFoodSource",
			Handler:    _ServiceChargeCompanyService_ListSurchargeAssociatedFoodSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/order/v1/service_charge_company_api.proto",
}
