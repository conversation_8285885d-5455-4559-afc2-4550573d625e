// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/organization/v1/camera_api.proto

package organizationapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get camera list params
type GetCameraListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	//
	// Types that are assignable to Filter:
	//
	//	*GetCameraListParams_IdogcamFilter
	//	*GetCameraListParams_AbckamFilter
	//	*GetCameraListParams_CameraFilter
	Filter isGetCameraListParams_Filter `protobuf_oneof:"filter"`
}

func (x *GetCameraListParams) Reset() {
	*x = GetCameraListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraListParams) ProtoMessage() {}

func (x *GetCameraListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraListParams.ProtoReflect.Descriptor instead.
func (*GetCameraListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_camera_api_proto_rawDescGZIP(), []int{0}
}

func (m *GetCameraListParams) GetFilter() isGetCameraListParams_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *GetCameraListParams) GetIdogcamFilter() *v1.IdogcamFilter {
	if x, ok := x.GetFilter().(*GetCameraListParams_IdogcamFilter); ok {
		return x.IdogcamFilter
	}
	return nil
}

func (x *GetCameraListParams) GetAbckamFilter() *v1.AbckamFilter {
	if x, ok := x.GetFilter().(*GetCameraListParams_AbckamFilter); ok {
		return x.AbckamFilter
	}
	return nil
}

func (x *GetCameraListParams) GetCameraFilter() *v1.CameraFilter {
	if x, ok := x.GetFilter().(*GetCameraListParams_CameraFilter); ok {
		return x.CameraFilter
	}
	return nil
}

type isGetCameraListParams_Filter interface {
	isGetCameraListParams_Filter()
}

type GetCameraListParams_IdogcamFilter struct {
	// iDogCamFilter
	IdogcamFilter *v1.IdogcamFilter `protobuf:"bytes,1,opt,name=idogcam_filter,json=idogcamFilter,proto3,oneof"`
}

type GetCameraListParams_AbckamFilter struct {
	// abcKamFilter
	AbckamFilter *v1.AbckamFilter `protobuf:"bytes,2,opt,name=abckam_filter,json=abckamFilter,proto3,oneof"`
}

type GetCameraListParams_CameraFilter struct {
	// cameraFilter
	CameraFilter *v1.CameraFilter `protobuf:"bytes,3,opt,name=camera_filter,json=cameraFilter,proto3,oneof"`
}

func (*GetCameraListParams_IdogcamFilter) isGetCameraListParams_Filter() {}

func (*GetCameraListParams_AbckamFilter) isGetCameraListParams_Filter() {}

func (*GetCameraListParams_CameraFilter) isGetCameraListParams_Filter() {}

// get camera list result
type GetCameraListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// camera list
	Cameras []*GetCameraListResult_CameraView `protobuf:"bytes,1,rep,name=cameras,proto3" json:"cameras,omitempty"`
}

func (x *GetCameraListResult) Reset() {
	*x = GetCameraListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraListResult) ProtoMessage() {}

func (x *GetCameraListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraListResult.ProtoReflect.Descriptor instead.
func (*GetCameraListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_camera_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetCameraListResult) GetCameras() []*GetCameraListResult_CameraView {
	if x != nil {
		return x.Cameras
	}
	return nil
}

// update camera param
type UpdateCameraParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// camera id
	CameraId int64 `protobuf:"varint,1,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// Is Active
	IsActive *bool `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	// Visibility Type
	VisibilityType *v1.VisibilityType `protobuf:"varint,11,opt,name=visibility_type,json=visibilityType,proto3,enum=moego.models.organization.v1.VisibilityType,oneof" json:"visibility_type,omitempty"`
	// id of the lodging unit
	LodgingUnitId *int64 `protobuf:"varint,14,opt,name=lodging_unit_id,json=lodgingUnitId,proto3,oneof" json:"lodging_unit_id,omitempty"`
}

func (x *UpdateCameraParams) Reset() {
	*x = UpdateCameraParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCameraParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCameraParams) ProtoMessage() {}

func (x *UpdateCameraParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCameraParams.ProtoReflect.Descriptor instead.
func (*UpdateCameraParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_camera_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateCameraParams) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *UpdateCameraParams) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *UpdateCameraParams) GetVisibilityType() v1.VisibilityType {
	if x != nil && x.VisibilityType != nil {
		return *x.VisibilityType
	}
	return v1.VisibilityType(0)
}

func (x *UpdateCameraParams) GetLodgingUnitId() int64 {
	if x != nil && x.LodgingUnitId != nil {
		return *x.LodgingUnitId
	}
	return 0
}

// update camera result
type UpdateCameraResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCameraResult) Reset() {
	*x = UpdateCameraResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCameraResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCameraResult) ProtoMessage() {}

func (x *UpdateCameraResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCameraResult.ProtoReflect.Descriptor instead.
func (*UpdateCameraResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_camera_api_proto_rawDescGZIP(), []int{3}
}

// camera view
type GetCameraListResult_CameraView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// camera info
	Camera *v1.CameraModel `protobuf:"bytes,1,opt,name=camera,proto3" json:"camera,omitempty"`
	// id of the lodging unit
	LodgingUnit *v11.LodgingUnitView `protobuf:"bytes,2,opt,name=lodging_unit,json=lodgingUnit,proto3,oneof" json:"lodging_unit,omitempty"`
}

func (x *GetCameraListResult_CameraView) Reset() {
	*x = GetCameraListResult_CameraView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCameraListResult_CameraView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCameraListResult_CameraView) ProtoMessage() {}

func (x *GetCameraListResult_CameraView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_camera_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCameraListResult_CameraView.ProtoReflect.Descriptor instead.
func (*GetCameraListResult_CameraView) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_camera_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetCameraListResult_CameraView) GetCamera() *v1.CameraModel {
	if x != nil {
		return x.Camera
	}
	return nil
}

func (x *GetCameraListResult_CameraView) GetLodgingUnit() *v11.LodgingUnitView {
	if x != nil {
		return x.LodgingUnit
	}
	return nil
}

var File_moego_api_organization_v1_camera_api_proto protoreflect.FileDescriptor

var file_moego_api_organization_v1_camera_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x54, 0x0a, 0x0e, 0x69, 0x64, 0x6f, 0x67, 0x63, 0x61, 0x6d, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x6f, 0x67, 0x63, 0x61, 0x6d, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x64, 0x6f, 0x67, 0x63, 0x61, 0x6d, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0d, 0x61, 0x62, 0x63, 0x6b, 0x61, 0x6d, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x62, 0x63, 0x6b,
	0x61, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x62, 0x63, 0x6b,
	0x61, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xa0, 0x02, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x53, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x1a, 0xb3, 0x01, 0x0a, 0x0a, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x56, 0x69, 0x65, 0x77, 0x12, 0x41, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x12, 0x51, 0x0a, 0x0c, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0b, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x9b, 0x02,
	0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x0f,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x02, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x32, 0xee, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x6c, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_organization_v1_camera_api_proto_rawDescOnce sync.Once
	file_moego_api_organization_v1_camera_api_proto_rawDescData = file_moego_api_organization_v1_camera_api_proto_rawDesc
)

func file_moego_api_organization_v1_camera_api_proto_rawDescGZIP() []byte {
	file_moego_api_organization_v1_camera_api_proto_rawDescOnce.Do(func() {
		file_moego_api_organization_v1_camera_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_organization_v1_camera_api_proto_rawDescData)
	})
	return file_moego_api_organization_v1_camera_api_proto_rawDescData
}

var file_moego_api_organization_v1_camera_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_api_organization_v1_camera_api_proto_goTypes = []interface{}{
	(*GetCameraListParams)(nil),            // 0: moego.api.organization.v1.GetCameraListParams
	(*GetCameraListResult)(nil),            // 1: moego.api.organization.v1.GetCameraListResult
	(*UpdateCameraParams)(nil),             // 2: moego.api.organization.v1.UpdateCameraParams
	(*UpdateCameraResult)(nil),             // 3: moego.api.organization.v1.UpdateCameraResult
	(*GetCameraListResult_CameraView)(nil), // 4: moego.api.organization.v1.GetCameraListResult.CameraView
	(*v1.IdogcamFilter)(nil),               // 5: moego.models.organization.v1.IdogcamFilter
	(*v1.AbckamFilter)(nil),                // 6: moego.models.organization.v1.AbckamFilter
	(*v1.CameraFilter)(nil),                // 7: moego.models.organization.v1.CameraFilter
	(v1.VisibilityType)(0),                 // 8: moego.models.organization.v1.VisibilityType
	(*v1.CameraModel)(nil),                 // 9: moego.models.organization.v1.CameraModel
	(*v11.LodgingUnitView)(nil),            // 10: moego.models.offering.v1.LodgingUnitView
}
var file_moego_api_organization_v1_camera_api_proto_depIdxs = []int32{
	5,  // 0: moego.api.organization.v1.GetCameraListParams.idogcam_filter:type_name -> moego.models.organization.v1.IdogcamFilter
	6,  // 1: moego.api.organization.v1.GetCameraListParams.abckam_filter:type_name -> moego.models.organization.v1.AbckamFilter
	7,  // 2: moego.api.organization.v1.GetCameraListParams.camera_filter:type_name -> moego.models.organization.v1.CameraFilter
	4,  // 3: moego.api.organization.v1.GetCameraListResult.cameras:type_name -> moego.api.organization.v1.GetCameraListResult.CameraView
	8,  // 4: moego.api.organization.v1.UpdateCameraParams.visibility_type:type_name -> moego.models.organization.v1.VisibilityType
	9,  // 5: moego.api.organization.v1.GetCameraListResult.CameraView.camera:type_name -> moego.models.organization.v1.CameraModel
	10, // 6: moego.api.organization.v1.GetCameraListResult.CameraView.lodging_unit:type_name -> moego.models.offering.v1.LodgingUnitView
	0,  // 7: moego.api.organization.v1.CameraService.GetCameraList:input_type -> moego.api.organization.v1.GetCameraListParams
	2,  // 8: moego.api.organization.v1.CameraService.UpdateCamera:input_type -> moego.api.organization.v1.UpdateCameraParams
	1,  // 9: moego.api.organization.v1.CameraService.GetCameraList:output_type -> moego.api.organization.v1.GetCameraListResult
	3,  // 10: moego.api.organization.v1.CameraService.UpdateCamera:output_type -> moego.api.organization.v1.UpdateCameraResult
	9,  // [9:11] is the sub-list for method output_type
	7,  // [7:9] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_organization_v1_camera_api_proto_init() }
func file_moego_api_organization_v1_camera_api_proto_init() {
	if File_moego_api_organization_v1_camera_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_organization_v1_camera_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_camera_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_camera_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCameraParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_camera_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCameraResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_camera_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCameraListResult_CameraView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_organization_v1_camera_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetCameraListParams_IdogcamFilter)(nil),
		(*GetCameraListParams_AbckamFilter)(nil),
		(*GetCameraListParams_CameraFilter)(nil),
	}
	file_moego_api_organization_v1_camera_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_camera_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_organization_v1_camera_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_organization_v1_camera_api_proto_goTypes,
		DependencyIndexes: file_moego_api_organization_v1_camera_api_proto_depIdxs,
		MessageInfos:      file_moego_api_organization_v1_camera_api_proto_msgTypes,
	}.Build()
	File_moego_api_organization_v1_camera_api_proto = out.File
	file_moego_api_organization_v1_camera_api_proto_rawDesc = nil
	file_moego_api_organization_v1_camera_api_proto_goTypes = nil
	file_moego_api_organization_v1_camera_api_proto_depIdxs = nil
}
