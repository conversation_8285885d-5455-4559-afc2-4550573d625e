// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/appointment_task_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentTaskServiceClient is the client API for AppointmentTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentTaskServiceClient interface {
	// List appointment task filters
	ListAppointmentTaskFilters(ctx context.Context, in *ListAppointmentTaskFiltersParams, opts ...grpc.CallOption) (*ListAppointmentTaskFiltersResult, error)
	// List appointment tasks by multiple group
	ListAppointmentTasksMultiGroup(ctx context.Context, in *ListAppointmentTasksParams, opts ...grpc.CallOption) (*ListAppointmentTasksMultiGroupResult, error)
	// List appointment task count by category
	ListAppointmentTaskCountByCategory(ctx context.Context, in *ListAppointmentTaskCountByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTaskCountByCategoryResult, error)
	// List appointment tasks by category
	ListAppointmentTasksByCategory(ctx context.Context, in *ListAppointmentTasksByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTasksByCategoryResult, error)
	// List owner appointment tasks
	ListOwnerAppointmentTasks(ctx context.Context, in *ListAppointmentTasksByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTasksByCategoryResult, error)
	// Update appointment task, e.g. assign staff, change status
	UpdateAppointmentTask(ctx context.Context, in *UpdateAppointmentTaskParams, opts ...grpc.CallOption) (*UpdateAppointmentTaskResult, error)
	// Batch update appointment task, e.g. assign staff, change status
	BatchUpdateAppointmentTask(ctx context.Context, in *BatchUpdateAppointmentTaskParams, opts ...grpc.CallOption) (*BatchUpdateAppointmentTaskResult, error)
	// Remove task staff
	RemoveTaskStaff(ctx context.Context, in *RemoveTaskStaffParams, opts ...grpc.CallOption) (*RemoveTaskStaffResult, error)
	// Remove task note
	RemoveTaskNote(ctx context.Context, in *RemoveTaskNoteParams, opts ...grpc.CallOption) (*RemoveTaskNoteResult, error)
	// Search for a pet with a task for the day
	SearchPetWithTaskDate(ctx context.Context, in *SearchPetWithTaskDateParams, opts ...grpc.CallOption) (*SearchPetWithTaskDateResult, error)
}

type appointmentTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentTaskServiceClient(cc grpc.ClientConnInterface) AppointmentTaskServiceClient {
	return &appointmentTaskServiceClient{cc}
}

func (c *appointmentTaskServiceClient) ListAppointmentTaskFilters(ctx context.Context, in *ListAppointmentTaskFiltersParams, opts ...grpc.CallOption) (*ListAppointmentTaskFiltersResult, error) {
	out := new(ListAppointmentTaskFiltersResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTaskFilters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListAppointmentTasksMultiGroup(ctx context.Context, in *ListAppointmentTasksParams, opts ...grpc.CallOption) (*ListAppointmentTasksMultiGroupResult, error) {
	out := new(ListAppointmentTasksMultiGroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTasksMultiGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListAppointmentTaskCountByCategory(ctx context.Context, in *ListAppointmentTaskCountByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTaskCountByCategoryResult, error) {
	out := new(ListAppointmentTaskCountByCategoryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTaskCountByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListAppointmentTasksByCategory(ctx context.Context, in *ListAppointmentTasksByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTasksByCategoryResult, error) {
	out := new(ListAppointmentTasksByCategoryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTasksByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) ListOwnerAppointmentTasks(ctx context.Context, in *ListAppointmentTasksByCategoryParams, opts ...grpc.CallOption) (*ListAppointmentTasksByCategoryResult, error) {
	out := new(ListAppointmentTasksByCategoryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/ListOwnerAppointmentTasks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) UpdateAppointmentTask(ctx context.Context, in *UpdateAppointmentTaskParams, opts ...grpc.CallOption) (*UpdateAppointmentTaskResult, error) {
	out := new(UpdateAppointmentTaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/UpdateAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) BatchUpdateAppointmentTask(ctx context.Context, in *BatchUpdateAppointmentTaskParams, opts ...grpc.CallOption) (*BatchUpdateAppointmentTaskResult, error) {
	out := new(BatchUpdateAppointmentTaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/BatchUpdateAppointmentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) RemoveTaskStaff(ctx context.Context, in *RemoveTaskStaffParams, opts ...grpc.CallOption) (*RemoveTaskStaffResult, error) {
	out := new(RemoveTaskStaffResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/RemoveTaskStaff", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) RemoveTaskNote(ctx context.Context, in *RemoveTaskNoteParams, opts ...grpc.CallOption) (*RemoveTaskNoteResult, error) {
	out := new(RemoveTaskNoteResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/RemoveTaskNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTaskServiceClient) SearchPetWithTaskDate(ctx context.Context, in *SearchPetWithTaskDateParams, opts ...grpc.CallOption) (*SearchPetWithTaskDateResult, error) {
	out := new(SearchPetWithTaskDateResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTaskService/SearchPetWithTaskDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentTaskServiceServer is the server API for AppointmentTaskService service.
// All implementations must embed UnimplementedAppointmentTaskServiceServer
// for forward compatibility
type AppointmentTaskServiceServer interface {
	// List appointment task filters
	ListAppointmentTaskFilters(context.Context, *ListAppointmentTaskFiltersParams) (*ListAppointmentTaskFiltersResult, error)
	// List appointment tasks by multiple group
	ListAppointmentTasksMultiGroup(context.Context, *ListAppointmentTasksParams) (*ListAppointmentTasksMultiGroupResult, error)
	// List appointment task count by category
	ListAppointmentTaskCountByCategory(context.Context, *ListAppointmentTaskCountByCategoryParams) (*ListAppointmentTaskCountByCategoryResult, error)
	// List appointment tasks by category
	ListAppointmentTasksByCategory(context.Context, *ListAppointmentTasksByCategoryParams) (*ListAppointmentTasksByCategoryResult, error)
	// List owner appointment tasks
	ListOwnerAppointmentTasks(context.Context, *ListAppointmentTasksByCategoryParams) (*ListAppointmentTasksByCategoryResult, error)
	// Update appointment task, e.g. assign staff, change status
	UpdateAppointmentTask(context.Context, *UpdateAppointmentTaskParams) (*UpdateAppointmentTaskResult, error)
	// Batch update appointment task, e.g. assign staff, change status
	BatchUpdateAppointmentTask(context.Context, *BatchUpdateAppointmentTaskParams) (*BatchUpdateAppointmentTaskResult, error)
	// Remove task staff
	RemoveTaskStaff(context.Context, *RemoveTaskStaffParams) (*RemoveTaskStaffResult, error)
	// Remove task note
	RemoveTaskNote(context.Context, *RemoveTaskNoteParams) (*RemoveTaskNoteResult, error)
	// Search for a pet with a task for the day
	SearchPetWithTaskDate(context.Context, *SearchPetWithTaskDateParams) (*SearchPetWithTaskDateResult, error)
	mustEmbedUnimplementedAppointmentTaskServiceServer()
}

// UnimplementedAppointmentTaskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentTaskServiceServer struct {
}

func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTaskFilters(context.Context, *ListAppointmentTaskFiltersParams) (*ListAppointmentTaskFiltersResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTaskFilters not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTasksMultiGroup(context.Context, *ListAppointmentTasksParams) (*ListAppointmentTasksMultiGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTasksMultiGroup not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTaskCountByCategory(context.Context, *ListAppointmentTaskCountByCategoryParams) (*ListAppointmentTaskCountByCategoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTaskCountByCategory not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListAppointmentTasksByCategory(context.Context, *ListAppointmentTasksByCategoryParams) (*ListAppointmentTasksByCategoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentTasksByCategory not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) ListOwnerAppointmentTasks(context.Context, *ListAppointmentTasksByCategoryParams) (*ListAppointmentTasksByCategoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOwnerAppointmentTasks not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) UpdateAppointmentTask(context.Context, *UpdateAppointmentTaskParams) (*UpdateAppointmentTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) BatchUpdateAppointmentTask(context.Context, *BatchUpdateAppointmentTaskParams) (*BatchUpdateAppointmentTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateAppointmentTask not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) RemoveTaskStaff(context.Context, *RemoveTaskStaffParams) (*RemoveTaskStaffResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTaskStaff not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) RemoveTaskNote(context.Context, *RemoveTaskNoteParams) (*RemoveTaskNoteResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTaskNote not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) SearchPetWithTaskDate(context.Context, *SearchPetWithTaskDateParams) (*SearchPetWithTaskDateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPetWithTaskDate not implemented")
}
func (UnimplementedAppointmentTaskServiceServer) mustEmbedUnimplementedAppointmentTaskServiceServer() {
}

// UnsafeAppointmentTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentTaskServiceServer will
// result in compilation errors.
type UnsafeAppointmentTaskServiceServer interface {
	mustEmbedUnimplementedAppointmentTaskServiceServer()
}

func RegisterAppointmentTaskServiceServer(s grpc.ServiceRegistrar, srv AppointmentTaskServiceServer) {
	s.RegisterService(&AppointmentTaskService_ServiceDesc, srv)
}

func _AppointmentTaskService_ListAppointmentTaskFilters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTaskFiltersParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskFilters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTaskFilters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskFilters(ctx, req.(*ListAppointmentTaskFiltersParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListAppointmentTasksMultiGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTasksParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasksMultiGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTasksMultiGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasksMultiGroup(ctx, req.(*ListAppointmentTasksParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListAppointmentTaskCountByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTaskCountByCategoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskCountByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTaskCountByCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTaskCountByCategory(ctx, req.(*ListAppointmentTaskCountByCategoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListAppointmentTasksByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTasksByCategoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasksByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/ListAppointmentTasksByCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListAppointmentTasksByCategory(ctx, req.(*ListAppointmentTasksByCategoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_ListOwnerAppointmentTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentTasksByCategoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).ListOwnerAppointmentTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/ListOwnerAppointmentTasks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).ListOwnerAppointmentTasks(ctx, req.(*ListAppointmentTasksByCategoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_UpdateAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).UpdateAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/UpdateAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).UpdateAppointmentTask(ctx, req.(*UpdateAppointmentTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_BatchUpdateAppointmentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateAppointmentTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).BatchUpdateAppointmentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/BatchUpdateAppointmentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).BatchUpdateAppointmentTask(ctx, req.(*BatchUpdateAppointmentTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_RemoveTaskStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTaskStaffParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).RemoveTaskStaff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/RemoveTaskStaff",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).RemoveTaskStaff(ctx, req.(*RemoveTaskStaffParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_RemoveTaskNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTaskNoteParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).RemoveTaskNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/RemoveTaskNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).RemoveTaskNote(ctx, req.(*RemoveTaskNoteParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTaskService_SearchPetWithTaskDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPetWithTaskDateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTaskServiceServer).SearchPetWithTaskDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTaskService/SearchPetWithTaskDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTaskServiceServer).SearchPetWithTaskDate(ctx, req.(*SearchPetWithTaskDateParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentTaskService_ServiceDesc is the grpc.ServiceDesc for AppointmentTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.AppointmentTaskService",
	HandlerType: (*AppointmentTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAppointmentTaskFilters",
			Handler:    _AppointmentTaskService_ListAppointmentTaskFilters_Handler,
		},
		{
			MethodName: "ListAppointmentTasksMultiGroup",
			Handler:    _AppointmentTaskService_ListAppointmentTasksMultiGroup_Handler,
		},
		{
			MethodName: "ListAppointmentTaskCountByCategory",
			Handler:    _AppointmentTaskService_ListAppointmentTaskCountByCategory_Handler,
		},
		{
			MethodName: "ListAppointmentTasksByCategory",
			Handler:    _AppointmentTaskService_ListAppointmentTasksByCategory_Handler,
		},
		{
			MethodName: "ListOwnerAppointmentTasks",
			Handler:    _AppointmentTaskService_ListOwnerAppointmentTasks_Handler,
		},
		{
			MethodName: "UpdateAppointmentTask",
			Handler:    _AppointmentTaskService_UpdateAppointmentTask_Handler,
		},
		{
			MethodName: "BatchUpdateAppointmentTask",
			Handler:    _AppointmentTaskService_BatchUpdateAppointmentTask_Handler,
		},
		{
			MethodName: "RemoveTaskStaff",
			Handler:    _AppointmentTaskService_RemoveTaskStaff_Handler,
		},
		{
			MethodName: "RemoveTaskNote",
			Handler:    _AppointmentTaskService_RemoveTaskNote_Handler,
		},
		{
			MethodName: "SearchPetWithTaskDate",
			Handler:    _AppointmentTaskService_SearchPetWithTaskDate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/appointment_task_api.proto",
}
