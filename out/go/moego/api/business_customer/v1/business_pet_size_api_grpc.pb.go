// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_size_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetSizeServiceClient is the client API for BusinessPetSizeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetSizeServiceClient interface {
	// List pet sizes of current company
	ListPetSize(ctx context.Context, in *ListPetSizeParams, opts ...grpc.CallOption) (*ListPetSizeResult, error)
	// Batch upsert pet sizes of current company
	BatchUpsertPetSize(ctx context.Context, in *BatchUpsertPetSizeParams, opts ...grpc.CallOption) (*BatchUpsertPetSizeResult, error)
}

type businessPetSizeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetSizeServiceClient(cc grpc.ClientConnInterface) BusinessPetSizeServiceClient {
	return &businessPetSizeServiceClient{cc}
}

func (c *businessPetSizeServiceClient) ListPetSize(ctx context.Context, in *ListPetSizeParams, opts ...grpc.CallOption) (*ListPetSizeResult, error) {
	out := new(ListPetSizeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetSizeService/ListPetSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetSizeServiceClient) BatchUpsertPetSize(ctx context.Context, in *BatchUpsertPetSizeParams, opts ...grpc.CallOption) (*BatchUpsertPetSizeResult, error) {
	out := new(BatchUpsertPetSizeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetSizeService/BatchUpsertPetSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetSizeServiceServer is the server API for BusinessPetSizeService service.
// All implementations must embed UnimplementedBusinessPetSizeServiceServer
// for forward compatibility
type BusinessPetSizeServiceServer interface {
	// List pet sizes of current company
	ListPetSize(context.Context, *ListPetSizeParams) (*ListPetSizeResult, error)
	// Batch upsert pet sizes of current company
	BatchUpsertPetSize(context.Context, *BatchUpsertPetSizeParams) (*BatchUpsertPetSizeResult, error)
	mustEmbedUnimplementedBusinessPetSizeServiceServer()
}

// UnimplementedBusinessPetSizeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetSizeServiceServer struct {
}

func (UnimplementedBusinessPetSizeServiceServer) ListPetSize(context.Context, *ListPetSizeParams) (*ListPetSizeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetSize not implemented")
}
func (UnimplementedBusinessPetSizeServiceServer) BatchUpsertPetSize(context.Context, *BatchUpsertPetSizeParams) (*BatchUpsertPetSizeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpsertPetSize not implemented")
}
func (UnimplementedBusinessPetSizeServiceServer) mustEmbedUnimplementedBusinessPetSizeServiceServer() {
}

// UnsafeBusinessPetSizeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetSizeServiceServer will
// result in compilation errors.
type UnsafeBusinessPetSizeServiceServer interface {
	mustEmbedUnimplementedBusinessPetSizeServiceServer()
}

func RegisterBusinessPetSizeServiceServer(s grpc.ServiceRegistrar, srv BusinessPetSizeServiceServer) {
	s.RegisterService(&BusinessPetSizeService_ServiceDesc, srv)
}

func _BusinessPetSizeService_ListPetSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetSizeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetSizeServiceServer).ListPetSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetSizeService/ListPetSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetSizeServiceServer).ListPetSize(ctx, req.(*ListPetSizeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetSizeService_BatchUpsertPetSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertPetSizeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetSizeServiceServer).BatchUpsertPetSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetSizeService/BatchUpsertPetSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetSizeServiceServer).BatchUpsertPetSize(ctx, req.(*BatchUpsertPetSizeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetSizeService_ServiceDesc is the grpc.ServiceDesc for BusinessPetSizeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetSizeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetSizeService",
	HandlerType: (*BusinessPetSizeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPetSize",
			Handler:    _BusinessPetSizeService_ListPetSize_Handler,
		},
		{
			MethodName: "BatchUpsertPetSize",
			Handler:    _BusinessPetSizeService_BatchUpsertPetSize_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_size_api.proto",
}
