// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/promotion/v1/prompt_api.proto

package promotionapipb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list promotions request
type ListPromotionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page name
	Page string `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	// filter, a custom filter specified by the page
	// e.g. {"ob_name":"CrazyPet"}
	Filter map[string]string `protobuf:"bytes,2,rep,name=filter,proto3" json:"filter,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListPromotionsRequest) Reset() {
	*x = ListPromotionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPromotionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPromotionsRequest) ProtoMessage() {}

func (x *ListPromotionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPromotionsRequest.ProtoReflect.Descriptor instead.
func (*ListPromotionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_prompt_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPromotionsRequest) GetPage() string {
	if x != nil {
		return x.Page
	}
	return ""
}

func (x *ListPromotionsRequest) GetFilter() map[string]string {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list promotions response
type ListPromotionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// promotions
	Promotions []*v1.SlotModel `protobuf:"bytes,1,rep,name=promotions,proto3" json:"promotions,omitempty"`
}

func (x *ListPromotionsResponse) Reset() {
	*x = ListPromotionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPromotionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPromotionsResponse) ProtoMessage() {}

func (x *ListPromotionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPromotionsResponse.ProtoReflect.Descriptor instead.
func (*ListPromotionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_prompt_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPromotionsResponse) GetPromotions() []*v1.SlotModel {
	if x != nil {
		return x.Promotions
	}
	return nil
}

// search coupons request
type SearchCouponsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// search condition
	SearchCondition *v1.CouponSearchCondition `protobuf:"bytes,1,opt,name=search_condition,json=searchCondition,proto3" json:"search_condition,omitempty"`
}

func (x *SearchCouponsRequest) Reset() {
	*x = SearchCouponsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponsRequest) ProtoMessage() {}

func (x *SearchCouponsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponsRequest.ProtoReflect.Descriptor instead.
func (*SearchCouponsRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_prompt_api_proto_rawDescGZIP(), []int{2}
}

func (x *SearchCouponsRequest) GetSearchCondition() *v1.CouponSearchCondition {
	if x != nil {
		return x.SearchCondition
	}
	return nil
}

// search coupons response
type SearchCouponsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available coupons
	AvailableCoupons []*v1.Coupon `protobuf:"bytes,1,rep,name=available_coupons,json=availableCoupons,proto3" json:"available_coupons,omitempty"`
	// unavailable coupons
	UnavailableCoupons []*v1.Coupon `protobuf:"bytes,2,rep,name=unavailable_coupons,json=unavailableCoupons,proto3" json:"unavailable_coupons,omitempty"`
	// membership subjects
	MembershipSubjects []*v11.MembershipModel `protobuf:"bytes,3,rep,name=membership_subjects,json=membershipSubjects,proto3" json:"membership_subjects,omitempty"`
	// package subjects
	PackageSubjects []*v12.CustomerPackageView `protobuf:"bytes,4,rep,name=package_subjects,json=packageSubjects,proto3" json:"package_subjects,omitempty"`
	// discount subjects
	DiscountSubjects []*v13.DiscountCodeCompositeView `protobuf:"bytes,5,rep,name=discount_subjects,json=discountSubjects,proto3" json:"discount_subjects,omitempty"`
}

func (x *SearchCouponsResponse) Reset() {
	*x = SearchCouponsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponsResponse) ProtoMessage() {}

func (x *SearchCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_promotion_v1_prompt_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponsResponse.ProtoReflect.Descriptor instead.
func (*SearchCouponsResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_promotion_v1_prompt_api_proto_rawDescGZIP(), []int{3}
}

func (x *SearchCouponsResponse) GetAvailableCoupons() []*v1.Coupon {
	if x != nil {
		return x.AvailableCoupons
	}
	return nil
}

func (x *SearchCouponsResponse) GetUnavailableCoupons() []*v1.Coupon {
	if x != nil {
		return x.UnavailableCoupons
	}
	return nil
}

func (x *SearchCouponsResponse) GetMembershipSubjects() []*v11.MembershipModel {
	if x != nil {
		return x.MembershipSubjects
	}
	return nil
}

func (x *SearchCouponsResponse) GetPackageSubjects() []*v12.CustomerPackageView {
	if x != nil {
		return x.PackageSubjects
	}
	return nil
}

func (x *SearchCouponsResponse) GetDiscountSubjects() []*v13.DiscountCodeCompositeView {
	if x != nil {
		return x.DiscountSubjects
	}
	return nil
}

var File_moego_api_promotion_v1_prompt_api_proto protoreflect.FileDescriptor

var file_moego_api_promotion_v1_prompt_api_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xde, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x6b,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x9a, 0x01,
	0x12, 0x10, 0x14, 0x22, 0x0e, 0x72, 0x0c, 0x18, 0xc8, 0x01, 0x52, 0x07, 0x6f, 0x62, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x39, 0x0a, 0x0b, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5e, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x73, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b,
	0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd6, 0x03, 0x0a, 0x15,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x52, 0x0a, 0x13, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x12, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x5c, 0x0a, 0x13, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x12, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x10, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x12, 0x61, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x32, 0xf1, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x0e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x0d, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7e, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_promotion_v1_prompt_api_proto_rawDescOnce sync.Once
	file_moego_api_promotion_v1_prompt_api_proto_rawDescData = file_moego_api_promotion_v1_prompt_api_proto_rawDesc
)

func file_moego_api_promotion_v1_prompt_api_proto_rawDescGZIP() []byte {
	file_moego_api_promotion_v1_prompt_api_proto_rawDescOnce.Do(func() {
		file_moego_api_promotion_v1_prompt_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_promotion_v1_prompt_api_proto_rawDescData)
	})
	return file_moego_api_promotion_v1_prompt_api_proto_rawDescData
}

var file_moego_api_promotion_v1_prompt_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_api_promotion_v1_prompt_api_proto_goTypes = []interface{}{
	(*ListPromotionsRequest)(nil),         // 0: moego.api.promotion.v1.ListPromotionsRequest
	(*ListPromotionsResponse)(nil),        // 1: moego.api.promotion.v1.ListPromotionsResponse
	(*SearchCouponsRequest)(nil),          // 2: moego.api.promotion.v1.SearchCouponsRequest
	(*SearchCouponsResponse)(nil),         // 3: moego.api.promotion.v1.SearchCouponsResponse
	nil,                                   // 4: moego.api.promotion.v1.ListPromotionsRequest.FilterEntry
	(*v1.SlotModel)(nil),                  // 5: moego.models.promotion.v1.SlotModel
	(*v1.CouponSearchCondition)(nil),      // 6: moego.models.promotion.v1.CouponSearchCondition
	(*v1.Coupon)(nil),                     // 7: moego.models.promotion.v1.Coupon
	(*v11.MembershipModel)(nil),           // 8: moego.models.membership.v1.MembershipModel
	(*v12.CustomerPackageView)(nil),       // 9: moego.api.appointment.v1.CustomerPackageView
	(*v13.DiscountCodeCompositeView)(nil), // 10: moego.models.marketing.v1.DiscountCodeCompositeView
}
var file_moego_api_promotion_v1_prompt_api_proto_depIdxs = []int32{
	4,  // 0: moego.api.promotion.v1.ListPromotionsRequest.filter:type_name -> moego.api.promotion.v1.ListPromotionsRequest.FilterEntry
	5,  // 1: moego.api.promotion.v1.ListPromotionsResponse.promotions:type_name -> moego.models.promotion.v1.SlotModel
	6,  // 2: moego.api.promotion.v1.SearchCouponsRequest.search_condition:type_name -> moego.models.promotion.v1.CouponSearchCondition
	7,  // 3: moego.api.promotion.v1.SearchCouponsResponse.available_coupons:type_name -> moego.models.promotion.v1.Coupon
	7,  // 4: moego.api.promotion.v1.SearchCouponsResponse.unavailable_coupons:type_name -> moego.models.promotion.v1.Coupon
	8,  // 5: moego.api.promotion.v1.SearchCouponsResponse.membership_subjects:type_name -> moego.models.membership.v1.MembershipModel
	9,  // 6: moego.api.promotion.v1.SearchCouponsResponse.package_subjects:type_name -> moego.api.appointment.v1.CustomerPackageView
	10, // 7: moego.api.promotion.v1.SearchCouponsResponse.discount_subjects:type_name -> moego.models.marketing.v1.DiscountCodeCompositeView
	0,  // 8: moego.api.promotion.v1.PromotionService.ListPromotions:input_type -> moego.api.promotion.v1.ListPromotionsRequest
	2,  // 9: moego.api.promotion.v1.PromotionService.SearchCoupons:input_type -> moego.api.promotion.v1.SearchCouponsRequest
	1,  // 10: moego.api.promotion.v1.PromotionService.ListPromotions:output_type -> moego.api.promotion.v1.ListPromotionsResponse
	3,  // 11: moego.api.promotion.v1.PromotionService.SearchCoupons:output_type -> moego.api.promotion.v1.SearchCouponsResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_promotion_v1_prompt_api_proto_init() }
func file_moego_api_promotion_v1_prompt_api_proto_init() {
	if File_moego_api_promotion_v1_prompt_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_promotion_v1_prompt_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPromotionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_promotion_v1_prompt_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPromotionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_promotion_v1_prompt_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_promotion_v1_prompt_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_promotion_v1_prompt_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_promotion_v1_prompt_api_proto_goTypes,
		DependencyIndexes: file_moego_api_promotion_v1_prompt_api_proto_depIdxs,
		MessageInfos:      file_moego_api_promotion_v1_prompt_api_proto_msgTypes,
	}.Build()
	File_moego_api_promotion_v1_prompt_api_proto = out.File
	file_moego_api_promotion_v1_prompt_api_proto_rawDesc = nil
	file_moego_api_promotion_v1_prompt_api_proto_goTypes = nil
	file_moego_api_promotion_v1_prompt_api_proto_depIdxs = nil
}
