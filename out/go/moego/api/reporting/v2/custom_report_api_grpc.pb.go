// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/reporting/v2/custom_report_api.proto

package reportingapipb

import (
	context "context"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CustomReportServiceClient is the client API for CustomReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomReportServiceClient interface {
	// SaveCustomReport
	SaveCustomReport(ctx context.Context, in *v2.SaveCustomReportParams, opts ...grpc.CallOption) (*v2.SaveCustomReportResult, error)
	// ModifyCustomDiagram
	ModifyCustomDiagram(ctx context.Context, in *v2.ModifyCustomDiagramParams, opts ...grpc.CallOption) (*v2.ModifyCustomDiagramResult, error)
	// DuplicateCustomReport
	DuplicateCustomReport(ctx context.Context, in *v2.DuplicateCustomReportParams, opts ...grpc.CallOption) (*v2.DuplicateCustomReportResult, error)
	// DeleteCustomReport
	DeleteCustomReport(ctx context.Context, in *v2.DeleteCustomReportParams, opts ...grpc.CallOption) (*v2.DeleteCustomReportResult, error)
}

type customReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomReportServiceClient(cc grpc.ClientConnInterface) CustomReportServiceClient {
	return &customReportServiceClient{cc}
}

func (c *customReportServiceClient) SaveCustomReport(ctx context.Context, in *v2.SaveCustomReportParams, opts ...grpc.CallOption) (*v2.SaveCustomReportResult, error) {
	out := new(v2.SaveCustomReportResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.CustomReportService/SaveCustomReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customReportServiceClient) ModifyCustomDiagram(ctx context.Context, in *v2.ModifyCustomDiagramParams, opts ...grpc.CallOption) (*v2.ModifyCustomDiagramResult, error) {
	out := new(v2.ModifyCustomDiagramResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.CustomReportService/ModifyCustomDiagram", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customReportServiceClient) DuplicateCustomReport(ctx context.Context, in *v2.DuplicateCustomReportParams, opts ...grpc.CallOption) (*v2.DuplicateCustomReportResult, error) {
	out := new(v2.DuplicateCustomReportResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.CustomReportService/DuplicateCustomReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customReportServiceClient) DeleteCustomReport(ctx context.Context, in *v2.DeleteCustomReportParams, opts ...grpc.CallOption) (*v2.DeleteCustomReportResult, error) {
	out := new(v2.DeleteCustomReportResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.CustomReportService/DeleteCustomReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomReportServiceServer is the server API for CustomReportService service.
// All implementations must embed UnimplementedCustomReportServiceServer
// for forward compatibility
type CustomReportServiceServer interface {
	// SaveCustomReport
	SaveCustomReport(context.Context, *v2.SaveCustomReportParams) (*v2.SaveCustomReportResult, error)
	// ModifyCustomDiagram
	ModifyCustomDiagram(context.Context, *v2.ModifyCustomDiagramParams) (*v2.ModifyCustomDiagramResult, error)
	// DuplicateCustomReport
	DuplicateCustomReport(context.Context, *v2.DuplicateCustomReportParams) (*v2.DuplicateCustomReportResult, error)
	// DeleteCustomReport
	DeleteCustomReport(context.Context, *v2.DeleteCustomReportParams) (*v2.DeleteCustomReportResult, error)
	mustEmbedUnimplementedCustomReportServiceServer()
}

// UnimplementedCustomReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCustomReportServiceServer struct {
}

func (UnimplementedCustomReportServiceServer) SaveCustomReport(context.Context, *v2.SaveCustomReportParams) (*v2.SaveCustomReportResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCustomReport not implemented")
}
func (UnimplementedCustomReportServiceServer) ModifyCustomDiagram(context.Context, *v2.ModifyCustomDiagramParams) (*v2.ModifyCustomDiagramResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyCustomDiagram not implemented")
}
func (UnimplementedCustomReportServiceServer) DuplicateCustomReport(context.Context, *v2.DuplicateCustomReportParams) (*v2.DuplicateCustomReportResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DuplicateCustomReport not implemented")
}
func (UnimplementedCustomReportServiceServer) DeleteCustomReport(context.Context, *v2.DeleteCustomReportParams) (*v2.DeleteCustomReportResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomReport not implemented")
}
func (UnimplementedCustomReportServiceServer) mustEmbedUnimplementedCustomReportServiceServer() {}

// UnsafeCustomReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomReportServiceServer will
// result in compilation errors.
type UnsafeCustomReportServiceServer interface {
	mustEmbedUnimplementedCustomReportServiceServer()
}

func RegisterCustomReportServiceServer(s grpc.ServiceRegistrar, srv CustomReportServiceServer) {
	s.RegisterService(&CustomReportService_ServiceDesc, srv)
}

func _CustomReportService_SaveCustomReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.SaveCustomReportParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomReportServiceServer).SaveCustomReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.CustomReportService/SaveCustomReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomReportServiceServer).SaveCustomReport(ctx, req.(*v2.SaveCustomReportParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomReportService_ModifyCustomDiagram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ModifyCustomDiagramParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomReportServiceServer).ModifyCustomDiagram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.CustomReportService/ModifyCustomDiagram",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomReportServiceServer).ModifyCustomDiagram(ctx, req.(*v2.ModifyCustomDiagramParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomReportService_DuplicateCustomReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.DuplicateCustomReportParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomReportServiceServer).DuplicateCustomReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.CustomReportService/DuplicateCustomReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomReportServiceServer).DuplicateCustomReport(ctx, req.(*v2.DuplicateCustomReportParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomReportService_DeleteCustomReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.DeleteCustomReportParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomReportServiceServer).DeleteCustomReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.CustomReportService/DeleteCustomReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomReportServiceServer).DeleteCustomReport(ctx, req.(*v2.DeleteCustomReportParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomReportService_ServiceDesc is the grpc.ServiceDesc for CustomReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.reporting.v2.CustomReportService",
	HandlerType: (*CustomReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SaveCustomReport",
			Handler:    _CustomReportService_SaveCustomReport_Handler,
		},
		{
			MethodName: "ModifyCustomDiagram",
			Handler:    _CustomReportService_ModifyCustomDiagram_Handler,
		},
		{
			MethodName: "DuplicateCustomReport",
			Handler:    _CustomReportService_DuplicateCustomReport_Handler,
		},
		{
			MethodName: "DeleteCustomReport",
			Handler:    _CustomReportService_DeleteCustomReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/reporting/v2/custom_report_api.proto",
}
