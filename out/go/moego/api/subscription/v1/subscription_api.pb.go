// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/subscription/v1/subscription_api.proto

package subscriptionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// product
type ProductView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 类型
	Type v1.Product_Type `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.subscription.v1.Product_Type" json:"type,omitempty"`
	// seller
	Seller *v1.User `protobuf:"bytes,5,opt,name=seller,proto3" json:"seller,omitempty"`
	// features
	Features []*v1.Feature `protobuf:"bytes,6,rep,name=features,proto3" json:"features,omitempty"`
	// business types
	BusinessType v1.Product_BusinessType `protobuf:"varint,7,opt,name=business_type,json=businessType,proto3,enum=moego.models.subscription.v1.Product_BusinessType" json:"business_type,omitempty"`
	// price
	Prices []*PriceView `protobuf:"bytes,8,rep,name=prices,proto3" json:"prices,omitempty"`
	// extra
	Extra *v1.ProductExtra `protobuf:"bytes,9,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *ProductView) Reset() {
	*x = ProductView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductView) ProtoMessage() {}

func (x *ProductView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductView.ProtoReflect.Descriptor instead.
func (*ProductView) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{0}
}

func (x *ProductView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProductView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProductView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProductView) GetType() v1.Product_Type {
	if x != nil {
		return x.Type
	}
	return v1.Product_Type(0)
}

func (x *ProductView) GetSeller() *v1.User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *ProductView) GetFeatures() []*v1.Feature {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *ProductView) GetBusinessType() v1.Product_BusinessType {
	if x != nil {
		return x.BusinessType
	}
	return v1.Product_BusinessType(0)
}

func (x *ProductView) GetPrices() []*PriceView {
	if x != nil {
		return x.Prices
	}
	return nil
}

func (x *ProductView) GetExtra() *v1.ProductExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// 价格
type PriceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 类型
	Type v1.Price_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.subscription.v1.Price_Type" json:"type,omitempty"`
	// 单价
	UnitAmount *money.Money `protobuf:"bytes,3,opt,name=unit_amount,json=unitAmount,proto3" json:"unit_amount,omitempty"`
	// 计费周期
	BillingCycle *v11.TimePeriod `protobuf:"bytes,4,opt,name=billing_cycle,json=billingCycle,proto3" json:"billing_cycle,omitempty"`
	// 预审规则
	Prequalification *v1.Price_Prequalification `protobuf:"bytes,5,opt,name=prequalification,proto3" json:"prequalification,omitempty"`
	// 能应用的discounts
	Discounts []*v1.Discount `protobuf:"bytes,6,rep,name=discounts,proto3" json:"discounts,omitempty"`
	// 应用后的单价
	DiscountedUnitAmount *money.Money `protobuf:"bytes,7,opt,name=discounted_unit_amount,json=discountedUnitAmount,proto3" json:"discounted_unit_amount,omitempty"`
	// 优惠百分比，由 (1 - discounted_unit_amount / unit_amount) * 100 得到
	DiscountPercentageOff *decimal.Decimal `protobuf:"bytes,8,opt,name=discount_percentage_off,json=discountPercentageOff,proto3" json:"discount_percentage_off,omitempty"`
}

func (x *PriceView) Reset() {
	*x = PriceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceView) ProtoMessage() {}

func (x *PriceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceView.ProtoReflect.Descriptor instead.
func (*PriceView) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{1}
}

func (x *PriceView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PriceView) GetType() v1.Price_Type {
	if x != nil {
		return x.Type
	}
	return v1.Price_Type(0)
}

func (x *PriceView) GetUnitAmount() *money.Money {
	if x != nil {
		return x.UnitAmount
	}
	return nil
}

func (x *PriceView) GetBillingCycle() *v11.TimePeriod {
	if x != nil {
		return x.BillingCycle
	}
	return nil
}

func (x *PriceView) GetPrequalification() *v1.Price_Prequalification {
	if x != nil {
		return x.Prequalification
	}
	return nil
}

func (x *PriceView) GetDiscounts() []*v1.Discount {
	if x != nil {
		return x.Discounts
	}
	return nil
}

func (x *PriceView) GetDiscountedUnitAmount() *money.Money {
	if x != nil {
		return x.DiscountedUnitAmount
	}
	return nil
}

func (x *PriceView) GetDiscountPercentageOff() *decimal.Decimal {
	if x != nil {
		return x.DiscountPercentageOff
	}
	return nil
}

// 订阅
type SubscriptionView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称，继承自 plan product
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 描述，继承自 plan product
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 状态
	Status v1.Subscription_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"status,omitempty"`
	// 订阅的有效期，起点为当前扣款时间，终点为下一次扣款时间
	ValidityPeriod *interval.Interval `protobuf:"bytes,6,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// 计费周期
	BillingCycle *v11.TimePeriod `protobuf:"bytes,7,opt,name=billing_cycle,json=billingCycle,proto3" json:"billing_cycle,omitempty"`
	// 宽限期
	GracePeriod *v11.TimePeriod `protobuf:"bytes,8,opt,name=grace_period,json=gracePeriod,proto3" json:"grace_period,omitempty"`
	// 是否在周期结束时取消订阅
	CancelAtPeriodEnd bool `protobuf:"varint,9,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// 买家
	Buyer *v1.User `protobuf:"bytes,10,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// 卖家
	Seller *v1.User `protobuf:"bytes,11,opt,name=seller,proto3" json:"seller,omitempty"`
	// purchase details
	PurchaseDetails []*PurchaseDetailView `protobuf:"bytes,12,rep,name=purchase_details,json=purchaseDetails,proto3" json:"purchase_details,omitempty"`
}

func (x *SubscriptionView) Reset() {
	*x = SubscriptionView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionView) ProtoMessage() {}

func (x *SubscriptionView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionView.ProtoReflect.Descriptor instead.
func (*SubscriptionView) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{2}
}

func (x *SubscriptionView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubscriptionView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SubscriptionView) GetStatus() v1.Subscription_Status {
	if x != nil {
		return x.Status
	}
	return v1.Subscription_Status(0)
}

func (x *SubscriptionView) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *SubscriptionView) GetBillingCycle() *v11.TimePeriod {
	if x != nil {
		return x.BillingCycle
	}
	return nil
}

func (x *SubscriptionView) GetGracePeriod() *v11.TimePeriod {
	if x != nil {
		return x.GracePeriod
	}
	return nil
}

func (x *SubscriptionView) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *SubscriptionView) GetBuyer() *v1.User {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *SubscriptionView) GetSeller() *v1.User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *SubscriptionView) GetPurchaseDetails() []*PurchaseDetailView {
	if x != nil {
		return x.PurchaseDetails
	}
	return nil
}

// 购买行为
type PurchaseDetailView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subscription_id
	SubscriptionId int64 `protobuf:"varint,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// Product
	Product *ProductView `protobuf:"bytes,3,opt,name=product,proto3" json:"product,omitempty"`
	// Price
	Price *PriceView `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	// Quantity
	Quantity int32 `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 总价
	TotalPrice *money.Money `protobuf:"bytes,6,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// 应用的折扣
	Discounts []*v1.Discount `protobuf:"bytes,7,rep,name=discounts,proto3" json:"discounts,omitempty"`
	// 原价
	OriginalTotalPrice *money.Money `protobuf:"bytes,8,opt,name=original_total_price,json=originalTotalPrice,proto3" json:"original_total_price,omitempty"`
	// 优惠百分比，由 (1 - total_price / original_total_price) * 100 得到
	DiscountPercentageOff *decimal.Decimal `protobuf:"bytes,9,opt,name=discount_percentage_off,json=discountPercentageOff,proto3" json:"discount_percentage_off,omitempty"`
}

func (x *PurchaseDetailView) Reset() {
	*x = PurchaseDetailView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseDetailView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseDetailView) ProtoMessage() {}

func (x *PurchaseDetailView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseDetailView.ProtoReflect.Descriptor instead.
func (*PurchaseDetailView) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{3}
}

func (x *PurchaseDetailView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PurchaseDetailView) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *PurchaseDetailView) GetProduct() *ProductView {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *PurchaseDetailView) GetPrice() *PriceView {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *PurchaseDetailView) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PurchaseDetailView) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *PurchaseDetailView) GetDiscounts() []*v1.Discount {
	if x != nil {
		return x.Discounts
	}
	return nil
}

func (x *PurchaseDetailView) GetOriginalTotalPrice() *money.Money {
	if x != nil {
		return x.OriginalTotalPrice
	}
	return nil
}

func (x *PurchaseDetailView) GetDiscountPercentageOff() *decimal.Decimal {
	if x != nil {
		return x.DiscountPercentageOff
	}
	return nil
}

// List Products Params.
type ListProductsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter by business type
	BusinessTypes []v1.Product_BusinessType `protobuf:"varint,2,rep,packed,name=business_types,json=businessTypes,proto3,enum=moego.models.subscription.v1.Product_BusinessType" json:"business_types,omitempty"`
	// filter by id
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListProductsParams) Reset() {
	*x = ListProductsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductsParams) ProtoMessage() {}

func (x *ListProductsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductsParams.ProtoReflect.Descriptor instead.
func (*ListProductsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListProductsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListProductsParams) GetBusinessTypes() []v1.Product_BusinessType {
	if x != nil {
		return x.BusinessTypes
	}
	return nil
}

func (x *ListProductsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// List Products Result.
type ListProductsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// products
	Products []*ProductView `protobuf:"bytes,2,rep,name=products,proto3" json:"products,omitempty"`
}

func (x *ListProductsResult) Reset() {
	*x = ListProductsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProductsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductsResult) ProtoMessage() {}

func (x *ListProductsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductsResult.ProtoReflect.Descriptor instead.
func (*ListProductsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListProductsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListProductsResult) GetProducts() []*ProductView {
	if x != nil {
		return x.Products
	}
	return nil
}

// create subscription Params
type CreateSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the purchase
	Purchases []*v1.Purchase `protobuf:"bytes,1,rep,name=purchases,proto3" json:"purchases,omitempty"`
	// 非 TRIAL 必填
	CardOnFileId *string `protobuf:"bytes,2,opt,name=card_on_file_id,json=cardOnFileId,proto3,oneof" json:"card_on_file_id,omitempty"`
}

func (x *CreateSubscriptionParams) Reset() {
	*x = CreateSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubscriptionParams) ProtoMessage() {}

func (x *CreateSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubscriptionParams.ProtoReflect.Descriptor instead.
func (*CreateSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{6}
}

func (x *CreateSubscriptionParams) GetPurchases() []*v1.Purchase {
	if x != nil {
		return x.Purchases
	}
	return nil
}

func (x *CreateSubscriptionParams) GetCardOnFileId() string {
	if x != nil && x.CardOnFileId != nil {
		return *x.CardOnFileId
	}
	return ""
}

// create subscription Result
type CreateSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription
	Subscription *SubscriptionView `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
	// 前端调 stripe sdk 时需要通过 client_secret 来确认支付状态
	// To be deprecated
	ExternalStripeClientSecret string `protobuf:"bytes,2,opt,name=external_stripe_client_secret,json=externalStripeClientSecret,proto3" json:"external_stripe_client_secret,omitempty"`
}

func (x *CreateSubscriptionResult) Reset() {
	*x = CreateSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubscriptionResult) ProtoMessage() {}

func (x *CreateSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubscriptionResult.ProtoReflect.Descriptor instead.
func (*CreateSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{7}
}

func (x *CreateSubscriptionResult) GetSubscription() *SubscriptionView {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *CreateSubscriptionResult) GetExternalStripeClientSecret() string {
	if x != nil {
		return x.ExternalStripeClientSecret
	}
	return ""
}

// ListSubscriptions Params.
type ListSubscriptionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// statuses
	Statuses []v1.Subscription_Status `protobuf:"varint,2,rep,packed,name=statuses,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"statuses,omitempty"`
	// business type
	PlanProductBusinessTypes []v1.Product_BusinessType `protobuf:"varint,3,rep,packed,name=plan_product_business_types,json=planProductBusinessTypes,proto3,enum=moego.models.subscription.v1.Product_BusinessType" json:"plan_product_business_types,omitempty"`
	// subscription ids
	Ids []int64 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListSubscriptionsParams) Reset() {
	*x = ListSubscriptionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsParams) ProtoMessage() {}

func (x *ListSubscriptionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsParams.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListSubscriptionsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListSubscriptionsParams) GetStatuses() []v1.Subscription_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListSubscriptionsParams) GetPlanProductBusinessTypes() []v1.Product_BusinessType {
	if x != nil {
		return x.PlanProductBusinessTypes
	}
	return nil
}

func (x *ListSubscriptionsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// ListSubscriptions Result.
type ListSubscriptionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// subscriptions
	Subscriptions []*SubscriptionView `protobuf:"bytes,2,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
}

func (x *ListSubscriptionsResult) Reset() {
	*x = ListSubscriptionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsResult) ProtoMessage() {}

func (x *ListSubscriptionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsResult.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListSubscriptionsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListSubscriptionsResult) GetSubscriptions() []*SubscriptionView {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

// CancelSubscription Params.
type CancelSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription id
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
}

func (x *CancelSubscriptionParams) Reset() {
	*x = CancelSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionParams) ProtoMessage() {}

func (x *CancelSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionParams.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{10}
}

func (x *CancelSubscriptionParams) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

// CancelSubscription Result.
type CancelSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription
	Subscription *SubscriptionView `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *CancelSubscriptionResult) Reset() {
	*x = CancelSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionResult) ProtoMessage() {}

func (x *CancelSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionResult.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{11}
}

func (x *CancelSubscriptionResult) GetSubscription() *SubscriptionView {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// PreviewPurchases Params.
type PreviewPurchasesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the purchase
	Purchases []*v1.Purchase `protobuf:"bytes,1,rep,name=purchases,proto3" json:"purchases,omitempty"`
}

func (x *PreviewPurchasesParams) Reset() {
	*x = PreviewPurchasesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPurchasesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPurchasesParams) ProtoMessage() {}

func (x *PreviewPurchasesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPurchasesParams.ProtoReflect.Descriptor instead.
func (*PreviewPurchasesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{12}
}

func (x *PreviewPurchasesParams) GetPurchases() []*v1.Purchase {
	if x != nil {
		return x.Purchases
	}
	return nil
}

// PreviewPurchases Result.
type PreviewPurchasesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the purchase details
	PurchaseDetails []*PurchaseDetailView `protobuf:"bytes,1,rep,name=purchase_details,json=purchaseDetails,proto3" json:"purchase_details,omitempty"`
}

func (x *PreviewPurchasesResult) Reset() {
	*x = PreviewPurchasesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPurchasesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPurchasesResult) ProtoMessage() {}

func (x *PreviewPurchasesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPurchasesResult.ProtoReflect.Descriptor instead.
func (*PreviewPurchasesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{13}
}

func (x *PreviewPurchasesResult) GetPurchaseDetails() []*PurchaseDetailView {
	if x != nil {
		return x.PurchaseDetails
	}
	return nil
}

// ListEntitlements Params.
type ListEntitlementsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// feature key
	FeatureKeys []v1.Feature_Key `protobuf:"varint,2,rep,packed,name=feature_keys,json=featureKeys,proto3,enum=moego.models.subscription.v1.Feature_Key" json:"feature_keys,omitempty"`
	// license statuses
	LicenseStatuses []v1.License_Status `protobuf:"varint,3,rep,packed,name=license_statuses,json=licenseStatuses,proto3,enum=moego.models.subscription.v1.License_Status" json:"license_statuses,omitempty"`
}

func (x *ListEntitlementsParams) Reset() {
	*x = ListEntitlementsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEntitlementsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEntitlementsParams) ProtoMessage() {}

func (x *ListEntitlementsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEntitlementsParams.ProtoReflect.Descriptor instead.
func (*ListEntitlementsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{14}
}

func (x *ListEntitlementsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListEntitlementsParams) GetFeatureKeys() []v1.Feature_Key {
	if x != nil {
		return x.FeatureKeys
	}
	return nil
}

func (x *ListEntitlementsParams) GetLicenseStatuses() []v1.License_Status {
	if x != nil {
		return x.LicenseStatuses
	}
	return nil
}

// ListEntitlements Result.
type ListEntitlementsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// entitlements
	Entitlements []*v1.Entitlement `protobuf:"bytes,2,rep,name=entitlements,proto3" json:"entitlements,omitempty"`
}

func (x *ListEntitlementsResult) Reset() {
	*x = ListEntitlementsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEntitlementsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEntitlementsResult) ProtoMessage() {}

func (x *ListEntitlementsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEntitlementsResult.ProtoReflect.Descriptor instead.
func (*ListEntitlementsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListEntitlementsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListEntitlementsResult) GetEntitlements() []*v1.Entitlement {
	if x != nil {
		return x.Entitlements
	}
	return nil
}

// update points Params
type UpdateCreditParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// change points user
	User *v1.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// credit can be positive or negative
	// used to control increase/decrease
	Credit int64 `protobuf:"varint,2,opt,name=credit,proto3" json:"credit,omitempty"`
	// type
	Type v1.UpdateCredit_Type `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.subscription.v1.UpdateCredit_Type" json:"type,omitempty"`
	// reason, enum
	Reason *v1.UpdateCredit_Reason `protobuf:"varint,4,opt,name=reason,proto3,enum=moego.models.subscription.v1.UpdateCredit_Reason,oneof" json:"reason,omitempty"`
	// note, customize
	Note *string `protobuf:"bytes,5,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// invoice id
	InvoiceId *int64 `protobuf:"varint,6,opt,name=invoice_id,json=invoiceId,proto3,oneof" json:"invoice_id,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,7,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
}

func (x *UpdateCreditParams) Reset() {
	*x = UpdateCreditParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditParams) ProtoMessage() {}

func (x *UpdateCreditParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditParams.ProtoReflect.Descriptor instead.
func (*UpdateCreditParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateCreditParams) GetUser() *v1.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateCreditParams) GetCredit() int64 {
	if x != nil {
		return x.Credit
	}
	return 0
}

func (x *UpdateCreditParams) GetType() v1.UpdateCredit_Type {
	if x != nil {
		return x.Type
	}
	return v1.UpdateCredit_Type(0)
}

func (x *UpdateCreditParams) GetReason() v1.UpdateCredit_Reason {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return v1.UpdateCredit_Reason(0)
}

func (x *UpdateCreditParams) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *UpdateCreditParams) GetInvoiceId() int64 {
	if x != nil && x.InvoiceId != nil {
		return *x.InvoiceId
	}
	return 0
}

func (x *UpdateCreditParams) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

// update credit Result
type UpdateCreditResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateCreditResult) Reset() {
	*x = UpdateCreditResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCreditResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCreditResult) ProtoMessage() {}

func (x *UpdateCreditResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCreditResult.ProtoReflect.Descriptor instead.
func (*UpdateCreditResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateCreditResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// list credit history change history
type ListCreditChangeHistoryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v1.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,99,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCreditChangeHistoryParams) Reset() {
	*x = ListCreditChangeHistoryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCreditChangeHistoryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreditChangeHistoryParams) ProtoMessage() {}

func (x *ListCreditChangeHistoryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreditChangeHistoryParams.ProtoReflect.Descriptor instead.
func (*ListCreditChangeHistoryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{18}
}

func (x *ListCreditChangeHistoryParams) GetUser() *v1.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ListCreditChangeHistoryParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list credit change history result
type ListCreditChangeHistoryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// change history list
	Histories []*ListCreditChangeHistoryResult_CreditChangeHistory `protobuf:"bytes,1,rep,name=histories,proto3" json:"histories,omitempty"`
	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,99,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCreditChangeHistoryResult) Reset() {
	*x = ListCreditChangeHistoryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCreditChangeHistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreditChangeHistoryResult) ProtoMessage() {}

func (x *ListCreditChangeHistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreditChangeHistoryResult.ProtoReflect.Descriptor instead.
func (*ListCreditChangeHistoryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{19}
}

func (x *ListCreditChangeHistoryResult) GetHistories() []*ListCreditChangeHistoryResult_CreditChangeHistory {
	if x != nil {
		return x.Histories
	}
	return nil
}

func (x *ListCreditChangeHistoryResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetCreditParams
type GetCreditParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	User *v1.User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *GetCreditParams) Reset() {
	*x = GetCreditParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditParams) ProtoMessage() {}

func (x *GetCreditParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditParams.ProtoReflect.Descriptor instead.
func (*GetCreditParams) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetCreditParams) GetUser() *v1.User {
	if x != nil {
		return x.User
	}
	return nil
}

// GetCreditResult
type GetCreditResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// credit, the unit is cents
	Credit int64 `protobuf:"varint,1,opt,name=credit,proto3" json:"credit,omitempty"`
}

func (x *GetCreditResult) Reset() {
	*x = GetCreditResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditResult) ProtoMessage() {}

func (x *GetCreditResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditResult.ProtoReflect.Descriptor instead.
func (*GetCreditResult) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetCreditResult) GetCredit() int64 {
	if x != nil {
		return x.Credit
	}
	return 0
}

// credit change history
type ListCreditChangeHistoryResult_CreditChangeHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// credit, it needs to be an integer,
	// which can default to cents.
	// for example, if it's 1.22, please enter 122,
	// if it's 100, please enter 10000.
	Credit int64 `protobuf:"varint,2,opt,name=credit,proto3" json:"credit,omitempty"`
	// type
	Type v1.UpdateCredit_Type `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.subscription.v1.UpdateCredit_Type" json:"type,omitempty"`
	// reason
	Reason v1.UpdateCredit_Reason `protobuf:"varint,4,opt,name=reason,proto3,enum=moego.models.subscription.v1.UpdateCredit_Reason" json:"reason,omitempty"`
	// note
	Note string `protobuf:"bytes,5,opt,name=note,proto3" json:"note,omitempty"`
	// operator
	User *v1.User `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
	// association
	Association *ListCreditChangeHistoryResult_CreditChangeHistory_Association `protobuf:"bytes,7,opt,name=association,proto3" json:"association,omitempty"`
	// time
	CreatedTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) Reset() {
	*x = ListCreditChangeHistoryResult_CreditChangeHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreditChangeHistoryResult_CreditChangeHistory) ProtoMessage() {}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreditChangeHistoryResult_CreditChangeHistory.ProtoReflect.Descriptor instead.
func (*ListCreditChangeHistoryResult_CreditChangeHistory) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetCredit() int64 {
	if x != nil {
		return x.Credit
	}
	return 0
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetType() v1.UpdateCredit_Type {
	if x != nil {
		return x.Type
	}
	return v1.UpdateCredit_Type(0)
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetReason() v1.UpdateCredit_Reason {
	if x != nil {
		return x.Reason
	}
	return v1.UpdateCredit_Reason(0)
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetUser() *v1.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetAssociation() *ListCreditChangeHistoryResult_CreditChangeHistory_Association {
	if x != nil {
		return x.Association
	}
	return nil
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory) GetCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

// association
// for example, if the type is payment the invoice id not empty
type ListCreditChangeHistoryResult_CreditChangeHistory_Association struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory_Association) Reset() {
	*x = ListCreditChangeHistoryResult_CreditChangeHistory_Association{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory_Association) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreditChangeHistoryResult_CreditChangeHistory_Association) ProtoMessage() {}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory_Association) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_subscription_v1_subscription_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreditChangeHistoryResult_CreditChangeHistory_Association.ProtoReflect.Descriptor instead.
func (*ListCreditChangeHistoryResult_CreditChangeHistory_Association) Descriptor() ([]byte, []int) {
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{19, 0, 0}
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory_Association) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *ListCreditChangeHistoryResult_CreditChangeHistory_Association) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

var File_moego_api_subscription_v1_subscription_api_proto protoreflect.FileDescriptor

var file_moego_api_subscription_v1_subscription_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xeb, 0x03, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65,
	0x6c, 0x6c, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x08, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3c, 0x0a, 0x06, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x70, 0x72, 0x69, 0x63, 0x65, 0x73, 0x12, 0x40,
	0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x22, 0x8f, 0x04, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3c,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x0b,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63,
	0x6c, 0x65, 0x12, 0x60, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x2e, 0x50, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x10, 0x70, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x16, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x52, 0x15, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f,
	0x66, 0x66, 0x22, 0xe4, 0x04, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0c, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x67, 0x72, 0x61,
	0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0b, 0x67, 0x72, 0x61,
	0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x05, 0x62, 0x75, 0x79,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12,
	0x58, 0x0a, 0x10, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xf6, 0x03, 0x0a, 0x12, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x3a, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x09, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x44,
	0x0a, 0x14, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x52, 0x15, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f,
	0x66, 0x66, 0x22, 0xc4, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x0e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x44, 0x0a, 0x09, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x52, 0x09, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x0f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x18,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x1d, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0xb0, 0x02, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x71, 0x0a, 0x1b, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x18, 0x70, 0x6c, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0xb0, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x51, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x43, 0x0a, 0x18, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x18, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5e, 0x0a, 0x16, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x44,
	0x0a, 0x09, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x52, 0x09, 0x70, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x73, 0x65, 0x73, 0x22, 0x72, 0x0a, 0x16, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58,
	0x0a, 0x10, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x82, 0x02, 0x0a, 0x16, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x0b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x4b, 0x65, 0x79, 0x73, 0x12, 0x57, 0x0a, 0x10, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x22, 0xab, 0x01,
	0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0c,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x98, 0x03, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x6e, 0x6f, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x9a, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x63,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xfb, 0x05, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x09, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x09, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xa9, 0x04, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x7a, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x53, 0x0a, 0x0b,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x49, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x29, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x32, 0xd7, 0x08, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x6c, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7e, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a,
	0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7e, 0x0a, 0x12, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x78, 0x0a, 0x10, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x78, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6c,
	0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8d, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x63, 0x0a, 0x09,
	0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x87, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_subscription_v1_subscription_api_proto_rawDescOnce sync.Once
	file_moego_api_subscription_v1_subscription_api_proto_rawDescData = file_moego_api_subscription_v1_subscription_api_proto_rawDesc
)

func file_moego_api_subscription_v1_subscription_api_proto_rawDescGZIP() []byte {
	file_moego_api_subscription_v1_subscription_api_proto_rawDescOnce.Do(func() {
		file_moego_api_subscription_v1_subscription_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_subscription_v1_subscription_api_proto_rawDescData)
	})
	return file_moego_api_subscription_v1_subscription_api_proto_rawDescData
}

var file_moego_api_subscription_v1_subscription_api_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_api_subscription_v1_subscription_api_proto_goTypes = []interface{}{
	(*ProductView)(nil),                                                   // 0: moego.api.subscription.v1.ProductView
	(*PriceView)(nil),                                                     // 1: moego.api.subscription.v1.PriceView
	(*SubscriptionView)(nil),                                              // 2: moego.api.subscription.v1.SubscriptionView
	(*PurchaseDetailView)(nil),                                            // 3: moego.api.subscription.v1.PurchaseDetailView
	(*ListProductsParams)(nil),                                            // 4: moego.api.subscription.v1.ListProductsParams
	(*ListProductsResult)(nil),                                            // 5: moego.api.subscription.v1.ListProductsResult
	(*CreateSubscriptionParams)(nil),                                      // 6: moego.api.subscription.v1.CreateSubscriptionParams
	(*CreateSubscriptionResult)(nil),                                      // 7: moego.api.subscription.v1.CreateSubscriptionResult
	(*ListSubscriptionsParams)(nil),                                       // 8: moego.api.subscription.v1.ListSubscriptionsParams
	(*ListSubscriptionsResult)(nil),                                       // 9: moego.api.subscription.v1.ListSubscriptionsResult
	(*CancelSubscriptionParams)(nil),                                      // 10: moego.api.subscription.v1.CancelSubscriptionParams
	(*CancelSubscriptionResult)(nil),                                      // 11: moego.api.subscription.v1.CancelSubscriptionResult
	(*PreviewPurchasesParams)(nil),                                        // 12: moego.api.subscription.v1.PreviewPurchasesParams
	(*PreviewPurchasesResult)(nil),                                        // 13: moego.api.subscription.v1.PreviewPurchasesResult
	(*ListEntitlementsParams)(nil),                                        // 14: moego.api.subscription.v1.ListEntitlementsParams
	(*ListEntitlementsResult)(nil),                                        // 15: moego.api.subscription.v1.ListEntitlementsResult
	(*UpdateCreditParams)(nil),                                            // 16: moego.api.subscription.v1.UpdateCreditParams
	(*UpdateCreditResult)(nil),                                            // 17: moego.api.subscription.v1.UpdateCreditResult
	(*ListCreditChangeHistoryParams)(nil),                                 // 18: moego.api.subscription.v1.ListCreditChangeHistoryParams
	(*ListCreditChangeHistoryResult)(nil),                                 // 19: moego.api.subscription.v1.ListCreditChangeHistoryResult
	(*GetCreditParams)(nil),                                               // 20: moego.api.subscription.v1.GetCreditParams
	(*GetCreditResult)(nil),                                               // 21: moego.api.subscription.v1.GetCreditResult
	(*ListCreditChangeHistoryResult_CreditChangeHistory)(nil),             // 22: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory
	(*ListCreditChangeHistoryResult_CreditChangeHistory_Association)(nil), // 23: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.Association
	(v1.Product_Type)(0),                                                  // 24: moego.models.subscription.v1.Product.Type
	(*v1.User)(nil),                                                       // 25: moego.models.subscription.v1.User
	(*v1.Feature)(nil),                                                    // 26: moego.models.subscription.v1.Feature
	(v1.Product_BusinessType)(0),                                          // 27: moego.models.subscription.v1.Product.BusinessType
	(*v1.ProductExtra)(nil),                                               // 28: moego.models.subscription.v1.ProductExtra
	(v1.Price_Type)(0),                                                    // 29: moego.models.subscription.v1.Price.Type
	(*money.Money)(nil),                                                   // 30: google.type.Money
	(*v11.TimePeriod)(nil),                                                // 31: moego.utils.v1.TimePeriod
	(*v1.Price_Prequalification)(nil),                                     // 32: moego.models.subscription.v1.Price.Prequalification
	(*v1.Discount)(nil),                                                   // 33: moego.models.subscription.v1.Discount
	(*decimal.Decimal)(nil),                                               // 34: google.type.Decimal
	(v1.Subscription_Status)(0),                                           // 35: moego.models.subscription.v1.Subscription.Status
	(*interval.Interval)(nil),                                             // 36: google.type.Interval
	(*v2.PaginationRequest)(nil),                                          // 37: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                         // 38: moego.utils.v2.PaginationResponse
	(*v1.Purchase)(nil),                                                   // 39: moego.models.subscription.v1.Purchase
	(v1.Feature_Key)(0),                                                   // 40: moego.models.subscription.v1.Feature.Key
	(v1.License_Status)(0),                                                // 41: moego.models.subscription.v1.License.Status
	(*v1.Entitlement)(nil),                                                // 42: moego.models.subscription.v1.Entitlement
	(v1.UpdateCredit_Type)(0),                                             // 43: moego.models.subscription.v1.UpdateCredit.Type
	(v1.UpdateCredit_Reason)(0),                                           // 44: moego.models.subscription.v1.UpdateCredit.Reason
	(*timestamppb.Timestamp)(nil),                                         // 45: google.protobuf.Timestamp
}
var file_moego_api_subscription_v1_subscription_api_proto_depIdxs = []int32{
	24, // 0: moego.api.subscription.v1.ProductView.type:type_name -> moego.models.subscription.v1.Product.Type
	25, // 1: moego.api.subscription.v1.ProductView.seller:type_name -> moego.models.subscription.v1.User
	26, // 2: moego.api.subscription.v1.ProductView.features:type_name -> moego.models.subscription.v1.Feature
	27, // 3: moego.api.subscription.v1.ProductView.business_type:type_name -> moego.models.subscription.v1.Product.BusinessType
	1,  // 4: moego.api.subscription.v1.ProductView.prices:type_name -> moego.api.subscription.v1.PriceView
	28, // 5: moego.api.subscription.v1.ProductView.extra:type_name -> moego.models.subscription.v1.ProductExtra
	29, // 6: moego.api.subscription.v1.PriceView.type:type_name -> moego.models.subscription.v1.Price.Type
	30, // 7: moego.api.subscription.v1.PriceView.unit_amount:type_name -> google.type.Money
	31, // 8: moego.api.subscription.v1.PriceView.billing_cycle:type_name -> moego.utils.v1.TimePeriod
	32, // 9: moego.api.subscription.v1.PriceView.prequalification:type_name -> moego.models.subscription.v1.Price.Prequalification
	33, // 10: moego.api.subscription.v1.PriceView.discounts:type_name -> moego.models.subscription.v1.Discount
	30, // 11: moego.api.subscription.v1.PriceView.discounted_unit_amount:type_name -> google.type.Money
	34, // 12: moego.api.subscription.v1.PriceView.discount_percentage_off:type_name -> google.type.Decimal
	35, // 13: moego.api.subscription.v1.SubscriptionView.status:type_name -> moego.models.subscription.v1.Subscription.Status
	36, // 14: moego.api.subscription.v1.SubscriptionView.validity_period:type_name -> google.type.Interval
	31, // 15: moego.api.subscription.v1.SubscriptionView.billing_cycle:type_name -> moego.utils.v1.TimePeriod
	31, // 16: moego.api.subscription.v1.SubscriptionView.grace_period:type_name -> moego.utils.v1.TimePeriod
	25, // 17: moego.api.subscription.v1.SubscriptionView.buyer:type_name -> moego.models.subscription.v1.User
	25, // 18: moego.api.subscription.v1.SubscriptionView.seller:type_name -> moego.models.subscription.v1.User
	3,  // 19: moego.api.subscription.v1.SubscriptionView.purchase_details:type_name -> moego.api.subscription.v1.PurchaseDetailView
	0,  // 20: moego.api.subscription.v1.PurchaseDetailView.product:type_name -> moego.api.subscription.v1.ProductView
	1,  // 21: moego.api.subscription.v1.PurchaseDetailView.price:type_name -> moego.api.subscription.v1.PriceView
	30, // 22: moego.api.subscription.v1.PurchaseDetailView.total_price:type_name -> google.type.Money
	33, // 23: moego.api.subscription.v1.PurchaseDetailView.discounts:type_name -> moego.models.subscription.v1.Discount
	30, // 24: moego.api.subscription.v1.PurchaseDetailView.original_total_price:type_name -> google.type.Money
	34, // 25: moego.api.subscription.v1.PurchaseDetailView.discount_percentage_off:type_name -> google.type.Decimal
	37, // 26: moego.api.subscription.v1.ListProductsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	27, // 27: moego.api.subscription.v1.ListProductsParams.business_types:type_name -> moego.models.subscription.v1.Product.BusinessType
	38, // 28: moego.api.subscription.v1.ListProductsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 29: moego.api.subscription.v1.ListProductsResult.products:type_name -> moego.api.subscription.v1.ProductView
	39, // 30: moego.api.subscription.v1.CreateSubscriptionParams.purchases:type_name -> moego.models.subscription.v1.Purchase
	2,  // 31: moego.api.subscription.v1.CreateSubscriptionResult.subscription:type_name -> moego.api.subscription.v1.SubscriptionView
	37, // 32: moego.api.subscription.v1.ListSubscriptionsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	35, // 33: moego.api.subscription.v1.ListSubscriptionsParams.statuses:type_name -> moego.models.subscription.v1.Subscription.Status
	27, // 34: moego.api.subscription.v1.ListSubscriptionsParams.plan_product_business_types:type_name -> moego.models.subscription.v1.Product.BusinessType
	38, // 35: moego.api.subscription.v1.ListSubscriptionsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	2,  // 36: moego.api.subscription.v1.ListSubscriptionsResult.subscriptions:type_name -> moego.api.subscription.v1.SubscriptionView
	2,  // 37: moego.api.subscription.v1.CancelSubscriptionResult.subscription:type_name -> moego.api.subscription.v1.SubscriptionView
	39, // 38: moego.api.subscription.v1.PreviewPurchasesParams.purchases:type_name -> moego.models.subscription.v1.Purchase
	3,  // 39: moego.api.subscription.v1.PreviewPurchasesResult.purchase_details:type_name -> moego.api.subscription.v1.PurchaseDetailView
	37, // 40: moego.api.subscription.v1.ListEntitlementsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	40, // 41: moego.api.subscription.v1.ListEntitlementsParams.feature_keys:type_name -> moego.models.subscription.v1.Feature.Key
	41, // 42: moego.api.subscription.v1.ListEntitlementsParams.license_statuses:type_name -> moego.models.subscription.v1.License.Status
	38, // 43: moego.api.subscription.v1.ListEntitlementsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	42, // 44: moego.api.subscription.v1.ListEntitlementsResult.entitlements:type_name -> moego.models.subscription.v1.Entitlement
	25, // 45: moego.api.subscription.v1.UpdateCreditParams.user:type_name -> moego.models.subscription.v1.User
	43, // 46: moego.api.subscription.v1.UpdateCreditParams.type:type_name -> moego.models.subscription.v1.UpdateCredit.Type
	44, // 47: moego.api.subscription.v1.UpdateCreditParams.reason:type_name -> moego.models.subscription.v1.UpdateCredit.Reason
	25, // 48: moego.api.subscription.v1.ListCreditChangeHistoryParams.user:type_name -> moego.models.subscription.v1.User
	37, // 49: moego.api.subscription.v1.ListCreditChangeHistoryParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	22, // 50: moego.api.subscription.v1.ListCreditChangeHistoryResult.histories:type_name -> moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory
	38, // 51: moego.api.subscription.v1.ListCreditChangeHistoryResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	25, // 52: moego.api.subscription.v1.GetCreditParams.user:type_name -> moego.models.subscription.v1.User
	43, // 53: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.type:type_name -> moego.models.subscription.v1.UpdateCredit.Type
	44, // 54: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.reason:type_name -> moego.models.subscription.v1.UpdateCredit.Reason
	25, // 55: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.user:type_name -> moego.models.subscription.v1.User
	23, // 56: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.association:type_name -> moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.Association
	45, // 57: moego.api.subscription.v1.ListCreditChangeHistoryResult.CreditChangeHistory.created_time:type_name -> google.protobuf.Timestamp
	4,  // 58: moego.api.subscription.v1.SubscriptionService.ListProducts:input_type -> moego.api.subscription.v1.ListProductsParams
	6,  // 59: moego.api.subscription.v1.SubscriptionService.CreateSubscription:input_type -> moego.api.subscription.v1.CreateSubscriptionParams
	8,  // 60: moego.api.subscription.v1.SubscriptionService.ListSubscriptions:input_type -> moego.api.subscription.v1.ListSubscriptionsParams
	10, // 61: moego.api.subscription.v1.SubscriptionService.CancelSubscription:input_type -> moego.api.subscription.v1.CancelSubscriptionParams
	12, // 62: moego.api.subscription.v1.SubscriptionService.PreviewPurchases:input_type -> moego.api.subscription.v1.PreviewPurchasesParams
	14, // 63: moego.api.subscription.v1.SubscriptionService.ListEntitlements:input_type -> moego.api.subscription.v1.ListEntitlementsParams
	16, // 64: moego.api.subscription.v1.SubscriptionService.UpdateCredit:input_type -> moego.api.subscription.v1.UpdateCreditParams
	18, // 65: moego.api.subscription.v1.SubscriptionService.ListCreditChangeHistory:input_type -> moego.api.subscription.v1.ListCreditChangeHistoryParams
	20, // 66: moego.api.subscription.v1.SubscriptionService.GetCredit:input_type -> moego.api.subscription.v1.GetCreditParams
	5,  // 67: moego.api.subscription.v1.SubscriptionService.ListProducts:output_type -> moego.api.subscription.v1.ListProductsResult
	7,  // 68: moego.api.subscription.v1.SubscriptionService.CreateSubscription:output_type -> moego.api.subscription.v1.CreateSubscriptionResult
	9,  // 69: moego.api.subscription.v1.SubscriptionService.ListSubscriptions:output_type -> moego.api.subscription.v1.ListSubscriptionsResult
	11, // 70: moego.api.subscription.v1.SubscriptionService.CancelSubscription:output_type -> moego.api.subscription.v1.CancelSubscriptionResult
	13, // 71: moego.api.subscription.v1.SubscriptionService.PreviewPurchases:output_type -> moego.api.subscription.v1.PreviewPurchasesResult
	15, // 72: moego.api.subscription.v1.SubscriptionService.ListEntitlements:output_type -> moego.api.subscription.v1.ListEntitlementsResult
	17, // 73: moego.api.subscription.v1.SubscriptionService.UpdateCredit:output_type -> moego.api.subscription.v1.UpdateCreditResult
	19, // 74: moego.api.subscription.v1.SubscriptionService.ListCreditChangeHistory:output_type -> moego.api.subscription.v1.ListCreditChangeHistoryResult
	21, // 75: moego.api.subscription.v1.SubscriptionService.GetCredit:output_type -> moego.api.subscription.v1.GetCreditResult
	67, // [67:76] is the sub-list for method output_type
	58, // [58:67] is the sub-list for method input_type
	58, // [58:58] is the sub-list for extension type_name
	58, // [58:58] is the sub-list for extension extendee
	0,  // [0:58] is the sub-list for field type_name
}

func init() { file_moego_api_subscription_v1_subscription_api_proto_init() }
func file_moego_api_subscription_v1_subscription_api_proto_init() {
	if File_moego_api_subscription_v1_subscription_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseDetailView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListProductsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListProductsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPurchasesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPurchasesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEntitlementsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEntitlementsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCreditResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCreditChangeHistoryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCreditChangeHistoryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCreditChangeHistoryResult_CreditChangeHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_subscription_v1_subscription_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCreditChangeHistoryResult_CreditChangeHistory_Association); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_subscription_v1_subscription_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_subscription_v1_subscription_api_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_subscription_v1_subscription_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_subscription_v1_subscription_api_proto_goTypes,
		DependencyIndexes: file_moego_api_subscription_v1_subscription_api_proto_depIdxs,
		MessageInfos:      file_moego_api_subscription_v1_subscription_api_proto_msgTypes,
	}.Build()
	File_moego_api_subscription_v1_subscription_api_proto = out.File
	file_moego_api_subscription_v1_subscription_api_proto_rawDesc = nil
	file_moego_api_subscription_v1_subscription_api_proto_goTypes = nil
	file_moego_api_subscription_v1_subscription_api_proto_depIdxs = nil
}
