// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/pet_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetServiceClient is the client API for PetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetServiceClient interface {
	// CreatePet creates a new pet.
	CreatePet(ctx context.Context, in *CreatePetParams, opts ...grpc.CallOption) (*CreatePetResult, error)
	// GetPet gets a pet by id.
	GetPet(ctx context.Context, in *GetPetParams, opts ...grpc.CallOption) (*GetPetResult, error)
	// ListPets lists all pets.
	ListPets(ctx context.Context, in *ListPetsParams, opts ...grpc.CallOption) (*ListPetsResult, error)
	// UpdatePet updates a pet by id.
	UpdatePet(ctx context.Context, in *UpdatePetParams, opts ...grpc.CallOption) (*UpdatePetResult, error)
	// submit pet vaccine request
	SubmitPetVaccineRequest(ctx context.Context, in *SubmitPetVaccineRequestParams, opts ...grpc.CallOption) (*SubmitPetVaccineRequestResult, error)
	// list pet vaccine expiration status
	ListPetVaccineExpirationStatus(ctx context.Context, in *ListPetVaccineExpirationStatusParams, opts ...grpc.CallOption) (*ListPetVaccineExpirationStatusResult, error)
}

type petServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetServiceClient(cc grpc.ClientConnInterface) PetServiceClient {
	return &petServiceClient{cc}
}

func (c *petServiceClient) CreatePet(ctx context.Context, in *CreatePetParams, opts ...grpc.CallOption) (*CreatePetResult, error) {
	out := new(CreatePetResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/CreatePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) GetPet(ctx context.Context, in *GetPetParams, opts ...grpc.CallOption) (*GetPetResult, error) {
	out := new(GetPetResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/GetPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) ListPets(ctx context.Context, in *ListPetsParams, opts ...grpc.CallOption) (*ListPetsResult, error) {
	out := new(ListPetsResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/ListPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) UpdatePet(ctx context.Context, in *UpdatePetParams, opts ...grpc.CallOption) (*UpdatePetResult, error) {
	out := new(UpdatePetResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/UpdatePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) SubmitPetVaccineRequest(ctx context.Context, in *SubmitPetVaccineRequestParams, opts ...grpc.CallOption) (*SubmitPetVaccineRequestResult, error) {
	out := new(SubmitPetVaccineRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/SubmitPetVaccineRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) ListPetVaccineExpirationStatus(ctx context.Context, in *ListPetVaccineExpirationStatusParams, opts ...grpc.CallOption) (*ListPetVaccineExpirationStatusResult, error) {
	out := new(ListPetVaccineExpirationStatusResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.PetService/ListPetVaccineExpirationStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetServiceServer is the server API for PetService service.
// All implementations must embed UnimplementedPetServiceServer
// for forward compatibility
type PetServiceServer interface {
	// CreatePet creates a new pet.
	CreatePet(context.Context, *CreatePetParams) (*CreatePetResult, error)
	// GetPet gets a pet by id.
	GetPet(context.Context, *GetPetParams) (*GetPetResult, error)
	// ListPets lists all pets.
	ListPets(context.Context, *ListPetsParams) (*ListPetsResult, error)
	// UpdatePet updates a pet by id.
	UpdatePet(context.Context, *UpdatePetParams) (*UpdatePetResult, error)
	// submit pet vaccine request
	SubmitPetVaccineRequest(context.Context, *SubmitPetVaccineRequestParams) (*SubmitPetVaccineRequestResult, error)
	// list pet vaccine expiration status
	ListPetVaccineExpirationStatus(context.Context, *ListPetVaccineExpirationStatusParams) (*ListPetVaccineExpirationStatusResult, error)
	mustEmbedUnimplementedPetServiceServer()
}

// UnimplementedPetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetServiceServer struct {
}

func (UnimplementedPetServiceServer) CreatePet(context.Context, *CreatePetParams) (*CreatePetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePet not implemented")
}
func (UnimplementedPetServiceServer) GetPet(context.Context, *GetPetParams) (*GetPetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPet not implemented")
}
func (UnimplementedPetServiceServer) ListPets(context.Context, *ListPetsParams) (*ListPetsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPets not implemented")
}
func (UnimplementedPetServiceServer) UpdatePet(context.Context, *UpdatePetParams) (*UpdatePetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePet not implemented")
}
func (UnimplementedPetServiceServer) SubmitPetVaccineRequest(context.Context, *SubmitPetVaccineRequestParams) (*SubmitPetVaccineRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitPetVaccineRequest not implemented")
}
func (UnimplementedPetServiceServer) ListPetVaccineExpirationStatus(context.Context, *ListPetVaccineExpirationStatusParams) (*ListPetVaccineExpirationStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetVaccineExpirationStatus not implemented")
}
func (UnimplementedPetServiceServer) mustEmbedUnimplementedPetServiceServer() {}

// UnsafePetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetServiceServer will
// result in compilation errors.
type UnsafePetServiceServer interface {
	mustEmbedUnimplementedPetServiceServer()
}

func RegisterPetServiceServer(s grpc.ServiceRegistrar, srv PetServiceServer) {
	s.RegisterService(&PetService_ServiceDesc, srv)
}

func _PetService_CreatePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).CreatePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/CreatePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).CreatePet(ctx, req.(*CreatePetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_GetPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).GetPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/GetPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).GetPet(ctx, req.(*GetPetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_ListPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).ListPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/ListPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).ListPets(ctx, req.(*ListPetsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_UpdatePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).UpdatePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/UpdatePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).UpdatePet(ctx, req.(*UpdatePetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_SubmitPetVaccineRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitPetVaccineRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).SubmitPetVaccineRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/SubmitPetVaccineRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).SubmitPetVaccineRequest(ctx, req.(*SubmitPetVaccineRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_ListPetVaccineExpirationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetVaccineExpirationStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).ListPetVaccineExpirationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.PetService/ListPetVaccineExpirationStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).ListPetVaccineExpirationStatus(ctx, req.(*ListPetVaccineExpirationStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PetService_ServiceDesc is the grpc.ServiceDesc for PetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.PetService",
	HandlerType: (*PetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePet",
			Handler:    _PetService_CreatePet_Handler,
		},
		{
			MethodName: "GetPet",
			Handler:    _PetService_GetPet_Handler,
		},
		{
			MethodName: "ListPets",
			Handler:    _PetService_ListPets_Handler,
		},
		{
			MethodName: "UpdatePet",
			Handler:    _PetService_UpdatePet_Handler,
		},
		{
			MethodName: "SubmitPetVaccineRequest",
			Handler:    _PetService_SubmitPetVaccineRequest_Handler,
		},
		{
			MethodName: "ListPetVaccineExpirationStatus",
			Handler:    _PetService_ListPetVaccineExpirationStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/pet_api.proto",
}
