// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/business_customer_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerServiceClient is the client API for BusinessCustomerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerServiceClient interface {
	// Create customer
	CreateCustomer(ctx context.Context, in *CreateCustomerParams, opts ...grpc.CallOption) (*CreateCustomerResult, error)
	// Update customer
	UpdateCustomer(ctx context.Context, in *UpdateCustomerParams, opts ...grpc.CallOption) (*UpdateCustomerResult, error)
	// Verify phone number
	VerifyPhoneNumber(ctx context.Context, in *VerifyPhoneNumberParams, opts ...grpc.CallOption) (*VerifyPhoneNumberResult, error)
	// Confirm phone number
	ConfirmPhoneNumber(ctx context.Context, in *ConfirmPhoneNumberParams, opts ...grpc.CallOption) (*ConfirmPhoneNumberResult, error)
	// Change login phone number
	ChangeLoginPhoneNumber(ctx context.Context, in *ChangeLoginPhoneNumberParams, opts ...grpc.CallOption) (*ChangeLoginPhoneNumberResult, error)
}

type businessCustomerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerServiceClient(cc grpc.ClientConnInterface) BusinessCustomerServiceClient {
	return &businessCustomerServiceClient{cc}
}

func (c *businessCustomerServiceClient) CreateCustomer(ctx context.Context, in *CreateCustomerParams, opts ...grpc.CallOption) (*CreateCustomerResult, error) {
	out := new(CreateCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BusinessCustomerService/CreateCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) UpdateCustomer(ctx context.Context, in *UpdateCustomerParams, opts ...grpc.CallOption) (*UpdateCustomerResult, error) {
	out := new(UpdateCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BusinessCustomerService/UpdateCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) VerifyPhoneNumber(ctx context.Context, in *VerifyPhoneNumberParams, opts ...grpc.CallOption) (*VerifyPhoneNumberResult, error) {
	out := new(VerifyPhoneNumberResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BusinessCustomerService/VerifyPhoneNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) ConfirmPhoneNumber(ctx context.Context, in *ConfirmPhoneNumberParams, opts ...grpc.CallOption) (*ConfirmPhoneNumberResult, error) {
	out := new(ConfirmPhoneNumberResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BusinessCustomerService/ConfirmPhoneNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) ChangeLoginPhoneNumber(ctx context.Context, in *ChangeLoginPhoneNumberParams, opts ...grpc.CallOption) (*ChangeLoginPhoneNumberResult, error) {
	out := new(ChangeLoginPhoneNumberResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BusinessCustomerService/ChangeLoginPhoneNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerServiceServer is the server API for BusinessCustomerService service.
// All implementations must embed UnimplementedBusinessCustomerServiceServer
// for forward compatibility
type BusinessCustomerServiceServer interface {
	// Create customer
	CreateCustomer(context.Context, *CreateCustomerParams) (*CreateCustomerResult, error)
	// Update customer
	UpdateCustomer(context.Context, *UpdateCustomerParams) (*UpdateCustomerResult, error)
	// Verify phone number
	VerifyPhoneNumber(context.Context, *VerifyPhoneNumberParams) (*VerifyPhoneNumberResult, error)
	// Confirm phone number
	ConfirmPhoneNumber(context.Context, *ConfirmPhoneNumberParams) (*ConfirmPhoneNumberResult, error)
	// Change login phone number
	ChangeLoginPhoneNumber(context.Context, *ChangeLoginPhoneNumberParams) (*ChangeLoginPhoneNumberResult, error)
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

// UnimplementedBusinessCustomerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerServiceServer struct {
}

func (UnimplementedBusinessCustomerServiceServer) CreateCustomer(context.Context, *CreateCustomerParams) (*CreateCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomer not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) UpdateCustomer(context.Context, *UpdateCustomerParams) (*UpdateCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomer not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) VerifyPhoneNumber(context.Context, *VerifyPhoneNumberParams) (*VerifyPhoneNumberResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyPhoneNumber not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) ConfirmPhoneNumber(context.Context, *ConfirmPhoneNumberParams) (*ConfirmPhoneNumberResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmPhoneNumber not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) ChangeLoginPhoneNumber(context.Context, *ChangeLoginPhoneNumberParams) (*ChangeLoginPhoneNumberResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeLoginPhoneNumber not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) mustEmbedUnimplementedBusinessCustomerServiceServer() {
}

// UnsafeBusinessCustomerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

func RegisterBusinessCustomerServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerServiceServer) {
	s.RegisterService(&BusinessCustomerService_ServiceDesc, srv)
}

func _BusinessCustomerService_CreateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).CreateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BusinessCustomerService/CreateCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).CreateCustomer(ctx, req.(*CreateCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_UpdateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).UpdateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BusinessCustomerService/UpdateCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).UpdateCustomer(ctx, req.(*UpdateCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_VerifyPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyPhoneNumberParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).VerifyPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BusinessCustomerService/VerifyPhoneNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).VerifyPhoneNumber(ctx, req.(*VerifyPhoneNumberParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_ConfirmPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmPhoneNumberParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).ConfirmPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BusinessCustomerService/ConfirmPhoneNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).ConfirmPhoneNumber(ctx, req.(*ConfirmPhoneNumberParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_ChangeLoginPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeLoginPhoneNumberParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).ChangeLoginPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BusinessCustomerService/ChangeLoginPhoneNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).ChangeLoginPhoneNumber(ctx, req.(*ChangeLoginPhoneNumberParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.BusinessCustomerService",
	HandlerType: (*BusinessCustomerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomer",
			Handler:    _BusinessCustomerService_CreateCustomer_Handler,
		},
		{
			MethodName: "UpdateCustomer",
			Handler:    _BusinessCustomerService_UpdateCustomer_Handler,
		},
		{
			MethodName: "VerifyPhoneNumber",
			Handler:    _BusinessCustomerService_VerifyPhoneNumber_Handler,
		},
		{
			MethodName: "ConfirmPhoneNumber",
			Handler:    _BusinessCustomerService_ConfirmPhoneNumber_Handler,
		},
		{
			MethodName: "ChangeLoginPhoneNumber",
			Handler:    _BusinessCustomerService_ChangeLoginPhoneNumber_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/business_customer_api.proto",
}
