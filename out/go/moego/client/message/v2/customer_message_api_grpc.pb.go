// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/message/v2/customer_message_api.proto

package messageapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CustomerMessageServiceClient is the client API for CustomerMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomerMessageServiceClient interface {
	// 向会话发送消息
	SendMessage(ctx context.Context, in *SendMessageParams, opts ...grpc.CallOption) (*SendMessageResult, error)
	// 获取对话列表
	ListChat(ctx context.Context, in *ListChatParams, opts ...grpc.CallOption) (*ListChatResult, error)
	// 获取会话的历史消息
	ListChatHistoricalMessage(ctx context.Context, in *ListChatHistoricalMessageParams, opts ...grpc.CallOption) (*ListChatMessageResult, error)
	// 获取会话的新消息
	ListChatNewMessage(ctx context.Context, in *ListChatNewMessageParams, opts ...grpc.CallOption) (*ListChatMessageResult, error)
	// 获取所有会话的未读消息数量
	GetAllChatUnreadMessageCount(ctx context.Context, in *GetAllChatUnreadMessageCountParams, opts ...grpc.CallOption) (*GetAllChatUnreadMessageCountResult, error)
}

type customerMessageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerMessageServiceClient(cc grpc.ClientConnInterface) CustomerMessageServiceClient {
	return &customerMessageServiceClient{cc}
}

func (c *customerMessageServiceClient) SendMessage(ctx context.Context, in *SendMessageParams, opts ...grpc.CallOption) (*SendMessageResult, error) {
	out := new(SendMessageResult)
	err := c.cc.Invoke(ctx, "/moego.client.message.v2.CustomerMessageService/SendMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerMessageServiceClient) ListChat(ctx context.Context, in *ListChatParams, opts ...grpc.CallOption) (*ListChatResult, error) {
	out := new(ListChatResult)
	err := c.cc.Invoke(ctx, "/moego.client.message.v2.CustomerMessageService/ListChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerMessageServiceClient) ListChatHistoricalMessage(ctx context.Context, in *ListChatHistoricalMessageParams, opts ...grpc.CallOption) (*ListChatMessageResult, error) {
	out := new(ListChatMessageResult)
	err := c.cc.Invoke(ctx, "/moego.client.message.v2.CustomerMessageService/ListChatHistoricalMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerMessageServiceClient) ListChatNewMessage(ctx context.Context, in *ListChatNewMessageParams, opts ...grpc.CallOption) (*ListChatMessageResult, error) {
	out := new(ListChatMessageResult)
	err := c.cc.Invoke(ctx, "/moego.client.message.v2.CustomerMessageService/ListChatNewMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerMessageServiceClient) GetAllChatUnreadMessageCount(ctx context.Context, in *GetAllChatUnreadMessageCountParams, opts ...grpc.CallOption) (*GetAllChatUnreadMessageCountResult, error) {
	out := new(GetAllChatUnreadMessageCountResult)
	err := c.cc.Invoke(ctx, "/moego.client.message.v2.CustomerMessageService/GetAllChatUnreadMessageCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerMessageServiceServer is the server API for CustomerMessageService service.
// All implementations must embed UnimplementedCustomerMessageServiceServer
// for forward compatibility
type CustomerMessageServiceServer interface {
	// 向会话发送消息
	SendMessage(context.Context, *SendMessageParams) (*SendMessageResult, error)
	// 获取对话列表
	ListChat(context.Context, *ListChatParams) (*ListChatResult, error)
	// 获取会话的历史消息
	ListChatHistoricalMessage(context.Context, *ListChatHistoricalMessageParams) (*ListChatMessageResult, error)
	// 获取会话的新消息
	ListChatNewMessage(context.Context, *ListChatNewMessageParams) (*ListChatMessageResult, error)
	// 获取所有会话的未读消息数量
	GetAllChatUnreadMessageCount(context.Context, *GetAllChatUnreadMessageCountParams) (*GetAllChatUnreadMessageCountResult, error)
	mustEmbedUnimplementedCustomerMessageServiceServer()
}

// UnimplementedCustomerMessageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCustomerMessageServiceServer struct {
}

func (UnimplementedCustomerMessageServiceServer) SendMessage(context.Context, *SendMessageParams) (*SendMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedCustomerMessageServiceServer) ListChat(context.Context, *ListChatParams) (*ListChatResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChat not implemented")
}
func (UnimplementedCustomerMessageServiceServer) ListChatHistoricalMessage(context.Context, *ListChatHistoricalMessageParams) (*ListChatMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChatHistoricalMessage not implemented")
}
func (UnimplementedCustomerMessageServiceServer) ListChatNewMessage(context.Context, *ListChatNewMessageParams) (*ListChatMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListChatNewMessage not implemented")
}
func (UnimplementedCustomerMessageServiceServer) GetAllChatUnreadMessageCount(context.Context, *GetAllChatUnreadMessageCountParams) (*GetAllChatUnreadMessageCountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllChatUnreadMessageCount not implemented")
}
func (UnimplementedCustomerMessageServiceServer) mustEmbedUnimplementedCustomerMessageServiceServer() {
}

// UnsafeCustomerMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerMessageServiceServer will
// result in compilation errors.
type UnsafeCustomerMessageServiceServer interface {
	mustEmbedUnimplementedCustomerMessageServiceServer()
}

func RegisterCustomerMessageServiceServer(s grpc.ServiceRegistrar, srv CustomerMessageServiceServer) {
	s.RegisterService(&CustomerMessageService_ServiceDesc, srv)
}

func _CustomerMessageService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerMessageServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.message.v2.CustomerMessageService/SendMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerMessageServiceServer).SendMessage(ctx, req.(*SendMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerMessageService_ListChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerMessageServiceServer).ListChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.message.v2.CustomerMessageService/ListChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerMessageServiceServer).ListChat(ctx, req.(*ListChatParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerMessageService_ListChatHistoricalMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatHistoricalMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerMessageServiceServer).ListChatHistoricalMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.message.v2.CustomerMessageService/ListChatHistoricalMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerMessageServiceServer).ListChatHistoricalMessage(ctx, req.(*ListChatHistoricalMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerMessageService_ListChatNewMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChatNewMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerMessageServiceServer).ListChatNewMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.message.v2.CustomerMessageService/ListChatNewMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerMessageServiceServer).ListChatNewMessage(ctx, req.(*ListChatNewMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerMessageService_GetAllChatUnreadMessageCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChatUnreadMessageCountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerMessageServiceServer).GetAllChatUnreadMessageCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.message.v2.CustomerMessageService/GetAllChatUnreadMessageCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerMessageServiceServer).GetAllChatUnreadMessageCount(ctx, req.(*GetAllChatUnreadMessageCountParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerMessageService_ServiceDesc is the grpc.ServiceDesc for CustomerMessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerMessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.message.v2.CustomerMessageService",
	HandlerType: (*CustomerMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMessage",
			Handler:    _CustomerMessageService_SendMessage_Handler,
		},
		{
			MethodName: "ListChat",
			Handler:    _CustomerMessageService_ListChat_Handler,
		},
		{
			MethodName: "ListChatHistoricalMessage",
			Handler:    _CustomerMessageService_ListChatHistoricalMessage_Handler,
		},
		{
			MethodName: "ListChatNewMessage",
			Handler:    _CustomerMessageService_ListChatNewMessage_Handler,
		},
		{
			MethodName: "GetAllChatUnreadMessageCount",
			Handler:    _CustomerMessageService_GetAllChatUnreadMessageCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/message/v2/customer_message_api.proto",
}
