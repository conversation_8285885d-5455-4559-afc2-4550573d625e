// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/grooming/v1/review_booster_api.proto

package groomingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/review_booster/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create review booster request
type CreateReviewBoosterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// review booster score
	Score int32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	// review content
	ReviewContent *string `protobuf:"bytes,3,opt,name=review_content,json=reviewContent,proto3,oneof" json:"review_content,omitempty"`
}

func (x *CreateReviewBoosterRequest) Reset() {
	*x = CreateReviewBoosterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReviewBoosterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReviewBoosterRequest) ProtoMessage() {}

func (x *CreateReviewBoosterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReviewBoosterRequest.ProtoReflect.Descriptor instead.
func (*CreateReviewBoosterRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateReviewBoosterRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *CreateReviewBoosterRequest) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *CreateReviewBoosterRequest) GetReviewContent() string {
	if x != nil && x.ReviewContent != nil {
		return *x.ReviewContent
	}
	return ""
}

// create review booster response
type CreateReviewBoosterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateReviewBoosterResponse) Reset() {
	*x = CreateReviewBoosterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReviewBoosterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReviewBoosterResponse) ProtoMessage() {}

func (x *CreateReviewBoosterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReviewBoosterResponse.ProtoReflect.Descriptor instead.
func (*CreateReviewBoosterResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{1}
}

// get review booster list request
type GetReviewBoosterListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetReviewBoosterListRequest) Reset() {
	*x = GetReviewBoosterListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewBoosterListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewBoosterListRequest) ProtoMessage() {}

func (x *GetReviewBoosterListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewBoosterListRequest.ProtoReflect.Descriptor instead.
func (*GetReviewBoosterListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetReviewBoosterListRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// get review booster list response
type GetReviewBoosterListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// review booster record list
	Records []*v1.ReviewBoosterRecordModelClientView `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *GetReviewBoosterListResponse) Reset() {
	*x = GetReviewBoosterListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewBoosterListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewBoosterListResponse) ProtoMessage() {}

func (x *GetReviewBoosterListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewBoosterListResponse.ProtoReflect.Descriptor instead.
func (*GetReviewBoosterListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetReviewBoosterListResponse) GetRecords() []*v1.ReviewBoosterRecordModelClientView {
	if x != nil {
		return x.Records
	}
	return nil
}

// get review booster config params
type GetReviewBoosterConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetReviewBoosterConfigParams) Reset() {
	*x = GetReviewBoosterConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewBoosterConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewBoosterConfigParams) ProtoMessage() {}

func (x *GetReviewBoosterConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewBoosterConfigParams.ProtoReflect.Descriptor instead.
func (*GetReviewBoosterConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetReviewBoosterConfigParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get review booster config result
type GetReviewBoosterConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// review booster config
	Config *v11.ReviewBoosterConfigClientView `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetReviewBoosterConfigResult) Reset() {
	*x = GetReviewBoosterConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReviewBoosterConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewBoosterConfigResult) ProtoMessage() {}

func (x *GetReviewBoosterConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewBoosterConfigResult.ProtoReflect.Descriptor instead.
func (*GetReviewBoosterConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetReviewBoosterConfigResult) GetConfig() *v11.ReviewBoosterConfigClientView {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_moego_client_grooming_v1_review_booster_api_proto protoreflect.FileDescriptor

var file_moego_client_grooming_v1_review_booster_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x3b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62,
	0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x05, 0x28, 0x01, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xe8, 0x07, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x1d, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42,
	0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x76, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x48, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0x75, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x55, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x32, 0xae, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x82, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42,
	0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_client_grooming_v1_review_booster_api_proto_rawDescOnce sync.Once
	file_moego_client_grooming_v1_review_booster_api_proto_rawDescData = file_moego_client_grooming_v1_review_booster_api_proto_rawDesc
)

func file_moego_client_grooming_v1_review_booster_api_proto_rawDescGZIP() []byte {
	file_moego_client_grooming_v1_review_booster_api_proto_rawDescOnce.Do(func() {
		file_moego_client_grooming_v1_review_booster_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_grooming_v1_review_booster_api_proto_rawDescData)
	})
	return file_moego_client_grooming_v1_review_booster_api_proto_rawDescData
}

var file_moego_client_grooming_v1_review_booster_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_client_grooming_v1_review_booster_api_proto_goTypes = []interface{}{
	(*CreateReviewBoosterRequest)(nil),            // 0: moego.client.grooming.v1.CreateReviewBoosterRequest
	(*CreateReviewBoosterResponse)(nil),           // 1: moego.client.grooming.v1.CreateReviewBoosterResponse
	(*GetReviewBoosterListRequest)(nil),           // 2: moego.client.grooming.v1.GetReviewBoosterListRequest
	(*GetReviewBoosterListResponse)(nil),          // 3: moego.client.grooming.v1.GetReviewBoosterListResponse
	(*GetReviewBoosterConfigParams)(nil),          // 4: moego.client.grooming.v1.GetReviewBoosterConfigParams
	(*GetReviewBoosterConfigResult)(nil),          // 5: moego.client.grooming.v1.GetReviewBoosterConfigResult
	(*v1.ReviewBoosterRecordModelClientView)(nil), // 6: moego.models.grooming.v1.ReviewBoosterRecordModelClientView
	(*v11.ReviewBoosterConfigClientView)(nil),     // 7: moego.models.review_booster.v1.ReviewBoosterConfigClientView
}
var file_moego_client_grooming_v1_review_booster_api_proto_depIdxs = []int32{
	6, // 0: moego.client.grooming.v1.GetReviewBoosterListResponse.records:type_name -> moego.models.grooming.v1.ReviewBoosterRecordModelClientView
	7, // 1: moego.client.grooming.v1.GetReviewBoosterConfigResult.config:type_name -> moego.models.review_booster.v1.ReviewBoosterConfigClientView
	0, // 2: moego.client.grooming.v1.ReviewBoosterService.CreateReviewBooster:input_type -> moego.client.grooming.v1.CreateReviewBoosterRequest
	2, // 3: moego.client.grooming.v1.ReviewBoosterService.GetReviewBoosterList:input_type -> moego.client.grooming.v1.GetReviewBoosterListRequest
	4, // 4: moego.client.grooming.v1.ReviewBoosterService.GetReviewBoosterConfig:input_type -> moego.client.grooming.v1.GetReviewBoosterConfigParams
	1, // 5: moego.client.grooming.v1.ReviewBoosterService.CreateReviewBooster:output_type -> moego.client.grooming.v1.CreateReviewBoosterResponse
	3, // 6: moego.client.grooming.v1.ReviewBoosterService.GetReviewBoosterList:output_type -> moego.client.grooming.v1.GetReviewBoosterListResponse
	5, // 7: moego.client.grooming.v1.ReviewBoosterService.GetReviewBoosterConfig:output_type -> moego.client.grooming.v1.GetReviewBoosterConfigResult
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_client_grooming_v1_review_booster_api_proto_init() }
func file_moego_client_grooming_v1_review_booster_api_proto_init() {
	if File_moego_client_grooming_v1_review_booster_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReviewBoosterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReviewBoosterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewBoosterListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewBoosterListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewBoosterConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReviewBoosterConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_grooming_v1_review_booster_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_grooming_v1_review_booster_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_grooming_v1_review_booster_api_proto_goTypes,
		DependencyIndexes: file_moego_client_grooming_v1_review_booster_api_proto_depIdxs,
		MessageInfos:      file_moego_client_grooming_v1_review_booster_api_proto_msgTypes,
	}.Build()
	File_moego_client_grooming_v1_review_booster_api_proto = out.File
	file_moego_client_grooming_v1_review_booster_api_proto_rawDesc = nil
	file_moego_client_grooming_v1_review_booster_api_proto_goTypes = nil
	file_moego_client_grooming_v1_review_booster_api_proto_depIdxs = nil
}
