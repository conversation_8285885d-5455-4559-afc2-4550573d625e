// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/deposit_rule_models.proto

package orderpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Deposit rule.
type DepositRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Filters. Due to protojson limitation, we have to wrap the repeated filters into a message.
	Filters *DepositFilters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	// Deposit config
	//
	// Types that are assignable to DepositConfig:
	//
	//	*DepositRule_DepositByAmount
	//	*DepositRule_DepositByPercentage
	DepositConfig isDepositRule_DepositConfig `protobuf_oneof:"deposit_config"`
}

func (x *DepositRule) Reset() {
	*x = DepositRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRule) ProtoMessage() {}

func (x *DepositRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRule.ProtoReflect.Descriptor instead.
func (*DepositRule) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{0}
}

func (x *DepositRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DepositRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepositRule) GetFilters() *DepositFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (m *DepositRule) GetDepositConfig() isDepositRule_DepositConfig {
	if m != nil {
		return m.DepositConfig
	}
	return nil
}

func (x *DepositRule) GetDepositByAmount() *money.Money {
	if x, ok := x.GetDepositConfig().(*DepositRule_DepositByAmount); ok {
		return x.DepositByAmount
	}
	return nil
}

func (x *DepositRule) GetDepositByPercentage() *decimal.Decimal {
	if x, ok := x.GetDepositConfig().(*DepositRule_DepositByPercentage); ok {
		return x.DepositByPercentage
	}
	return nil
}

type isDepositRule_DepositConfig interface {
	isDepositRule_DepositConfig()
}

type DepositRule_DepositByAmount struct {
	// By amount
	DepositByAmount *money.Money `protobuf:"bytes,4,opt,name=deposit_by_amount,json=depositByAmount,proto3,oneof"`
}

type DepositRule_DepositByPercentage struct {
	// By percentage (1 for 1%)
	DepositByPercentage *decimal.Decimal `protobuf:"bytes,5,opt,name=deposit_by_percentage,json=depositByPercentage,proto3,oneof"`
}

func (*DepositRule_DepositByAmount) isDepositRule_DepositConfig() {}

func (*DepositRule_DepositByPercentage) isDepositRule_DepositConfig() {}

// Deposit filters
type DepositFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Filters. If multiple filters are present, they are combined with AND. If a filter of some type is not present, this
	// type of filter is not applied.
	Filters []*DepositFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *DepositFilters) Reset() {
	*x = DepositFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositFilters) ProtoMessage() {}

func (x *DepositFilters) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositFilters.ProtoReflect.Descriptor instead.
func (*DepositFilters) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{1}
}

func (x *DepositFilters) GetFilters() []*DepositFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Deposit filter
type DepositFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The specific filter.
	//
	// Types that are assignable to Filter:
	//
	//	*DepositFilter_ClientGroupFilter
	//	*DepositFilter_ServiceFilter
	//	*DepositFilter_DateRangeFilter
	Filter isDepositFilter_Filter `protobuf_oneof:"filter"`
}

func (x *DepositFilter) Reset() {
	*x = DepositFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositFilter) ProtoMessage() {}

func (x *DepositFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositFilter.ProtoReflect.Descriptor instead.
func (*DepositFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{2}
}

func (m *DepositFilter) GetFilter() isDepositFilter_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *DepositFilter) GetClientGroupFilter() *DepositRuleClientGroupFilter {
	if x, ok := x.GetFilter().(*DepositFilter_ClientGroupFilter); ok {
		return x.ClientGroupFilter
	}
	return nil
}

func (x *DepositFilter) GetServiceFilter() *DepositRuleServiceFilter {
	if x, ok := x.GetFilter().(*DepositFilter_ServiceFilter); ok {
		return x.ServiceFilter
	}
	return nil
}

func (x *DepositFilter) GetDateRangeFilter() *DepositRuleDateRangeFilter {
	if x, ok := x.GetFilter().(*DepositFilter_DateRangeFilter); ok {
		return x.DateRangeFilter
	}
	return nil
}

type isDepositFilter_Filter interface {
	isDepositFilter_Filter()
}

type DepositFilter_ClientGroupFilter struct {
	// Client group
	// If absent, no filter on client group is applied.
	ClientGroupFilter *DepositRuleClientGroupFilter `protobuf:"bytes,1,opt,name=client_group_filter,json=clientGroupFilter,proto3,oneof"`
}

type DepositFilter_ServiceFilter struct {
	// Services
	// If absent, no filter on services is applied.
	ServiceFilter *DepositRuleServiceFilter `protobuf:"bytes,2,opt,name=service_filter,json=serviceFilter,proto3,oneof"`
}

type DepositFilter_DateRangeFilter struct {
	// Date range
	// If absent, no expiration.
	DateRangeFilter *DepositRuleDateRangeFilter `protobuf:"bytes,3,opt,name=date_range_filter,json=dateRangeFilter,proto3,oneof"`
}

func (*DepositFilter_ClientGroupFilter) isDepositFilter_Filter() {}

func (*DepositFilter_ServiceFilter) isDepositFilter_Filter() {}

func (*DepositFilter_DateRangeFilter) isDepositFilter_Filter() {}

// Client group filter
type DepositRuleClientGroupFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Match new visitor
	NewVisitors bool `protobuf:"varint,1,opt,name=new_visitors,json=newVisitors,proto3" json:"new_visitors,omitempty"`
	// Existing customers
	ExistingCustomers bool `protobuf:"varint,2,opt,name=existing_customers,json=existingCustomers,proto3" json:"existing_customers,omitempty"`
	// Filter json for existing customers. The structure is the same as the request body.filters of
	// POST/customer/smart-list.
	ExistingCustomersFilterJson string `protobuf:"bytes,3,opt,name=existing_customers_filter_json,json=existingCustomersFilterJson,proto3" json:"existing_customers_filter_json,omitempty"`
}

func (x *DepositRuleClientGroupFilter) Reset() {
	*x = DepositRuleClientGroupFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRuleClientGroupFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRuleClientGroupFilter) ProtoMessage() {}

func (x *DepositRuleClientGroupFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRuleClientGroupFilter.ProtoReflect.Descriptor instead.
func (*DepositRuleClientGroupFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{3}
}

func (x *DepositRuleClientGroupFilter) GetNewVisitors() bool {
	if x != nil {
		return x.NewVisitors
	}
	return false
}

func (x *DepositRuleClientGroupFilter) GetExistingCustomers() bool {
	if x != nil {
		return x.ExistingCustomers
	}
	return false
}

func (x *DepositRuleClientGroupFilter) GetExistingCustomersFilterJson() string {
	if x != nil {
		return x.ExistingCustomersFilterJson
	}
	return ""
}

// Service filter
type DepositRuleServiceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Applied services. These services are filtered with OR.
	ServicesByType []*DepositRuleServiceFilter_ServicesByType `protobuf:"bytes,1,rep,name=services_by_type,json=servicesByType,proto3" json:"services_by_type,omitempty"`
}

func (x *DepositRuleServiceFilter) Reset() {
	*x = DepositRuleServiceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRuleServiceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRuleServiceFilter) ProtoMessage() {}

func (x *DepositRuleServiceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRuleServiceFilter.ProtoReflect.Descriptor instead.
func (*DepositRuleServiceFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{4}
}

func (x *DepositRuleServiceFilter) GetServicesByType() []*DepositRuleServiceFilter_ServicesByType {
	if x != nil {
		return x.ServicesByType
	}
	return nil
}

// Date range filter
type DepositRuleDateRangeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Range
	Range *interval.Interval `protobuf:"bytes,1,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *DepositRuleDateRangeFilter) Reset() {
	*x = DepositRuleDateRangeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRuleDateRangeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRuleDateRangeFilter) ProtoMessage() {}

func (x *DepositRuleDateRangeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRuleDateRangeFilter.ProtoReflect.Descriptor instead.
func (*DepositRuleDateRangeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{5}
}

func (x *DepositRuleDateRangeFilter) GetRange() *interval.Interval {
	if x != nil {
		return x.Range
	}
	return nil
}

// Service by type
type DepositRuleServiceFilter_ServicesByType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	ServiceType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_type,omitempty"`
	// Is all
	IsAll bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// Service IDs, only if is_all is false
	ServiceIds []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *DepositRuleServiceFilter_ServicesByType) Reset() {
	*x = DepositRuleServiceFilter_ServicesByType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositRuleServiceFilter_ServicesByType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositRuleServiceFilter_ServicesByType) ProtoMessage() {}

func (x *DepositRuleServiceFilter_ServicesByType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositRuleServiceFilter_ServicesByType.ProtoReflect.Descriptor instead.
func (*DepositRuleServiceFilter_ServicesByType) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DepositRuleServiceFilter_ServicesByType) GetServiceType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceItemType(0)
}

func (x *DepositRuleServiceFilter_ServicesByType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *DepositRuleServiceFilter_ServicesByType) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

var File_moego_models_order_v1_deposit_rule_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_deposit_rule_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4,
	0x02, 0x0a, 0x0b, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x5f, 0x62, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x42,
	0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x15, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x13,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x42, 0x79, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x50, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xbb, 0x02, 0x0a, 0x0d, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x13, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x11, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x58, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x11, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xb5, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f, 0x76, 0x69,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x65,
	0x77, 0x56, 0x69, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x43, 0x0a, 0x1e, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1b, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x9d, 0x02,
	0x0a, 0x18, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x10, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x42, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x1a, 0x96, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x49, 0x0a,
	0x1a, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x05, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_deposit_rule_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_deposit_rule_models_proto_rawDescData = file_moego_models_order_v1_deposit_rule_models_proto_rawDesc
)

func file_moego_models_order_v1_deposit_rule_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_deposit_rule_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_deposit_rule_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_deposit_rule_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_deposit_rule_models_proto_rawDescData
}

var file_moego_models_order_v1_deposit_rule_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_order_v1_deposit_rule_models_proto_goTypes = []interface{}{
	(*DepositRule)(nil),                             // 0: moego.models.order.v1.DepositRule
	(*DepositFilters)(nil),                          // 1: moego.models.order.v1.DepositFilters
	(*DepositFilter)(nil),                           // 2: moego.models.order.v1.DepositFilter
	(*DepositRuleClientGroupFilter)(nil),            // 3: moego.models.order.v1.DepositRuleClientGroupFilter
	(*DepositRuleServiceFilter)(nil),                // 4: moego.models.order.v1.DepositRuleServiceFilter
	(*DepositRuleDateRangeFilter)(nil),              // 5: moego.models.order.v1.DepositRuleDateRangeFilter
	(*DepositRuleServiceFilter_ServicesByType)(nil), // 6: moego.models.order.v1.DepositRuleServiceFilter.ServicesByType
	(*money.Money)(nil),                             // 7: google.type.Money
	(*decimal.Decimal)(nil),                         // 8: google.type.Decimal
	(*interval.Interval)(nil),                       // 9: google.type.Interval
	(v1.ServiceItemType)(0),                         // 10: moego.models.offering.v1.ServiceItemType
}
var file_moego_models_order_v1_deposit_rule_models_proto_depIdxs = []int32{
	1,  // 0: moego.models.order.v1.DepositRule.filters:type_name -> moego.models.order.v1.DepositFilters
	7,  // 1: moego.models.order.v1.DepositRule.deposit_by_amount:type_name -> google.type.Money
	8,  // 2: moego.models.order.v1.DepositRule.deposit_by_percentage:type_name -> google.type.Decimal
	2,  // 3: moego.models.order.v1.DepositFilters.filters:type_name -> moego.models.order.v1.DepositFilter
	3,  // 4: moego.models.order.v1.DepositFilter.client_group_filter:type_name -> moego.models.order.v1.DepositRuleClientGroupFilter
	4,  // 5: moego.models.order.v1.DepositFilter.service_filter:type_name -> moego.models.order.v1.DepositRuleServiceFilter
	5,  // 6: moego.models.order.v1.DepositFilter.date_range_filter:type_name -> moego.models.order.v1.DepositRuleDateRangeFilter
	6,  // 7: moego.models.order.v1.DepositRuleServiceFilter.services_by_type:type_name -> moego.models.order.v1.DepositRuleServiceFilter.ServicesByType
	9,  // 8: moego.models.order.v1.DepositRuleDateRangeFilter.range:type_name -> google.type.Interval
	10, // 9: moego.models.order.v1.DepositRuleServiceFilter.ServicesByType.service_type:type_name -> moego.models.offering.v1.ServiceItemType
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_deposit_rule_models_proto_init() }
func file_moego_models_order_v1_deposit_rule_models_proto_init() {
	if File_moego_models_order_v1_deposit_rule_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRuleClientGroupFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRuleServiceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRuleDateRangeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositRuleServiceFilter_ServicesByType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*DepositRule_DepositByAmount)(nil),
		(*DepositRule_DepositByPercentage)(nil),
	}
	file_moego_models_order_v1_deposit_rule_models_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*DepositFilter_ClientGroupFilter)(nil),
		(*DepositFilter_ServiceFilter)(nil),
		(*DepositFilter_DateRangeFilter)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_deposit_rule_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_deposit_rule_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_deposit_rule_models_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v1_deposit_rule_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_deposit_rule_models_proto = out.File
	file_moego_models_order_v1_deposit_rule_models_proto_rawDesc = nil
	file_moego_models_order_v1_deposit_rule_models_proto_goTypes = nil
	file_moego_models_order_v1_deposit_rule_models_proto_depIdxs = nil
}
