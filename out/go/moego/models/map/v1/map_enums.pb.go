// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/map/v1/map_enums.proto

package mappb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegionLevel
// civil entity region level in country
type RegionLevel int32

const (
	// unspecified
	RegionLevel_REGION_LEVEL_UNSPECIFIED RegionLevel = 0
	// country
	RegionLevel_LV0 RegionLevel = 1
	// a first-order civil entity below the country level.
	// within the United States, these administrative levels are states.
	// within the China, these administrative levels are provinces.
	RegionLevel_LV1 RegionLevel = 2
	// a second-order civil entity below the country level.
	// within the United States, these administrative levels are counties/districts.
	// within the China, these administrative levels are cities.
	RegionLevel_LV2 RegionLevel = 3
	// a third-order civil entity below the country level.
	// within the United States, these administrative levels are township/cities.
	// within the China, these administrative levels are counties/districts.
	RegionLevel_LV3 RegionLevel = 4
	// A fourth-order civil entity below the country level.
	// This type indicates a minor civil division. e.g. village
	RegionLevel_LV4 RegionLevel = 5
	// A fifth-order civil entity below the country level.
	// This type indicates a minor civil division. e.g. ward
	RegionLevel_LV5 RegionLevel = 6
	// A sixth-order civil entity below the country level.
	// This type indicates a minor civil division. e.g. street
	RegionLevel_LV6 RegionLevel = 7
)

// Enum value maps for RegionLevel.
var (
	RegionLevel_name = map[int32]string{
		0: "REGION_LEVEL_UNSPECIFIED",
		1: "LV0",
		2: "LV1",
		3: "LV2",
		4: "LV3",
		5: "LV4",
		6: "LV5",
		7: "LV6",
	}
	RegionLevel_value = map[string]int32{
		"REGION_LEVEL_UNSPECIFIED": 0,
		"LV0":                      1,
		"LV1":                      2,
		"LV2":                      3,
		"LV3":                      4,
		"LV4":                      5,
		"LV5":                      6,
		"LV6":                      7,
	}
)

func (x RegionLevel) Enum() *RegionLevel {
	p := new(RegionLevel)
	*p = x
	return p
}

func (x RegionLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegionLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_map_v1_map_enums_proto_enumTypes[0].Descriptor()
}

func (RegionLevel) Type() protoreflect.EnumType {
	return &file_moego_models_map_v1_map_enums_proto_enumTypes[0]
}

func (x RegionLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegionLevel.Descriptor instead.
func (RegionLevel) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_enums_proto_rawDescGZIP(), []int{0}
}

// RegionStatus
type RegionStatus int32

const (
	// unspecified
	RegionStatus_REGION_STATUS_UNSPECIFIED RegionStatus = 0
	// region status normal
	RegionStatus_ACTIVE RegionStatus = 1
	// region is expired
	RegionStatus_EXPIRED RegionStatus = 2
	// region is deleted
	RegionStatus_DELETED RegionStatus = 3
)

// Enum value maps for RegionStatus.
var (
	RegionStatus_name = map[int32]string{
		0: "REGION_STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "EXPIRED",
		3: "DELETED",
	}
	RegionStatus_value = map[string]int32{
		"REGION_STATUS_UNSPECIFIED": 0,
		"ACTIVE":                    1,
		"EXPIRED":                   2,
		"DELETED":                   3,
	}
)

func (x RegionStatus) Enum() *RegionStatus {
	p := new(RegionStatus)
	*p = x
	return p
}

func (x RegionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_map_v1_map_enums_proto_enumTypes[1].Descriptor()
}

func (RegionStatus) Type() protoreflect.EnumType {
	return &file_moego_models_map_v1_map_enums_proto_enumTypes[1]
}

func (x RegionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegionStatus.Descriptor instead.
func (RegionStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_enums_proto_rawDescGZIP(), []int{1}
}

// AreaRelation
type AreaRelation int32

const (
	// unspecified
	AreaRelation_AREA_RELATION_UNSPECIFIED AreaRelation = 0
	// no relation
	AreaRelation_NOTHING AreaRelation = 1
	// adjacent
	AreaRelation_ADJACENT AreaRelation = 2
	// intersect
	AreaRelation_INTERSECT AreaRelation = 3
	// coincide
	AreaRelation_COINCIDE AreaRelation = 4
	// contain
	AreaRelation_CONTAIN AreaRelation = 5
)

// Enum value maps for AreaRelation.
var (
	AreaRelation_name = map[int32]string{
		0: "AREA_RELATION_UNSPECIFIED",
		1: "NOTHING",
		2: "ADJACENT",
		3: "INTERSECT",
		4: "COINCIDE",
		5: "CONTAIN",
	}
	AreaRelation_value = map[string]int32{
		"AREA_RELATION_UNSPECIFIED": 0,
		"NOTHING":                   1,
		"ADJACENT":                  2,
		"INTERSECT":                 3,
		"COINCIDE":                  4,
		"CONTAIN":                   5,
	}
)

func (x AreaRelation) Enum() *AreaRelation {
	p := new(AreaRelation)
	*p = x
	return p
}

func (x AreaRelation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AreaRelation) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_map_v1_map_enums_proto_enumTypes[2].Descriptor()
}

func (AreaRelation) Type() protoreflect.EnumType {
	return &file_moego_models_map_v1_map_enums_proto_enumTypes[2]
}

func (x AreaRelation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AreaRelation.Descriptor instead.
func (AreaRelation) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_map_v1_map_enums_proto protoreflect.FileDescriptor

var file_moego_models_map_v1_map_enums_proto_rawDesc = []byte{
	0x0a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x70, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2a, 0x6a, 0x0a, 0x0b, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x47,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x56, 0x30, 0x10, 0x01,
	0x12, 0x07, 0x0a, 0x03, 0x4c, 0x56, 0x31, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x56, 0x32,
	0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x56, 0x33, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x4c,
	0x56, 0x34, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x56, 0x35, 0x10, 0x06, 0x12, 0x07, 0x0a,
	0x03, 0x4c, 0x56, 0x36, 0x10, 0x07, 0x2a, 0x53, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x47, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x72, 0x0a, 0x0c, 0x41,
	0x72, 0x65, 0x61, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x52, 0x45, 0x41, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f,
	0x54, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x4a, 0x41, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x45,
	0x43, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45,
	0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x10, 0x05, 0x42,
	0x6f, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x4e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61, 0x70, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_map_v1_map_enums_proto_rawDescOnce sync.Once
	file_moego_models_map_v1_map_enums_proto_rawDescData = file_moego_models_map_v1_map_enums_proto_rawDesc
)

func file_moego_models_map_v1_map_enums_proto_rawDescGZIP() []byte {
	file_moego_models_map_v1_map_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_map_v1_map_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_map_v1_map_enums_proto_rawDescData)
	})
	return file_moego_models_map_v1_map_enums_proto_rawDescData
}

var file_moego_models_map_v1_map_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_map_v1_map_enums_proto_goTypes = []interface{}{
	(RegionLevel)(0),  // 0: moego.models.map.v1.RegionLevel
	(RegionStatus)(0), // 1: moego.models.map.v1.RegionStatus
	(AreaRelation)(0), // 2: moego.models.map.v1.AreaRelation
}
var file_moego_models_map_v1_map_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_map_v1_map_enums_proto_init() }
func file_moego_models_map_v1_map_enums_proto_init() {
	if File_moego_models_map_v1_map_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_map_v1_map_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_map_v1_map_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_map_v1_map_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_map_v1_map_enums_proto_enumTypes,
	}.Build()
	File_moego_models_map_v1_map_enums_proto = out.File
	file_moego_models_map_v1_map_enums_proto_rawDesc = nil
	file_moego_models_map_v1_map_enums_proto_goTypes = nil
	file_moego_models_map_v1_map_enums_proto_depIdxs = nil
}
