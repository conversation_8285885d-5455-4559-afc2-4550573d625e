// @since 2024-06-12 14:10:17
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/subscription_defs.proto

package membershippb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/billing/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The Subscription Full Definition
type SubscriptionCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,2,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// membership revision, if set, will check
	// the membership revision is it or not.
	MembershipRevision *int32 `protobuf:"varint,3,opt,name=membership_revision,json=membershipRevision,proto3,oneof" json:"membership_revision,omitempty"`
	// external cof id, should go with internal cof id
	// frontend should add card first and use the cof id then
	// FIXME(Ritchie): please save a cof in our system.
	ExternalCardId string `protobuf:"bytes,4,opt,name=external_card_id,json=externalCardId,proto3" json:"external_card_id,omitempty"`
	// 支持创建未来的预约
	StartAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	// payment behavior
	PaymentBehavior *v1.SubscriptionModel_PaymentBehavior `protobuf:"varint,6,opt,name=payment_behavior,json=paymentBehavior,proto3,enum=moego.models.billing.v1.SubscriptionModel_PaymentBehavior,oneof" json:"payment_behavior,omitempty"`
}

func (x *SubscriptionCreateDef) Reset() {
	*x = SubscriptionCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionCreateDef) ProtoMessage() {}

func (x *SubscriptionCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionCreateDef.ProtoReflect.Descriptor instead.
func (*SubscriptionCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_defs_proto_rawDescGZIP(), []int{0}
}

func (x *SubscriptionCreateDef) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SubscriptionCreateDef) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *SubscriptionCreateDef) GetMembershipRevision() int32 {
	if x != nil && x.MembershipRevision != nil {
		return *x.MembershipRevision
	}
	return 0
}

func (x *SubscriptionCreateDef) GetExternalCardId() string {
	if x != nil {
		return x.ExternalCardId
	}
	return ""
}

func (x *SubscriptionCreateDef) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *SubscriptionCreateDef) GetPaymentBehavior() v1.SubscriptionModel_PaymentBehavior {
	if x != nil && x.PaymentBehavior != nil {
		return *x.PaymentBehavior
	}
	return v1.SubscriptionModel_PaymentBehavior(0)
}

// The Subscription Partial Definition
type SubscriptionUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// external card id
	ExternalCardId *string `protobuf:"bytes,1,opt,name=external_card_id,json=externalCardId,proto3,oneof" json:"external_card_id,omitempty"`
}

func (x *SubscriptionUpdateDef) Reset() {
	*x = SubscriptionUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionUpdateDef) ProtoMessage() {}

func (x *SubscriptionUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionUpdateDef.ProtoReflect.Descriptor instead.
func (*SubscriptionUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_defs_proto_rawDescGZIP(), []int{1}
}

func (x *SubscriptionUpdateDef) GetExternalCardId() string {
	if x != nil && x.ExternalCardId != nil {
		return *x.ExternalCardId
	}
	return ""
}

var File_moego_models_membership_v1_subscription_defs_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_subscription_defs_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x03,
	0x0a, 0x15, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12,
	0x3d, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x72, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x12, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x33,
	0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x0a, 0x18, 0x64, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x6a, 0x0a, 0x10, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x48, 0x01, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x13,
	0x0a, 0x11, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x22, 0x5b, 0x0a, 0x15, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2d, 0x0a, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_subscription_defs_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_subscription_defs_proto_rawDescData = file_moego_models_membership_v1_subscription_defs_proto_rawDesc
)

func file_moego_models_membership_v1_subscription_defs_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_subscription_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_subscription_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_subscription_defs_proto_rawDescData)
	})
	return file_moego_models_membership_v1_subscription_defs_proto_rawDescData
}

var file_moego_models_membership_v1_subscription_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_membership_v1_subscription_defs_proto_goTypes = []interface{}{
	(*SubscriptionCreateDef)(nil),             // 0: moego.models.membership.v1.SubscriptionCreateDef
	(*SubscriptionUpdateDef)(nil),             // 1: moego.models.membership.v1.SubscriptionUpdateDef
	(*timestamppb.Timestamp)(nil),             // 2: google.protobuf.Timestamp
	(v1.SubscriptionModel_PaymentBehavior)(0), // 3: moego.models.billing.v1.SubscriptionModel.PaymentBehavior
}
var file_moego_models_membership_v1_subscription_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.membership.v1.SubscriptionCreateDef.start_at:type_name -> google.protobuf.Timestamp
	3, // 1: moego.models.membership.v1.SubscriptionCreateDef.payment_behavior:type_name -> moego.models.billing.v1.SubscriptionModel.PaymentBehavior
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_subscription_defs_proto_init() }
func file_moego_models_membership_v1_subscription_defs_proto_init() {
	if File_moego_models_membership_v1_subscription_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_subscription_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_membership_v1_subscription_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_subscription_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_subscription_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_subscription_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_membership_v1_subscription_defs_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_subscription_defs_proto = out.File
	file_moego_models_membership_v1_subscription_defs_proto_rawDesc = nil
	file_moego_models_membership_v1_subscription_defs_proto_goTypes = nil
	file_moego_models_membership_v1_subscription_defs_proto_depIdxs = nil
}
