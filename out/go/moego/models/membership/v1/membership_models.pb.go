// @since 2024-06-12 11:04:31
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/membership_models.proto

package membershippb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// membership status
type MembershipModel_Status int32

const (
	// default
	MembershipModel_STATUS_UNSPECIFIED MembershipModel_Status = 0
	// active
	MembershipModel_ACTIVE MembershipModel_Status = 1
	// inactive
	MembershipModel_INACTIVE MembershipModel_Status = 2
)

// Enum value maps for MembershipModel_Status.
var (
	MembershipModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	MembershipModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"INACTIVE":           2,
	}
)

func (x MembershipModel_Status) Enum() *MembershipModel_Status {
	p := new(MembershipModel_Status)
	*p = x
	return p
}

func (x MembershipModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MembershipModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_membership_models_proto_enumTypes[0].Descriptor()
}

func (MembershipModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_membership_models_proto_enumTypes[0]
}

func (x MembershipModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MembershipModel_Status.Descriptor instead.
func (MembershipModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{0, 0}
}

// membership billing cycle
type MembershipModel_BillingCycle int32

const (
	// default
	MembershipModel_BILLING_CYCLE_UNSPECIFIED MembershipModel_BillingCycle = 0
	// monthly
	MembershipModel_MONTHLY MembershipModel_BillingCycle = 1
	// annually
	MembershipModel_ANNUALLY MembershipModel_BillingCycle = 2
	// weekly
	MembershipModel_WEEKLY MembershipModel_BillingCycle = 3
)

// Enum value maps for MembershipModel_BillingCycle.
var (
	MembershipModel_BillingCycle_name = map[int32]string{
		0: "BILLING_CYCLE_UNSPECIFIED",
		1: "MONTHLY",
		2: "ANNUALLY",
		3: "WEEKLY",
	}
	MembershipModel_BillingCycle_value = map[string]int32{
		"BILLING_CYCLE_UNSPECIFIED": 0,
		"MONTHLY":                   1,
		"ANNUALLY":                  2,
		"WEEKLY":                    3,
	}
)

func (x MembershipModel_BillingCycle) Enum() *MembershipModel_BillingCycle {
	p := new(MembershipModel_BillingCycle)
	*p = x
	return p
}

func (x MembershipModel_BillingCycle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MembershipModel_BillingCycle) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_membership_models_proto_enumTypes[1].Descriptor()
}

func (MembershipModel_BillingCycle) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_membership_models_proto_enumTypes[1]
}

func (x MembershipModel_BillingCycle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MembershipModel_BillingCycle.Descriptor instead.
func (MembershipModel_BillingCycle) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{0, 1}
}

// The Membership model
type MembershipModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the product id in subscription service
	InternalProductId int64 `protobuf:"varint,2,opt,name=internal_product_id,json=internalProductId,proto3" json:"internal_product_id,omitempty"`
	// the membership name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// the description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// the status
	Status MembershipModel_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status" json:"status,omitempty"`
	// the price
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// the tax id
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle MembershipModel_BillingCycle `protobuf:"varint,8,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle" json:"billing_cycle,omitempty"`
	// policy
	Policy string `protobuf:"bytes,9,opt,name=policy,proto3" json:"policy,omitempty"`
	// the company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the price id
	PriceId int64 `protobuf:"varint,11,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,12,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// revision, any update action will +1, default is 1
	Revision int32 `protobuf:"varint,16,opt,name=revision,proto3" json:"revision,omitempty"`
	// the total price
	TotalPrice *money.Money `protobuf:"bytes,17,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// the total tax
	TotalTax *money.Money `protobuf:"bytes,18,opt,name=total_tax,json=totalTax,proto3" json:"total_tax,omitempty"`
	// enable purchase for online booking
	EnableOnlineBooking bool `protobuf:"varint,19,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// enable membership benefits discount
	EnableDiscountBenefits bool `protobuf:"varint,20,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3" json:"enable_discount_benefits,omitempty"`
	// enable membership benefits quantity
	EnableQuantityBenefits bool `protobuf:"varint,21,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3" json:"enable_quantity_benefits,omitempty"`
	// billing cycyle day of week
	BillingCycleDayOfWeek dayofweek.DayOfWeek `protobuf:"varint,22,opt,name=billing_cycle_day_of_week,json=billingCycleDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"billing_cycle_day_of_week,omitempty"`
	// breed filter
	BreedFilter bool `protobuf:"varint,23,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// customized breed
	CustomizedBreed []*v11.CustomizedBreed `protobuf:"bytes,24,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,25,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,26,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,27,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,28,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
}

func (x *MembershipModel) Reset() {
	*x = MembershipModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipModel) ProtoMessage() {}

func (x *MembershipModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipModel.ProtoReflect.Descriptor instead.
func (*MembershipModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{0}
}

func (x *MembershipModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipModel) GetInternalProductId() int64 {
	if x != nil {
		return x.InternalProductId
	}
	return 0
}

func (x *MembershipModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MembershipModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MembershipModel) GetStatus() MembershipModel_Status {
	if x != nil {
		return x.Status
	}
	return MembershipModel_STATUS_UNSPECIFIED
}

func (x *MembershipModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *MembershipModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *MembershipModel) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *MembershipModel) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *MembershipModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MembershipModel) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *MembershipModel) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *MembershipModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MembershipModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MembershipModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *MembershipModel) GetRevision() int32 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *MembershipModel) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *MembershipModel) GetTotalTax() *money.Money {
	if x != nil {
		return x.TotalTax
	}
	return nil
}

func (x *MembershipModel) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *MembershipModel) GetEnableDiscountBenefits() bool {
	if x != nil {
		return x.EnableDiscountBenefits
	}
	return false
}

func (x *MembershipModel) GetEnableQuantityBenefits() bool {
	if x != nil {
		return x.EnableQuantityBenefits
	}
	return false
}

func (x *MembershipModel) GetBillingCycleDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.BillingCycleDayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *MembershipModel) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *MembershipModel) GetCustomizedBreed() []*v11.CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *MembershipModel) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *MembershipModel) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

func (x *MembershipModel) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *MembershipModel) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

// The Membership model
type MembershipModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the membership name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// the description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// the status
	Status MembershipModel_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.membership.v1.MembershipModel_Status" json:"status,omitempty"`
	// the price
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// the tax id
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle MembershipModel_BillingCycle `protobuf:"varint,8,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle" json:"billing_cycle,omitempty"`
	// policy
	Policy string `protobuf:"bytes,9,opt,name=policy,proto3" json:"policy,omitempty"`
	// the company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the price id
	PriceId int64 `protobuf:"varint,11,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,12,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// revision, any update action will +1
	Revision int32 `protobuf:"varint,16,opt,name=revision,proto3" json:"revision,omitempty"`
	// the total price
	TotalPrice *money.Money `protobuf:"bytes,17,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// the total tax
	TotalTax *money.Money `protobuf:"bytes,18,opt,name=total_tax,json=totalTax,proto3" json:"total_tax,omitempty"`
	// billing cycle day of week
	BillingCycleDayOfWeek dayofweek.DayOfWeek `protobuf:"varint,19,opt,name=billing_cycle_day_of_week,json=billingCycleDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"billing_cycle_day_of_week,omitempty"`
	// enable purchase for online booking
	EnableOnlineBooking bool `protobuf:"varint,20,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// enable membership benefits discount
	EnableDiscountBenefits bool `protobuf:"varint,21,opt,name=enable_discount_benefits,json=enableDiscountBenefits,proto3" json:"enable_discount_benefits,omitempty"`
	// enable membership benefits quantity
	EnableQuantityBenefits bool `protobuf:"varint,22,opt,name=enable_quantity_benefits,json=enableQuantityBenefits,proto3" json:"enable_quantity_benefits,omitempty"`
}

func (x *MembershipModelPublicView) Reset() {
	*x = MembershipModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipModelPublicView) ProtoMessage() {}

func (x *MembershipModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipModelPublicView.ProtoReflect.Descriptor instead.
func (*MembershipModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{1}
}

func (x *MembershipModelPublicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipModelPublicView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MembershipModelPublicView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MembershipModelPublicView) GetStatus() MembershipModel_Status {
	if x != nil {
		return x.Status
	}
	return MembershipModel_STATUS_UNSPECIFIED
}

func (x *MembershipModelPublicView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *MembershipModelPublicView) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *MembershipModelPublicView) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *MembershipModelPublicView) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *MembershipModelPublicView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MembershipModelPublicView) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *MembershipModelPublicView) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *MembershipModelPublicView) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MembershipModelPublicView) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MembershipModelPublicView) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *MembershipModelPublicView) GetRevision() int32 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *MembershipModelPublicView) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *MembershipModelPublicView) GetTotalTax() *money.Money {
	if x != nil {
		return x.TotalTax
	}
	return nil
}

func (x *MembershipModelPublicView) GetBillingCycleDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.BillingCycleDayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *MembershipModelPublicView) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *MembershipModelPublicView) GetEnableDiscountBenefits() bool {
	if x != nil {
		return x.EnableDiscountBenefits
	}
	return false
}

func (x *MembershipModelPublicView) GetEnableQuantityBenefits() bool {
	if x != nil {
		return x.EnableQuantityBenefits
	}
	return false
}

// the membership usage summary model
type MembershipSummaryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the membership id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// in subscription count
	InSubscriptionCount int32 `protobuf:"varint,2,opt,name=in_subscription_count,json=inSubscriptionCount,proto3" json:"in_subscription_count,omitempty"`
	// cancelled count
	CancelledCount int32 `protobuf:"varint,3,opt,name=cancelled_count,json=cancelledCount,proto3" json:"cancelled_count,omitempty"`
	// paused count
	PausedCount int32 `protobuf:"varint,4,opt,name=paused_count,json=pausedCount,proto3" json:"paused_count,omitempty"`
}

func (x *MembershipSummaryModel) Reset() {
	*x = MembershipSummaryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipSummaryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipSummaryModel) ProtoMessage() {}

func (x *MembershipSummaryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipSummaryModel.ProtoReflect.Descriptor instead.
func (*MembershipSummaryModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{2}
}

func (x *MembershipSummaryModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MembershipSummaryModel) GetInSubscriptionCount() int32 {
	if x != nil {
		return x.InSubscriptionCount
	}
	return 0
}

func (x *MembershipSummaryModel) GetCancelledCount() int32 {
	if x != nil {
		return x.CancelledCount
	}
	return 0
}

func (x *MembershipSummaryModel) GetPausedCount() int32 {
	if x != nil {
		return x.PausedCount
	}
	return 0
}

// The benefit item
type BenefitRecommendView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// benefit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// redeem scenario item
	ScenarioItem *RedeemScenarioItem `protobuf:"bytes,2,opt,name=scenario_item,json=scenarioItem,proto3" json:"scenario_item,omitempty"`
	// benefit detail
	//
	// Types that are assignable to BenefitDetail:
	//
	//	*BenefitRecommendView_Discount
	//	*BenefitRecommendView_Quality
	BenefitDetail isBenefitRecommendView_BenefitDetail `protobuf_oneof:"benefit_detail"`
	// membership id
	MembershipId *int64 `protobuf:"varint,6,opt,name=membership_id,json=membershipId,proto3,oneof" json:"membership_id,omitempty"`
}

func (x *BenefitRecommendView) Reset() {
	*x = BenefitRecommendView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitRecommendView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitRecommendView) ProtoMessage() {}

func (x *BenefitRecommendView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitRecommendView.ProtoReflect.Descriptor instead.
func (*BenefitRecommendView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{3}
}

func (x *BenefitRecommendView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BenefitRecommendView) GetScenarioItem() *RedeemScenarioItem {
	if x != nil {
		return x.ScenarioItem
	}
	return nil
}

func (m *BenefitRecommendView) GetBenefitDetail() isBenefitRecommendView_BenefitDetail {
	if m != nil {
		return m.BenefitDetail
	}
	return nil
}

func (x *BenefitRecommendView) GetDiscount() *DiscountBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitRecommendView_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *BenefitRecommendView) GetQuality() *QualityBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitRecommendView_Quality); ok {
		return x.Quality
	}
	return nil
}

func (x *BenefitRecommendView) GetMembershipId() int64 {
	if x != nil && x.MembershipId != nil {
		return *x.MembershipId
	}
	return 0
}

type isBenefitRecommendView_BenefitDetail interface {
	isBenefitRecommendView_BenefitDetail()
}

type BenefitRecommendView_Discount struct {
	// discount
	Discount *DiscountBenefitModel `protobuf:"bytes,4,opt,name=discount,proto3,oneof"`
}

type BenefitRecommendView_Quality struct {
	// quality, a typo, should be quantity
	Quality *QualityBenefitModel `protobuf:"bytes,5,opt,name=quality,proto3,oneof"`
}

func (*BenefitRecommendView_Discount) isBenefitRecommendView_BenefitDetail() {}

func (*BenefitRecommendView_Quality) isBenefitRecommendView_BenefitDetail() {}

// membership usage view
type MembershipUsageView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order item id
	OrderItemId int64 `protobuf:"varint,1,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// price reduction, aka discount amount
	PriceReduction float64 `protobuf:"fixed64,7,opt,name=price_reduction,json=priceReduction,proto3" json:"price_reduction,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,2,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// benefit detail
	//
	// Types that are assignable to BenefitDetail:
	//
	//	*MembershipUsageView_Discount
	//	*MembershipUsageView_Quantity
	BenefitDetail isMembershipUsageView_BenefitDetail `protobuf_oneof:"benefit_detail"`
	// redeem quantity, only for quantity benefit
	RedeemQuantity int64 `protobuf:"varint,6,opt,name=redeem_quantity,json=redeemQuantity,proto3" json:"redeem_quantity,omitempty"`
}

func (x *MembershipUsageView) Reset() {
	*x = MembershipUsageView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipUsageView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipUsageView) ProtoMessage() {}

func (x *MembershipUsageView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipUsageView.ProtoReflect.Descriptor instead.
func (*MembershipUsageView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{4}
}

func (x *MembershipUsageView) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *MembershipUsageView) GetPriceReduction() float64 {
	if x != nil {
		return x.PriceReduction
	}
	return 0
}

func (x *MembershipUsageView) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (m *MembershipUsageView) GetBenefitDetail() isMembershipUsageView_BenefitDetail {
	if m != nil {
		return m.BenefitDetail
	}
	return nil
}

func (x *MembershipUsageView) GetDiscount() *DiscountBenefitModel {
	if x, ok := x.GetBenefitDetail().(*MembershipUsageView_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *MembershipUsageView) GetQuantity() *QualityBenefitModel {
	if x, ok := x.GetBenefitDetail().(*MembershipUsageView_Quantity); ok {
		return x.Quantity
	}
	return nil
}

func (x *MembershipUsageView) GetRedeemQuantity() int64 {
	if x != nil {
		return x.RedeemQuantity
	}
	return 0
}

type isMembershipUsageView_BenefitDetail interface {
	isMembershipUsageView_BenefitDetail()
}

type MembershipUsageView_Discount struct {
	// discount
	Discount *DiscountBenefitModel `protobuf:"bytes,4,opt,name=discount,proto3,oneof"`
}

type MembershipUsageView_Quantity struct {
	// quantity
	Quantity *QualityBenefitModel `protobuf:"bytes,5,opt,name=quantity,proto3,oneof"`
}

func (*MembershipUsageView_Discount) isMembershipUsageView_BenefitDetail() {}

func (*MembershipUsageView_Quantity) isMembershipUsageView_BenefitDetail() {}

// discount benefit model
type DiscountBenefitModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,4,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target ids
	TargetIds []int64 `protobuf:"varint,6,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
	// is all
	IsAll bool `protobuf:"varint,7,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// discount unit
	DiscountUnit DiscountUnit `protobuf:"varint,8,opt,name=discount_unit,json=discountUnit,proto3,enum=moego.models.membership.v1.DiscountUnit" json:"discount_unit,omitempty"`
	// discount value
	DiscountValue float64 `protobuf:"fixed64,9,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	// feature id
	FeatureId int64 `protobuf:"varint,10,opt,name=feature_id,json=featureId,proto3" json:"feature_id,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deleted at
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
}

func (x *DiscountBenefitModel) Reset() {
	*x = DiscountBenefitModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountBenefitModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountBenefitModel) ProtoMessage() {}

func (x *DiscountBenefitModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountBenefitModel.ProtoReflect.Descriptor instead.
func (*DiscountBenefitModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{5}
}

func (x *DiscountBenefitModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DiscountBenefitModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DiscountBenefitModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DiscountBenefitModel) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *DiscountBenefitModel) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *DiscountBenefitModel) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

func (x *DiscountBenefitModel) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *DiscountBenefitModel) GetDiscountUnit() DiscountUnit {
	if x != nil {
		return x.DiscountUnit
	}
	return DiscountUnit_UNIT_UNSPECIFIED
}

func (x *DiscountBenefitModel) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *DiscountBenefitModel) GetFeatureId() int64 {
	if x != nil {
		return x.FeatureId
	}
	return 0
}

func (x *DiscountBenefitModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DiscountBenefitModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DiscountBenefitModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// quality benefit model, a typo, should be quantity
type QualityBenefitModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,4,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// target sub type
	TargetSubType TargetSubType `protobuf:"varint,6,opt,name=target_sub_type,json=targetSubType,proto3,enum=moego.models.membership.v1.TargetSubType" json:"target_sub_type,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,7,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// is limited
	IsLimited bool `protobuf:"varint,8,opt,name=is_limited,json=isLimited,proto3" json:"is_limited,omitempty"`
	// limited value
	LimitedValue int64 `protobuf:"varint,9,opt,name=limited_value,json=limitedValue,proto3" json:"limited_value,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deleted at
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// feature id
	FeatureId int64 `protobuf:"varint,13,opt,name=feature_id,json=featureId,proto3" json:"feature_id,omitempty"`
}

func (x *QualityBenefitModel) Reset() {
	*x = QualityBenefitModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QualityBenefitModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QualityBenefitModel) ProtoMessage() {}

func (x *QualityBenefitModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QualityBenefitModel.ProtoReflect.Descriptor instead.
func (*QualityBenefitModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{6}
}

func (x *QualityBenefitModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QualityBenefitModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *QualityBenefitModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *QualityBenefitModel) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *QualityBenefitModel) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *QualityBenefitModel) GetTargetSubType() TargetSubType {
	if x != nil {
		return x.TargetSubType
	}
	return TargetSubType_TARGET_SUB_TYPE_UNSPECIFIED
}

func (x *QualityBenefitModel) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *QualityBenefitModel) GetIsLimited() bool {
	if x != nil {
		return x.IsLimited
	}
	return false
}

func (x *QualityBenefitModel) GetLimitedValue() int64 {
	if x != nil {
		return x.LimitedValue
	}
	return 0
}

func (x *QualityBenefitModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *QualityBenefitModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *QualityBenefitModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *QualityBenefitModel) GetFeatureId() int64 {
	if x != nil {
		return x.FeatureId
	}
	return 0
}

// The benefit item
type BenefitRedeemView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// benefit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// target type
	TargetType TargetType `protobuf:"varint,2,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// recommend target id， 只有在需要推荐的时候才存在该字段
	RecommendTargetId *int64 `protobuf:"varint,3,opt,name=recommend_target_id,json=recommendTargetId,proto3,oneof" json:"recommend_target_id,omitempty"`
	// 实际的 target id，只有在 redeem 之后才存在该字段
	ActualTargetId *int64 `protobuf:"varint,4,opt,name=actual_target_id,json=actualTargetId,proto3,oneof" json:"actual_target_id,omitempty"`
	// benefit detail
	//
	// Types that are assignable to BenefitDetail:
	//
	//	*BenefitRedeemView_Discount
	//	*BenefitRedeemView_Quality
	BenefitDetail isBenefitRedeemView_BenefitDetail `protobuf_oneof:"benefit_detail"`
	// redeem amount
	RedeemAmount *int64 `protobuf:"varint,7,opt,name=redeem_amount,json=redeemAmount,proto3,oneof" json:"redeem_amount,omitempty"`
}

func (x *BenefitRedeemView) Reset() {
	*x = BenefitRedeemView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitRedeemView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitRedeemView) ProtoMessage() {}

func (x *BenefitRedeemView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitRedeemView.ProtoReflect.Descriptor instead.
func (*BenefitRedeemView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{7}
}

func (x *BenefitRedeemView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BenefitRedeemView) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *BenefitRedeemView) GetRecommendTargetId() int64 {
	if x != nil && x.RecommendTargetId != nil {
		return *x.RecommendTargetId
	}
	return 0
}

func (x *BenefitRedeemView) GetActualTargetId() int64 {
	if x != nil && x.ActualTargetId != nil {
		return *x.ActualTargetId
	}
	return 0
}

func (m *BenefitRedeemView) GetBenefitDetail() isBenefitRedeemView_BenefitDetail {
	if m != nil {
		return m.BenefitDetail
	}
	return nil
}

func (x *BenefitRedeemView) GetDiscount() *DiscountBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitRedeemView_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *BenefitRedeemView) GetQuality() *QualityBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitRedeemView_Quality); ok {
		return x.Quality
	}
	return nil
}

func (x *BenefitRedeemView) GetRedeemAmount() int64 {
	if x != nil && x.RedeemAmount != nil {
		return *x.RedeemAmount
	}
	return 0
}

type isBenefitRedeemView_BenefitDetail interface {
	isBenefitRedeemView_BenefitDetail()
}

type BenefitRedeemView_Discount struct {
	// discount
	Discount *DiscountBenefitModel `protobuf:"bytes,5,opt,name=discount,proto3,oneof"`
}

type BenefitRedeemView_Quality struct {
	// quality, a typo, should be quantity
	Quality *QualityBenefitModel `protobuf:"bytes,6,opt,name=quality,proto3,oneof"`
}

func (*BenefitRedeemView_Discount) isBenefitRedeemView_BenefitDetail() {}

func (*BenefitRedeemView_Quality) isBenefitRedeemView_BenefitDetail() {}

// The benefit summary view
type BenefitSummaryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the benefit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the target type
	TargetType TargetType `protobuf:"varint,2,opt,name=target_type,json=targetType,proto3,enum=moego.models.membership.v1.TargetType" json:"target_type,omitempty"`
	// the target id
	TargetId int64 `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// the benefit detail
	//
	// Types that are assignable to BenefitDetail:
	//
	//	*BenefitSummaryView_Discount
	//	*BenefitSummaryView_Quality
	BenefitDetail isBenefitSummaryView_BenefitDetail `protobuf_oneof:"benefit_detail"`
	// quantity remaining, only for quality benefit
	QuantityRemaining *int64 `protobuf:"varint,6,opt,name=quantity_remaining,json=quantityRemaining,proto3,oneof" json:"quantity_remaining,omitempty"`
	// target name
	TargetName *string `protobuf:"bytes,7,opt,name=target_name,json=targetName,proto3,oneof" json:"target_name,omitempty"`
}

func (x *BenefitSummaryView) Reset() {
	*x = BenefitSummaryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitSummaryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitSummaryView) ProtoMessage() {}

func (x *BenefitSummaryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_membership_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitSummaryView.ProtoReflect.Descriptor instead.
func (*BenefitSummaryView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_membership_models_proto_rawDescGZIP(), []int{8}
}

func (x *BenefitSummaryView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BenefitSummaryView) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *BenefitSummaryView) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (m *BenefitSummaryView) GetBenefitDetail() isBenefitSummaryView_BenefitDetail {
	if m != nil {
		return m.BenefitDetail
	}
	return nil
}

func (x *BenefitSummaryView) GetDiscount() *DiscountBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitSummaryView_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *BenefitSummaryView) GetQuality() *QualityBenefitModel {
	if x, ok := x.GetBenefitDetail().(*BenefitSummaryView_Quality); ok {
		return x.Quality
	}
	return nil
}

func (x *BenefitSummaryView) GetQuantityRemaining() int64 {
	if x != nil && x.QuantityRemaining != nil {
		return *x.QuantityRemaining
	}
	return 0
}

func (x *BenefitSummaryView) GetTargetName() string {
	if x != nil && x.TargetName != nil {
		return *x.TargetName
	}
	return ""
}

type isBenefitSummaryView_BenefitDetail interface {
	isBenefitSummaryView_BenefitDetail()
}

type BenefitSummaryView_Discount struct {
	// discount
	Discount *DiscountBenefitModel `protobuf:"bytes,4,opt,name=discount,proto3,oneof"`
}

type BenefitSummaryView_Quality struct {
	// quality, a typo, should be quantity
	Quality *QualityBenefitModel `protobuf:"bytes,5,opt,name=quality,proto3,oneof"`
}

func (*BenefitSummaryView_Discount) isBenefitSummaryView_BenefitDetail() {}

func (*BenefitSummaryView_Quality) isBenefitSummaryView_BenefitDetail() {}

var File_moego_models_membership_v1_membership_models_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_membership_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x0b, 0x0a, 0x0f, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x13,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x0d, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74,
	0x61, 0x78, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x54, 0x61, 0x78, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x51, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x50,
	0x0a, 0x19, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x15, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74, 0x22, 0x3a, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x22, 0x54, 0x0a, 0x0c, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x49, 0x4c,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x59, 0x43, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x4f, 0x4e, 0x54,
	0x48, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x4c,
	0x59, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x10, 0x03, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x9e,
	0x08, 0x0a, 0x19, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x0d, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74,
	0x61, 0x78, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x54, 0x61, 0x78, 0x12, 0x50, 0x0a, 0x19, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77,
	0x65, 0x65, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65,
	0x6b, 0x52, 0x15, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x18,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22,
	0xa8, 0x01, 0x0a, 0x16, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e,
	0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x69, 0x6e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x75, 0x73, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70,
	0x61, 0x75, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe6, 0x02, 0x0a, 0x14, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0d, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x53, 0x63,
	0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x73, 0x63, 0x65, 0x6e,
	0x61, 0x72, 0x69, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x4e, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x08,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0c,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x10, 0x0a, 0x0e, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x5f, 0x69, 0x64, 0x22, 0xe1, 0x02, 0x0a, 0x13, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x55, 0x73, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x0d, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x4e, 0x0a,
	0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a,
	0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x00, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x10, 0x0a, 0x0e, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xe4, 0x04, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x12, 0x4d, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55,
	0x6e, 0x69, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xeb,
	0x04, 0x0a, 0x13, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xe8, 0x03, 0x0a,
	0x11, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x2d, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x0e, 0x61, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x4e, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x4b, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x48, 0x00, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x0d,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xba, 0x03, 0x0a, 0x12, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x12, 0x32, 0x0a, 0x12, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65,
	0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52,
	0x11, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x15, 0x0a,
	0x13, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_membership_models_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_membership_models_proto_rawDescData = file_moego_models_membership_v1_membership_models_proto_rawDesc
)

func file_moego_models_membership_v1_membership_models_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_membership_models_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_membership_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_membership_models_proto_rawDescData)
	})
	return file_moego_models_membership_v1_membership_models_proto_rawDescData
}

var file_moego_models_membership_v1_membership_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_membership_v1_membership_models_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_models_membership_v1_membership_models_proto_goTypes = []interface{}{
	(MembershipModel_Status)(0),       // 0: moego.models.membership.v1.MembershipModel.Status
	(MembershipModel_BillingCycle)(0), // 1: moego.models.membership.v1.MembershipModel.BillingCycle
	(*MembershipModel)(nil),           // 2: moego.models.membership.v1.MembershipModel
	(*MembershipModelPublicView)(nil), // 3: moego.models.membership.v1.MembershipModelPublicView
	(*MembershipSummaryModel)(nil),    // 4: moego.models.membership.v1.MembershipSummaryModel
	(*BenefitRecommendView)(nil),      // 5: moego.models.membership.v1.BenefitRecommendView
	(*MembershipUsageView)(nil),       // 6: moego.models.membership.v1.MembershipUsageView
	(*DiscountBenefitModel)(nil),      // 7: moego.models.membership.v1.DiscountBenefitModel
	(*QualityBenefitModel)(nil),       // 8: moego.models.membership.v1.QualityBenefitModel
	(*BenefitRedeemView)(nil),         // 9: moego.models.membership.v1.BenefitRedeemView
	(*BenefitSummaryView)(nil),        // 10: moego.models.membership.v1.BenefitSummaryView
	(*v1.TimePeriod)(nil),             // 11: moego.utils.v1.TimePeriod
	(*timestamppb.Timestamp)(nil),     // 12: google.protobuf.Timestamp
	(*money.Money)(nil),               // 13: google.type.Money
	(dayofweek.DayOfWeek)(0),          // 14: google.type.DayOfWeek
	(*v11.CustomizedBreed)(nil),       // 15: moego.models.offering.v1.CustomizedBreed
	(*RedeemScenarioItem)(nil),        // 16: moego.models.membership.v1.RedeemScenarioItem
	(TargetType)(0),                   // 17: moego.models.membership.v1.TargetType
	(DiscountUnit)(0),                 // 18: moego.models.membership.v1.DiscountUnit
	(TargetSubType)(0),                // 19: moego.models.membership.v1.TargetSubType
}
var file_moego_models_membership_v1_membership_models_proto_depIdxs = []int32{
	0,  // 0: moego.models.membership.v1.MembershipModel.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	1,  // 1: moego.models.membership.v1.MembershipModel.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	11, // 2: moego.models.membership.v1.MembershipModel.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	12, // 3: moego.models.membership.v1.MembershipModel.created_at:type_name -> google.protobuf.Timestamp
	12, // 4: moego.models.membership.v1.MembershipModel.updated_at:type_name -> google.protobuf.Timestamp
	12, // 5: moego.models.membership.v1.MembershipModel.deleted_at:type_name -> google.protobuf.Timestamp
	13, // 6: moego.models.membership.v1.MembershipModel.total_price:type_name -> google.type.Money
	13, // 7: moego.models.membership.v1.MembershipModel.total_tax:type_name -> google.type.Money
	14, // 8: moego.models.membership.v1.MembershipModel.billing_cycle_day_of_week:type_name -> google.type.DayOfWeek
	15, // 9: moego.models.membership.v1.MembershipModel.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	0,  // 10: moego.models.membership.v1.MembershipModelPublicView.status:type_name -> moego.models.membership.v1.MembershipModel.Status
	1,  // 11: moego.models.membership.v1.MembershipModelPublicView.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	11, // 12: moego.models.membership.v1.MembershipModelPublicView.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	12, // 13: moego.models.membership.v1.MembershipModelPublicView.created_at:type_name -> google.protobuf.Timestamp
	12, // 14: moego.models.membership.v1.MembershipModelPublicView.updated_at:type_name -> google.protobuf.Timestamp
	12, // 15: moego.models.membership.v1.MembershipModelPublicView.deleted_at:type_name -> google.protobuf.Timestamp
	13, // 16: moego.models.membership.v1.MembershipModelPublicView.total_price:type_name -> google.type.Money
	13, // 17: moego.models.membership.v1.MembershipModelPublicView.total_tax:type_name -> google.type.Money
	14, // 18: moego.models.membership.v1.MembershipModelPublicView.billing_cycle_day_of_week:type_name -> google.type.DayOfWeek
	16, // 19: moego.models.membership.v1.BenefitRecommendView.scenario_item:type_name -> moego.models.membership.v1.RedeemScenarioItem
	7,  // 20: moego.models.membership.v1.BenefitRecommendView.discount:type_name -> moego.models.membership.v1.DiscountBenefitModel
	8,  // 21: moego.models.membership.v1.BenefitRecommendView.quality:type_name -> moego.models.membership.v1.QualityBenefitModel
	7,  // 22: moego.models.membership.v1.MembershipUsageView.discount:type_name -> moego.models.membership.v1.DiscountBenefitModel
	8,  // 23: moego.models.membership.v1.MembershipUsageView.quantity:type_name -> moego.models.membership.v1.QualityBenefitModel
	17, // 24: moego.models.membership.v1.DiscountBenefitModel.target_type:type_name -> moego.models.membership.v1.TargetType
	18, // 25: moego.models.membership.v1.DiscountBenefitModel.discount_unit:type_name -> moego.models.membership.v1.DiscountUnit
	12, // 26: moego.models.membership.v1.DiscountBenefitModel.created_at:type_name -> google.protobuf.Timestamp
	12, // 27: moego.models.membership.v1.DiscountBenefitModel.updated_at:type_name -> google.protobuf.Timestamp
	12, // 28: moego.models.membership.v1.DiscountBenefitModel.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 29: moego.models.membership.v1.QualityBenefitModel.target_type:type_name -> moego.models.membership.v1.TargetType
	19, // 30: moego.models.membership.v1.QualityBenefitModel.target_sub_type:type_name -> moego.models.membership.v1.TargetSubType
	12, // 31: moego.models.membership.v1.QualityBenefitModel.created_at:type_name -> google.protobuf.Timestamp
	12, // 32: moego.models.membership.v1.QualityBenefitModel.updated_at:type_name -> google.protobuf.Timestamp
	12, // 33: moego.models.membership.v1.QualityBenefitModel.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 34: moego.models.membership.v1.BenefitRedeemView.target_type:type_name -> moego.models.membership.v1.TargetType
	7,  // 35: moego.models.membership.v1.BenefitRedeemView.discount:type_name -> moego.models.membership.v1.DiscountBenefitModel
	8,  // 36: moego.models.membership.v1.BenefitRedeemView.quality:type_name -> moego.models.membership.v1.QualityBenefitModel
	17, // 37: moego.models.membership.v1.BenefitSummaryView.target_type:type_name -> moego.models.membership.v1.TargetType
	7,  // 38: moego.models.membership.v1.BenefitSummaryView.discount:type_name -> moego.models.membership.v1.DiscountBenefitModel
	8,  // 39: moego.models.membership.v1.BenefitSummaryView.quality:type_name -> moego.models.membership.v1.QualityBenefitModel
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_membership_models_proto_init() }
func file_moego_models_membership_v1_membership_models_proto_init() {
	if File_moego_models_membership_v1_membership_models_proto != nil {
		return
	}
	file_moego_models_membership_v1_redeem_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_membership_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipSummaryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitRecommendView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipUsageView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountBenefitModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QualityBenefitModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitRedeemView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_membership_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitSummaryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*BenefitRecommendView_Discount)(nil),
		(*BenefitRecommendView_Quality)(nil),
	}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*MembershipUsageView_Discount)(nil),
		(*MembershipUsageView_Quantity)(nil),
	}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*BenefitRedeemView_Discount)(nil),
		(*BenefitRedeemView_Quality)(nil),
	}
	file_moego_models_membership_v1_membership_models_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*BenefitSummaryView_Discount)(nil),
		(*BenefitSummaryView_Quality)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_membership_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_membership_models_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_membership_models_proto_depIdxs,
		EnumInfos:         file_moego_models_membership_v1_membership_models_proto_enumTypes,
		MessageInfos:      file_moego_models_membership_v1_membership_models_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_membership_models_proto = out.File
	file_moego_models_membership_v1_membership_models_proto_rawDesc = nil
	file_moego_models_membership_v1_membership_models_proto_goTypes = nil
	file_moego_models_membership_v1_membership_models_proto_depIdxs = nil
}
