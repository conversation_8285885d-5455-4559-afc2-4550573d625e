// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v2/message_enums.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// message send channel
type Channel int32

const (
	// unspecified
	Channel_CHANNEL_UNSPECIFIED Channel = 0
	// sms
	Channel_SMS Channel = 1
	// email
	Channel_EMAIL Channel = 2
	// call
	Channel_CALL Channel = 3
	// In-App msg
	Channel_APP Channel = 4
)

// Enum value maps for Channel.
var (
	Channel_name = map[int32]string{
		0: "CHANNEL_UNSPECIFIED",
		1: "SMS",
		2: "EMAIL",
		3: "CALL",
		4: "APP",
	}
	Channel_value = map[string]int32{
		"CHANNEL_UNSPECIFIED": 0,
		"SMS":                 1,
		"EMAIL":               2,
		"CALL":                3,
		"APP":                 4,
	}
)

func (x Channel) Enum() *Channel {
	p := new(Channel)
	*p = x
	return p
}

func (x Channel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Channel) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_enums_proto_enumTypes[0].Descriptor()
}

func (Channel) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_enums_proto_enumTypes[0]
}

func (x Channel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Channel.Descriptor instead.
func (Channel) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_enums_proto_rawDescGZIP(), []int{0}
}

// message sender and receiver role
type Role int32

const (
	// unspecified
	Role_ROLE_UNSPECIFIED Role = 0
	// business
	Role_BUSINESS Role = 1
	// customer
	Role_CUSTOMER Role = 2
	// platform
	Role_PLATFORM Role = 3
)

// Enum value maps for Role.
var (
	Role_name = map[int32]string{
		0: "ROLE_UNSPECIFIED",
		1: "BUSINESS",
		2: "CUSTOMER",
		3: "PLATFORM",
	}
	Role_value = map[string]int32{
		"ROLE_UNSPECIFIED": 0,
		"BUSINESS":         1,
		"CUSTOMER":         2,
		"PLATFORM":         3,
	}
)

func (x Role) Enum() *Role {
	p := new(Role)
	*p = x
	return p
}

func (x Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Role) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_enums_proto_enumTypes[1].Descriptor()
}

func (Role) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_enums_proto_enumTypes[1]
}

func (x Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Role.Descriptor instead.
func (Role) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_enums_proto_rawDescGZIP(), []int{1}
}

// message status
type MessageStatus int32

const (
	// 未知状态
	MessageStatus_MSG_STATUS_UNSPECIFIED MessageStatus = 0
	// 消息已创建
	MessageStatus_MSG_CREATED MessageStatus = 100
	// 消息已投递
	MessageStatus_MSG_DELIVERED MessageStatus = 200
	// 消息投递失败
	MessageStatus_MSG_DELIVER_FAILED MessageStatus = 250
	// 消息已删除
	MessageStatus_MSG_DELETED MessageStatus = 400
)

// Enum value maps for MessageStatus.
var (
	MessageStatus_name = map[int32]string{
		0:   "MSG_STATUS_UNSPECIFIED",
		100: "MSG_CREATED",
		200: "MSG_DELIVERED",
		250: "MSG_DELIVER_FAILED",
		400: "MSG_DELETED",
	}
	MessageStatus_value = map[string]int32{
		"MSG_STATUS_UNSPECIFIED": 0,
		"MSG_CREATED":            100,
		"MSG_DELIVERED":          200,
		"MSG_DELIVER_FAILED":     250,
		"MSG_DELETED":            400,
	}
)

func (x MessageStatus) Enum() *MessageStatus {
	p := new(MessageStatus)
	*p = x
	return p
}

func (x MessageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_enums_proto_enumTypes[2].Descriptor()
}

func (MessageStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_enums_proto_enumTypes[2]
}

func (x MessageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageStatus.Descriptor instead.
func (MessageStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_enums_proto_rawDescGZIP(), []int{2}
}

// 消息内容的类型
type ContentType int32

const (
	// 未指定类型
	ContentType_CONTENT_TYPE_UNSPECIFIED ContentType = 0
	// 纯文本
	ContentType_CONTENT_PLAINTEXT ContentType = 1
	// 图片
	ContentType_CONTENT_PICTURE ContentType = 2
	// 视频
	ContentType_CONTENT_VIDEO ContentType = 3
)

// Enum value maps for ContentType.
var (
	ContentType_name = map[int32]string{
		0: "CONTENT_TYPE_UNSPECIFIED",
		1: "CONTENT_PLAINTEXT",
		2: "CONTENT_PICTURE",
		3: "CONTENT_VIDEO",
	}
	ContentType_value = map[string]int32{
		"CONTENT_TYPE_UNSPECIFIED": 0,
		"CONTENT_PLAINTEXT":        1,
		"CONTENT_PICTURE":          2,
		"CONTENT_VIDEO":            3,
	}
)

func (x ContentType) Enum() *ContentType {
	p := new(ContentType)
	*p = x
	return p
}

func (x ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_enums_proto_enumTypes[3].Descriptor()
}

func (ContentType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_enums_proto_enumTypes[3]
}

func (x ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContentType.Descriptor instead.
func (ContentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_enums_proto_rawDescGZIP(), []int{3}
}

// chat status
type ChatStatus int32

const (
	// 未指定状态
	ChatStatus_CHAT_STATUS_UNSPECIFIED ChatStatus = 0
	// 会话已打开
	ChatStatus_CHAT_OPENED ChatStatus = 100
	// 会话已关闭
	ChatStatus_CHAT_CLOSED ChatStatus = 200
	// 会话已封锁
	ChatStatus_CHAT_BLOCKED ChatStatus = 300
	// 会话已删除
	ChatStatus_CHAT_DELETED ChatStatus = 400
)

// Enum value maps for ChatStatus.
var (
	ChatStatus_name = map[int32]string{
		0:   "CHAT_STATUS_UNSPECIFIED",
		100: "CHAT_OPENED",
		200: "CHAT_CLOSED",
		300: "CHAT_BLOCKED",
		400: "CHAT_DELETED",
	}
	ChatStatus_value = map[string]int32{
		"CHAT_STATUS_UNSPECIFIED": 0,
		"CHAT_OPENED":             100,
		"CHAT_CLOSED":             200,
		"CHAT_BLOCKED":            300,
		"CHAT_DELETED":            400,
	}
)

func (x ChatStatus) Enum() *ChatStatus {
	p := new(ChatStatus)
	*p = x
	return p
}

func (x ChatStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_enums_proto_enumTypes[4].Descriptor()
}

func (ChatStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_enums_proto_enumTypes[4]
}

func (x ChatStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatStatus.Descriptor instead.
func (ChatStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_enums_proto_rawDescGZIP(), []int{4}
}

var File_moego_models_message_v2_message_enums_proto protoreflect.FileDescriptor

var file_moego_models_message_v2_message_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2a, 0x49, 0x0a, 0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x4d,
	0x53, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x50, 0x50, 0x10,
	0x04, 0x2a, 0x46, 0x0a, 0x04, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x4f, 0x4c,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50,
	0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x03, 0x2a, 0x7b, 0x0a, 0x0d, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x53,
	0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x64, 0x12, 0x12, 0x0a, 0x0d, 0x4d, 0x53, 0x47, 0x5f, 0x44,
	0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0xc8, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x4d,
	0x53, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0xfa, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4d, 0x53, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x90, 0x03, 0x2a, 0x6a, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f,
	0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x49, 0x43, 0x54, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x10, 0x03, 0x2a, 0x72, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1b, 0x0a, 0x17, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44, 0x10, 0x64, 0x12, 0x10,
	0x0a, 0x0b, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0xc8, 0x01,
	0x12, 0x11, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x10, 0xac, 0x02, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x90, 0x03, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v2_message_enums_proto_rawDescOnce sync.Once
	file_moego_models_message_v2_message_enums_proto_rawDescData = file_moego_models_message_v2_message_enums_proto_rawDesc
)

func file_moego_models_message_v2_message_enums_proto_rawDescGZIP() []byte {
	file_moego_models_message_v2_message_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v2_message_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v2_message_enums_proto_rawDescData)
	})
	return file_moego_models_message_v2_message_enums_proto_rawDescData
}

var file_moego_models_message_v2_message_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_moego_models_message_v2_message_enums_proto_goTypes = []interface{}{
	(Channel)(0),       // 0: moego.models.message.v2.Channel
	(Role)(0),          // 1: moego.models.message.v2.Role
	(MessageStatus)(0), // 2: moego.models.message.v2.MessageStatus
	(ContentType)(0),   // 3: moego.models.message.v2.ContentType
	(ChatStatus)(0),    // 4: moego.models.message.v2.ChatStatus
}
var file_moego_models_message_v2_message_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_message_v2_message_enums_proto_init() }
func file_moego_models_message_v2_message_enums_proto_init() {
	if File_moego_models_message_v2_message_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v2_message_enums_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v2_message_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v2_message_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v2_message_enums_proto_enumTypes,
	}.Build()
	File_moego_models_message_v2_message_enums_proto = out.File
	file_moego_models_message_v2_message_enums_proto_rawDesc = nil
	file_moego_models_message_v2_message_enums_proto_goTypes = nil
	file_moego_models_message_v2_message_enums_proto_depIdxs = nil
}
