// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/marketing_email_models.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// marketing email template type
type MarketingEmailTemplateModel_MarketingEmailTemplateType int32

const (
	// unspecified
	MarketingEmailTemplateModel_MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED MarketingEmailTemplateModel_MarketingEmailTemplateType = 0
	// Merry Christmas
	MarketingEmailTemplateModel_MERRY_CHRISTMAS MarketingEmailTemplateModel_MarketingEmailTemplateType = 1
	// Thanksgiving
	MarketingEmailTemplateModel_THANKSGIVING MarketingEmailTemplateModel_MarketingEmailTemplateType = 2
	// Halloween
	MarketingEmailTemplateModel_HALLOWEEN MarketingEmailTemplateModel_MarketingEmailTemplateType = 3
	// Introduce pet parent app
	MarketingEmailTemplateModel_INTRODUCE_PET_PARENT_APP MarketingEmailTemplateModel_MarketingEmailTemplateType = 4
	// Win back former customers
	MarketingEmailTemplateModel_WIN_BACK_FORMER_CUSTOMERS MarketingEmailTemplateModel_MarketingEmailTemplateType = 5
	// Book again
	MarketingEmailTemplateModel_BOOK_AGAIN MarketingEmailTemplateModel_MarketingEmailTemplateType = 6
	// Review booster
	MarketingEmailTemplateModel_REVIEW_BOOSTER MarketingEmailTemplateModel_MarketingEmailTemplateType = 7
	// Fill time slots
	MarketingEmailTemplateModel_FILL_TIME_SLOTS MarketingEmailTemplateModel_MarketingEmailTemplateType = 8
	// Introduce groomer
	MarketingEmailTemplateModel_INTRODUCE_GROOMER MarketingEmailTemplateModel_MarketingEmailTemplateType = 9
	// Converter prospects
	MarketingEmailTemplateModel_CONVERTER_PROSPECTS MarketingEmailTemplateModel_MarketingEmailTemplateType = 10
	// Enterprise customize
	MarketingEmailTemplateModel_ENTERPRISE_CUSTOMIZE MarketingEmailTemplateModel_MarketingEmailTemplateType = 11
)

// Enum value maps for MarketingEmailTemplateModel_MarketingEmailTemplateType.
var (
	MarketingEmailTemplateModel_MarketingEmailTemplateType_name = map[int32]string{
		0:  "MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED",
		1:  "MERRY_CHRISTMAS",
		2:  "THANKSGIVING",
		3:  "HALLOWEEN",
		4:  "INTRODUCE_PET_PARENT_APP",
		5:  "WIN_BACK_FORMER_CUSTOMERS",
		6:  "BOOK_AGAIN",
		7:  "REVIEW_BOOSTER",
		8:  "FILL_TIME_SLOTS",
		9:  "INTRODUCE_GROOMER",
		10: "CONVERTER_PROSPECTS",
		11: "ENTERPRISE_CUSTOMIZE",
	}
	MarketingEmailTemplateModel_MarketingEmailTemplateType_value = map[string]int32{
		"MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED": 0,
		"MERRY_CHRISTMAS":           1,
		"THANKSGIVING":              2,
		"HALLOWEEN":                 3,
		"INTRODUCE_PET_PARENT_APP":  4,
		"WIN_BACK_FORMER_CUSTOMERS": 5,
		"BOOK_AGAIN":                6,
		"REVIEW_BOOSTER":            7,
		"FILL_TIME_SLOTS":           8,
		"INTRODUCE_GROOMER":         9,
		"CONVERTER_PROSPECTS":       10,
		"ENTERPRISE_CUSTOMIZE":      11,
	}
)

func (x MarketingEmailTemplateModel_MarketingEmailTemplateType) Enum() *MarketingEmailTemplateModel_MarketingEmailTemplateType {
	p := new(MarketingEmailTemplateModel_MarketingEmailTemplateType)
	*p = x
	return p
}

func (x MarketingEmailTemplateModel_MarketingEmailTemplateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketingEmailTemplateModel_MarketingEmailTemplateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_marketing_email_models_proto_enumTypes[0].Descriptor()
}

func (MarketingEmailTemplateModel_MarketingEmailTemplateType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_marketing_email_models_proto_enumTypes[0]
}

func (x MarketingEmailTemplateModel_MarketingEmailTemplateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarketingEmailTemplateModel_MarketingEmailTemplateType.Descriptor instead.
func (MarketingEmailTemplateModel_MarketingEmailTemplateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{2, 0}
}

// marketing email model
type MarketingEmailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// email subject
	Subject string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	// email content
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// for schedule emails, the send time
	SendAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=send_at,json=sendAt,proto3" json:"send_at,omitempty"`
	// recipients
	Recipients []*RecipientDef `protobuf:"bytes,5,rep,name=recipients,proto3" json:"recipients,omitempty"`
	// email attachment urls
	AttachmentUrls []*AttachmentDef `protobuf:"bytes,6,rep,name=attachment_urls,json=attachmentUrls,proto3" json:"attachment_urls,omitempty"`
	// last update time (only for draft)
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// count of recipients
	RecipientsNumber int64 `protobuf:"varint,8,opt,name=recipients_number,json=recipientsNumber,proto3" json:"recipients_number,omitempty"`
	// open rate
	OpenedNumber int64 `protobuf:"varint,9,opt,name=opened_number,json=openedNumber,proto3" json:"opened_number,omitempty"`
	// email status
	Status MarketingEmailStatus `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.message.v1.MarketingEmailStatus" json:"status,omitempty"`
	// count of delivered
	DeliveredNumber int64 `protobuf:"varint,11,opt,name=delivered_number,json=deliveredNumber,proto3" json:"delivered_number,omitempty"`
	// count of clicked
	ClickedNumber int64 `protobuf:"varint,12,opt,name=clicked_number,json=clickedNumber,proto3" json:"clicked_number,omitempty"`
	// count of replied
	RepliedNumber int64 `protobuf:"varint,13,opt,name=replied_number,json=repliedNumber,proto3" json:"replied_number,omitempty"`
	// client filter condition, JSON string
	ClientFilter string `protobuf:"bytes,14,opt,name=client_filter,json=clientFilter,proto3" json:"client_filter,omitempty"`
	// if any unread reply
	HasUnreadReply bool `protobuf:"varint,15,opt,name=has_unread_reply,json=hasUnreadReply,proto3" json:"has_unread_reply,omitempty"`
}

func (x *MarketingEmailModel) Reset() {
	*x = MarketingEmailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailModel) ProtoMessage() {}

func (x *MarketingEmailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailModel.ProtoReflect.Descriptor instead.
func (*MarketingEmailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{0}
}

func (x *MarketingEmailModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingEmailModel) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *MarketingEmailModel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MarketingEmailModel) GetSendAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SendAt
	}
	return nil
}

func (x *MarketingEmailModel) GetRecipients() []*RecipientDef {
	if x != nil {
		return x.Recipients
	}
	return nil
}

func (x *MarketingEmailModel) GetAttachmentUrls() []*AttachmentDef {
	if x != nil {
		return x.AttachmentUrls
	}
	return nil
}

func (x *MarketingEmailModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *MarketingEmailModel) GetRecipientsNumber() int64 {
	if x != nil {
		return x.RecipientsNumber
	}
	return 0
}

func (x *MarketingEmailModel) GetOpenedNumber() int64 {
	if x != nil {
		return x.OpenedNumber
	}
	return 0
}

func (x *MarketingEmailModel) GetStatus() MarketingEmailStatus {
	if x != nil {
		return x.Status
	}
	return MarketingEmailStatus_MARKETING_EMAIL_STATUS_UNSPECIFIED
}

func (x *MarketingEmailModel) GetDeliveredNumber() int64 {
	if x != nil {
		return x.DeliveredNumber
	}
	return 0
}

func (x *MarketingEmailModel) GetClickedNumber() int64 {
	if x != nil {
		return x.ClickedNumber
	}
	return 0
}

func (x *MarketingEmailModel) GetRepliedNumber() int64 {
	if x != nil {
		return x.RepliedNumber
	}
	return 0
}

func (x *MarketingEmailModel) GetClientFilter() string {
	if x != nil {
		return x.ClientFilter
	}
	return ""
}

func (x *MarketingEmailModel) GetHasUnreadReply() bool {
	if x != nil {
		return x.HasUnreadReply
	}
	return false
}

// marketing email model brief view
type MarketingEmailModelBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// email subject
	Subject string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	// email status
	Status MarketingEmailStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.message.v1.MarketingEmailStatus" json:"status,omitempty"`
	// last update time (only for draft)
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// sent time
	SentTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=sent_time,json=sentTime,proto3" json:"sent_time,omitempty"`
	// count of recipients
	RecipientsNumber int64 `protobuf:"varint,6,opt,name=recipients_number,json=recipientsNumber,proto3" json:"recipients_number,omitempty"`
	// open rate
	OpenedNumber int64 `protobuf:"varint,7,opt,name=opened_number,json=openedNumber,proto3" json:"opened_number,omitempty"`
	// if any unread reply
	HasUnreadReply bool `protobuf:"varint,8,opt,name=has_unread_reply,json=hasUnreadReply,proto3" json:"has_unread_reply,omitempty"`
}

func (x *MarketingEmailModelBriefView) Reset() {
	*x = MarketingEmailModelBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailModelBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailModelBriefView) ProtoMessage() {}

func (x *MarketingEmailModelBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailModelBriefView.ProtoReflect.Descriptor instead.
func (*MarketingEmailModelBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{1}
}

func (x *MarketingEmailModelBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingEmailModelBriefView) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *MarketingEmailModelBriefView) GetStatus() MarketingEmailStatus {
	if x != nil {
		return x.Status
	}
	return MarketingEmailStatus_MARKETING_EMAIL_STATUS_UNSPECIFIED
}

func (x *MarketingEmailModelBriefView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *MarketingEmailModelBriefView) GetSentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SentTime
	}
	return nil
}

func (x *MarketingEmailModelBriefView) GetRecipientsNumber() int64 {
	if x != nil {
		return x.RecipientsNumber
	}
	return 0
}

func (x *MarketingEmailModelBriefView) GetOpenedNumber() int64 {
	if x != nil {
		return x.OpenedNumber
	}
	return 0
}

func (x *MarketingEmailModelBriefView) GetHasUnreadReply() bool {
	if x != nil {
		return x.HasUnreadReply
	}
	return false
}

// marketing email template model
type MarketingEmailTemplateModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// template name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// template picture url
	PictureUrl string `protobuf:"bytes,3,opt,name=picture_url,json=pictureUrl,proto3" json:"picture_url,omitempty"`
	// template content
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// template subject
	Subject string `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	// template description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// default client filter of this template, JSON string
	ClientFilter string `protobuf:"bytes,7,opt,name=client_filter,json=clientFilter,proto3" json:"client_filter,omitempty"`
	// template type
	Type MarketingEmailTemplateModel_MarketingEmailTemplateType `protobuf:"varint,8,opt,name=type,proto3,enum=moego.models.message.v1.MarketingEmailTemplateModel_MarketingEmailTemplateType" json:"type,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,9,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *MarketingEmailTemplateModel) Reset() {
	*x = MarketingEmailTemplateModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailTemplateModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailTemplateModel) ProtoMessage() {}

func (x *MarketingEmailTemplateModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailTemplateModel.ProtoReflect.Descriptor instead.
func (*MarketingEmailTemplateModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{2}
}

func (x *MarketingEmailTemplateModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingEmailTemplateModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetPictureUrl() string {
	if x != nil {
		return x.PictureUrl
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetClientFilter() string {
	if x != nil {
		return x.ClientFilter
	}
	return ""
}

func (x *MarketingEmailTemplateModel) GetType() MarketingEmailTemplateModel_MarketingEmailTemplateType {
	if x != nil {
		return x.Type
	}
	return MarketingEmailTemplateModel_MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *MarketingEmailTemplateModel) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// marketing email template model brief view
type MarketingEmailTemplateModelBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// template name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// template picture url
	PictureUrl string `protobuf:"bytes,3,opt,name=picture_url,json=pictureUrl,proto3" json:"picture_url,omitempty"`
	// template description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// template subject
	Subject string `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	// template type
	Type MarketingEmailTemplateModel_MarketingEmailTemplateType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.message.v1.MarketingEmailTemplateModel_MarketingEmailTemplateType" json:"type,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,7,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *MarketingEmailTemplateModelBriefView) Reset() {
	*x = MarketingEmailTemplateModelBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailTemplateModelBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailTemplateModelBriefView) ProtoMessage() {}

func (x *MarketingEmailTemplateModelBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailTemplateModelBriefView.ProtoReflect.Descriptor instead.
func (*MarketingEmailTemplateModelBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{3}
}

func (x *MarketingEmailTemplateModelBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingEmailTemplateModelBriefView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MarketingEmailTemplateModelBriefView) GetPictureUrl() string {
	if x != nil {
		return x.PictureUrl
	}
	return ""
}

func (x *MarketingEmailTemplateModelBriefView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MarketingEmailTemplateModelBriefView) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *MarketingEmailTemplateModelBriefView) GetType() MarketingEmailTemplateModel_MarketingEmailTemplateType {
	if x != nil {
		return x.Type
	}
	return MarketingEmailTemplateModel_MARKETING_EMAIL_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *MarketingEmailTemplateModelBriefView) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// marketing email recipient model
type MarketingEmailRecipientModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// recipient id
	RecipientId int64 `protobuf:"varint,2,opt,name=recipient_id,json=recipientId,proto3" json:"recipient_id,omitempty"`
	// recipient email
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// recipient name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// recipient open status
	IsOpened bool `protobuf:"varint,5,opt,name=is_opened,json=isOpened,proto3" json:"is_opened,omitempty"`
	// recipient click status
	IsClicked bool `protobuf:"varint,6,opt,name=is_clicked,json=isClicked,proto3" json:"is_clicked,omitempty"`
	// recipient reply status
	IsReplied bool `protobuf:"varint,7,opt,name=is_replied,json=isReplied,proto3" json:"is_replied,omitempty"`
	// whether the recipient reply is read
	IsReplyRead bool `protobuf:"varint,8,opt,name=is_reply_read,json=isReplyRead,proto3" json:"is_reply_read,omitempty"`
	// recipient send status
	SendStatus MarketingEmailRecipientSendStatus `protobuf:"varint,9,opt,name=send_status,json=sendStatus,proto3,enum=moego.models.message.v1.MarketingEmailRecipientSendStatus" json:"send_status,omitempty"`
	// open time
	OpenTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=open_time,json=openTime,proto3" json:"open_time,omitempty"`
	// click time
	ClickTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=click_time,json=clickTime,proto3" json:"click_time,omitempty"`
	// reply time
	ReplyTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=reply_time,json=replyTime,proto3" json:"reply_time,omitempty"`
}

func (x *MarketingEmailRecipientModel) Reset() {
	*x = MarketingEmailRecipientModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailRecipientModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailRecipientModel) ProtoMessage() {}

func (x *MarketingEmailRecipientModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailRecipientModel.ProtoReflect.Descriptor instead.
func (*MarketingEmailRecipientModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{4}
}

func (x *MarketingEmailRecipientModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *MarketingEmailRecipientModel) GetRecipientId() int64 {
	if x != nil {
		return x.RecipientId
	}
	return 0
}

func (x *MarketingEmailRecipientModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MarketingEmailRecipientModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MarketingEmailRecipientModel) GetIsOpened() bool {
	if x != nil {
		return x.IsOpened
	}
	return false
}

func (x *MarketingEmailRecipientModel) GetIsClicked() bool {
	if x != nil {
		return x.IsClicked
	}
	return false
}

func (x *MarketingEmailRecipientModel) GetIsReplied() bool {
	if x != nil {
		return x.IsReplied
	}
	return false
}

func (x *MarketingEmailRecipientModel) GetIsReplyRead() bool {
	if x != nil {
		return x.IsReplyRead
	}
	return false
}

func (x *MarketingEmailRecipientModel) GetSendStatus() MarketingEmailRecipientSendStatus {
	if x != nil {
		return x.SendStatus
	}
	return MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_UNSPECIFIED
}

func (x *MarketingEmailRecipientModel) GetOpenTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OpenTime
	}
	return nil
}

func (x *MarketingEmailRecipientModel) GetClickTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ClickTime
	}
	return nil
}

func (x *MarketingEmailRecipientModel) GetReplyTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReplyTime
	}
	return nil
}

// marketing email recipient model brief view
type MarketingEmailApptBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appt id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appt revenue amount
	Revenue string `protobuf:"bytes,2,opt,name=revenue,proto3" json:"revenue,omitempty"`
	// customer name
	CustomerName string `protobuf:"bytes,3,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// customer email
	CustomerEmail string `protobuf:"bytes,4,opt,name=customer_email,json=customerEmail,proto3" json:"customer_email,omitempty"`
	// appt created time
	CreatedTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
}

func (x *MarketingEmailApptBriefView) Reset() {
	*x = MarketingEmailApptBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingEmailApptBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingEmailApptBriefView) ProtoMessage() {}

func (x *MarketingEmailApptBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_marketing_email_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingEmailApptBriefView.ProtoReflect.Descriptor instead.
func (*MarketingEmailApptBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP(), []int{5}
}

func (x *MarketingEmailApptBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingEmailApptBriefView) GetRevenue() string {
	if x != nil {
		return x.Revenue
	}
	return ""
}

func (x *MarketingEmailApptBriefView) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *MarketingEmailApptBriefView) GetCustomerEmail() string {
	if x != nil {
		return x.CustomerEmail
	}
	return ""
}

func (x *MarketingEmailApptBriefView) GetCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

var File_moego_models_message_v1_marketing_email_models_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_marketing_email_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x05, 0x0a, 0x13, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x0a, 0x72, 0x65,
	0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x4f, 0x0a, 0x0f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x66, 0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d,
	0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x75, 0x6e,
	0x72, 0x65, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x68, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x81, 0x03, 0x0a, 0x1c, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x37, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08,
	0x73, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x6e, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x61,
	0x73, 0x5f, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0xab, 0x05, 0x0a, 0x1b, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x63, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0xc1,
	0x02, 0x0a, 0x1a, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a,
	0x29, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x4d, 0x45, 0x52, 0x52, 0x59, 0x5f, 0x43, 0x48, 0x52, 0x49, 0x53, 0x54, 0x4d, 0x41, 0x53, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x48, 0x41, 0x4e, 0x4b, 0x53, 0x47, 0x49, 0x56, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x45, 0x4e,
	0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x45, 0x5f,
	0x50, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x04,
	0x12, 0x1d, 0x0a, 0x19, 0x57, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x53, 0x10, 0x05, 0x12,
	0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x41, 0x47, 0x41, 0x49, 0x4e, 0x10, 0x06, 0x12,
	0x12, 0x0a, 0x0e, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45,
	0x52, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x53, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4e, 0x54, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x09, 0x12,
	0x17, 0x0a, 0x13, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f,
	0x53, 0x50, 0x45, 0x43, 0x54, 0x53, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45,
	0x10, 0x0b, 0x22, 0xb1, 0x02, 0x0a, 0x24, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x63, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x97, 0x04, 0x0a, 0x1c, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e,
	0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x61,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x52, 0x65, 0x61, 0x64, 0x12, 0x5b, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x37, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xd2, 0x01, 0x0a, 0x1b, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x41, 0x70, 0x70, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_marketing_email_models_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_marketing_email_models_proto_rawDescData = file_moego_models_message_v1_marketing_email_models_proto_rawDesc
)

func file_moego_models_message_v1_marketing_email_models_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_marketing_email_models_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_marketing_email_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_marketing_email_models_proto_rawDescData)
	})
	return file_moego_models_message_v1_marketing_email_models_proto_rawDescData
}

var file_moego_models_message_v1_marketing_email_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_message_v1_marketing_email_models_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_message_v1_marketing_email_models_proto_goTypes = []interface{}{
	(MarketingEmailTemplateModel_MarketingEmailTemplateType)(0), // 0: moego.models.message.v1.MarketingEmailTemplateModel.MarketingEmailTemplateType
	(*MarketingEmailModel)(nil),                                 // 1: moego.models.message.v1.MarketingEmailModel
	(*MarketingEmailModelBriefView)(nil),                        // 2: moego.models.message.v1.MarketingEmailModelBriefView
	(*MarketingEmailTemplateModel)(nil),                         // 3: moego.models.message.v1.MarketingEmailTemplateModel
	(*MarketingEmailTemplateModelBriefView)(nil),                // 4: moego.models.message.v1.MarketingEmailTemplateModelBriefView
	(*MarketingEmailRecipientModel)(nil),                        // 5: moego.models.message.v1.MarketingEmailRecipientModel
	(*MarketingEmailApptBriefView)(nil),                         // 6: moego.models.message.v1.MarketingEmailApptBriefView
	(*timestamppb.Timestamp)(nil),                               // 7: google.protobuf.Timestamp
	(*RecipientDef)(nil),                                        // 8: moego.models.message.v1.RecipientDef
	(*AttachmentDef)(nil),                                       // 9: moego.models.message.v1.AttachmentDef
	(MarketingEmailStatus)(0),                                   // 10: moego.models.message.v1.MarketingEmailStatus
	(MarketingEmailRecipientSendStatus)(0),                      // 11: moego.models.message.v1.MarketingEmailRecipientSendStatus
}
var file_moego_models_message_v1_marketing_email_models_proto_depIdxs = []int32{
	7,  // 0: moego.models.message.v1.MarketingEmailModel.send_at:type_name -> google.protobuf.Timestamp
	8,  // 1: moego.models.message.v1.MarketingEmailModel.recipients:type_name -> moego.models.message.v1.RecipientDef
	9,  // 2: moego.models.message.v1.MarketingEmailModel.attachment_urls:type_name -> moego.models.message.v1.AttachmentDef
	7,  // 3: moego.models.message.v1.MarketingEmailModel.update_time:type_name -> google.protobuf.Timestamp
	10, // 4: moego.models.message.v1.MarketingEmailModel.status:type_name -> moego.models.message.v1.MarketingEmailStatus
	10, // 5: moego.models.message.v1.MarketingEmailModelBriefView.status:type_name -> moego.models.message.v1.MarketingEmailStatus
	7,  // 6: moego.models.message.v1.MarketingEmailModelBriefView.update_time:type_name -> google.protobuf.Timestamp
	7,  // 7: moego.models.message.v1.MarketingEmailModelBriefView.sent_time:type_name -> google.protobuf.Timestamp
	0,  // 8: moego.models.message.v1.MarketingEmailTemplateModel.type:type_name -> moego.models.message.v1.MarketingEmailTemplateModel.MarketingEmailTemplateType
	0,  // 9: moego.models.message.v1.MarketingEmailTemplateModelBriefView.type:type_name -> moego.models.message.v1.MarketingEmailTemplateModel.MarketingEmailTemplateType
	11, // 10: moego.models.message.v1.MarketingEmailRecipientModel.send_status:type_name -> moego.models.message.v1.MarketingEmailRecipientSendStatus
	7,  // 11: moego.models.message.v1.MarketingEmailRecipientModel.open_time:type_name -> google.protobuf.Timestamp
	7,  // 12: moego.models.message.v1.MarketingEmailRecipientModel.click_time:type_name -> google.protobuf.Timestamp
	7,  // 13: moego.models.message.v1.MarketingEmailRecipientModel.reply_time:type_name -> google.protobuf.Timestamp
	7,  // 14: moego.models.message.v1.MarketingEmailApptBriefView.created_time:type_name -> google.protobuf.Timestamp
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_marketing_email_models_proto_init() }
func file_moego_models_message_v1_marketing_email_models_proto_init() {
	if File_moego_models_message_v1_marketing_email_models_proto != nil {
		return
	}
	file_moego_models_message_v1_marketing_email_defs_proto_init()
	file_moego_models_message_v1_marketing_email_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailModelBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailTemplateModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailTemplateModelBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailRecipientModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v1_marketing_email_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingEmailApptBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_marketing_email_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_marketing_email_models_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_marketing_email_models_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v1_marketing_email_models_proto_enumTypes,
		MessageInfos:      file_moego_models_message_v1_marketing_email_models_proto_msgTypes,
	}.Build()
	File_moego_models_message_v1_marketing_email_models_proto = out.File
	file_moego_models_message_v1_marketing_email_models_proto_rawDesc = nil
	file_moego_models_message_v1_marketing_email_models_proto_goTypes = nil
	file_moego_models_message_v1_marketing_email_models_proto_depIdxs = nil
}
