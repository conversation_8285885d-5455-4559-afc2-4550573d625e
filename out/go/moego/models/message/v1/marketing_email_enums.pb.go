// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/marketing_email_enums.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// email status
type MarketingEmailStatus int32

const (
	// unspecific status
	MarketingEmailStatus_MARKETING_EMAIL_STATUS_UNSPECIFIED MarketingEmailStatus = 0
	// sent
	MarketingEmailStatus_MARKETING_EMAIL_STATUS_SENT MarketingEmailStatus = 1
	// schedule
	MarketingEmailStatus_MARKETING_EMAIL_STATUS_SCHEDULE MarketingEmailStatus = 2
	// draft
	MarketingEmailStatus_MARKETING_EMAIL_STATUS_DRAFT MarketingEmailStatus = 3
)

// Enum value maps for MarketingEmailStatus.
var (
	MarketingEmailStatus_name = map[int32]string{
		0: "MARKETING_EMAIL_STATUS_UNSPECIFIED",
		1: "MARKETING_EMAIL_STATUS_SENT",
		2: "MARKETING_EMAIL_STATUS_SCHEDULE",
		3: "MARKETING_EMAIL_STATUS_DRAFT",
	}
	MarketingEmailStatus_value = map[string]int32{
		"MARKETING_EMAIL_STATUS_UNSPECIFIED": 0,
		"MARKETING_EMAIL_STATUS_SENT":        1,
		"MARKETING_EMAIL_STATUS_SCHEDULE":    2,
		"MARKETING_EMAIL_STATUS_DRAFT":       3,
	}
)

func (x MarketingEmailStatus) Enum() *MarketingEmailStatus {
	p := new(MarketingEmailStatus)
	*p = x
	return p
}

func (x MarketingEmailStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketingEmailStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[0].Descriptor()
}

func (MarketingEmailStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[0]
}

func (x MarketingEmailStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarketingEmailStatus.Descriptor instead.
func (MarketingEmailStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_enums_proto_rawDescGZIP(), []int{0}
}

// recipient status
type MarketingEmailRecipientStatus int32

const (
	// unspecific status
	MarketingEmailRecipientStatus_MARKETING_EMAIL_RECIPIENT_STATUS_UNSPECIFIED MarketingEmailRecipientStatus = 0
	// sent
	MarketingEmailRecipientStatus_MARKETING_EMAIL_RECIPIENT_STATUS_SENT MarketingEmailRecipientStatus = 1
	// opened
	MarketingEmailRecipientStatus_MARKETING_EMAIL_RECIPIENT_STATUS_OPENED MarketingEmailRecipientStatus = 2
	// clicked
	MarketingEmailRecipientStatus_MARKETING_EMAIL_RECIPIENT_STATUS_CLICKED MarketingEmailRecipientStatus = 3
	// replied
	MarketingEmailRecipientStatus_MARKETING_EMAIL_RECIPIENT_STATUS_REPLIED MarketingEmailRecipientStatus = 4
)

// Enum value maps for MarketingEmailRecipientStatus.
var (
	MarketingEmailRecipientStatus_name = map[int32]string{
		0: "MARKETING_EMAIL_RECIPIENT_STATUS_UNSPECIFIED",
		1: "MARKETING_EMAIL_RECIPIENT_STATUS_SENT",
		2: "MARKETING_EMAIL_RECIPIENT_STATUS_OPENED",
		3: "MARKETING_EMAIL_RECIPIENT_STATUS_CLICKED",
		4: "MARKETING_EMAIL_RECIPIENT_STATUS_REPLIED",
	}
	MarketingEmailRecipientStatus_value = map[string]int32{
		"MARKETING_EMAIL_RECIPIENT_STATUS_UNSPECIFIED": 0,
		"MARKETING_EMAIL_RECIPIENT_STATUS_SENT":        1,
		"MARKETING_EMAIL_RECIPIENT_STATUS_OPENED":      2,
		"MARKETING_EMAIL_RECIPIENT_STATUS_CLICKED":     3,
		"MARKETING_EMAIL_RECIPIENT_STATUS_REPLIED":     4,
	}
)

func (x MarketingEmailRecipientStatus) Enum() *MarketingEmailRecipientStatus {
	p := new(MarketingEmailRecipientStatus)
	*p = x
	return p
}

func (x MarketingEmailRecipientStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketingEmailRecipientStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[1].Descriptor()
}

func (MarketingEmailRecipientStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[1]
}

func (x MarketingEmailRecipientStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarketingEmailRecipientStatus.Descriptor instead.
func (MarketingEmailRecipientStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_enums_proto_rawDescGZIP(), []int{1}
}

// recipient send status
type MarketingEmailRecipientSendStatus int32

const (
	// unspecific status
	MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_UNSPECIFIED MarketingEmailRecipientSendStatus = 0
	// waiting
	MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_WAITING MarketingEmailRecipientSendStatus = 1
	// sent
	MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SENT MarketingEmailRecipientSendStatus = 2
	// failed
	MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_FAILED MarketingEmailRecipientSendStatus = 3
	// schedule
	MarketingEmailRecipientSendStatus_MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SCHEDULE MarketingEmailRecipientSendStatus = 4
)

// Enum value maps for MarketingEmailRecipientSendStatus.
var (
	MarketingEmailRecipientSendStatus_name = map[int32]string{
		0: "MARKETING_EMAIL_RECIPIENT_SEND_STATUS_UNSPECIFIED",
		1: "MARKETING_EMAIL_RECIPIENT_SEND_STATUS_WAITING",
		2: "MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SENT",
		3: "MARKETING_EMAIL_RECIPIENT_SEND_STATUS_FAILED",
		4: "MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SCHEDULE",
	}
	MarketingEmailRecipientSendStatus_value = map[string]int32{
		"MARKETING_EMAIL_RECIPIENT_SEND_STATUS_UNSPECIFIED": 0,
		"MARKETING_EMAIL_RECIPIENT_SEND_STATUS_WAITING":     1,
		"MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SENT":        2,
		"MARKETING_EMAIL_RECIPIENT_SEND_STATUS_FAILED":      3,
		"MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SCHEDULE":    4,
	}
)

func (x MarketingEmailRecipientSendStatus) Enum() *MarketingEmailRecipientSendStatus {
	p := new(MarketingEmailRecipientSendStatus)
	*p = x
	return p
}

func (x MarketingEmailRecipientSendStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketingEmailRecipientSendStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[2].Descriptor()
}

func (MarketingEmailRecipientSendStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[2]
}

func (x MarketingEmailRecipientSendStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarketingEmailRecipientSendStatus.Descriptor instead.
func (MarketingEmailRecipientSendStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_enums_proto_rawDescGZIP(), []int{2}
}

// email event type
type EmailEventType int32

const (
	// unspecific status
	EmailEventType_EMAIL_EVENT_TYPE_UNSPECIFIED EmailEventType = 0
	// delivered
	EmailEventType_EMAIL_EVENT_TYPE_DELIVERED EmailEventType = 1
	// opened
	EmailEventType_EMAIL_EVENT_TYPE_OPENED EmailEventType = 2
	// clicked
	EmailEventType_EMAIL_EVENT_TYPE_CLICKED EmailEventType = 3
	// replied
	EmailEventType_EMAIL_EVENT_TYPE_REPLIED EmailEventType = 4
)

// Enum value maps for EmailEventType.
var (
	EmailEventType_name = map[int32]string{
		0: "EMAIL_EVENT_TYPE_UNSPECIFIED",
		1: "EMAIL_EVENT_TYPE_DELIVERED",
		2: "EMAIL_EVENT_TYPE_OPENED",
		3: "EMAIL_EVENT_TYPE_CLICKED",
		4: "EMAIL_EVENT_TYPE_REPLIED",
	}
	EmailEventType_value = map[string]int32{
		"EMAIL_EVENT_TYPE_UNSPECIFIED": 0,
		"EMAIL_EVENT_TYPE_DELIVERED":   1,
		"EMAIL_EVENT_TYPE_OPENED":      2,
		"EMAIL_EVENT_TYPE_CLICKED":     3,
		"EMAIL_EVENT_TYPE_REPLIED":     4,
	}
)

func (x EmailEventType) Enum() *EmailEventType {
	p := new(EmailEventType)
	*p = x
	return p
}

func (x EmailEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[3].Descriptor()
}

func (EmailEventType) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_marketing_email_enums_proto_enumTypes[3]
}

func (x EmailEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailEventType.Descriptor instead.
func (EmailEventType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_marketing_email_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_message_v1_marketing_email_enums_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_marketing_email_enums_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2a, 0xa6,
	0x01, 0x0a, 0x14, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x41, 0x52, 0x4b, 0x45,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1f, 0x0a, 0x1b, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x03, 0x2a, 0x85, 0x02, 0x0a, 0x1d, 0x4d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x2c, 0x4d, 0x41, 0x52,
	0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43,
	0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x4d,
	0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52,
	0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x53, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x2c, 0x0a, 0x28, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x49, 0x45, 0x44, 0x10, 0x04, 0x2a,
	0xa3, 0x02, 0x0a, 0x21, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x31, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49,
	0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d,
	0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12,
	0x2e, 0x0a, 0x2a, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x30, 0x0a, 0x2c, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x32, 0x0a, 0x2e, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x49, 0x50, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x45, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x10, 0x04, 0x2a, 0xab, 0x01, 0x0a, 0x0e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44,
	0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x50, 0x45, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x43,
	0x4b, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4c, 0x49, 0x45,
	0x44, 0x10, 0x04, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_marketing_email_enums_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_marketing_email_enums_proto_rawDescData = file_moego_models_message_v1_marketing_email_enums_proto_rawDesc
)

func file_moego_models_message_v1_marketing_email_enums_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_marketing_email_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_marketing_email_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_marketing_email_enums_proto_rawDescData)
	})
	return file_moego_models_message_v1_marketing_email_enums_proto_rawDescData
}

var file_moego_models_message_v1_marketing_email_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_message_v1_marketing_email_enums_proto_goTypes = []interface{}{
	(MarketingEmailStatus)(0),              // 0: moego.models.message.v1.MarketingEmailStatus
	(MarketingEmailRecipientStatus)(0),     // 1: moego.models.message.v1.MarketingEmailRecipientStatus
	(MarketingEmailRecipientSendStatus)(0), // 2: moego.models.message.v1.MarketingEmailRecipientSendStatus
	(EmailEventType)(0),                    // 3: moego.models.message.v1.EmailEventType
}
var file_moego_models_message_v1_marketing_email_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_marketing_email_enums_proto_init() }
func file_moego_models_message_v1_marketing_email_enums_proto_init() {
	if File_moego_models_message_v1_marketing_email_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_marketing_email_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_marketing_email_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_marketing_email_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v1_marketing_email_enums_proto_enumTypes,
	}.Build()
	File_moego_models_message_v1_marketing_email_enums_proto = out.File
	file_moego_models_message_v1_marketing_email_enums_proto_rawDesc = nil
	file_moego_models_message_v1_marketing_email_enums_proto_goTypes = nil
	file_moego_models_message_v1_marketing_email_enums_proto_depIdxs = nil
}
