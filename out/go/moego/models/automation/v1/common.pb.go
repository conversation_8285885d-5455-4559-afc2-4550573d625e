// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/automation/v1/common.proto

package automationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MessageStatus
type MessageStatus int32

const (
	// unspecified
	MessageStatus_MESSAGE_STATUS_UNSPECIFIED MessageStatus = 0
	// send success
	MessageStatus_SENDING MessageStatus = 1
	// twilio call back success
	MessageStatus_SUCCESS MessageStatus = 2
	// twilio call back fail
	MessageStatus_FAIL MessageStatus = 3
)

// Enum value maps for MessageStatus.
var (
	MessageStatus_name = map[int32]string{
		0: "MESSAGE_STATUS_UNSPECIFIED",
		1: "SENDING",
		2: "SUCCESS",
		3: "FAIL",
	}
	MessageStatus_value = map[string]int32{
		"MESSAGE_STATUS_UNSPECIFIED": 0,
		"SENDING":                    1,
		"SUCCESS":                    2,
		"FAIL":                       3,
	}
)

func (x MessageStatus) Enum() *MessageStatus {
	p := new(MessageStatus)
	*p = x
	return p
}

func (x MessageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_common_proto_enumTypes[0].Descriptor()
}

func (MessageStatus) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_common_proto_enumTypes[0]
}

func (x MessageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageStatus.Descriptor instead.
func (MessageStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_common_proto_rawDescGZIP(), []int{0}
}

// UserActionType
type UserActionType int32

const (
	// unspecified
	UserActionType_USER_ACTION_TYPE_UNSPECIFIED UserActionType = 0
	// create appointment action
	UserActionType_CREATE_APPT UserActionType = 10
)

// Enum value maps for UserActionType.
var (
	UserActionType_name = map[int32]string{
		0:  "USER_ACTION_TYPE_UNSPECIFIED",
		10: "CREATE_APPT",
	}
	UserActionType_value = map[string]int32{
		"USER_ACTION_TYPE_UNSPECIFIED": 0,
		"CREATE_APPT":                  10,
	}
)

func (x UserActionType) Enum() *UserActionType {
	p := new(UserActionType)
	*p = x
	return p
}

func (x UserActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_common_proto_enumTypes[1].Descriptor()
}

func (UserActionType) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_common_proto_enumTypes[1]
}

func (x UserActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserActionType.Descriptor instead.
func (UserActionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_common_proto_rawDescGZIP(), []int{1}
}

// unit type for value
type TimeDuration_Unit int32

const (
	// unspecified
	TimeDuration_UNIT_UNSPECIFIED TimeDuration_Unit = 0
	// day Unit
	TimeDuration_DAY TimeDuration_Unit = 1
	// week Unit
	TimeDuration_WEEK TimeDuration_Unit = 2
	// month Unit
	TimeDuration_MONTH TimeDuration_Unit = 3
	// hour Unit
	TimeDuration_HOUR TimeDuration_Unit = 4
)

// Enum value maps for TimeDuration_Unit.
var (
	TimeDuration_Unit_name = map[int32]string{
		0: "UNIT_UNSPECIFIED",
		1: "DAY",
		2: "WEEK",
		3: "MONTH",
		4: "HOUR",
	}
	TimeDuration_Unit_value = map[string]int32{
		"UNIT_UNSPECIFIED": 0,
		"DAY":              1,
		"WEEK":             2,
		"MONTH":            3,
		"HOUR":             4,
	}
)

func (x TimeDuration_Unit) Enum() *TimeDuration_Unit {
	p := new(TimeDuration_Unit)
	*p = x
	return p
}

func (x TimeDuration_Unit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeDuration_Unit) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_common_proto_enumTypes[2].Descriptor()
}

func (TimeDuration_Unit) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_common_proto_enumTypes[2]
}

func (x TimeDuration_Unit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeDuration_Unit.Descriptor instead.
func (TimeDuration_Unit) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_common_proto_rawDescGZIP(), []int{1, 0}
}

// Field
type Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// place_holder_name
	PlaceHolderName string `protobuf:"bytes,3,opt,name=place_holder_name,json=placeHolderName,proto3" json:"place_holder_name,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Field) Reset() {
	*x = Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *Field) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Field) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Field) GetPlaceHolderName() string {
	if x != nil {
		return x.PlaceHolderName
	}
	return ""
}

func (x *Field) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// TimeDuration x Hour/Day/Week/Month
type TimeDuration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unit
	Unit TimeDuration_Unit `protobuf:"varint,1,opt,name=unit,proto3,enum=moego.models.automation.v1.TimeDuration_Unit" json:"unit,omitempty"`
	// value
	Value int64 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,3,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *TimeDuration) Reset() {
	*x = TimeDuration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeDuration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeDuration) ProtoMessage() {}

func (x *TimeDuration) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeDuration.ProtoReflect.Descriptor instead.
func (*TimeDuration) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *TimeDuration) GetUnit() TimeDuration_Unit {
	if x != nil {
		return x.Unit
	}
	return TimeDuration_UNIT_UNSPECIFIED
}

func (x *TimeDuration) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *TimeDuration) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

var File_moego_models_automation_v1_common_proto protoreflect.FileDescriptor

var file_moego_models_automation_v1_common_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xe4, 0x01, 0x0a, 0x0c, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x6e, 0x69, 0x74,
	0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x04, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x10, 0x55,
	0x4e, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x45,
	0x45, 0x4b, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x03, 0x12,
	0x08, 0x0a, 0x04, 0x48, 0x4f, 0x55, 0x52, 0x10, 0x04, 0x2a, 0x53, 0x0a, 0x0d, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x45,
	0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x2a, 0x43,
	0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x1c, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x54, 0x10, 0x0a, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_automation_v1_common_proto_rawDescOnce sync.Once
	file_moego_models_automation_v1_common_proto_rawDescData = file_moego_models_automation_v1_common_proto_rawDesc
)

func file_moego_models_automation_v1_common_proto_rawDescGZIP() []byte {
	file_moego_models_automation_v1_common_proto_rawDescOnce.Do(func() {
		file_moego_models_automation_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_automation_v1_common_proto_rawDescData)
	})
	return file_moego_models_automation_v1_common_proto_rawDescData
}

var file_moego_models_automation_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_automation_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_automation_v1_common_proto_goTypes = []interface{}{
	(MessageStatus)(0),          // 0: moego.models.automation.v1.MessageStatus
	(UserActionType)(0),         // 1: moego.models.automation.v1.UserActionType
	(TimeDuration_Unit)(0),      // 2: moego.models.automation.v1.TimeDuration.Unit
	(*Field)(nil),               // 3: moego.models.automation.v1.Field
	(*TimeDuration)(nil),        // 4: moego.models.automation.v1.TimeDuration
	(*durationpb.Duration)(nil), // 5: google.protobuf.Duration
}
var file_moego_models_automation_v1_common_proto_depIdxs = []int32{
	2, // 0: moego.models.automation.v1.TimeDuration.unit:type_name -> moego.models.automation.v1.TimeDuration.Unit
	5, // 1: moego.models.automation.v1.TimeDuration.duration:type_name -> google.protobuf.Duration
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_automation_v1_common_proto_init() }
func file_moego_models_automation_v1_common_proto_init() {
	if File_moego_models_automation_v1_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_automation_v1_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeDuration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_automation_v1_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_automation_v1_common_proto_goTypes,
		DependencyIndexes: file_moego_models_automation_v1_common_proto_depIdxs,
		EnumInfos:         file_moego_models_automation_v1_common_proto_enumTypes,
		MessageInfos:      file_moego_models_automation_v1_common_proto_msgTypes,
	}.Build()
	File_moego_models_automation_v1_common_proto = out.File
	file_moego_models_automation_v1_common_proto_rawDesc = nil
	file_moego_models_automation_v1_common_proto_goTypes = nil
	file_moego_models_automation_v1_common_proto_depIdxs = nil
}
