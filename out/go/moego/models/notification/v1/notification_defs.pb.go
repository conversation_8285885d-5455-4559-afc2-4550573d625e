// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/notification/v1/notification_defs.proto

package notificationpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// notification list sort definition
type NotificationSortDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sort field
	Field NotificationSortField `protobuf:"varint,1,opt,name=field,proto3,enum=moego.models.notification.v1.NotificationSortField" json:"field,omitempty"`
	// sort asc or desc
	Asc bool `protobuf:"varint,2,opt,name=asc,proto3" json:"asc,omitempty"`
}

func (x *NotificationSortDef) Reset() {
	*x = NotificationSortDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationSortDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationSortDef) ProtoMessage() {}

func (x *NotificationSortDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationSortDef.ProtoReflect.Descriptor instead.
func (*NotificationSortDef) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_defs_proto_rawDescGZIP(), []int{0}
}

func (x *NotificationSortDef) GetField() NotificationSortField {
	if x != nil {
		return x.Field
	}
	return NotificationSortField_NOTIFICATION_SORT_FIELD_UNSPECIFIED
}

func (x *NotificationSortDef) GetAsc() bool {
	if x != nil {
		return x.Asc
	}
	return false
}

// app push definition
type AppPushDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// content source
	//
	// Types that are assignable to ContentSource:
	//
	//	*AppPushDef_FromTemplate
	//	*AppPushDef_FromContent
	ContentSource isAppPushDef_ContentSource `protobuf_oneof:"content_source"`
	// push token source
	Source PushTokenSource `protobuf:"varint,3,opt,name=source,proto3,enum=moego.models.notification.v1.PushTokenSource" json:"source,omitempty"`
	// badge
	Badge *uint64 `protobuf:"varint,4,opt,name=badge,proto3,oneof" json:"badge,omitempty"`
}

func (x *AppPushDef) Reset() {
	*x = AppPushDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppPushDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppPushDef) ProtoMessage() {}

func (x *AppPushDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppPushDef.ProtoReflect.Descriptor instead.
func (*AppPushDef) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_defs_proto_rawDescGZIP(), []int{1}
}

func (m *AppPushDef) GetContentSource() isAppPushDef_ContentSource {
	if m != nil {
		return m.ContentSource
	}
	return nil
}

func (x *AppPushDef) GetFromTemplate() bool {
	if x, ok := x.GetContentSource().(*AppPushDef_FromTemplate); ok {
		return x.FromTemplate
	}
	return false
}

func (x *AppPushDef) GetFromContent() *AppPushByContentDef {
	if x, ok := x.GetContentSource().(*AppPushDef_FromContent); ok {
		return x.FromContent
	}
	return nil
}

func (x *AppPushDef) GetSource() PushTokenSource {
	if x != nil {
		return x.Source
	}
	return PushTokenSource_PUSH_TOKEN_SOURCE_UNSPECIFIED
}

func (x *AppPushDef) GetBadge() uint64 {
	if x != nil && x.Badge != nil {
		return *x.Badge
	}
	return 0
}

type isAppPushDef_ContentSource interface {
	isAppPushDef_ContentSource()
}

type AppPushDef_FromTemplate struct {
	// from template
	FromTemplate bool `protobuf:"varint,1,opt,name=from_template,json=fromTemplate,proto3,oneof"`
}

type AppPushDef_FromContent struct {
	// from content
	FromContent *AppPushByContentDef `protobuf:"bytes,2,opt,name=from_content,json=fromContent,proto3,oneof"`
}

func (*AppPushDef_FromTemplate) isAppPushDef_ContentSource() {}

func (*AppPushDef_FromContent) isAppPushDef_ContentSource() {}

// app push by content definition
type AppPushByContentDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title
	Title *string `protobuf:"bytes,1,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// content
	Content *string `protobuf:"bytes,2,opt,name=content,proto3,oneof" json:"content,omitempty"`
}

func (x *AppPushByContentDef) Reset() {
	*x = AppPushByContentDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppPushByContentDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppPushByContentDef) ProtoMessage() {}

func (x *AppPushByContentDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_notification_v1_notification_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppPushByContentDef.ProtoReflect.Descriptor instead.
func (*AppPushByContentDef) Descriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_defs_proto_rawDescGZIP(), []int{2}
}

func (x *AppPushByContentDef) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *AppPushByContentDef) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

var File_moego_models_notification_v1_notification_defs_proto protoreflect.FileDescriptor

var file_moego_models_notification_v1_notification_defs_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7c, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x53, 0x0a, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x61,
	0x73, 0x63, 0x22, 0x93, 0x02, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68, 0x44, 0x65,
	0x66, 0x12, 0x25, 0x0a, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x66, 0x72, 0x6f, 0x6d,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x50, 0x75, 0x73, 0x68, 0x42, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x66, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x4f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x19, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x48, 0x01, 0x52, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x62, 0x61, 0x64, 0x67, 0x65, 0x22, 0x7d, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x50,
	0x75, 0x73, 0x68, 0x42, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12,
	0x25, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_notification_v1_notification_defs_proto_rawDescOnce sync.Once
	file_moego_models_notification_v1_notification_defs_proto_rawDescData = file_moego_models_notification_v1_notification_defs_proto_rawDesc
)

func file_moego_models_notification_v1_notification_defs_proto_rawDescGZIP() []byte {
	file_moego_models_notification_v1_notification_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_notification_v1_notification_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_notification_v1_notification_defs_proto_rawDescData)
	})
	return file_moego_models_notification_v1_notification_defs_proto_rawDescData
}

var file_moego_models_notification_v1_notification_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_notification_v1_notification_defs_proto_goTypes = []interface{}{
	(*NotificationSortDef)(nil), // 0: moego.models.notification.v1.NotificationSortDef
	(*AppPushDef)(nil),          // 1: moego.models.notification.v1.AppPushDef
	(*AppPushByContentDef)(nil), // 2: moego.models.notification.v1.AppPushByContentDef
	(NotificationSortField)(0),  // 3: moego.models.notification.v1.NotificationSortField
	(PushTokenSource)(0),        // 4: moego.models.notification.v1.PushTokenSource
}
var file_moego_models_notification_v1_notification_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.notification.v1.NotificationSortDef.field:type_name -> moego.models.notification.v1.NotificationSortField
	2, // 1: moego.models.notification.v1.AppPushDef.from_content:type_name -> moego.models.notification.v1.AppPushByContentDef
	4, // 2: moego.models.notification.v1.AppPushDef.source:type_name -> moego.models.notification.v1.PushTokenSource
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_notification_v1_notification_defs_proto_init() }
func file_moego_models_notification_v1_notification_defs_proto_init() {
	if File_moego_models_notification_v1_notification_defs_proto != nil {
		return
	}
	file_moego_models_notification_v1_notification_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_notification_v1_notification_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationSortDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_notification_v1_notification_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppPushDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_notification_v1_notification_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppPushByContentDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_notification_v1_notification_defs_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*AppPushDef_FromTemplate)(nil),
		(*AppPushDef_FromContent)(nil),
	}
	file_moego_models_notification_v1_notification_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_notification_v1_notification_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_notification_v1_notification_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_notification_v1_notification_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_notification_v1_notification_defs_proto_msgTypes,
	}.Build()
	File_moego_models_notification_v1_notification_defs_proto = out.File
	file_moego_models_notification_v1_notification_defs_proto_rawDesc = nil
	file_moego_models_notification_v1_notification_defs_proto_goTypes = nil
	file_moego_models_notification_v1_notification_defs_proto_depIdxs = nil
}
