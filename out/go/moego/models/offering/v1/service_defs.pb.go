// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/service_defs.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create service def
type CreateServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the service
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description of the service
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// category of the service
	CategoryId *int64 `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// is the service inactive
	Inactive bool `protobuf:"varint,4,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// images of this service
	Images []string `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	// color code
	ColorCode *string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// override by location
	//
	// Deprecated: Do not use.
	LocationOverrideList []*LocationOverrideRule `protobuf:"bytes,8,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// service item type
	ServiceItemType *ServiceItemType `protobuf:"varint,9,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,10,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,11,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,12,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,13,opt,name=duration,proto3" json:"duration,omitempty"`
	// whether add to commission base
	AddToCommissionBase bool `protobuf:"varint,14,opt,name=add_to_commission_base,json=addToCommissionBase,proto3" json:"add_to_commission_base,omitempty"`
	// whether can tip
	CanTip bool `protobuf:"varint,15,opt,name=can_tip,json=canTip,proto3" json:"can_tip,omitempty"`
	// whether the service is available for all locations
	IsAllLocation bool `protobuf:"varint,17,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,19,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	CustomizedBreed []*CustomizedBreed `protobuf:"bytes,20,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,21,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,22,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// weight filter only for compatible with old version, use pet_size in new version
	//
	// Deprecated: Do not use.
	WeightFilter bool `protobuf:"varint,23,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	// weight range (only if weight_filter is true)
	//
	// Deprecated: Do not use.
	WeightRange []float64 `protobuf:"fixed64,24,rep,packed,name=weight_range,json=weightRange,proto3" json:"weight_range,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,25,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,26,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// required dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,27,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// whether the service is available for all lodging(only if require_dedicated_lodging is true)
	LodgingFilter bool `protobuf:"varint,28,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
	CustomizedLodgings []int64 `protobuf:"varint,29,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// whether the add on is available for all services(only for add on)
	ServiceFilter *bool `protobuf:"varint,30,opt,name=service_filter,json=serviceFilter,proto3,oneof" json:"service_filter,omitempty"`
	// service filters(only for add on)
	ServiceFilterList []*ServiceFilter `protobuf:"bytes,31,rep,name=service_filter_list,json=serviceFilterList,proto3" json:"service_filter_list,omitempty"`
	// service type
	Type *ServiceType `protobuf:"varint,32,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType,oneof" json:"type,omitempty"`
	// whether the service require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,33,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// max duration
	MaxDuration *int32 `protobuf:"varint,34,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// auto rollover rule
	AutoRolloverRule *AutoRolloverRuleDef `protobuf:"bytes,35,opt,name=auto_rollover_rule,json=autoRolloverRule,proto3,oneof" json:"auto_rollover_rule,omitempty"`
	// available staffs
	AvailableStaffIdList []int64 `protobuf:"varint,37,rep,packed,name=available_staff_id_list,json=availableStaffIdList,proto3" json:"available_staff_id_list,omitempty"`
	// location staff override rules
	LocationStaffOverrideList []*LocationStaffOverrideRule `protobuf:"bytes,38,rep,name=location_staff_override_list,json=locationStaffOverrideList,proto3" json:"location_staff_override_list,omitempty"`
	// available business ids
	AvailableBusinessIdList []int64 `protobuf:"varint,39,rep,packed,name=available_business_id_list,json=availableBusinessIdList,proto3" json:"available_business_id_list,omitempty"`
	// whether the service is available for all staff
	AvailableForAllStaff bool `protobuf:"varint,40,opt,name=available_for_all_staff,json=availableForAllStaff,proto3" json:"available_for_all_staff,omitempty"`
	// pet code filter
	PetCodeFilter *CreateServiceDef_PetCodeFilter `protobuf:"bytes,41,opt,name=pet_code_filter,json=petCodeFilter,proto3,oneof" json:"pet_code_filter,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,42,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// source default is MoeGo Platform
	Source *ServiceModel_Source `protobuf:"varint,43,opt,name=source,proto3,enum=moego.models.offering.v1.ServiceModel_Source,oneof" json:"source,omitempty"`
	// number of sessions, only for training
	NumSessions *int32 `protobuf:"varint,44,opt,name=num_sessions,json=numSessions,proto3,oneof" json:"num_sessions,omitempty"`
	// duration of each session in minutes, only for training
	DurationSessionMin *int32 `protobuf:"varint,45,opt,name=duration_session_min,json=durationSessionMin,proto3,oneof" json:"duration_session_min,omitempty"`
	// capacity of group class, zero means unlimited, only for training
	Capacity *int32 `protobuf:"varint,46,opt,name=capacity,proto3,oneof" json:"capacity,omitempty"`
	// whether it require a prerequisite class
	IsRequirePrerequisiteClass bool `protobuf:"varint,48,opt,name=is_require_prerequisite_class,json=isRequirePrerequisiteClass,proto3" json:"is_require_prerequisite_class,omitempty"`
	// prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
	PrerequisiteClassIds []int64 `protobuf:"varint,49,rep,packed,name=prerequisite_class_ids,json=prerequisiteClassIds,proto3" json:"prerequisite_class_ids,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired *bool `protobuf:"varint,50,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3,oneof" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb *bool `protobuf:"varint,51,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3,oneof" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId *int64 `protobuf:"varint,52,opt,name=evaluation_id,json=evaluationId,proto3,oneof" json:"evaluation_id,omitempty"`
	// additional service rule
	AdditionalServiceRule *AdditionalServiceRule `protobuf:"bytes,53,opt,name=additional_service_rule,json=additionalServiceRule,proto3,oneof" json:"additional_service_rule,omitempty"`
}

func (x *CreateServiceDef) Reset() {
	*x = CreateServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceDef) ProtoMessage() {}

func (x *CreateServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceDef.ProtoReflect.Descriptor instead.
func (*CreateServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateServiceDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateServiceDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateServiceDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *CreateServiceDef) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *CreateServiceDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CreateServiceDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

// Deprecated: Do not use.
func (x *CreateServiceDef) GetLocationOverrideList() []*LocationOverrideRule {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *CreateServiceDef) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *CreateServiceDef) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CreateServiceDef) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *CreateServiceDef) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *CreateServiceDef) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *CreateServiceDef) GetAddToCommissionBase() bool {
	if x != nil {
		return x.AddToCommissionBase
	}
	return false
}

func (x *CreateServiceDef) GetCanTip() bool {
	if x != nil {
		return x.CanTip
	}
	return false
}

func (x *CreateServiceDef) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *CreateServiceDef) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *CreateServiceDef) GetCustomizedBreed() []*CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *CreateServiceDef) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *CreateServiceDef) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateServiceDef) GetWeightFilter() bool {
	if x != nil {
		return x.WeightFilter
	}
	return false
}

// Deprecated: Do not use.
func (x *CreateServiceDef) GetWeightRange() []float64 {
	if x != nil {
		return x.WeightRange
	}
	return nil
}

func (x *CreateServiceDef) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *CreateServiceDef) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *CreateServiceDef) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *CreateServiceDef) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *CreateServiceDef) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *CreateServiceDef) GetServiceFilter() bool {
	if x != nil && x.ServiceFilter != nil {
		return *x.ServiceFilter
	}
	return false
}

func (x *CreateServiceDef) GetServiceFilterList() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilterList
	}
	return nil
}

func (x *CreateServiceDef) GetType() ServiceType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *CreateServiceDef) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *CreateServiceDef) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *CreateServiceDef) GetAutoRolloverRule() *AutoRolloverRuleDef {
	if x != nil {
		return x.AutoRolloverRule
	}
	return nil
}

func (x *CreateServiceDef) GetAvailableStaffIdList() []int64 {
	if x != nil {
		return x.AvailableStaffIdList
	}
	return nil
}

func (x *CreateServiceDef) GetLocationStaffOverrideList() []*LocationStaffOverrideRule {
	if x != nil {
		return x.LocationStaffOverrideList
	}
	return nil
}

func (x *CreateServiceDef) GetAvailableBusinessIdList() []int64 {
	if x != nil {
		return x.AvailableBusinessIdList
	}
	return nil
}

func (x *CreateServiceDef) GetAvailableForAllStaff() bool {
	if x != nil {
		return x.AvailableForAllStaff
	}
	return false
}

func (x *CreateServiceDef) GetPetCodeFilter() *CreateServiceDef_PetCodeFilter {
	if x != nil {
		return x.PetCodeFilter
	}
	return nil
}

func (x *CreateServiceDef) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *CreateServiceDef) GetSource() ServiceModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ServiceModel_SOURCE_UNSPECIFIED
}

func (x *CreateServiceDef) GetNumSessions() int32 {
	if x != nil && x.NumSessions != nil {
		return *x.NumSessions
	}
	return 0
}

func (x *CreateServiceDef) GetDurationSessionMin() int32 {
	if x != nil && x.DurationSessionMin != nil {
		return *x.DurationSessionMin
	}
	return 0
}

func (x *CreateServiceDef) GetCapacity() int32 {
	if x != nil && x.Capacity != nil {
		return *x.Capacity
	}
	return 0
}

func (x *CreateServiceDef) GetIsRequirePrerequisiteClass() bool {
	if x != nil {
		return x.IsRequirePrerequisiteClass
	}
	return false
}

func (x *CreateServiceDef) GetPrerequisiteClassIds() []int64 {
	if x != nil {
		return x.PrerequisiteClassIds
	}
	return nil
}

func (x *CreateServiceDef) GetIsEvaluationRequired() bool {
	if x != nil && x.IsEvaluationRequired != nil {
		return *x.IsEvaluationRequired
	}
	return false
}

func (x *CreateServiceDef) GetIsEvaluationRequiredForOb() bool {
	if x != nil && x.IsEvaluationRequiredForOb != nil {
		return *x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *CreateServiceDef) GetEvaluationId() int64 {
	if x != nil && x.EvaluationId != nil {
		return *x.EvaluationId
	}
	return 0
}

func (x *CreateServiceDef) GetAdditionalServiceRule() *AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// update service def
type UpdateServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// name of the service
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the service
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// category of the service
	CategoryId int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// is the service inactive
	Inactive bool `protobuf:"varint,5,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// images of this service
	Images []string `protobuf:"bytes,6,rep,name=images,proto3" json:"images,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// override by location
	//
	// Deprecated: Do not use.
	LocationOverrideList []*LocationOverrideRule `protobuf:"bytes,9,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,10,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,11,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,12,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,13,opt,name=duration,proto3" json:"duration,omitempty"`
	// whether add to commission base
	AddToCommissionBase bool `protobuf:"varint,14,opt,name=add_to_commission_base,json=addToCommissionBase,proto3" json:"add_to_commission_base,omitempty"`
	// whether can tip
	CanTip bool `protobuf:"varint,15,opt,name=can_tip,json=canTip,proto3" json:"can_tip,omitempty"`
	// whether the service is available for all locations
	IsAllLocation bool `protobuf:"varint,16,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,17,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	CustomizedBreed []*CustomizedBreed `protobuf:"bytes,18,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,19,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,20,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// weight filter only for compatible with old version, use pet_size in new version
	//
	// Deprecated: Do not use.
	WeightFilter bool `protobuf:"varint,21,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	// weight range (only if weight_filter is true)
	//
	// Deprecated: Do not use.
	WeightRange []float64 `protobuf:"fixed64,22,rep,packed,name=weight_range,json=weightRange,proto3" json:"weight_range,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,23,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,24,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// required dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,25,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// whether the service is available for all lodging(only if require_dedicated_lodging is true)
	LodgingFilter bool `protobuf:"varint,26,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
	CustomizedLodgings []int64 `protobuf:"varint,27,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// whether the add on is available for all services(only for add on)
	ServiceFilter bool `protobuf:"varint,29,opt,name=service_filter,json=serviceFilter,proto3" json:"service_filter,omitempty"`
	// service filters(only for add on)
	ServiceFilterList []*ServiceFilter `protobuf:"bytes,30,rep,name=service_filter_list,json=serviceFilterList,proto3" json:"service_filter_list,omitempty"`
	// whether the service require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,31,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// max duration
	MaxDuration *int32 `protobuf:"varint,32,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// auto rollover rule
	AutoRolloverRule *AutoRolloverRuleDef `protobuf:"bytes,35,opt,name=auto_rollover_rule,json=autoRolloverRule,proto3,oneof" json:"auto_rollover_rule,omitempty"`
	// available staffs
	AvailableStaffIdList []int64 `protobuf:"varint,37,rep,packed,name=available_staff_id_list,json=availableStaffIdList,proto3" json:"available_staff_id_list,omitempty"`
	// location staff override rules
	LocationStaffOverrideList []*LocationStaffOverrideRule `protobuf:"bytes,38,rep,name=location_staff_override_list,json=locationStaffOverrideList,proto3" json:"location_staff_override_list,omitempty"`
	// available business ids
	AvailableBusinessIdList []int64 `protobuf:"varint,39,rep,packed,name=available_business_id_list,json=availableBusinessIdList,proto3" json:"available_business_id_list,omitempty"`
	// whether the service is available for all staff
	AvailableForAllStaff bool `protobuf:"varint,40,opt,name=available_for_all_staff,json=availableForAllStaff,proto3" json:"available_for_all_staff,omitempty"`
	// pet code filter
	PetCodeFilter *UpdateServiceDef_PetCodeFilter `protobuf:"bytes,41,opt,name=pet_code_filter,json=petCodeFilter,proto3,oneof" json:"pet_code_filter,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,42,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// source
	Source *ServiceModel_Source `protobuf:"varint,43,opt,name=source,proto3,enum=moego.models.offering.v1.ServiceModel_Source,oneof" json:"source,omitempty"`
	// number of sessions, only for training
	NumSessions *int32 `protobuf:"varint,44,opt,name=num_sessions,json=numSessions,proto3,oneof" json:"num_sessions,omitempty"`
	// duration of each session in minutes, only for training
	DurationSessionMin *int32 `protobuf:"varint,45,opt,name=duration_session_min,json=durationSessionMin,proto3,oneof" json:"duration_session_min,omitempty"`
	// capacity of group class, zero means unlimited, only for training
	Capacity *int32 `protobuf:"varint,46,opt,name=capacity,proto3,oneof" json:"capacity,omitempty"`
	// whether it require a prerequisite class
	IsRequirePrerequisiteClass bool `protobuf:"varint,48,opt,name=is_require_prerequisite_class,json=isRequirePrerequisiteClass,proto3" json:"is_require_prerequisite_class,omitempty"`
	// prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
	PrerequisiteClassIds []int64 `protobuf:"varint,49,rep,packed,name=prerequisite_class_ids,json=prerequisiteClassIds,proto3" json:"prerequisite_class_ids,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired *bool `protobuf:"varint,50,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3,oneof" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb *bool `protobuf:"varint,51,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3,oneof" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId *int64 `protobuf:"varint,52,opt,name=evaluation_id,json=evaluationId,proto3,oneof" json:"evaluation_id,omitempty"`
	// additional service rule
	AdditionalServiceRule *AdditionalServiceRule `protobuf:"bytes,53,opt,name=additional_service_rule,json=additionalServiceRule,proto3,oneof" json:"additional_service_rule,omitempty"`
}

func (x *UpdateServiceDef) Reset() {
	*x = UpdateServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceDef) ProtoMessage() {}

func (x *UpdateServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceDef.ProtoReflect.Descriptor instead.
func (*UpdateServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateServiceDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateServiceDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateServiceDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateServiceDef) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UpdateServiceDef) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *UpdateServiceDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *UpdateServiceDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

// Deprecated: Do not use.
func (x *UpdateServiceDef) GetLocationOverrideList() []*LocationOverrideRule {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *UpdateServiceDef) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *UpdateServiceDef) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *UpdateServiceDef) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *UpdateServiceDef) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *UpdateServiceDef) GetAddToCommissionBase() bool {
	if x != nil {
		return x.AddToCommissionBase
	}
	return false
}

func (x *UpdateServiceDef) GetCanTip() bool {
	if x != nil {
		return x.CanTip
	}
	return false
}

func (x *UpdateServiceDef) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *UpdateServiceDef) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *UpdateServiceDef) GetCustomizedBreed() []*CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *UpdateServiceDef) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *UpdateServiceDef) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

// Deprecated: Do not use.
func (x *UpdateServiceDef) GetWeightFilter() bool {
	if x != nil {
		return x.WeightFilter
	}
	return false
}

// Deprecated: Do not use.
func (x *UpdateServiceDef) GetWeightRange() []float64 {
	if x != nil {
		return x.WeightRange
	}
	return nil
}

func (x *UpdateServiceDef) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *UpdateServiceDef) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *UpdateServiceDef) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *UpdateServiceDef) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *UpdateServiceDef) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *UpdateServiceDef) GetServiceFilter() bool {
	if x != nil {
		return x.ServiceFilter
	}
	return false
}

func (x *UpdateServiceDef) GetServiceFilterList() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilterList
	}
	return nil
}

func (x *UpdateServiceDef) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *UpdateServiceDef) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *UpdateServiceDef) GetAutoRolloverRule() *AutoRolloverRuleDef {
	if x != nil {
		return x.AutoRolloverRule
	}
	return nil
}

func (x *UpdateServiceDef) GetAvailableStaffIdList() []int64 {
	if x != nil {
		return x.AvailableStaffIdList
	}
	return nil
}

func (x *UpdateServiceDef) GetLocationStaffOverrideList() []*LocationStaffOverrideRule {
	if x != nil {
		return x.LocationStaffOverrideList
	}
	return nil
}

func (x *UpdateServiceDef) GetAvailableBusinessIdList() []int64 {
	if x != nil {
		return x.AvailableBusinessIdList
	}
	return nil
}

func (x *UpdateServiceDef) GetAvailableForAllStaff() bool {
	if x != nil {
		return x.AvailableForAllStaff
	}
	return false
}

func (x *UpdateServiceDef) GetPetCodeFilter() *UpdateServiceDef_PetCodeFilter {
	if x != nil {
		return x.PetCodeFilter
	}
	return nil
}

func (x *UpdateServiceDef) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *UpdateServiceDef) GetSource() ServiceModel_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ServiceModel_SOURCE_UNSPECIFIED
}

func (x *UpdateServiceDef) GetNumSessions() int32 {
	if x != nil && x.NumSessions != nil {
		return *x.NumSessions
	}
	return 0
}

func (x *UpdateServiceDef) GetDurationSessionMin() int32 {
	if x != nil && x.DurationSessionMin != nil {
		return *x.DurationSessionMin
	}
	return 0
}

func (x *UpdateServiceDef) GetCapacity() int32 {
	if x != nil && x.Capacity != nil {
		return *x.Capacity
	}
	return 0
}

func (x *UpdateServiceDef) GetIsRequirePrerequisiteClass() bool {
	if x != nil {
		return x.IsRequirePrerequisiteClass
	}
	return false
}

func (x *UpdateServiceDef) GetPrerequisiteClassIds() []int64 {
	if x != nil {
		return x.PrerequisiteClassIds
	}
	return nil
}

func (x *UpdateServiceDef) GetIsEvaluationRequired() bool {
	if x != nil && x.IsEvaluationRequired != nil {
		return *x.IsEvaluationRequired
	}
	return false
}

func (x *UpdateServiceDef) GetIsEvaluationRequiredForOb() bool {
	if x != nil && x.IsEvaluationRequiredForOb != nil {
		return *x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *UpdateServiceDef) GetEvaluationId() int64 {
	if x != nil && x.EvaluationId != nil {
		return *x.EvaluationId
	}
	return 0
}

func (x *UpdateServiceDef) GetAdditionalServiceRule() *AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// service customized info by pet
type ServiceWithPetCustomizedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// price, service price in company level
	Price float64 `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,4,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// duration, service duration in company level
	Duration int32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	// is save price
	IsSavePrice bool `protobuf:"varint,6,opt,name=is_save_price,json=isSavePrice,proto3" json:"is_save_price,omitempty"`
	// is save duration
	IsSaveDuration bool `protobuf:"varint,7,opt,name=is_save_duration,json=isSaveDuration,proto3" json:"is_save_duration,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *ServiceWithPetCustomizedInfo) Reset() {
	*x = ServiceWithPetCustomizedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceWithPetCustomizedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceWithPetCustomizedInfo) ProtoMessage() {}

func (x *ServiceWithPetCustomizedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceWithPetCustomizedInfo.ProtoReflect.Descriptor instead.
func (*ServiceWithPetCustomizedInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceWithPetCustomizedInfo) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceWithPetCustomizedInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceWithPetCustomizedInfo) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceWithPetCustomizedInfo) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *ServiceWithPetCustomizedInfo) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceWithPetCustomizedInfo) GetIsSavePrice() bool {
	if x != nil {
		return x.IsSavePrice
	}
	return false
}

func (x *ServiceWithPetCustomizedInfo) GetIsSaveDuration() bool {
	if x != nil {
		return x.IsSaveDuration
	}
	return false
}

func (x *ServiceWithPetCustomizedInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ServiceWithPetCustomizedInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

// service applicable filter
type ServiceApplicableFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by pet
	FilterByPet *ServiceFilterByPet `protobuf:"bytes,1,opt,name=filter_by_pet,json=filterByPet,proto3" json:"filter_by_pet,omitempty"`
	// filter by selected service
	FilterByService *ServiceFilterByService `protobuf:"bytes,2,opt,name=filter_by_service,json=filterByService,proto3" json:"filter_by_service,omitempty"`
	// filter by selected lodging
	FilterByLodging *ServiceFilterByLodging `protobuf:"bytes,3,opt,name=filter_by_lodging,json=filterByLodging,proto3" json:"filter_by_lodging,omitempty"`
}

func (x *ServiceApplicableFilter) Reset() {
	*x = ServiceApplicableFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceApplicableFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceApplicableFilter) ProtoMessage() {}

func (x *ServiceApplicableFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceApplicableFilter.ProtoReflect.Descriptor instead.
func (*ServiceApplicableFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceApplicableFilter) GetFilterByPet() *ServiceFilterByPet {
	if x != nil {
		return x.FilterByPet
	}
	return nil
}

func (x *ServiceApplicableFilter) GetFilterByService() *ServiceFilterByService {
	if x != nil {
		return x.FilterByService
	}
	return nil
}

func (x *ServiceApplicableFilter) GetFilterByLodging() *ServiceFilterByLodging {
	if x != nil {
		return x.FilterByLodging
	}
	return nil
}

// service filter by pet
type ServiceFilterByPet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type
	PetType v1.PetType `protobuf:"varint,1,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet breed
	PetBreed *string `protobuf:"bytes,2,opt,name=pet_breed,json=petBreed,proto3,oneof" json:"pet_breed,omitempty"`
	// pet size
	PetSizeId *int64 `protobuf:"varint,3,opt,name=pet_size_id,json=petSizeId,proto3,oneof" json:"pet_size_id,omitempty"`
	// weight, should be deprecated, use pet size
	PetWeight *float64 `protobuf:"fixed64,4,opt,name=pet_weight,json=petWeight,proto3,oneof" json:"pet_weight,omitempty"`
	// pet coat type id
	PetCoatTypeId *int64 `protobuf:"varint,5,opt,name=pet_coat_type_id,json=petCoatTypeId,proto3,oneof" json:"pet_coat_type_id,omitempty"`
	// pet code ids
	PetCodeIds []int64 `protobuf:"varint,6,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *ServiceFilterByPet) Reset() {
	*x = ServiceFilterByPet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilterByPet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByPet) ProtoMessage() {}

func (x *ServiceFilterByPet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByPet.ProtoReflect.Descriptor instead.
func (*ServiceFilterByPet) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{4}
}

func (x *ServiceFilterByPet) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *ServiceFilterByPet) GetPetBreed() string {
	if x != nil && x.PetBreed != nil {
		return *x.PetBreed
	}
	return ""
}

func (x *ServiceFilterByPet) GetPetSizeId() int64 {
	if x != nil && x.PetSizeId != nil {
		return *x.PetSizeId
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetWeight() float64 {
	if x != nil && x.PetWeight != nil {
		return *x.PetWeight
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetCoatTypeId() int64 {
	if x != nil && x.PetCoatTypeId != nil {
		return *x.PetCoatTypeId
	}
	return 0
}

func (x *ServiceFilterByPet) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// service filter by selected service
type ServiceFilterByService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id list
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// service item type, only support one item type in one filter
	ServiceItemType *ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
}

func (x *ServiceFilterByService) Reset() {
	*x = ServiceFilterByService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilterByService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByService) ProtoMessage() {}

func (x *ServiceFilterByService) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByService.ProtoReflect.Descriptor instead.
func (*ServiceFilterByService) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceFilterByService) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ServiceFilterByService) GetServiceItemType() ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

// service filter by selected lodging
type ServiceFilterByLodging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging id list
	LodgingTypeIds []int64 `protobuf:"varint,1,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
}

func (x *ServiceFilterByLodging) Reset() {
	*x = ServiceFilterByLodging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilterByLodging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilterByLodging) ProtoMessage() {}

func (x *ServiceFilterByLodging) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilterByLodging.ProtoReflect.Descriptor instead.
func (*ServiceFilterByLodging) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceFilterByLodging) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// pet code filter
type CreateServiceDef_PetCodeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether to filter by white list or black list
	IsWhiteList bool `protobuf:"varint,1,opt,name=is_white_list,json=isWhiteList,proto3" json:"is_white_list,omitempty"`
	// whether it applies to all pet codes.
	IsAllPetCode bool `protobuf:"varint,2,opt,name=is_all_pet_code,json=isAllPetCode,proto3" json:"is_all_pet_code,omitempty"`
	// pet code list, only valid when is_all_pet_code is false
	PetCodeIds []int64 `protobuf:"varint,3,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *CreateServiceDef_PetCodeFilter) Reset() {
	*x = CreateServiceDef_PetCodeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceDef_PetCodeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceDef_PetCodeFilter) ProtoMessage() {}

func (x *CreateServiceDef_PetCodeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceDef_PetCodeFilter.ProtoReflect.Descriptor instead.
func (*CreateServiceDef_PetCodeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateServiceDef_PetCodeFilter) GetIsWhiteList() bool {
	if x != nil {
		return x.IsWhiteList
	}
	return false
}

func (x *CreateServiceDef_PetCodeFilter) GetIsAllPetCode() bool {
	if x != nil {
		return x.IsAllPetCode
	}
	return false
}

func (x *CreateServiceDef_PetCodeFilter) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// pet code filter
type UpdateServiceDef_PetCodeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether to filter by white list or black list
	IsWhiteList bool `protobuf:"varint,1,opt,name=is_white_list,json=isWhiteList,proto3" json:"is_white_list,omitempty"`
	// whether it applies to all pet codes.
	IsAllPetCode bool `protobuf:"varint,2,opt,name=is_all_pet_code,json=isAllPetCode,proto3" json:"is_all_pet_code,omitempty"`
	// pet code list, only valid when is_all_pet_code is false
	PetCodeIds []int64 `protobuf:"varint,3,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *UpdateServiceDef_PetCodeFilter) Reset() {
	*x = UpdateServiceDef_PetCodeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceDef_PetCodeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceDef_PetCodeFilter) ProtoMessage() {}

func (x *UpdateServiceDef_PetCodeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceDef_PetCodeFilter.ProtoReflect.Descriptor instead.
func (*UpdateServiceDef_PetCodeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_defs_proto_rawDescGZIP(), []int{1, 0}
}

func (x *UpdateServiceDef_PetCodeFilter) GetIsWhiteList() bool {
	if x != nil {
		return x.IsWhiteList
	}
	return false
}

func (x *UpdateServiceDef_PetCodeFilter) GetIsAllPetCode() bool {
	if x != nil {
		return x.IsAllPetCode
	}
	return false
}

func (x *UpdateServiceDef_PetCodeFilter) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

var File_moego_models_offering_v1_service_defs_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_service_defs_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x19, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xdc, 0x0b, 0x48, 0x00, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04,
	0x08, 0x00, 0x10, 0x0a, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0a,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x07, 0x48, 0x02, 0x52, 0x09, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x68, 0x0a, 0x16, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x48, 0x03, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x24, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x16, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x61, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x6e, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x63, 0x61,
	0x6e, 0x54, 0x69, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12,
	0x27, 0x0a, 0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0c, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x18, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74, 0x12, 0x3a, 0x0a, 0x19, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2a, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x13, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2f, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x0b, 0x6d, 0x61, 0x78,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x12, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x48, 0x07, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x6f,
	0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a,
	0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x25, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x1c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x26, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x19, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x27, 0x20, 0x03, 0x28, 0x03, 0x52, 0x17,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x18, 0x28, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x65,
	0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x66, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x48, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x12, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x2a, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48,
	0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c,
	0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2c, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x0a, 0x52, 0x0b, 0x6e,
	0x75, 0x6d, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a,
	0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x48, 0x0b, 0x52, 0x12, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x0c, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69,
	0x74, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x30, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a,
	0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72,
	0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x31, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x70, 0x72, 0x65, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73,
	0x12, 0x39, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x32, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x0d, 0x52, 0x14, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x1d, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x62, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x0e, 0x52, 0x19, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4f, 0x62, 0x88,
	0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x34, 0x20, 0x01, 0x28, 0x03, 0x48, 0x0f, 0x52, 0x0c, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x17,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x48, 0x10,
	0x52, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x89, 0x01, 0x0a, 0x0d, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d,
	0x69, 0x73, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d, 0x61, 0x78, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x63, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x42, 0x20, 0x0a, 0x1e, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x6f, 0x62, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x4a, 0x04, 0x08, 0x24, 0x10, 0x25, 0x52, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x22, 0xc7, 0x17, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18,
	0xdc, 0x0b, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x28, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x27, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x07, 0x52, 0x09, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x16, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x61, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x6e, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x63, 0x61,
	0x6e, 0x54, 0x69, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12,
	0x27, 0x0a, 0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0c, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x16, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x61, 0x74, 0x18, 0x18, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74, 0x12, 0x3a, 0x0a, 0x19, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2f, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72,
	0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x48, 0x01, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65,
	0x72, 0x52, 0x75, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x25, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x74, 0x0a, 0x1c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x26, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x19, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x27, 0x20, 0x03, 0x28, 0x03, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x28, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f,
	0x72, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x65, 0x0a, 0x0f, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x02, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x3f, 0x0a, 0x12, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x10, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x54, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x03, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x04, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x14, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e,
	0x18, 0x2d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48,
	0x05, 0x52, 0x12, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x41, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x5f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x18, 0x30, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x31, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x16, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x32, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x14, 0x69,
	0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x62, 0x18, 0x33, 0x20, 0x01, 0x28, 0x08, 0x48, 0x08, 0x52,
	0x19, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4f, 0x62, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x34,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x09, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x48, 0x0a, 0x52, 0x15, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x89, 0x01, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x2d, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05,
	0x10, 0xe8, 0x07, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6e, 0x75, 0x6d,
	0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x69, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x42,
	0x19, 0x0a, 0x17, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x20, 0x0a, 0x1e, 0x5f, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x62, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x1a,
	0x0a, 0x18, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x4a, 0x04, 0x08, 0x24, 0x10, 0x25,
	0x52, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0xde,
	0x02, 0x0a, 0x1c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x69, 0x74, 0x68, 0x50, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55,
	0x6e, 0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x61, 0x76, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69,
	0x73, 0x53, 0x61, 0x76, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0xa7, 0x02, 0x0a, 0x17, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0d, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x50, 0x65, 0x74,
	0x52, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x50, 0x65, 0x74, 0x12, 0x5c, 0x0a,
	0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5c, 0x0a, 0x11, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42,
	0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x22, 0xcf, 0x02, 0x0a, 0x12, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x50, 0x65, 0x74,
	0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20,
	0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x08, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x09, 0x70, 0x65, 0x74,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x10, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x16,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x5a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x16, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x73, 0x42, 0x7e, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_service_defs_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_service_defs_proto_rawDescData = file_moego_models_offering_v1_service_defs_proto_rawDesc
)

func file_moego_models_offering_v1_service_defs_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_service_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_service_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_service_defs_proto_rawDescData)
	})
	return file_moego_models_offering_v1_service_defs_proto_rawDescData
}

var file_moego_models_offering_v1_service_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_models_offering_v1_service_defs_proto_goTypes = []interface{}{
	(*CreateServiceDef)(nil),               // 0: moego.models.offering.v1.CreateServiceDef
	(*UpdateServiceDef)(nil),               // 1: moego.models.offering.v1.UpdateServiceDef
	(*ServiceWithPetCustomizedInfo)(nil),   // 2: moego.models.offering.v1.ServiceWithPetCustomizedInfo
	(*ServiceApplicableFilter)(nil),        // 3: moego.models.offering.v1.ServiceApplicableFilter
	(*ServiceFilterByPet)(nil),             // 4: moego.models.offering.v1.ServiceFilterByPet
	(*ServiceFilterByService)(nil),         // 5: moego.models.offering.v1.ServiceFilterByService
	(*ServiceFilterByLodging)(nil),         // 6: moego.models.offering.v1.ServiceFilterByLodging
	(*CreateServiceDef_PetCodeFilter)(nil), // 7: moego.models.offering.v1.CreateServiceDef.PetCodeFilter
	(*UpdateServiceDef_PetCodeFilter)(nil), // 8: moego.models.offering.v1.UpdateServiceDef.PetCodeFilter
	(*LocationOverrideRule)(nil),           // 9: moego.models.offering.v1.LocationOverrideRule
	(ServiceItemType)(0),                   // 10: moego.models.offering.v1.ServiceItemType
	(ServicePriceUnit)(0),                  // 11: moego.models.offering.v1.ServicePriceUnit
	(*CustomizedBreed)(nil),                // 12: moego.models.offering.v1.CustomizedBreed
	(*ServiceFilter)(nil),                  // 13: moego.models.offering.v1.ServiceFilter
	(ServiceType)(0),                       // 14: moego.models.offering.v1.ServiceType
	(*AutoRolloverRuleDef)(nil),            // 15: moego.models.offering.v1.AutoRolloverRuleDef
	(*LocationStaffOverrideRule)(nil),      // 16: moego.models.offering.v1.LocationStaffOverrideRule
	(ServiceModel_Source)(0),               // 17: moego.models.offering.v1.ServiceModel.Source
	(*AdditionalServiceRule)(nil),          // 18: moego.models.offering.v1.AdditionalServiceRule
	(v1.PetType)(0),                        // 19: moego.models.customer.v1.PetType
}
var file_moego_models_offering_v1_service_defs_proto_depIdxs = []int32{
	9,  // 0: moego.models.offering.v1.CreateServiceDef.location_override_list:type_name -> moego.models.offering.v1.LocationOverrideRule
	10, // 1: moego.models.offering.v1.CreateServiceDef.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	11, // 2: moego.models.offering.v1.CreateServiceDef.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	12, // 3: moego.models.offering.v1.CreateServiceDef.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	13, // 4: moego.models.offering.v1.CreateServiceDef.service_filter_list:type_name -> moego.models.offering.v1.ServiceFilter
	14, // 5: moego.models.offering.v1.CreateServiceDef.type:type_name -> moego.models.offering.v1.ServiceType
	15, // 6: moego.models.offering.v1.CreateServiceDef.auto_rollover_rule:type_name -> moego.models.offering.v1.AutoRolloverRuleDef
	16, // 7: moego.models.offering.v1.CreateServiceDef.location_staff_override_list:type_name -> moego.models.offering.v1.LocationStaffOverrideRule
	7,  // 8: moego.models.offering.v1.CreateServiceDef.pet_code_filter:type_name -> moego.models.offering.v1.CreateServiceDef.PetCodeFilter
	17, // 9: moego.models.offering.v1.CreateServiceDef.source:type_name -> moego.models.offering.v1.ServiceModel.Source
	18, // 10: moego.models.offering.v1.CreateServiceDef.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	9,  // 11: moego.models.offering.v1.UpdateServiceDef.location_override_list:type_name -> moego.models.offering.v1.LocationOverrideRule
	11, // 12: moego.models.offering.v1.UpdateServiceDef.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	12, // 13: moego.models.offering.v1.UpdateServiceDef.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	13, // 14: moego.models.offering.v1.UpdateServiceDef.service_filter_list:type_name -> moego.models.offering.v1.ServiceFilter
	15, // 15: moego.models.offering.v1.UpdateServiceDef.auto_rollover_rule:type_name -> moego.models.offering.v1.AutoRolloverRuleDef
	16, // 16: moego.models.offering.v1.UpdateServiceDef.location_staff_override_list:type_name -> moego.models.offering.v1.LocationStaffOverrideRule
	8,  // 17: moego.models.offering.v1.UpdateServiceDef.pet_code_filter:type_name -> moego.models.offering.v1.UpdateServiceDef.PetCodeFilter
	17, // 18: moego.models.offering.v1.UpdateServiceDef.source:type_name -> moego.models.offering.v1.ServiceModel.Source
	18, // 19: moego.models.offering.v1.UpdateServiceDef.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	11, // 20: moego.models.offering.v1.ServiceWithPetCustomizedInfo.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	4,  // 21: moego.models.offering.v1.ServiceApplicableFilter.filter_by_pet:type_name -> moego.models.offering.v1.ServiceFilterByPet
	5,  // 22: moego.models.offering.v1.ServiceApplicableFilter.filter_by_service:type_name -> moego.models.offering.v1.ServiceFilterByService
	6,  // 23: moego.models.offering.v1.ServiceApplicableFilter.filter_by_lodging:type_name -> moego.models.offering.v1.ServiceFilterByLodging
	19, // 24: moego.models.offering.v1.ServiceFilterByPet.pet_type:type_name -> moego.models.customer.v1.PetType
	10, // 25: moego.models.offering.v1.ServiceFilterByService.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_service_defs_proto_init() }
func file_moego_models_offering_v1_service_defs_proto_init() {
	if File_moego_models_offering_v1_service_defs_proto != nil {
		return
	}
	file_moego_models_offering_v1_auto_rollover_rule_defs_proto_init()
	file_moego_models_offering_v1_service_enum_proto_init()
	file_moego_models_offering_v1_service_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_service_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceWithPetCustomizedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceApplicableFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilterByPet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilterByService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilterByLodging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceDef_PetCodeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceDef_PetCodeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_service_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_defs_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_defs_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_service_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_service_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_service_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_service_defs_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_service_defs_proto = out.File
	file_moego_models_offering_v1_service_defs_proto_rawDesc = nil
	file_moego_models_offering_v1_service_defs_proto_goTypes = nil
	file_moego_models_offering_v1_service_defs_proto_depIdxs = nil
}
