// @since 2025-06-06 11:14:03
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/smart_scheduler/v1/time_slot_models.proto

package smartschedulerpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The TimeSlot
type TimeSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// start time
	StartTime int32 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime *int32 `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// time slot type
	TimeSlotType TimeSlotType `protobuf:"varint,5,opt,name=time_slot_type,json=timeSlotType,proto3,enum=moego.models.smart_scheduler.v1.TimeSlotType" json:"time_slot_type,omitempty"`
	// booked pet count
	BookedPetCount int32 `protobuf:"varint,6,opt,name=booked_pet_count,json=bookedPetCount,proto3" json:"booked_pet_count,omitempty"`
	// pet capacity
	PetCapacity int32 `protobuf:"varint,7,opt,name=pet_capacity,json=petCapacity,proto3" json:"pet_capacity,omitempty"`
	// booked pet info
	BookedInfos []*BookedInfo `protobuf:"bytes,8,rep,name=booked_infos,json=bookedInfos,proto3" json:"booked_infos,omitempty"`
}

func (x *TimeSlot) Reset() {
	*x = TimeSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSlot) ProtoMessage() {}

func (x *TimeSlot) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSlot.ProtoReflect.Descriptor instead.
func (*TimeSlot) Descriptor() ([]byte, []int) {
	return file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescGZIP(), []int{0}
}

func (x *TimeSlot) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *TimeSlot) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TimeSlot) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *TimeSlot) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *TimeSlot) GetTimeSlotType() TimeSlotType {
	if x != nil {
		return x.TimeSlotType
	}
	return TimeSlotType_TIME_SLOT_TYPE_UNSPECIFIED
}

func (x *TimeSlot) GetBookedPetCount() int32 {
	if x != nil {
		return x.BookedPetCount
	}
	return 0
}

func (x *TimeSlot) GetPetCapacity() int32 {
	if x != nil {
		return x.PetCapacity
	}
	return 0
}

func (x *TimeSlot) GetBookedInfos() []*BookedInfo {
	if x != nil {
		return x.BookedInfos
	}
	return nil
}

// booked info
type BookedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// customer last name
	CustomerLastName string `protobuf:"bytes,4,opt,name=customer_last_name,json=customerLastName,proto3" json:"customer_last_name,omitempty"`
}

func (x *BookedInfo) Reset() {
	*x = BookedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookedInfo) ProtoMessage() {}

func (x *BookedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookedInfo.ProtoReflect.Descriptor instead.
func (*BookedInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescGZIP(), []int{1}
}

func (x *BookedInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BookedInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *BookedInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *BookedInfo) GetCustomerLastName() string {
	if x != nil {
		return x.CustomerLastName
	}
	return ""
}

// pet param
type PetParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet index
	PetIndex int64 `protobuf:"varint,1,opt,name=pet_index,json=petIndex,proto3" json:"pet_index,omitempty"`
	// pet type
	PetType v1.PetType `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet breed
	Breed string `protobuf:"bytes,3,opt,name=breed,proto3" json:"breed,omitempty"`
	// pet weight
	Weight *string `protobuf:"bytes,4,opt,name=weight,proto3,oneof" json:"weight,omitempty"`
	// pet coat
	Coat *string `protobuf:"bytes,5,opt,name=coat,proto3,oneof" json:"coat,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,6,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *PetParam) Reset() {
	*x = PetParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetParam) ProtoMessage() {}

func (x *PetParam) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetParam.ProtoReflect.Descriptor instead.
func (*PetParam) Descriptor() ([]byte, []int) {
	return file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescGZIP(), []int{2}
}

func (x *PetParam) GetPetIndex() int64 {
	if x != nil {
		return x.PetIndex
	}
	return 0
}

func (x *PetParam) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *PetParam) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *PetParam) GetWeight() string {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return ""
}

func (x *PetParam) GetCoat() string {
	if x != nil && x.Coat != nil {
		return *x.Coat
	}
	return ""
}

func (x *PetParam) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *PetParam) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

var File_moego_models_smart_scheduler_v1_time_slot_models_proto protoreflect.FileDescriptor

var file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x03, 0x0a,
	0x08, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b,
	0x28, 0x00, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x61, 0x72,
	0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x6f,
	0x6b, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x4e, 0x0a, 0x0c, 0x62, 0x6f, 0x6f, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x61, 0x72,
	0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x6b, 0x65,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x0a, 0x42, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xca, 0x02, 0x0a, 0x08, 0x50, 0x65, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x24, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x70, 0x65, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x48, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x04, 0x63, 0x6f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x04, 0x63, 0x6f, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x42, 0x92, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d,
	0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x65, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescOnce sync.Once
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescData = file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDesc
)

func file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescGZIP() []byte {
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescOnce.Do(func() {
		file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescData)
	})
	return file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDescData
}

var file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_smart_scheduler_v1_time_slot_models_proto_goTypes = []interface{}{
	(*TimeSlot)(nil),   // 0: moego.models.smart_scheduler.v1.TimeSlot
	(*BookedInfo)(nil), // 1: moego.models.smart_scheduler.v1.BookedInfo
	(*PetParam)(nil),   // 2: moego.models.smart_scheduler.v1.PetParam
	(TimeSlotType)(0),  // 3: moego.models.smart_scheduler.v1.TimeSlotType
	(v1.PetType)(0),    // 4: moego.models.customer.v1.PetType
}
var file_moego_models_smart_scheduler_v1_time_slot_models_proto_depIdxs = []int32{
	3, // 0: moego.models.smart_scheduler.v1.TimeSlot.time_slot_type:type_name -> moego.models.smart_scheduler.v1.TimeSlotType
	1, // 1: moego.models.smart_scheduler.v1.TimeSlot.booked_infos:type_name -> moego.models.smart_scheduler.v1.BookedInfo
	4, // 2: moego.models.smart_scheduler.v1.PetParam.pet_type:type_name -> moego.models.customer.v1.PetType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_smart_scheduler_v1_time_slot_models_proto_init() }
func file_moego_models_smart_scheduler_v1_time_slot_models_proto_init() {
	if File_moego_models_smart_scheduler_v1_time_slot_models_proto != nil {
		return
	}
	file_moego_models_smart_scheduler_v1_time_slot_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_smart_scheduler_v1_time_slot_models_proto_goTypes,
		DependencyIndexes: file_moego_models_smart_scheduler_v1_time_slot_models_proto_depIdxs,
		MessageInfos:      file_moego_models_smart_scheduler_v1_time_slot_models_proto_msgTypes,
	}.Build()
	File_moego_models_smart_scheduler_v1_time_slot_models_proto = out.File
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_rawDesc = nil
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_goTypes = nil
	file_moego_models_smart_scheduler_v1_time_slot_models_proto_depIdxs = nil
}
