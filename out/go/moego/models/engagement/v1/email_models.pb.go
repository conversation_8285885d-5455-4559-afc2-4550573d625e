// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/engagement/v1/email_models.proto

package engagementpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Sender email usage type
type SenderEmailUsageType int32

const (
	// SenderEmailUsageType_UNSPECIFIED 无效值.
	SenderEmailUsageType_SENDER_EMAIL_USAGE_TYPE_UNSPECIFIED SenderEmailUsageType = 0
	// default
	SenderEmailUsageType_SENDER_EMAIL_USAGE_TYPE_DEFAULT SenderEmailUsageType = 1
	// customize
	SenderEmailUsageType_SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE SenderEmailUsageType = 2
)

// Enum value maps for SenderEmailUsageType.
var (
	SenderEmailUsageType_name = map[int32]string{
		0: "SENDER_EMAIL_USAGE_TYPE_UNSPECIFIED",
		1: "SENDER_EMAIL_USAGE_TYPE_DEFAULT",
		2: "SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE",
	}
	SenderEmailUsageType_value = map[string]int32{
		"SENDER_EMAIL_USAGE_TYPE_UNSPECIFIED": 0,
		"SENDER_EMAIL_USAGE_TYPE_DEFAULT":     1,
		"SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE":   2,
	}
)

func (x SenderEmailUsageType) Enum() *SenderEmailUsageType {
	p := new(SenderEmailUsageType)
	*p = x
	return p
}

func (x SenderEmailUsageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SenderEmailUsageType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_email_models_proto_enumTypes[0].Descriptor()
}

func (SenderEmailUsageType) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_email_models_proto_enumTypes[0]
}

func (x SenderEmailUsageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SenderEmailUsageType.Descriptor instead.
func (SenderEmailUsageType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_email_models_proto_rawDescGZIP(), []int{0}
}

// VerifyStatus is the status of the email verification.
type DomainVerifyStatus int32

const (
	// VERIFY_STATUS_UNSPECIFIED 无效值.
	DomainVerifyStatus_VERIFY_STATUS_UNSPECIFIED DomainVerifyStatus = 0
	// VERIFY_STATUS_INIT 此面向敌，初始状态，需要通过发送确认（码）邮件来进行确认.
	DomainVerifyStatus_VERIFY_STATUS_INIT DomainVerifyStatus = 1
	// VERIFY_STATUS_CONFIRMED 已确认，但仍旧无法使用，需要配置 CNAME 进行验证。
	DomainVerifyStatus_VERIFY_STATUS_CONFIRMED DomainVerifyStatus = 2
	// VERIFY_STATUS_VERIFIED 已验证，可以使用.
	DomainVerifyStatus_VERIFY_STATUS_VERIFIED DomainVerifyStatus = 3
)

// Enum value maps for DomainVerifyStatus.
var (
	DomainVerifyStatus_name = map[int32]string{
		0: "VERIFY_STATUS_UNSPECIFIED",
		1: "VERIFY_STATUS_INIT",
		2: "VERIFY_STATUS_CONFIRMED",
		3: "VERIFY_STATUS_VERIFIED",
	}
	DomainVerifyStatus_value = map[string]int32{
		"VERIFY_STATUS_UNSPECIFIED": 0,
		"VERIFY_STATUS_INIT":        1,
		"VERIFY_STATUS_CONFIRMED":   2,
		"VERIFY_STATUS_VERIFIED":    3,
	}
)

func (x DomainVerifyStatus) Enum() *DomainVerifyStatus {
	p := new(DomainVerifyStatus)
	*p = x
	return p
}

func (x DomainVerifyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DomainVerifyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_email_models_proto_enumTypes[1].Descriptor()
}

func (DomainVerifyStatus) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_email_models_proto_enumTypes[1]
}

func (x DomainVerifyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DomainVerifyStatus.Descriptor instead.
func (DomainVerifyStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_email_models_proto_rawDescGZIP(), []int{1}
}

// DNSRecordType is the type of the DNS record.
type DNSRecordType int32

const (
	// DNS_RECORD_TYPE_UNSPECIFIED 无效值.
	DNSRecordType_DNS_RECORD_TYPE_UNSPECIFIED DNSRecordType = 0
	// DNS_RECORD_TYPE_CNAME CNAME 记录.
	DNSRecordType_DNS_RECORD_TYPE_CNAME DNSRecordType = 1
	// DNS_RECORD_TYPE_TXT TXT 记录.
	DNSRecordType_DNS_RECORD_TYPE_TXT DNSRecordType = 2
)

// Enum value maps for DNSRecordType.
var (
	DNSRecordType_name = map[int32]string{
		0: "DNS_RECORD_TYPE_UNSPECIFIED",
		1: "DNS_RECORD_TYPE_CNAME",
		2: "DNS_RECORD_TYPE_TXT",
	}
	DNSRecordType_value = map[string]int32{
		"DNS_RECORD_TYPE_UNSPECIFIED": 0,
		"DNS_RECORD_TYPE_CNAME":       1,
		"DNS_RECORD_TYPE_TXT":         2,
	}
)

func (x DNSRecordType) Enum() *DNSRecordType {
	p := new(DNSRecordType)
	*p = x
	return p
}

func (x DNSRecordType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DNSRecordType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_email_models_proto_enumTypes[2].Descriptor()
}

func (DNSRecordType) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_email_models_proto_enumTypes[2]
}

func (x DNSRecordType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DNSRecordType.Descriptor instead.
func (DNSRecordType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_email_models_proto_rawDescGZIP(), []int{2}
}

// SenderEmail is a model for sender email.
type SenderEmailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID is the unique identifier for the sender email.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Email is the email address of the sender.
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// Name is the name of the sender.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// VerifyStatus is the status of the email verification.
	VerifyStatus DomainVerifyStatus `protobuf:"varint,4,opt,name=verify_status,json=verifyStatus,proto3,enum=moego.models.engagement.v1.DomainVerifyStatus" json:"verify_status,omitempty"`
	// dns records
	DnsRecords []*DNSRecordModel `protobuf:"bytes,5,rep,name=dns_records,json=dnsRecords,proto3" json:"dns_records,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,7,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *SenderEmailModel) Reset() {
	*x = SenderEmailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_email_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SenderEmailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SenderEmailModel) ProtoMessage() {}

func (x *SenderEmailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_email_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SenderEmailModel.ProtoReflect.Descriptor instead.
func (*SenderEmailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_email_models_proto_rawDescGZIP(), []int{0}
}

func (x *SenderEmailModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SenderEmailModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SenderEmailModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SenderEmailModel) GetVerifyStatus() DomainVerifyStatus {
	if x != nil {
		return x.VerifyStatus
	}
	return DomainVerifyStatus_VERIFY_STATUS_UNSPECIFIED
}

func (x *SenderEmailModel) GetDnsRecords() []*DNSRecordModel {
	if x != nil {
		return x.DnsRecords
	}
	return nil
}

func (x *SenderEmailModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// DNSRecord is a model for DNS record.
type DNSRecordModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type is the type of the DNS record.
	Type DNSRecordType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.engagement.v1.DNSRecordType" json:"type,omitempty"`
	// host 记录主机, 当 record 直接解析为根域名时没有 host
	Host *string `protobuf:"bytes,2,opt,name=host,proto3,oneof" json:"host,omitempty"`
	// value 记录值
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DNSRecordModel) Reset() {
	*x = DNSRecordModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_email_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DNSRecordModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSRecordModel) ProtoMessage() {}

func (x *DNSRecordModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_email_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSRecordModel.ProtoReflect.Descriptor instead.
func (*DNSRecordModel) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_email_models_proto_rawDescGZIP(), []int{1}
}

func (x *DNSRecordModel) GetType() DNSRecordType {
	if x != nil {
		return x.Type
	}
	return DNSRecordType_DNS_RECORD_TYPE_UNSPECIFIED
}

func (x *DNSRecordModel) GetHost() string {
	if x != nil && x.Host != nil {
		return *x.Host
	}
	return ""
}

func (x *DNSRecordModel) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_moego_models_engagement_v1_email_models_proto protoreflect.FileDescriptor

var file_moego_models_engagement_v1_email_models_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22, 0x8f, 0x02, 0x0a, 0x10,
	0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x4b, 0x0a, 0x0b, 0x64, 0x6e, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0a, 0x64, 0x6e, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x87, 0x01,
	0x0a, 0x0e, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x2a, 0x8b, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x27, 0x0a, 0x23, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x4e,
	0x44, 0x45, 0x52, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x01, 0x12, 0x25,
	0x0a, 0x21, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x49, 0x5a, 0x45, 0x10, 0x02, 0x2a, 0x84, 0x01, 0x0a, 0x12, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x64, 0x0a, 0x0d,
	0x44, 0x4e, 0x53, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x1b, 0x44, 0x4e, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19,
	0x0a, 0x15, 0x44, 0x4e, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x4e, 0x53,
	0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x58, 0x54,
	0x10, 0x02, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_engagement_v1_email_models_proto_rawDescOnce sync.Once
	file_moego_models_engagement_v1_email_models_proto_rawDescData = file_moego_models_engagement_v1_email_models_proto_rawDesc
)

func file_moego_models_engagement_v1_email_models_proto_rawDescGZIP() []byte {
	file_moego_models_engagement_v1_email_models_proto_rawDescOnce.Do(func() {
		file_moego_models_engagement_v1_email_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_engagement_v1_email_models_proto_rawDescData)
	})
	return file_moego_models_engagement_v1_email_models_proto_rawDescData
}

var file_moego_models_engagement_v1_email_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_engagement_v1_email_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_engagement_v1_email_models_proto_goTypes = []interface{}{
	(SenderEmailUsageType)(0), // 0: moego.models.engagement.v1.SenderEmailUsageType
	(DomainVerifyStatus)(0),   // 1: moego.models.engagement.v1.DomainVerifyStatus
	(DNSRecordType)(0),        // 2: moego.models.engagement.v1.DNSRecordType
	(*SenderEmailModel)(nil),  // 3: moego.models.engagement.v1.SenderEmailModel
	(*DNSRecordModel)(nil),    // 4: moego.models.engagement.v1.DNSRecordModel
}
var file_moego_models_engagement_v1_email_models_proto_depIdxs = []int32{
	1, // 0: moego.models.engagement.v1.SenderEmailModel.verify_status:type_name -> moego.models.engagement.v1.DomainVerifyStatus
	4, // 1: moego.models.engagement.v1.SenderEmailModel.dns_records:type_name -> moego.models.engagement.v1.DNSRecordModel
	2, // 2: moego.models.engagement.v1.DNSRecordModel.type:type_name -> moego.models.engagement.v1.DNSRecordType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_engagement_v1_email_models_proto_init() }
func file_moego_models_engagement_v1_email_models_proto_init() {
	if File_moego_models_engagement_v1_email_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_engagement_v1_email_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SenderEmailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_email_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DNSRecordModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_engagement_v1_email_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_engagement_v1_email_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_engagement_v1_email_models_proto_goTypes,
		DependencyIndexes: file_moego_models_engagement_v1_email_models_proto_depIdxs,
		EnumInfos:         file_moego_models_engagement_v1_email_models_proto_enumTypes,
		MessageInfos:      file_moego_models_engagement_v1_email_models_proto_msgTypes,
	}.Build()
	File_moego_models_engagement_v1_email_models_proto = out.File
	file_moego_models_engagement_v1_email_models_proto_rawDesc = nil
	file_moego_models_engagement_v1_email_models_proto_goTypes = nil
	file_moego_models_engagement_v1_email_models_proto_depIdxs = nil
}
