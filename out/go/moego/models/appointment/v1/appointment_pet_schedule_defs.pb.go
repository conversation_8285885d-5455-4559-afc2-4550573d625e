// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/appointment_pet_schedule_defs.proto

package appointmentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Pet's feeding and medication schedules
type PetScheduleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// Appointment feeding schedules
	Feedings []*AppointmentPetFeedingScheduleDef `protobuf:"bytes,2,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Appointment medication schedules
	Medications []*AppointmentPetMedicationScheduleDef `protobuf:"bytes,3,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *PetScheduleDef) Reset() {
	*x = PetScheduleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetScheduleDef) ProtoMessage() {}

func (x *PetScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetScheduleDef.ProtoReflect.Descriptor instead.
func (*PetScheduleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PetScheduleDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetScheduleDef) GetFeedings() []*AppointmentPetFeedingScheduleDef {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *PetScheduleDef) GetMedications() []*AppointmentPetMedicationScheduleDef {
	if x != nil {
		return x.Medications
	}
	return nil
}

// appointment pet schedule def
type AppointmentPetScheduleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet's schedules
	Schedules []*PetScheduleDef `protobuf:"bytes,1,rep,name=schedules,proto3" json:"schedules,omitempty"`
	// Schedule settings
	ScheduleSettings []*AppointmentPetScheduleSettingModel `protobuf:"bytes,2,rep,name=schedule_settings,json=scheduleSettings,proto3" json:"schedule_settings,omitempty"`
}

func (x *AppointmentPetScheduleDef) Reset() {
	*x = AppointmentPetScheduleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentPetScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetScheduleDef) ProtoMessage() {}

func (x *AppointmentPetScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetScheduleDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetScheduleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescGZIP(), []int{1}
}

func (x *AppointmentPetScheduleDef) GetSchedules() []*PetScheduleDef {
	if x != nil {
		return x.Schedules
	}
	return nil
}

func (x *AppointmentPetScheduleDef) GetScheduleSettings() []*AppointmentPetScheduleSettingModel {
	if x != nil {
		return x.ScheduleSettings
	}
	return nil
}

var File_moego_models_appointment_v1_appointment_pet_schedule_defs_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x47,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x02, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x6c, 0x0a, 0x08, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e,
	0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08,
	0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x75, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0xd4, 0x01, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x49, 0x0a,
	0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x6c, 0x0a, 0x11, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x10, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescData = file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDesc
)

func file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDescData
}

var file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_goTypes = []interface{}{
	(*PetScheduleDef)(nil),                      // 0: moego.models.appointment.v1.PetScheduleDef
	(*AppointmentPetScheduleDef)(nil),           // 1: moego.models.appointment.v1.AppointmentPetScheduleDef
	(*AppointmentPetFeedingScheduleDef)(nil),    // 2: moego.models.appointment.v1.AppointmentPetFeedingScheduleDef
	(*AppointmentPetMedicationScheduleDef)(nil), // 3: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
	(*AppointmentPetScheduleSettingModel)(nil),  // 4: moego.models.appointment.v1.AppointmentPetScheduleSettingModel
}
var file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.appointment.v1.PetScheduleDef.feedings:type_name -> moego.models.appointment.v1.AppointmentPetFeedingScheduleDef
	3, // 1: moego.models.appointment.v1.PetScheduleDef.medications:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
	0, // 2: moego.models.appointment.v1.AppointmentPetScheduleDef.schedules:type_name -> moego.models.appointment.v1.PetScheduleDef
	4, // 3: moego.models.appointment.v1.AppointmentPetScheduleDef.schedule_settings:type_name -> moego.models.appointment.v1.AppointmentPetScheduleSettingModel
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_init() }
func file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_init() {
	if File_moego_models_appointment_v1_appointment_pet_schedule_defs_proto != nil {
		return
	}
	file_moego_models_appointment_v1_appointment_pet_feeding_schedule_defs_proto_init()
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_init()
	file_moego_models_appointment_v1_appointment_pet_schedule_setting_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetScheduleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentPetScheduleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_appointment_pet_schedule_defs_proto = out.File
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_rawDesc = nil
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_goTypes = nil
	file_moego_models_appointment_v1_appointment_pet_schedule_defs_proto_depIdxs = nil
}
