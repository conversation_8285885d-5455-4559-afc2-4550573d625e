// @since 2024-06-24 17:19:30
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/daily_report_enums.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// question type
type QuestionType int32

const (
	// unspecified value
	QuestionType_QUESTION_TYPE_UNSPECIFIED QuestionType = 0
	// single_choice
	QuestionType_SINGLE_CHOICE QuestionType = 1
	// multi_choice
	QuestionType_MULTI_CHOICE QuestionType = 2
	// text_input( long_text_input )
	QuestionType_TEXT_INPUT QuestionType = 3
	// body_view
	QuestionType_BODY_VIEW QuestionType = 4
	// short_text_input
	QuestionType_SHORT_TEXT_INPUT QuestionType = 5
	// tag_choice
	QuestionType_TAG_CHOICE QuestionType = 6
)

// Enum value maps for QuestionType.
var (
	QuestionType_name = map[int32]string{
		0: "QUESTION_TYPE_UNSPECIFIED",
		1: "SINGLE_CHOICE",
		2: "MULTI_CHOICE",
		3: "TEXT_INPUT",
		4: "BODY_VIEW",
		5: "SHORT_TEXT_INPUT",
		6: "TAG_CHOICE",
	}
	QuestionType_value = map[string]int32{
		"QUESTION_TYPE_UNSPECIFIED": 0,
		"SINGLE_CHOICE":             1,
		"MULTI_CHOICE":              2,
		"TEXT_INPUT":                3,
		"BODY_VIEW":                 4,
		"SHORT_TEXT_INPUT":          5,
		"TAG_CHOICE":                6,
	}
)

func (x QuestionType) Enum() *QuestionType {
	p := new(QuestionType)
	*p = x
	return p
}

func (x QuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[0].Descriptor()
}

func (QuestionType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[0]
}

func (x QuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionType.Descriptor instead.
func (QuestionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_enums_proto_rawDescGZIP(), []int{0}
}

// question category type
type QuestionCategoryType int32

const (
	// unspecified value
	QuestionCategoryType_QUESTION_CATEGORY_TYPE_UNSPECIFIED QuestionCategoryType = 0
	// feedback
	QuestionCategoryType_FEEDBACK QuestionCategoryType = 1
	// pet condition
	QuestionCategoryType_PET_CONDITION QuestionCategoryType = 2
	// customize feedback
	QuestionCategoryType_CUSTOMIZE_FEEDBACK QuestionCategoryType = 3
)

// Enum value maps for QuestionCategoryType.
var (
	QuestionCategoryType_name = map[int32]string{
		0: "QUESTION_CATEGORY_TYPE_UNSPECIFIED",
		1: "FEEDBACK",
		2: "PET_CONDITION",
		3: "CUSTOMIZE_FEEDBACK",
	}
	QuestionCategoryType_value = map[string]int32{
		"QUESTION_CATEGORY_TYPE_UNSPECIFIED": 0,
		"FEEDBACK":                           1,
		"PET_CONDITION":                      2,
		"CUSTOMIZE_FEEDBACK":                 3,
	}
)

func (x QuestionCategoryType) Enum() *QuestionCategoryType {
	p := new(QuestionCategoryType)
	*p = x
	return p
}

func (x QuestionCategoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionCategoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[1].Descriptor()
}

func (QuestionCategoryType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[1]
}

func (x QuestionCategoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionCategoryType.Descriptor instead.
func (QuestionCategoryType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_enums_proto_rawDescGZIP(), []int{1}
}

// report status
type ReportCardStatus int32

const (
	// unspecified value
	ReportCardStatus_REPORT_CARD_STATUS_UNSPECIFIED ReportCardStatus = 0
	// created
	ReportCardStatus_REPORT_CARD_CREATED ReportCardStatus = 1
	// draft
	ReportCardStatus_REPORT_CARD_DRAFT ReportCardStatus = 2
	// ready
	ReportCardStatus_REPORT_CARD_READY ReportCardStatus = 3
	// sent
	ReportCardStatus_REPORT_CARD_SENT ReportCardStatus = 4
)

// Enum value maps for ReportCardStatus.
var (
	ReportCardStatus_name = map[int32]string{
		0: "REPORT_CARD_STATUS_UNSPECIFIED",
		1: "REPORT_CARD_CREATED",
		2: "REPORT_CARD_DRAFT",
		3: "REPORT_CARD_READY",
		4: "REPORT_CARD_SENT",
	}
	ReportCardStatus_value = map[string]int32{
		"REPORT_CARD_STATUS_UNSPECIFIED": 0,
		"REPORT_CARD_CREATED":            1,
		"REPORT_CARD_DRAFT":              2,
		"REPORT_CARD_READY":              3,
		"REPORT_CARD_SENT":               4,
	}
)

func (x ReportCardStatus) Enum() *ReportCardStatus {
	p := new(ReportCardStatus)
	*p = x
	return p
}

func (x ReportCardStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportCardStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[2].Descriptor()
}

func (ReportCardStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[2]
}

func (x ReportCardStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportCardStatus.Descriptor instead.
func (ReportCardStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_enums_proto_rawDescGZIP(), []int{2}
}

// send method
type SendMethod int32

const (
	// unspecified value
	SendMethod_SEND_METHOD_UNSPECIFIED SendMethod = 0
	// sms
	SendMethod_SEND_METHOD_SMS SendMethod = 1
	// email
	SendMethod_SEND_METHOD_EMAIL SendMethod = 2
)

// Enum value maps for SendMethod.
var (
	SendMethod_name = map[int32]string{
		0: "SEND_METHOD_UNSPECIFIED",
		1: "SEND_METHOD_SMS",
		2: "SEND_METHOD_EMAIL",
	}
	SendMethod_value = map[string]int32{
		"SEND_METHOD_UNSPECIFIED": 0,
		"SEND_METHOD_SMS":         1,
		"SEND_METHOD_EMAIL":       2,
	}
)

func (x SendMethod) Enum() *SendMethod {
	p := new(SendMethod)
	*p = x
	return p
}

func (x SendMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[3].Descriptor()
}

func (SendMethod) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes[3]
}

func (x SendMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendMethod.Descriptor instead.
func (SendMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_appointment_v1_daily_report_enums_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_daily_report_enums_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x69, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2a, 0x97, 0x01, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x43, 0x48,
	0x4f, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f,
	0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x58, 0x54,
	0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x4f, 0x44, 0x59,
	0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x48, 0x4f, 0x52, 0x54,
	0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x05, 0x12, 0x0e, 0x0a,
	0x0a, 0x54, 0x41, 0x47, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x06, 0x2a, 0x77, 0x0a,
	0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x50,
	0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x10, 0x03, 0x2a, 0x93, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x13, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x02, 0x12,
	0x15, 0x0a, 0x11, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x2a, 0x55, 0x0a, 0x0a,
	0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45,
	0x4e, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x10, 0x02, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_daily_report_enums_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_daily_report_enums_proto_rawDescData = file_moego_models_appointment_v1_daily_report_enums_proto_rawDesc
)

func file_moego_models_appointment_v1_daily_report_enums_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_daily_report_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_daily_report_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_daily_report_enums_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_daily_report_enums_proto_rawDescData
}

var file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_appointment_v1_daily_report_enums_proto_goTypes = []interface{}{
	(QuestionType)(0),         // 0: moego.models.appointment.v1.QuestionType
	(QuestionCategoryType)(0), // 1: moego.models.appointment.v1.QuestionCategoryType
	(ReportCardStatus)(0),     // 2: moego.models.appointment.v1.ReportCardStatus
	(SendMethod)(0),           // 3: moego.models.appointment.v1.SendMethod
}
var file_moego_models_appointment_v1_daily_report_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_daily_report_enums_proto_init() }
func file_moego_models_appointment_v1_daily_report_enums_proto_init() {
	if File_moego_models_appointment_v1_daily_report_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_daily_report_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_daily_report_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_daily_report_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_daily_report_enums_proto_enumTypes,
	}.Build()
	File_moego_models_appointment_v1_daily_report_enums_proto = out.File
	file_moego_models_appointment_v1_daily_report_enums_proto_rawDesc = nil
	file_moego_models_appointment_v1_daily_report_enums_proto_goTypes = nil
	file_moego_models_appointment_v1_daily_report_enums_proto_depIdxs = nil
}
