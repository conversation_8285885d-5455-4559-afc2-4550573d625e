// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/clock_in_out_setting_defs.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update clock in/out setting definition
type UpdateClockInOutSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enable clock in/out
	EnableClockInOut *bool `protobuf:"varint,1,opt,name=enable_clock_in_out,json=enableClockInOut,proto3,oneof" json:"enable_clock_in_out,omitempty"`
	// clock in/out notify
	ClockInOutNotify *bool `protobuf:"varint,2,opt,name=clock_in_out_notify,json=clockInOutNotify,proto3,oneof" json:"clock_in_out_notify,omitempty"`
	// enable clock in/out overnight
	EnableClockInOutOvernight *bool `protobuf:"varint,3,opt,name=enable_clock_in_out_overnight,json=enableClockInOutOvernight,proto3,oneof" json:"enable_clock_in_out_overnight,omitempty"`
}

func (x *UpdateClockInOutSettingDef) Reset() {
	*x = UpdateClockInOutSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockInOutSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockInOutSettingDef) ProtoMessage() {}

func (x *UpdateClockInOutSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockInOutSettingDef.ProtoReflect.Descriptor instead.
func (*UpdateClockInOutSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateClockInOutSettingDef) GetEnableClockInOut() bool {
	if x != nil && x.EnableClockInOut != nil {
		return *x.EnableClockInOut
	}
	return false
}

func (x *UpdateClockInOutSettingDef) GetClockInOutNotify() bool {
	if x != nil && x.ClockInOutNotify != nil {
		return *x.ClockInOutNotify
	}
	return false
}

func (x *UpdateClockInOutSettingDef) GetEnableClockInOutOvernight() bool {
	if x != nil && x.EnableClockInOutOvernight != nil {
		return *x.EnableClockInOutOvernight
	}
	return false
}

var File_moego_models_organization_v1_clock_in_out_setting_defs_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x22, 0x9d, 0x02, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x13, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f,
	0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x10, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x32, 0x0a, 0x13, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x10,
	0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x1d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x6e,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x19, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x4f, 0x76,
	0x65, 0x72, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f,
	0x75, 0x74, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x42, 0x20, 0x0a, 0x1e, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x6e, 0x69, 0x67, 0x68, 0x74, 0x42, 0x8a, 0x01, 0x0a,
	0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescData = file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDesc
)

func file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescData)
	})
	return file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDescData
}

var file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_organization_v1_clock_in_out_setting_defs_proto_goTypes = []interface{}{
	(*UpdateClockInOutSettingDef)(nil), // 0: moego.models.organization.v1.UpdateClockInOutSettingDef
}
var file_moego_models_organization_v1_clock_in_out_setting_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_clock_in_out_setting_defs_proto_init() }
func file_moego_models_organization_v1_clock_in_out_setting_defs_proto_init() {
	if File_moego_models_organization_v1_clock_in_out_setting_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockInOutSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_clock_in_out_setting_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_clock_in_out_setting_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_clock_in_out_setting_defs_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_clock_in_out_setting_defs_proto = out.File
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_rawDesc = nil
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_goTypes = nil
	file_moego_models_organization_v1_clock_in_out_setting_defs_proto_depIdxs = nil
}
