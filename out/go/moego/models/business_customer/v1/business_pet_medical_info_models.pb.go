// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_medical_info_models.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet medical info model
type BusinessPetMedicalInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// vet name
	VetName string `protobuf:"bytes,2,opt,name=vet_name,json=vetName,proto3" json:"vet_name,omitempty"`
	// vet phone number
	VetPhoneNumber string `protobuf:"bytes,3,opt,name=vet_phone_number,json=vetPhoneNumber,proto3" json:"vet_phone_number,omitempty"`
	// vet address
	VetAddress string `protobuf:"bytes,4,opt,name=vet_address,json=vetAddress,proto3" json:"vet_address,omitempty"`
	// emergency contact name
	EmergencyContactName string `protobuf:"bytes,5,opt,name=emergency_contact_name,json=emergencyContactName,proto3" json:"emergency_contact_name,omitempty"`
	// emergency contact phone number
	EmergencyContactPhoneNumber string `protobuf:"bytes,6,opt,name=emergency_contact_phone_number,json=emergencyContactPhoneNumber,proto3" json:"emergency_contact_phone_number,omitempty"`
	// health issues
	HealthIssues string `protobuf:"bytes,7,opt,name=health_issues,json=healthIssues,proto3" json:"health_issues,omitempty"`
}

func (x *BusinessPetMedicalInfoModel) Reset() {
	*x = BusinessPetMedicalInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetMedicalInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetMedicalInfoModel) ProtoMessage() {}

func (x *BusinessPetMedicalInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetMedicalInfoModel.ProtoReflect.Descriptor instead.
func (*BusinessPetMedicalInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetMedicalInfoModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BusinessPetMedicalInfoModel) GetVetName() string {
	if x != nil {
		return x.VetName
	}
	return ""
}

func (x *BusinessPetMedicalInfoModel) GetVetPhoneNumber() string {
	if x != nil {
		return x.VetPhoneNumber
	}
	return ""
}

func (x *BusinessPetMedicalInfoModel) GetVetAddress() string {
	if x != nil {
		return x.VetAddress
	}
	return ""
}

func (x *BusinessPetMedicalInfoModel) GetEmergencyContactName() string {
	if x != nil {
		return x.EmergencyContactName
	}
	return ""
}

func (x *BusinessPetMedicalInfoModel) GetEmergencyContactPhoneNumber() string {
	if x != nil {
		return x.EmergencyContactPhoneNumber
	}
	return ""
}

func (x *BusinessPetMedicalInfoModel) GetHealthIssues() string {
	if x != nil {
		return x.HealthIssues
	}
	return ""
}

var File_moego_models_business_customer_v1_business_pet_medical_info_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDesc = []byte{
	0x0a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0xba, 0x02,
	0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x15, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x76, 0x65, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x74, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x74,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x76, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x43, 0x0a, 0x1e, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x69, 0x73, 0x73, 0x75, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_goTypes = []interface{}{
	(*BusinessPetMedicalInfoModel)(nil), // 0: moego.models.business_customer.v1.BusinessPetMedicalInfoModel
}
var file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_init() }
func file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_medical_info_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetMedicalInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_medical_info_models_proto = out.File
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_medical_info_models_proto_depIdxs = nil
}
