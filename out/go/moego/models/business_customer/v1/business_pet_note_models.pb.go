// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_note_models.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet note model
type BusinessPetNoteModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// note
	Note string `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
	// created by (staff id)
	CreatedBy int64 `protobuf:"varint,4,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// updated by (staff id)
	UpdatedBy int64 `protobuf:"varint,5,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// is pinned
	IsPinned bool `protobuf:"varint,8,opt,name=is_pinned,json=isPinned,proto3" json:"is_pinned,omitempty"`
	// pinned at
	PinnedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=pinned_at,json=pinnedAt,proto3" json:"pinned_at,omitempty"`
}

func (x *BusinessPetNoteModel) Reset() {
	*x = BusinessPetNoteModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_note_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetNoteModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetNoteModel) ProtoMessage() {}

func (x *BusinessPetNoteModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_note_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetNoteModel.ProtoReflect.Descriptor instead.
func (*BusinessPetNoteModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetNoteModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessPetNoteModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BusinessPetNoteModel) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *BusinessPetNoteModel) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *BusinessPetNoteModel) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *BusinessPetNoteModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BusinessPetNoteModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BusinessPetNoteModel) GetIsPinned() bool {
	if x != nil {
		return x.IsPinned
	}
	return false
}

func (x *BusinessPetNoteModel) GetPinnedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PinnedAt
	}
	return nil
}

var File_moego_models_business_customer_v1_business_pet_note_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDesc = []byte{
	0x0a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdb, 0x02, 0x0a, 0x14, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x70,
	0x69, 0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x69, 0x6e, 0x6e,
	0x65, 0x64, 0x41, 0x74, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_note_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_business_customer_v1_business_pet_note_models_proto_goTypes = []interface{}{
	(*BusinessPetNoteModel)(nil),  // 0: moego.models.business_customer.v1.BusinessPetNoteModel
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_moego_models_business_customer_v1_business_pet_note_models_proto_depIdxs = []int32{
	1, // 0: moego.models.business_customer.v1.BusinessPetNoteModel.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: moego.models.business_customer.v1.BusinessPetNoteModel.updated_at:type_name -> google.protobuf.Timestamp
	1, // 2: moego.models.business_customer.v1.BusinessPetNoteModel.pinned_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_note_models_proto_init() }
func file_moego_models_business_customer_v1_business_pet_note_models_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_note_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_note_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetNoteModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_note_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_note_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_note_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_note_models_proto = out.File
	file_moego_models_business_customer_v1_business_pet_note_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_note_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_note_models_proto_depIdxs = nil
}
