// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_incident_report_models.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet incident report model
type BusinessPetIncidentReportModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// incident report id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id list
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// incident timestamp
	IncidentTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=incident_time,json=incidentTime,proto3" json:"incident_time,omitempty"`
	// incident type id
	IncidentTypeId int64 `protobuf:"varint,4,opt,name=incident_type_id,json=incidentTypeId,proto3" json:"incident_type_id,omitempty"`
	// incident description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// attachment url list
	AttachmentFiles []*PetIncidentAttachment `protobuf:"bytes,6,rep,name=attachment_files,json=attachmentFiles,proto3" json:"attachment_files,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,7,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// is staff injured
	IsStaffInjured bool `protobuf:"varint,8,opt,name=is_staff_injured,json=isStaffInjured,proto3" json:"is_staff_injured,omitempty"`
	// is pet injured
	IsPetInjured bool `protobuf:"varint,9,opt,name=is_pet_injured,json=isPetInjured,proto3" json:"is_pet_injured,omitempty"`
	// is vet visit
	IsVetVisited bool `protobuf:"varint,10,opt,name=is_vet_visited,json=isVetVisited,proto3" json:"is_vet_visited,omitempty"`
}

func (x *BusinessPetIncidentReportModel) Reset() {
	*x = BusinessPetIncidentReportModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetIncidentReportModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetIncidentReportModel) ProtoMessage() {}

func (x *BusinessPetIncidentReportModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetIncidentReportModel.ProtoReflect.Descriptor instead.
func (*BusinessPetIncidentReportModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetIncidentReportModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessPetIncidentReportModel) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *BusinessPetIncidentReportModel) GetIncidentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.IncidentTime
	}
	return nil
}

func (x *BusinessPetIncidentReportModel) GetIncidentTypeId() int64 {
	if x != nil {
		return x.IncidentTypeId
	}
	return 0
}

func (x *BusinessPetIncidentReportModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BusinessPetIncidentReportModel) GetAttachmentFiles() []*PetIncidentAttachment {
	if x != nil {
		return x.AttachmentFiles
	}
	return nil
}

func (x *BusinessPetIncidentReportModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessPetIncidentReportModel) GetIsStaffInjured() bool {
	if x != nil {
		return x.IsStaffInjured
	}
	return false
}

func (x *BusinessPetIncidentReportModel) GetIsPetInjured() bool {
	if x != nil {
		return x.IsPetInjured
	}
	return false
}

func (x *BusinessPetIncidentReportModel) GetIsVetVisited() bool {
	if x != nil {
		return x.IsVetVisited
	}
	return false
}

// pet incident attachment
type PetIncidentAttachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// attachment url
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// attachment name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PetIncidentAttachment) Reset() {
	*x = PetIncidentAttachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetIncidentAttachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetIncidentAttachment) ProtoMessage() {}

func (x *PetIncidentAttachment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetIncidentAttachment.ProtoReflect.Descriptor instead.
func (*PetIncidentAttachment) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescGZIP(), []int{1}
}

func (x *PetIncidentAttachment) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PetIncidentAttachment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_moego_models_business_customer_v1_business_pet_incident_report_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDesc = []byte{
	0x0a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd2, 0x03, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a,
	0x0d, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0c, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x10, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x6e, 0x6a,
	0x75, 0x72, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x6a, 0x75, 0x72, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x65, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x74,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x56, 0x65, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x64, 0x22, 0x3d, 0x0a, 0x15, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_goTypes = []interface{}{
	(*BusinessPetIncidentReportModel)(nil), // 0: moego.models.business_customer.v1.BusinessPetIncidentReportModel
	(*PetIncidentAttachment)(nil),          // 1: moego.models.business_customer.v1.PetIncidentAttachment
	(*timestamppb.Timestamp)(nil),          // 2: google.protobuf.Timestamp
}
var file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_depIdxs = []int32{
	2, // 0: moego.models.business_customer.v1.BusinessPetIncidentReportModel.incident_time:type_name -> google.protobuf.Timestamp
	1, // 1: moego.models.business_customer.v1.BusinessPetIncidentReportModel.attachment_files:type_name -> moego.models.business_customer.v1.PetIncidentAttachment
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_init() }
func file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_incident_report_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetIncidentReportModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetIncidentAttachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_incident_report_models_proto = out.File
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_incident_report_models_proto_depIdxs = nil
}
