// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_feeding_schedule_defs.proto

package businesscustomerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Business pet feeding schedule model
type BusinessPetFeedingScheduleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// feeding amount, such as 1.2, 1/2, 1 etc.
	FeedingAmount string `protobuf:"bytes,3,opt,name=feeding_amount,json=feedingAmount,proto3" json:"feeding_amount,omitempty"`
	// feeding unit, pet_metadata.metadata_value, metadata_name = 2
	FeedingUnit string `protobuf:"bytes,4,opt,name=feeding_unit,json=feedingUnit,proto3" json:"feeding_unit,omitempty"`
	// feeding type, pet_metadata.metadata_value, metadata_name = 3
	FeedingType string `protobuf:"bytes,5,opt,name=feeding_type,json=feedingType,proto3" json:"feeding_type,omitempty"`
	// feeding source, pet_metadata.metadata_value, metadata_name = 4
	FeedingSource string `protobuf:"bytes,6,opt,name=feeding_source,json=feedingSource,proto3" json:"feeding_source,omitempty"`
	// feeding instruction, pet_metadata.metadata_value, metadata_name = 5
	FeedingInstruction *string `protobuf:"bytes,7,opt,name=feeding_instruction,json=feedingInstruction,proto3,oneof" json:"feeding_instruction,omitempty"`
	// feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	FeedingTimes []*BusinessPetScheduleTimeDef `protobuf:"bytes,8,rep,name=feeding_times,json=feedingTimes,proto3" json:"feeding_times,omitempty"`
	// feeding note
	FeedingNote *string `protobuf:"bytes,9,opt,name=feeding_note,json=feedingNote,proto3,oneof" json:"feeding_note,omitempty"`
}

func (x *BusinessPetFeedingScheduleDef) Reset() {
	*x = BusinessPetFeedingScheduleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetFeedingScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetFeedingScheduleDef) ProtoMessage() {}

func (x *BusinessPetFeedingScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetFeedingScheduleDef.ProtoReflect.Descriptor instead.
func (*BusinessPetFeedingScheduleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetFeedingScheduleDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingAmount() string {
	if x != nil {
		return x.FeedingAmount
	}
	return ""
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingUnit() string {
	if x != nil {
		return x.FeedingUnit
	}
	return ""
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingType() string {
	if x != nil {
		return x.FeedingType
	}
	return ""
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingSource() string {
	if x != nil {
		return x.FeedingSource
	}
	return ""
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingInstruction() string {
	if x != nil && x.FeedingInstruction != nil {
		return *x.FeedingInstruction
	}
	return ""
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingTimes() []*BusinessPetScheduleTimeDef {
	if x != nil {
		return x.FeedingTimes
	}
	return nil
}

func (x *BusinessPetFeedingScheduleDef) GetFeedingNote() string {
	if x != nil && x.FeedingNote != nil {
		return *x.FeedingNote
	}
	return ""
}

// Business pet feeding schedule view
type BusinessPetFeedingScheduleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// feeding id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// feeding amount, such as 1.2, 1/2, 1 etc.
	FeedingAmount string `protobuf:"bytes,3,opt,name=feeding_amount,json=feedingAmount,proto3" json:"feeding_amount,omitempty"`
	// feeding unit, pet_metadata.metadata_value, metadata_name = 2
	FeedingUnit string `protobuf:"bytes,4,opt,name=feeding_unit,json=feedingUnit,proto3" json:"feeding_unit,omitempty"`
	// feeding type, pet_metadata.metadata_value, metadata_name = 3
	FeedingType string `protobuf:"bytes,5,opt,name=feeding_type,json=feedingType,proto3" json:"feeding_type,omitempty"`
	// feeding source, pet_metadata.metadata_value, metadata_name = 4
	FeedingSource string `protobuf:"bytes,6,opt,name=feeding_source,json=feedingSource,proto3" json:"feeding_source,omitempty"`
	// feeding instruction, pet_metadata.metadata_value, metadata_name = 5
	FeedingInstruction *string `protobuf:"bytes,7,opt,name=feeding_instruction,json=feedingInstruction,proto3,oneof" json:"feeding_instruction,omitempty"`
	// feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	FeedingTimes []*BusinessPetScheduleTimeDef `protobuf:"bytes,8,rep,name=feeding_times,json=feedingTimes,proto3" json:"feeding_times,omitempty"`
	// feeding note
	FeedingNote *string `protobuf:"bytes,9,opt,name=feeding_note,json=feedingNote,proto3,oneof" json:"feeding_note,omitempty"`
}

func (x *BusinessPetFeedingScheduleView) Reset() {
	*x = BusinessPetFeedingScheduleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetFeedingScheduleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetFeedingScheduleView) ProtoMessage() {}

func (x *BusinessPetFeedingScheduleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetFeedingScheduleView.ProtoReflect.Descriptor instead.
func (*BusinessPetFeedingScheduleView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessPetFeedingScheduleView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessPetFeedingScheduleView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BusinessPetFeedingScheduleView) GetFeedingAmount() string {
	if x != nil {
		return x.FeedingAmount
	}
	return ""
}

func (x *BusinessPetFeedingScheduleView) GetFeedingUnit() string {
	if x != nil {
		return x.FeedingUnit
	}
	return ""
}

func (x *BusinessPetFeedingScheduleView) GetFeedingType() string {
	if x != nil {
		return x.FeedingType
	}
	return ""
}

func (x *BusinessPetFeedingScheduleView) GetFeedingSource() string {
	if x != nil {
		return x.FeedingSource
	}
	return ""
}

func (x *BusinessPetFeedingScheduleView) GetFeedingInstruction() string {
	if x != nil && x.FeedingInstruction != nil {
		return *x.FeedingInstruction
	}
	return ""
}

func (x *BusinessPetFeedingScheduleView) GetFeedingTimes() []*BusinessPetScheduleTimeDef {
	if x != nil {
		return x.FeedingTimes
	}
	return nil
}

func (x *BusinessPetFeedingScheduleView) GetFeedingNote() string {
	if x != nil && x.FeedingNote != nil {
		return *x.FeedingNote
	}
	return ""
}

var File_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a,
	0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x04, 0x0a, 0x1d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x3e, 0x0a, 0x13, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x12, 0x66, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x6e, 0x0a, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04,
	0x08, 0x01, 0x10, 0x63, 0x52, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x12, 0x30, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0x80, 0x50, 0x48, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0xa0, 0x04,
	0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0e, 0x66, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x13, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x12,
	0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x0d, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x63, 0x52, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x50, 0x48, 0x01, 0x52, 0x0b, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x66, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x74, 0x65,
	0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_goTypes = []interface{}{
	(*BusinessPetFeedingScheduleDef)(nil),  // 0: moego.models.business_customer.v1.BusinessPetFeedingScheduleDef
	(*BusinessPetFeedingScheduleView)(nil), // 1: moego.models.business_customer.v1.BusinessPetFeedingScheduleView
	(*BusinessPetScheduleTimeDef)(nil),     // 2: moego.models.business_customer.v1.BusinessPetScheduleTimeDef
}
var file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.business_customer.v1.BusinessPetFeedingScheduleDef.feeding_times:type_name -> moego.models.business_customer.v1.BusinessPetScheduleTimeDef
	2, // 1: moego.models.business_customer.v1.BusinessPetFeedingScheduleView.feeding_times:type_name -> moego.models.business_customer.v1.BusinessPetScheduleTimeDef
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_init() }
func file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto != nil {
		return
	}
	file_moego_models_business_customer_v1_business_pet_schedule_setting_defs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetFeedingScheduleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetFeedingScheduleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto = out.File
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_feeding_schedule_defs_proto_depIdxs = nil
}
