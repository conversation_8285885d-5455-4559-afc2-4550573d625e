// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/ob_access_enums.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ob access type
type AccessType int32

const (
	// unspecified
	AccessType_ACCESS_TYPE_UNSPECIFIED AccessType = 0
	// access by phone
	AccessType_ACCESS_TYPE_BY_PHONE AccessType = 1
	// access by email
	AccessType_ACCESS_TYPE_BY_EMAIL AccessType = 2
)

// Enum value maps for AccessType.
var (
	AccessType_name = map[int32]string{
		0: "ACCESS_TYPE_UNSPECIFIED",
		1: "ACCESS_TYPE_BY_PHONE",
		2: "ACCESS_TYPE_BY_EMAIL",
	}
	AccessType_value = map[string]int32{
		"ACCESS_TYPE_UNSPECIFIED": 0,
		"ACCESS_TYPE_BY_PHONE":    1,
		"ACCESS_TYPE_BY_EMAIL":    2,
	}
)

func (x AccessType) Enum() *AccessType {
	p := new(AccessType)
	*p = x
	return p
}

func (x AccessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_access_enums_proto_enumTypes[0].Descriptor()
}

func (AccessType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_access_enums_proto_enumTypes[0]
}

func (x AccessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessType.Descriptor instead.
func (AccessType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_online_booking_v1_ob_access_enums_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_ob_access_enums_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x5d, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x42, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x10, 0x02, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescData = file_moego_models_online_booking_v1_ob_access_enums_proto_rawDesc
)

func file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_ob_access_enums_proto_rawDescData
}

var file_moego_models_online_booking_v1_ob_access_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_online_booking_v1_ob_access_enums_proto_goTypes = []interface{}{
	(AccessType)(0), // 0: moego.models.online_booking.v1.AccessType
}
var file_moego_models_online_booking_v1_ob_access_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_ob_access_enums_proto_init() }
func file_moego_models_online_booking_v1_ob_access_enums_proto_init() {
	if File_moego_models_online_booking_v1_ob_access_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_ob_access_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_ob_access_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_ob_access_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_online_booking_v1_ob_access_enums_proto_enumTypes,
	}.Build()
	File_moego_models_online_booking_v1_ob_access_enums_proto = out.File
	file_moego_models_online_booking_v1_ob_access_enums_proto_rawDesc = nil
	file_moego_models_online_booking_v1_ob_access_enums_proto_goTypes = nil
	file_moego_models_online_booking_v1_ob_access_enums_proto_depIdxs = nil
}
