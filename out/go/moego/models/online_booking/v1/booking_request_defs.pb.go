// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/booking_request_defs.proto

package onlinebookingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The booking request definition
type BookingRequestDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id, generated after the booking request is scheduled
	AppointmentId *int64 `protobuf:"varint,4,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// start date, format: yyyy-mm-dd
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// start time, the minutes from 00:00
	StartTime int32 `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// is paid flag
	IsPaid bool `protobuf:"varint,7,opt,name=is_paid,json=isPaid,proto3" json:"is_paid,omitempty"`
	// additional note
	AdditionalNote *string `protobuf:"bytes,8,opt,name=additional_note,json=additionalNote,proto3,oneof" json:"additional_note,omitempty"`
	// source platform
	SourcePlatform BookingRequestSourcePlatform `protobuf:"varint,9,opt,name=source_platform,json=sourcePlatform,proto3,enum=moego.models.online_booking.v1.BookingRequestSourcePlatform" json:"source_platform,omitempty"`
}

func (x *BookingRequestDef) Reset() {
	*x = BookingRequestDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingRequestDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingRequestDef) ProtoMessage() {}

func (x *BookingRequestDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingRequestDef.ProtoReflect.Descriptor instead.
func (*BookingRequestDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BookingRequestDef) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BookingRequestDef) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *BookingRequestDef) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *BookingRequestDef) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *BookingRequestDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *BookingRequestDef) GetIsPaid() bool {
	if x != nil {
		return x.IsPaid
	}
	return false
}

func (x *BookingRequestDef) GetAdditionalNote() string {
	if x != nil && x.AdditionalNote != nil {
		return *x.AdditionalNote
	}
	return ""
}

func (x *BookingRequestDef) GetSourcePlatform() BookingRequestSourcePlatform {
	if x != nil {
		return x.SourcePlatform
	}
	return BookingRequestSourcePlatform_BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED
}

// Pet to lodging def
type PetToLodgingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,2,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
}

func (x *PetToLodgingDef) Reset() {
	*x = PetToLodgingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToLodgingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToLodgingDef) ProtoMessage() {}

func (x *PetToLodgingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToLodgingDef.ProtoReflect.Descriptor instead.
func (*PetToLodgingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{1}
}

func (x *PetToLodgingDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToLodgingDef) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

// Pet to staff def
type PetToStaffDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// start time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *PetToStaffDef) Reset() {
	*x = PetToStaffDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToStaffDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToStaffDef) ProtoMessage() {}

func (x *PetToStaffDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToStaffDef.ProtoReflect.Descriptor instead.
func (*PetToStaffDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{2}
}

func (x *PetToStaffDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToStaffDef) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *PetToStaffDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *PetToStaffDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

// Pet to service def
type PetToServiceDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// old evaluation service id
	FromEvaluationServiceId *int64 `protobuf:"varint,2,opt,name=from_evaluation_service_id,json=fromEvaluationServiceId,proto3,oneof" json:"from_evaluation_service_id,omitempty"`
	// new evaluation service id
	ToEvaluationServiceId *int64 `protobuf:"varint,3,opt,name=to_evaluation_service_id,json=toEvaluationServiceId,proto3,oneof" json:"to_evaluation_service_id,omitempty"`
}

func (x *PetToServiceDef) Reset() {
	*x = PetToServiceDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetToServiceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetToServiceDef) ProtoMessage() {}

func (x *PetToServiceDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetToServiceDef.ProtoReflect.Descriptor instead.
func (*PetToServiceDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{3}
}

func (x *PetToServiceDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetToServiceDef) GetFromEvaluationServiceId() int64 {
	if x != nil && x.FromEvaluationServiceId != nil {
		return *x.FromEvaluationServiceId
	}
	return 0
}

func (x *PetToServiceDef) GetToEvaluationServiceId() int64 {
	if x != nil && x.ToEvaluationServiceId != nil {
		return *x.ToEvaluationServiceId
	}
	return 0
}

// OB request pet params
type Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id, New pet has virtual id, existing pet has id
	PetId *int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// Name
	PetName *string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3,oneof" json:"pet_name,omitempty"`
	// Avatar path
	AvatarPath *string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// Breed
	Breed *string `protobuf:"bytes,4,opt,name=breed,proto3,oneof" json:"breed,omitempty"`
	// Breed mix
	BreedMix *int32 `protobuf:"varint,5,opt,name=breed_mix,json=breedMix,proto3,oneof" json:"breed_mix,omitempty"`
	// Pet type id
	PetTypeId *int32 `protobuf:"varint,6,opt,name=pet_type_id,json=petTypeId,proto3,oneof" json:"pet_type_id,omitempty"`
	// Gender
	Gender *int32 `protobuf:"varint,7,opt,name=gender,proto3,oneof" json:"gender,omitempty"`
	// Birthday
	Birthday *string `protobuf:"bytes,8,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// Weight
	Weight *string `protobuf:"bytes,9,opt,name=weight,proto3,oneof" json:"weight,omitempty"`
	// Fixed
	Fixed *string `protobuf:"bytes,10,opt,name=fixed,proto3,oneof" json:"fixed,omitempty"`
	// Behavior
	Behavior *string `protobuf:"bytes,11,opt,name=behavior,proto3,oneof" json:"behavior,omitempty"`
	// Hair length
	HairLength *string `protobuf:"bytes,12,opt,name=hair_length,json=hairLength,proto3,oneof" json:"hair_length,omitempty"`
	// Expiry notification
	ExpiryNotification *int32 `protobuf:"varint,13,opt,name=expiry_notification,json=expiryNotification,proto3,oneof" json:"expiry_notification,omitempty"`
	// Vet name
	VetName *string `protobuf:"bytes,14,opt,name=vet_name,json=vetName,proto3,oneof" json:"vet_name,omitempty"`
	// Vet phone
	VetPhone *string `protobuf:"bytes,15,opt,name=vet_phone,json=vetPhone,proto3,oneof" json:"vet_phone,omitempty"`
	// Vet address
	VetAddress *string `protobuf:"bytes,16,opt,name=vet_address,json=vetAddress,proto3,oneof" json:"vet_address,omitempty"`
	// Emergency contact name
	EmergencyContactName *string `protobuf:"bytes,17,opt,name=emergency_contact_name,json=emergencyContactName,proto3,oneof" json:"emergency_contact_name,omitempty"`
	// Emergency contact phone
	EmergencyContactPhone *string `protobuf:"bytes,18,opt,name=emergency_contact_phone,json=emergencyContactPhone,proto3,oneof" json:"emergency_contact_phone,omitempty"`
	// Health issues
	HealthIssues *string `protobuf:"bytes,19,opt,name=health_issues,json=healthIssues,proto3,oneof" json:"health_issues,omitempty"`
	// Vaccines
	VaccineList []*Pet_Vaccine `protobuf:"bytes,20,rep,name=vaccine_list,json=vaccineList,proto3" json:"vaccine_list,omitempty"`
	// Pet image
	PetImage *string `protobuf:"bytes,21,opt,name=pet_image,json=petImage,proto3,oneof" json:"pet_image,omitempty"`
	// Is selected
	//
	//	bool is_selected = 22;
	//
	// Pet question answers
	PetQuestionAnswers map[string]*structpb.Value `protobuf:"bytes,25,rep,name=pet_question_answers,json=petQuestionAnswers,proto3" json:"pet_question_answers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Pet) Reset() {
	*x = Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet) ProtoMessage() {}

func (x *Pet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet.ProtoReflect.Descriptor instead.
func (*Pet) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{4}
}

func (x *Pet) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *Pet) GetPetName() string {
	if x != nil && x.PetName != nil {
		return *x.PetName
	}
	return ""
}

func (x *Pet) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *Pet) GetBreed() string {
	if x != nil && x.Breed != nil {
		return *x.Breed
	}
	return ""
}

func (x *Pet) GetBreedMix() int32 {
	if x != nil && x.BreedMix != nil {
		return *x.BreedMix
	}
	return 0
}

func (x *Pet) GetPetTypeId() int32 {
	if x != nil && x.PetTypeId != nil {
		return *x.PetTypeId
	}
	return 0
}

func (x *Pet) GetGender() int32 {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return 0
}

func (x *Pet) GetBirthday() string {
	if x != nil && x.Birthday != nil {
		return *x.Birthday
	}
	return ""
}

func (x *Pet) GetWeight() string {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return ""
}

func (x *Pet) GetFixed() string {
	if x != nil && x.Fixed != nil {
		return *x.Fixed
	}
	return ""
}

func (x *Pet) GetBehavior() string {
	if x != nil && x.Behavior != nil {
		return *x.Behavior
	}
	return ""
}

func (x *Pet) GetHairLength() string {
	if x != nil && x.HairLength != nil {
		return *x.HairLength
	}
	return ""
}

func (x *Pet) GetExpiryNotification() int32 {
	if x != nil && x.ExpiryNotification != nil {
		return *x.ExpiryNotification
	}
	return 0
}

func (x *Pet) GetVetName() string {
	if x != nil && x.VetName != nil {
		return *x.VetName
	}
	return ""
}

func (x *Pet) GetVetPhone() string {
	if x != nil && x.VetPhone != nil {
		return *x.VetPhone
	}
	return ""
}

func (x *Pet) GetVetAddress() string {
	if x != nil && x.VetAddress != nil {
		return *x.VetAddress
	}
	return ""
}

func (x *Pet) GetEmergencyContactName() string {
	if x != nil && x.EmergencyContactName != nil {
		return *x.EmergencyContactName
	}
	return ""
}

func (x *Pet) GetEmergencyContactPhone() string {
	if x != nil && x.EmergencyContactPhone != nil {
		return *x.EmergencyContactPhone
	}
	return ""
}

func (x *Pet) GetHealthIssues() string {
	if x != nil && x.HealthIssues != nil {
		return *x.HealthIssues
	}
	return ""
}

func (x *Pet) GetVaccineList() []*Pet_Vaccine {
	if x != nil {
		return x.VaccineList
	}
	return nil
}

func (x *Pet) GetPetImage() string {
	if x != nil && x.PetImage != nil {
		return *x.PetImage
	}
	return ""
}

func (x *Pet) GetPetQuestionAnswers() map[string]*structpb.Value {
	if x != nil {
		return x.PetQuestionAnswers
	}
	return nil
}

// Feeding
type Feeding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Feeding time
	Time []*FeedingModel_FeedingSchedule `protobuf:"bytes,1,rep,name=time,proto3" json:"time,omitempty"`
	// Feeding unit
	Unit *string `protobuf:"bytes,3,opt,name=unit,proto3,oneof" json:"unit,omitempty"`
	// Food type
	FoodType *string `protobuf:"bytes,4,opt,name=food_type,json=foodType,proto3,oneof" json:"food_type,omitempty"`
	// Food source
	FoodSource *string `protobuf:"bytes,5,opt,name=food_source,json=foodSource,proto3,oneof" json:"food_source,omitempty"`
	// Feeding instructions
	Instruction *string `protobuf:"bytes,6,opt,name=instruction,proto3,oneof" json:"instruction,omitempty"`
	// Feeding note
	Note *string `protobuf:"bytes,7,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// Feeding amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,8,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
}

func (x *Feeding) Reset() {
	*x = Feeding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feeding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feeding) ProtoMessage() {}

func (x *Feeding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feeding.ProtoReflect.Descriptor instead.
func (*Feeding) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{5}
}

func (x *Feeding) GetTime() []*FeedingModel_FeedingSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *Feeding) GetUnit() string {
	if x != nil && x.Unit != nil {
		return *x.Unit
	}
	return ""
}

func (x *Feeding) GetFoodType() string {
	if x != nil && x.FoodType != nil {
		return *x.FoodType
	}
	return ""
}

func (x *Feeding) GetFoodSource() string {
	if x != nil && x.FoodSource != nil {
		return *x.FoodSource
	}
	return ""
}

func (x *Feeding) GetInstruction() string {
	if x != nil && x.Instruction != nil {
		return *x.Instruction
	}
	return ""
}

func (x *Feeding) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *Feeding) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

// Medication
type Medication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Medication time
	Time []*MedicationModel_MedicationSchedule `protobuf:"bytes,1,rep,name=time,proto3" json:"time,omitempty"`
	// Medication unit
	Unit *string `protobuf:"bytes,3,opt,name=unit,proto3,oneof" json:"unit,omitempty"`
	// Medication name
	MedicationName *string `protobuf:"bytes,4,opt,name=medication_name,json=medicationName,proto3,oneof" json:"medication_name,omitempty"`
	// Medication notes
	Notes *string `protobuf:"bytes,5,opt,name=notes,proto3,oneof" json:"notes,omitempty"`
	// Medication amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,8,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
}

func (x *Medication) Reset() {
	*x = Medication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Medication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Medication) ProtoMessage() {}

func (x *Medication) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Medication.ProtoReflect.Descriptor instead.
func (*Medication) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{6}
}

func (x *Medication) GetTime() []*MedicationModel_MedicationSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *Medication) GetUnit() string {
	if x != nil && x.Unit != nil {
		return *x.Unit
	}
	return ""
}

func (x *Medication) GetMedicationName() string {
	if x != nil && x.MedicationName != nil {
		return *x.MedicationName
	}
	return ""
}

func (x *Medication) GetNotes() string {
	if x != nil && x.Notes != nil {
		return *x.Notes
	}
	return ""
}

func (x *Medication) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

// Boarding addon
type BoardingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Specific dates
	Dates []string `protobuf:"bytes,4,rep,name=dates,proto3" json:"dates,omitempty"`
	// Quantity per day
	QuantityPerDay *int32 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// date type
	DateType *v1.PetDetailDateType `protobuf:"varint,9,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,10,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *BoardingAddon) Reset() {
	*x = BoardingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingAddon) ProtoMessage() {}

func (x *BoardingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingAddon.ProtoReflect.Descriptor instead.
func (*BoardingAddon) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{7}
}

func (x *BoardingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoardingAddon) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *BoardingAddon) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *BoardingAddon) GetDateType() v1.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v1.PetDetailDateType(0)
}

func (x *BoardingAddon) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

// Daycare addon
type DaycareAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Is every day
	IsEveryDay *bool `protobuf:"varint,3,opt,name=is_every_day,json=isEveryDay,proto3,oneof" json:"is_every_day,omitempty"`
	// Specific dates
	Dates []string `protobuf:"bytes,4,rep,name=dates,proto3" json:"dates,omitempty"`
	// Quantity per day
	QuantityPerDay *int32 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
}

func (x *DaycareAddon) Reset() {
	*x = DaycareAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareAddon) ProtoMessage() {}

func (x *DaycareAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareAddon.ProtoReflect.Descriptor instead.
func (*DaycareAddon) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{8}
}

func (x *DaycareAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareAddon) GetIsEveryDay() bool {
	if x != nil && x.IsEveryDay != nil {
		return *x.IsEveryDay
	}
	return false
}

func (x *DaycareAddon) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *DaycareAddon) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

// Daycare addon
type GroomingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GroomingAddon) Reset() {
	*x = GroomingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingAddon) ProtoMessage() {}

func (x *GroomingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingAddon.ProtoReflect.Descriptor instead.
func (*GroomingAddon) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{9}
}

func (x *GroomingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Grooming service
type GroomingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// start date
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// addons
	Addons []*GroomingAddon `protobuf:"bytes,4,rep,name=addons,proto3" json:"addons,omitempty"`
}

func (x *GroomingServiceDetail) Reset() {
	*x = GroomingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingServiceDetail) ProtoMessage() {}

func (x *GroomingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingServiceDetail.ProtoReflect.Descriptor instead.
func (*GroomingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{10}
}

func (x *GroomingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GroomingServiceDetail) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *GroomingServiceDetail) GetAddons() []*GroomingAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Boarding service
type BoardingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Arrival date
	// 当 boarding service detail 加入 waitlist 时，这个参数可以为空
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// Arrival time, in minutes since midnight
	ArrivalTime int32 `protobuf:"varint,4,opt,name=arrival_time,json=arrivalTime,proto3" json:"arrival_time,omitempty"`
	// Pick up date
	// 当 boarding service detail 加入 waitlist 时，这个参数可以为空
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// Pick up time, in minutes since midnight
	PickupTime int32 `protobuf:"varint,6,opt,name=pickup_time,json=pickupTime,proto3" json:"pickup_time,omitempty"`
	// Addons
	Addons []*BoardingAddon `protobuf:"bytes,11,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feedings
	Feedings []*Feeding `protobuf:"bytes,12,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*Medication `protobuf:"bytes,13,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *BoardingServiceDetail) Reset() {
	*x = BoardingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingServiceDetail) ProtoMessage() {}

func (x *BoardingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingServiceDetail.ProtoReflect.Descriptor instead.
func (*BoardingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{11}
}

func (x *BoardingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BoardingServiceDetail) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *BoardingServiceDetail) GetArrivalTime() int32 {
	if x != nil {
		return x.ArrivalTime
	}
	return 0
}

func (x *BoardingServiceDetail) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *BoardingServiceDetail) GetPickupTime() int32 {
	if x != nil {
		return x.PickupTime
	}
	return 0
}

func (x *BoardingServiceDetail) GetAddons() []*BoardingAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *BoardingServiceDetail) GetFeedings() []*Feeding {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *BoardingServiceDetail) GetMedications() []*Medication {
	if x != nil {
		return x.Medications
	}
	return nil
}

// Daycare service
type DaycareServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Dates of the daycare, in the format of "yyyy-MM-dd"
	// 当 daycare service detail 加入 waitlist 时，这个参数可以为空
	Dates []string `protobuf:"bytes,2,rep,name=dates,proto3" json:"dates,omitempty"`
	// Arrival time, in minutes since midnight
	ArrivalTime int32 `protobuf:"varint,4,opt,name=arrival_time,json=arrivalTime,proto3" json:"arrival_time,omitempty"`
	// Pick up time, in minutes since midnight
	PickupTime *int32 `protobuf:"varint,6,opt,name=pickup_time,json=pickupTime,proto3,oneof" json:"pickup_time,omitempty"`
	// Addons
	Addons []*DaycareAddon `protobuf:"bytes,12,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feedings
	Feedings []*Feeding `protobuf:"bytes,13,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*Medication `protobuf:"bytes,14,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *DaycareServiceDetail) Reset() {
	*x = DaycareServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareServiceDetail) ProtoMessage() {}

func (x *DaycareServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareServiceDetail.ProtoReflect.Descriptor instead.
func (*DaycareServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{12}
}

func (x *DaycareServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *DaycareServiceDetail) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *DaycareServiceDetail) GetArrivalTime() int32 {
	if x != nil {
		return x.ArrivalTime
	}
	return 0
}

func (x *DaycareServiceDetail) GetPickupTime() int32 {
	if x != nil && x.PickupTime != nil {
		return *x.PickupTime
	}
	return 0
}

func (x *DaycareServiceDetail) GetAddons() []*DaycareAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *DaycareServiceDetail) GetFeedings() []*Feeding {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *DaycareServiceDetail) GetMedications() []*Medication {
	if x != nil {
		return x.Medications
	}
	return nil
}

// Evaluation service
type EvaluationServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Evaluation service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The date of the evaluation, in the format of "yyyy-MM-dd"
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// Minutes since midnight
	Time int32 `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *EvaluationServiceDetail) Reset() {
	*x = EvaluationServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceDetail) ProtoMessage() {}

func (x *EvaluationServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceDetail.ProtoReflect.Descriptor instead.
func (*EvaluationServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{13}
}

func (x *EvaluationServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *EvaluationServiceDetail) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *EvaluationServiceDetail) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

// Dog walking service
type DogWalkingServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dog walking service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The date of the evaluation, in the format of "yyyy-MM-dd"
	Date string `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// Minutes since midnight
	Time int32 `protobuf:"varint,5,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *DogWalkingServiceDetail) Reset() {
	*x = DogWalkingServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DogWalkingServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DogWalkingServiceDetail) ProtoMessage() {}

func (x *DogWalkingServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DogWalkingServiceDetail.ProtoReflect.Descriptor instead.
func (*DogWalkingServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{14}
}

func (x *DogWalkingServiceDetail) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *DogWalkingServiceDetail) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DogWalkingServiceDetail) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

// Group class service
type GroupClassServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group class instance id
	GroupClassInstanceId int64 `protobuf:"varint,1,opt,name=group_class_instance_id,json=groupClassInstanceId,proto3" json:"group_class_instance_id,omitempty"`
	// The trainer id, same to the staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The dates of each group session
	Dates []string `protobuf:"bytes,3,rep,name=dates,proto3" json:"dates,omitempty"`
	// The start time of per group class session
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end time of per group class session
	EndTime int32 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *GroupClassServiceDetail) Reset() {
	*x = GroupClassServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassServiceDetail) ProtoMessage() {}

func (x *GroupClassServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassServiceDetail.ProtoReflect.Descriptor instead.
func (*GroupClassServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{15}
}

func (x *GroupClassServiceDetail) GetGroupClassInstanceId() int64 {
	if x != nil {
		return x.GroupClassInstanceId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroupClassServiceDetail) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *GroupClassServiceDetail) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroupClassServiceDetail) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// OB request service
type ServiceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	//
	// Types that are assignable to Service:
	//
	//	*ServiceDetail_Grooming
	//	*ServiceDetail_Boarding
	//	*ServiceDetail_Daycare
	//	*ServiceDetail_Evaluation
	//	*ServiceDetail_DogWalking
	//	*ServiceDetail_GroupClass
	Service isServiceDetail_Service `protobuf_oneof:"service"`
}

func (x *ServiceDetail) Reset() {
	*x = ServiceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail) ProtoMessage() {}

func (x *ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail.ProtoReflect.Descriptor instead.
func (*ServiceDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{16}
}

func (m *ServiceDetail) GetService() isServiceDetail_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *ServiceDetail) GetGrooming() *GroomingServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_Grooming); ok {
		return x.Grooming
	}
	return nil
}

func (x *ServiceDetail) GetBoarding() *BoardingServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *ServiceDetail) GetDaycare() *DaycareServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_Daycare); ok {
		return x.Daycare
	}
	return nil
}

func (x *ServiceDetail) GetEvaluation() *EvaluationServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_Evaluation); ok {
		return x.Evaluation
	}
	return nil
}

func (x *ServiceDetail) GetDogWalking() *DogWalkingServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_DogWalking); ok {
		return x.DogWalking
	}
	return nil
}

func (x *ServiceDetail) GetGroupClass() *GroupClassServiceDetail {
	if x, ok := x.GetService().(*ServiceDetail_GroupClass); ok {
		return x.GroupClass
	}
	return nil
}

type isServiceDetail_Service interface {
	isServiceDetail_Service()
}

type ServiceDetail_Grooming struct {
	// Grooming service
	Grooming *GroomingServiceDetail `protobuf:"bytes,1,opt,name=grooming,proto3,oneof"`
}

type ServiceDetail_Boarding struct {
	// Boarding service
	Boarding *BoardingServiceDetail `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type ServiceDetail_Daycare struct {
	// Daycare service
	Daycare *DaycareServiceDetail `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"`
}

type ServiceDetail_Evaluation struct {
	// Evaluation service
	Evaluation *EvaluationServiceDetail `protobuf:"bytes,4,opt,name=evaluation,proto3,oneof"`
}

type ServiceDetail_DogWalking struct {
	// Dog walking service
	DogWalking *DogWalkingServiceDetail `protobuf:"bytes,5,opt,name=dog_walking,json=dogWalking,proto3,oneof"`
}

type ServiceDetail_GroupClass struct {
	// Group class service
	GroupClass *GroupClassServiceDetail `protobuf:"bytes,6,opt,name=group_class,json=groupClass,proto3,oneof"`
}

func (*ServiceDetail_Grooming) isServiceDetail_Service() {}

func (*ServiceDetail_Boarding) isServiceDetail_Service() {}

func (*ServiceDetail_Daycare) isServiceDetail_Service() {}

func (*ServiceDetail_Evaluation) isServiceDetail_Service() {}

func (*ServiceDetail_DogWalking) isServiceDetail_Service() {}

func (*ServiceDetail_GroupClass) isServiceDetail_Service() {}

// Pet service details
type PetServiceDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet id
	Pet *Pet `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// Is new pet
	IsNewPet bool `protobuf:"varint,2,opt,name=is_new_pet,json=isNewPet,proto3" json:"is_new_pet,omitempty"`
	// Service id
	ServiceDetails []*ServiceDetail `protobuf:"bytes,3,rep,name=service_details,json=serviceDetails,proto3" json:"service_details,omitempty"`
}

func (x *PetServiceDetails) Reset() {
	*x = PetServiceDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetServiceDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetServiceDetails) ProtoMessage() {}

func (x *PetServiceDetails) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetServiceDetails.ProtoReflect.Descriptor instead.
func (*PetServiceDetails) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{17}
}

func (x *PetServiceDetails) GetPet() *Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *PetServiceDetails) GetIsNewPet() bool {
	if x != nil {
		return x.IsNewPet
	}
	return false
}

func (x *PetServiceDetails) GetServiceDetails() []*ServiceDetail {
	if x != nil {
		return x.ServiceDetails
	}
	return nil
}

// OB request vaccine params
type Pet_Vaccine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Vaccine binding id
	VaccineBindingId *int64 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3,oneof" json:"vaccine_binding_id,omitempty"`
	// Vaccine name
	Type *int32 `protobuf:"varint,2,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// Vaccine id
	VaccineId *int32 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3,oneof" json:"vaccine_id,omitempty"`
	// Vaccine name
	ExpirationDate *string `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// Vaccine document
	VaccineDocument *string `protobuf:"bytes,5,opt,name=vaccine_document,json=vaccineDocument,proto3,oneof" json:"vaccine_document,omitempty"`
	// Document urls
	DocumentUrls []string `protobuf:"bytes,6,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *Pet_Vaccine) Reset() {
	*x = Pet_Vaccine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet_Vaccine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet_Vaccine) ProtoMessage() {}

func (x *Pet_Vaccine) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet_Vaccine.ProtoReflect.Descriptor instead.
func (*Pet_Vaccine) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Pet_Vaccine) GetVaccineBindingId() int64 {
	if x != nil && x.VaccineBindingId != nil {
		return *x.VaccineBindingId
	}
	return 0
}

func (x *Pet_Vaccine) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Pet_Vaccine) GetVaccineId() int32 {
	if x != nil && x.VaccineId != nil {
		return *x.VaccineId
	}
	return 0
}

func (x *Pet_Vaccine) GetExpirationDate() string {
	if x != nil && x.ExpirationDate != nil {
		return *x.ExpirationDate
	}
	return ""
}

func (x *Pet_Vaccine) GetVaccineDocument() string {
	if x != nil && x.VaccineDocument != nil {
		return *x.VaccineDocument
	}
	return ""
}

func (x *Pet_Vaccine) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

var File_moego_models_online_booking_v1_booking_request_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_booking_request_defs_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed, 0x03, 0x0a, 0x11, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x66, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x33, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72,
	0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28,
	0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x50, 0x61, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xd0, 0x0f, 0x48, 0x01, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x71, 0x0a,
	0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x62, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x54, 0x6f,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x0d,
	0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xff, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x1a, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x17, 0x66, 0x72, 0x6f, 0x6d, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x18, 0x74, 0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01,
	0x52, 0x15, 0x74, 0x6f, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x74, 0x6f,
	0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x9b, 0x0f, 0x0a, 0x03, 0x50, 0x65, 0x74, 0x12, 0x23,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff,
	0x01, 0x48, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2e, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x02,
	0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12,
	0x23, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69,
	0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00,
	0x48, 0x04, 0x52, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x69, 0x78, 0x88, 0x01, 0x01, 0x12,
	0x2c, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x05, 0x52,
	0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0xd0, 0x01, 0x01, 0x48, 0x07, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x08, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x05, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x09, 0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x29, 0x0a, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0a, 0x52, 0x08, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0b, 0x68, 0x61,
	0x69, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0b, 0x52, 0x0a, 0x68, 0x61, 0x69,
	0x72, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x13, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00,
	0x48, 0x0c, 0x52, 0x12, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x08, 0x76, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x09, 0x76, 0x65, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01,
	0x48, 0x0e, 0x52, 0x08, 0x76, 0x65, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x24, 0x0a, 0x0b, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x0a, 0x76, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48,
	0x10, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x17, 0x65, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x11, 0x52, 0x15, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x32, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0x80, 0x10, 0x48, 0x12, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0x80, 0x10, 0x48, 0x13, 0x52, 0x08, 0x70, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x6d, 0x0a, 0x14, 0x70, 0x65, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x70, 0x65,
	0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73,
	0x1a, 0x5d, 0x0a, 0x17, 0x50, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x8e, 0x03, 0x0a, 0x07, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x3a, 0x0a, 0x12, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x01,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0xd0, 0x01, 0x01, 0x48, 0x03,
	0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52,
	0x0f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x68,
	0x61, 0x69, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x19, 0x0a,
	0x17, 0x5f, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x6d, 0x65,
	0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x69, 0x73, 0x73, 0x75, 0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x22, 0xab, 0x03, 0x0a, 0x07, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x50, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x09, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xff, 0x01, 0x48, 0x01, 0x52, 0x08, 0x66, 0x6f, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x2e, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01,
	0x48, 0x02, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10,
	0x48, 0x03, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x04, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x05, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x66,
	0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e,
	0x6f, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x72, 0x22, 0xc8, 0x02, 0x0a, 0x0a, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x04, 0x75, 0x6e, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x48, 0x00, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0f,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48,
	0x01, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x02, 0x52,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0a, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x22, 0xd2, 0x02,
	0x0a, 0x0d, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22,
	0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65,
	0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x48, 0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x0c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c,
	0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52,
	0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88,
	0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x79, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x22, 0x28, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x22, 0xde, 0x01, 0x0a, 0x15, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32,
	0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a,
	0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x22, 0x9f, 0x04, 0x0a, 0x15, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e,
	0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d,
	0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00,
	0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34,
	0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0,
	0x0b, 0x28, 0x00, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x45, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x06,
	0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0b, 0x6d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xc8, 0x03, 0x0a, 0x14, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72,
	0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52,
	0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a,
	0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x22, 0x9a, 0x01, 0x0a, 0x17, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xbe, 0x01,
	0x0a, 0x17, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x93,
	0x02, 0x0a, 0x17, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3e, 0x0a, 0x17, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x42,
	0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x2c, 0xfa,
	0x42, 0x29, 0x92, 0x01, 0x26, 0x08, 0x01, 0x10, 0x64, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e,
	0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32,
	0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b,
	0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xae, 0x04, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x53, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48,
	0x00, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x53, 0x0a, 0x08, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x50, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x12, 0x59, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48,
	0x00, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a,
	0x0b, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x64,
	0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x5a, 0x0a, 0x0b, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x0e, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xca, 0x01, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x03, 0x70,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x70, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x50, 0x65, 0x74, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescData = file_moego_models_online_booking_v1_booking_request_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_booking_request_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_moego_models_online_booking_v1_booking_request_defs_proto_goTypes = []interface{}{
	(*BookingRequestDef)(nil),                  // 0: moego.models.online_booking.v1.BookingRequestDef
	(*PetToLodgingDef)(nil),                    // 1: moego.models.online_booking.v1.PetToLodgingDef
	(*PetToStaffDef)(nil),                      // 2: moego.models.online_booking.v1.PetToStaffDef
	(*PetToServiceDef)(nil),                    // 3: moego.models.online_booking.v1.PetToServiceDef
	(*Pet)(nil),                                // 4: moego.models.online_booking.v1.Pet
	(*Feeding)(nil),                            // 5: moego.models.online_booking.v1.Feeding
	(*Medication)(nil),                         // 6: moego.models.online_booking.v1.Medication
	(*BoardingAddon)(nil),                      // 7: moego.models.online_booking.v1.BoardingAddon
	(*DaycareAddon)(nil),                       // 8: moego.models.online_booking.v1.DaycareAddon
	(*GroomingAddon)(nil),                      // 9: moego.models.online_booking.v1.GroomingAddon
	(*GroomingServiceDetail)(nil),              // 10: moego.models.online_booking.v1.GroomingServiceDetail
	(*BoardingServiceDetail)(nil),              // 11: moego.models.online_booking.v1.BoardingServiceDetail
	(*DaycareServiceDetail)(nil),               // 12: moego.models.online_booking.v1.DaycareServiceDetail
	(*EvaluationServiceDetail)(nil),            // 13: moego.models.online_booking.v1.EvaluationServiceDetail
	(*DogWalkingServiceDetail)(nil),            // 14: moego.models.online_booking.v1.DogWalkingServiceDetail
	(*GroupClassServiceDetail)(nil),            // 15: moego.models.online_booking.v1.GroupClassServiceDetail
	(*ServiceDetail)(nil),                      // 16: moego.models.online_booking.v1.ServiceDetail
	(*PetServiceDetails)(nil),                  // 17: moego.models.online_booking.v1.PetServiceDetails
	nil,                                        // 18: moego.models.online_booking.v1.Pet.PetQuestionAnswersEntry
	(*Pet_Vaccine)(nil),                        // 19: moego.models.online_booking.v1.Pet.Vaccine
	(BookingRequestSourcePlatform)(0),          // 20: moego.models.online_booking.v1.BookingRequestSourcePlatform
	(*FeedingModel_FeedingSchedule)(nil),       // 21: moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	(*MedicationModel_MedicationSchedule)(nil), // 22: moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	(v1.PetDetailDateType)(0),                  // 23: moego.models.appointment.v1.PetDetailDateType
	(*date.Date)(nil),                          // 24: google.type.Date
	(*structpb.Value)(nil),                     // 25: google.protobuf.Value
}
var file_moego_models_online_booking_v1_booking_request_defs_proto_depIdxs = []int32{
	20, // 0: moego.models.online_booking.v1.BookingRequestDef.source_platform:type_name -> moego.models.online_booking.v1.BookingRequestSourcePlatform
	19, // 1: moego.models.online_booking.v1.Pet.vaccine_list:type_name -> moego.models.online_booking.v1.Pet.Vaccine
	18, // 2: moego.models.online_booking.v1.Pet.pet_question_answers:type_name -> moego.models.online_booking.v1.Pet.PetQuestionAnswersEntry
	21, // 3: moego.models.online_booking.v1.Feeding.time:type_name -> moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	22, // 4: moego.models.online_booking.v1.Medication.time:type_name -> moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	23, // 5: moego.models.online_booking.v1.BoardingAddon.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	24, // 6: moego.models.online_booking.v1.BoardingAddon.start_date:type_name -> google.type.Date
	9,  // 7: moego.models.online_booking.v1.GroomingServiceDetail.addons:type_name -> moego.models.online_booking.v1.GroomingAddon
	7,  // 8: moego.models.online_booking.v1.BoardingServiceDetail.addons:type_name -> moego.models.online_booking.v1.BoardingAddon
	5,  // 9: moego.models.online_booking.v1.BoardingServiceDetail.feedings:type_name -> moego.models.online_booking.v1.Feeding
	6,  // 10: moego.models.online_booking.v1.BoardingServiceDetail.medications:type_name -> moego.models.online_booking.v1.Medication
	8,  // 11: moego.models.online_booking.v1.DaycareServiceDetail.addons:type_name -> moego.models.online_booking.v1.DaycareAddon
	5,  // 12: moego.models.online_booking.v1.DaycareServiceDetail.feedings:type_name -> moego.models.online_booking.v1.Feeding
	6,  // 13: moego.models.online_booking.v1.DaycareServiceDetail.medications:type_name -> moego.models.online_booking.v1.Medication
	10, // 14: moego.models.online_booking.v1.ServiceDetail.grooming:type_name -> moego.models.online_booking.v1.GroomingServiceDetail
	11, // 15: moego.models.online_booking.v1.ServiceDetail.boarding:type_name -> moego.models.online_booking.v1.BoardingServiceDetail
	12, // 16: moego.models.online_booking.v1.ServiceDetail.daycare:type_name -> moego.models.online_booking.v1.DaycareServiceDetail
	13, // 17: moego.models.online_booking.v1.ServiceDetail.evaluation:type_name -> moego.models.online_booking.v1.EvaluationServiceDetail
	14, // 18: moego.models.online_booking.v1.ServiceDetail.dog_walking:type_name -> moego.models.online_booking.v1.DogWalkingServiceDetail
	15, // 19: moego.models.online_booking.v1.ServiceDetail.group_class:type_name -> moego.models.online_booking.v1.GroupClassServiceDetail
	4,  // 20: moego.models.online_booking.v1.PetServiceDetails.pet:type_name -> moego.models.online_booking.v1.Pet
	16, // 21: moego.models.online_booking.v1.PetServiceDetails.service_details:type_name -> moego.models.online_booking.v1.ServiceDetail
	25, // 22: moego.models.online_booking.v1.Pet.PetQuestionAnswersEntry.value:type_name -> google.protobuf.Value
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_booking_request_defs_proto_init() }
func file_moego_models_online_booking_v1_booking_request_defs_proto_init() {
	if File_moego_models_online_booking_v1_booking_request_defs_proto != nil {
		return
	}
	file_moego_models_online_booking_v1_booking_request_enums_proto_init()
	file_moego_models_online_booking_v1_feeding_models_proto_init()
	file_moego_models_online_booking_v1_medication_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingRequestDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToLodgingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToStaffDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetToServiceDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feeding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Medication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DogWalkingServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetServiceDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet_Vaccine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*ServiceDetail_Grooming)(nil),
		(*ServiceDetail_Boarding)(nil),
		(*ServiceDetail_Daycare)(nil),
		(*ServiceDetail_Evaluation)(nil),
		(*ServiceDetail_DogWalking)(nil),
		(*ServiceDetail_GroupClass)(nil),
	}
	file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes[19].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_booking_request_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_booking_request_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_booking_request_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_booking_request_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_booking_request_defs_proto = out.File
	file_moego_models_online_booking_v1_booking_request_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_booking_request_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_booking_request_defs_proto_depIdxs = nil
}
