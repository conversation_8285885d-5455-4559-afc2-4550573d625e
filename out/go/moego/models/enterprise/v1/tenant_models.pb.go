// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/tenant_models.proto

package enterprisepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum for tenant status
type TenantModel_Status int32

const (
	// UNSPECIFIED is the default value
	TenantModel_TENANT_STATUS_UNSPECIFIED TenantModel_Status = 0
	// 实例化
	TenantModel_INSTANTIATED TenantModel_Status = 1
	// 激活
	TenantModel_LIVE TenantModel_Status = 2
	// 删除
	TenantModel_DELETED TenantModel_Status = 3
)

// Enum value maps for TenantModel_Status.
var (
	TenantModel_Status_name = map[int32]string{
		0: "TENANT_STATUS_UNSPECIFIED",
		1: "INSTANTIATED",
		2: "LIVE",
		3: "DELETED",
	}
	TenantModel_Status_value = map[string]int32{
		"TENANT_STATUS_UNSPECIFIED": 0,
		"INSTANTIATED":              1,
		"LIVE":                      2,
		"DELETED":                   3,
	}
)

func (x TenantModel_Status) Enum() *TenantModel_Status {
	p := new(TenantModel_Status)
	*p = x
	return p
}

func (x TenantModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[0].Descriptor()
}

func (TenantModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[0]
}

func (x TenantModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantModel_Status.Descriptor instead.
func (TenantModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{1, 0}
}

// enum for own type
type TenantModel_OwnType int32

const (
	// UNSPECIFIED is the default value
	TenantModel_OWN_TYPE_UNSPECIFIED TenantModel_OwnType = 0
	// 加盟商
	TenantModel_CUSTOMIZATION TenantModel_OwnType = 1
	// enterprise 企业
	TenantModel_ENTERPRISE TenantModel_OwnType = 2
)

// Enum value maps for TenantModel_OwnType.
var (
	TenantModel_OwnType_name = map[int32]string{
		0: "OWN_TYPE_UNSPECIFIED",
		1: "CUSTOMIZATION",
		2: "ENTERPRISE",
	}
	TenantModel_OwnType_value = map[string]int32{
		"OWN_TYPE_UNSPECIFIED": 0,
		"CUSTOMIZATION":        1,
		"ENTERPRISE":           2,
	}
)

func (x TenantModel_OwnType) Enum() *TenantModel_OwnType {
	p := new(TenantModel_OwnType)
	*p = x
	return p
}

func (x TenantModel_OwnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantModel_OwnType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[1].Descriptor()
}

func (TenantModel_OwnType) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[1]
}

func (x TenantModel_OwnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantModel_OwnType.Descriptor instead.
func (TenantModel_OwnType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{1, 1}
}

// type
type TenantModel_Type int32

const (
	// UNSPECIFIED is the default value
	TenantModel_TYPE_UNSPECIFIED TenantModel_Type = 0
	// normal tenant
	TenantModel_NORMAL TenantModel_Type = 1
	// training tenant
	TenantModel_TRAINING TenantModel_Type = 2
	// template tenant
	TenantModel_TEMPLATE TenantModel_Type = 3
)

// Enum value maps for TenantModel_Type.
var (
	TenantModel_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "NORMAL",
		2: "TRAINING",
		3: "TEMPLATE",
	}
	TenantModel_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"NORMAL":           1,
		"TRAINING":         2,
		"TEMPLATE":         3,
	}
)

func (x TenantModel_Type) Enum() *TenantModel_Type {
	p := new(TenantModel_Type)
	*p = x
	return p
}

func (x TenantModel_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantModel_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[2].Descriptor()
}

func (TenantModel_Type) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[2]
}

func (x TenantModel_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantModel_Type.Descriptor instead.
func (TenantModel_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{1, 2}
}

// type
type TenantObject_Type int32

const (
	// unspecified
	TenantObject_TYPE_UNSPECIFIED TenantObject_Type = 0
	// all
	TenantObject_ALL TenantObject_Type = 1
	// tenant
	TenantObject_TENANT TenantObject_Type = 2
	// tenant group
	TenantObject_TENANT_GROUP TenantObject_Type = 3
)

// Enum value maps for TenantObject_Type.
var (
	TenantObject_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "ALL",
		2: "TENANT",
		3: "TENANT_GROUP",
	}
	TenantObject_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"ALL":              1,
		"TENANT":           2,
		"TENANT_GROUP":     3,
	}
)

func (x TenantObject_Type) Enum() *TenantObject_Type {
	p := new(TenantObject_Type)
	*p = x
	return p
}

func (x TenantObject_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantObject_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[3].Descriptor()
}

func (TenantObject_Type) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_models_proto_enumTypes[3]
}

func (x TenantObject_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantObject_Type.Descriptor instead.
func (TenantObject_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{2, 0}
}

// tenant view
type TenantView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TenantView) Reset() {
	*x = TenantView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantView) ProtoMessage() {}

func (x *TenantView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantView.ProtoReflect.Descriptor instead.
func (*TenantView) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{0}
}

func (x *TenantView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// tenant
type TenantModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// company id
	RelatedCompanyId int64 `protobuf:"varint,3,opt,name=related_company_id,json=relatedCompanyId,proto3" json:"related_company_id,omitempty"`
	// van num
	VanNum int32 `protobuf:"varint,4,opt,name=van_num,json=vanNum,proto3" json:"van_num,omitempty"`
	// location num
	LocationNum int32 `protobuf:"varint,5,opt,name=location_num,json=locationNum,proto3" json:"location_num,omitempty"`
	// territory id
	TerritoryId int64 `protobuf:"varint,6,opt,name=territory_id,json=territoryId,proto3" json:"territory_id,omitempty"`
	// template id
	TemplateId int64 `protobuf:"varint,7,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// name
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,9,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// status
	Status TenantModel_Status `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.enterprise.v1.TenantModel_Status" json:"status,omitempty"`
	// own type
	OwnType TenantModel_OwnType `protobuf:"varint,11,opt,name=own_type,json=ownType,proto3,enum=moego.models.enterprise.v1.TenantModel_OwnType" json:"own_type,omitempty"`
	// joined time
	JoinedTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=joined_time,json=joinedTime,proto3" json:"joined_time,omitempty"`
	// state
	State string `protobuf:"bytes,13,opt,name=state,proto3" json:"state,omitempty"`
	// city
	City string `protobuf:"bytes,14,opt,name=city,proto3" json:"city,omitempty"`
	// type
	Type TenantModel_Type `protobuf:"varint,15,opt,name=type,proto3,enum=moego.models.enterprise.v1.TenantModel_Type" json:"type,omitempty"`
}

func (x *TenantModel) Reset() {
	*x = TenantModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantModel) ProtoMessage() {}

func (x *TenantModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantModel.ProtoReflect.Descriptor instead.
func (*TenantModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{1}
}

func (x *TenantModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantModel) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *TenantModel) GetRelatedCompanyId() int64 {
	if x != nil {
		return x.RelatedCompanyId
	}
	return 0
}

func (x *TenantModel) GetVanNum() int32 {
	if x != nil {
		return x.VanNum
	}
	return 0
}

func (x *TenantModel) GetLocationNum() int32 {
	if x != nil {
		return x.LocationNum
	}
	return 0
}

func (x *TenantModel) GetTerritoryId() int64 {
	if x != nil {
		return x.TerritoryId
	}
	return 0
}

func (x *TenantModel) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *TenantModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TenantModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *TenantModel) GetStatus() TenantModel_Status {
	if x != nil {
		return x.Status
	}
	return TenantModel_TENANT_STATUS_UNSPECIFIED
}

func (x *TenantModel) GetOwnType() TenantModel_OwnType {
	if x != nil {
		return x.OwnType
	}
	return TenantModel_OWN_TYPE_UNSPECIFIED
}

func (x *TenantModel) GetJoinedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedTime
	}
	return nil
}

func (x *TenantModel) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *TenantModel) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *TenantModel) GetType() TenantModel_Type {
	if x != nil {
		return x.Type
	}
	return TenantModel_TYPE_UNSPECIFIED
}

// TenantObject
// 作为 Enterprise Hub 中对 Tenant 和 Tenant Group 的统一抽象
type TenantObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// type
	Type TenantObject_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.enterprise.v1.TenantObject_Type" json:"type,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TenantObject) Reset() {
	*x = TenantObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantObject) ProtoMessage() {}

func (x *TenantObject) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantObject.ProtoReflect.Descriptor instead.
func (*TenantObject) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP(), []int{2}
}

func (x *TenantObject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantObject) GetType() TenantObject_Type {
	if x != nil {
		return x.Type
	}
	return TenantObject_TYPE_UNSPECIFIED
}

func (x *TenantObject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_moego_models_enterprise_v1_tenant_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_tenant_models_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a,
	0x0a, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0xc0, 0x06, 0x0a, 0x0b, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x76, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x65, 0x72, 0x72, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x65, 0x72, 0x72, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a,
	0x08, 0x6f, 0x77, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x6a, 0x6f, 0x69,
	0x6e, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x6a, 0x6f, 0x69, 0x6e,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x40, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x50, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19,
	0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x49,
	0x4e, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x4c, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x10, 0x03, 0x22, 0x46, 0x0a, 0x07, 0x4f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x10, 0x02, 0x22, 0x44, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f,
	0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45,
	0x10, 0x03, 0x22, 0xba, 0x01, 0x0a, 0x0c, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x0c, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x03, 0x42,
	0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_tenant_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_tenant_models_proto_rawDescData = file_moego_models_enterprise_v1_tenant_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_tenant_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_tenant_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_tenant_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_tenant_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_tenant_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_tenant_models_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_enterprise_v1_tenant_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_enterprise_v1_tenant_models_proto_goTypes = []interface{}{
	(TenantModel_Status)(0),       // 0: moego.models.enterprise.v1.TenantModel.Status
	(TenantModel_OwnType)(0),      // 1: moego.models.enterprise.v1.TenantModel.OwnType
	(TenantModel_Type)(0),         // 2: moego.models.enterprise.v1.TenantModel.Type
	(TenantObject_Type)(0),        // 3: moego.models.enterprise.v1.TenantObject.Type
	(*TenantView)(nil),            // 4: moego.models.enterprise.v1.TenantView
	(*TenantModel)(nil),           // 5: moego.models.enterprise.v1.TenantModel
	(*TenantObject)(nil),          // 6: moego.models.enterprise.v1.TenantObject
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
}
var file_moego_models_enterprise_v1_tenant_models_proto_depIdxs = []int32{
	0, // 0: moego.models.enterprise.v1.TenantModel.status:type_name -> moego.models.enterprise.v1.TenantModel.Status
	1, // 1: moego.models.enterprise.v1.TenantModel.own_type:type_name -> moego.models.enterprise.v1.TenantModel.OwnType
	7, // 2: moego.models.enterprise.v1.TenantModel.joined_time:type_name -> google.protobuf.Timestamp
	2, // 3: moego.models.enterprise.v1.TenantModel.type:type_name -> moego.models.enterprise.v1.TenantModel.Type
	3, // 4: moego.models.enterprise.v1.TenantObject.type:type_name -> moego.models.enterprise.v1.TenantObject.Type
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_tenant_models_proto_init() }
func file_moego_models_enterprise_v1_tenant_models_proto_init() {
	if File_moego_models_enterprise_v1_tenant_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_tenant_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_tenant_models_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_tenant_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_tenant_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_tenant_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_tenant_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_tenant_models_proto = out.File
	file_moego_models_enterprise_v1_tenant_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_tenant_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_tenant_models_proto_depIdxs = nil
}
