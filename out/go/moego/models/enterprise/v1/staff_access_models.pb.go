// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/staff_access_models.proto

package enterprisepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// staff access relation
type StaffAccess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// resource
	Resource *TenantObject `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
}

func (x *StaffAccess) Reset() {
	*x = StaffAccess{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_staff_access_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAccess) ProtoMessage() {}

func (x *StaffAccess) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_staff_access_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAccess.ProtoReflect.Descriptor instead.
func (*StaffAccess) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_staff_access_models_proto_rawDescGZIP(), []int{0}
}

func (x *StaffAccess) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StaffAccess) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffAccess) GetResource() *TenantObject {
	if x != nil {
		return x.Resource
	}
	return nil
}

var File_moego_models_enterprise_v1_staff_access_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_staff_access_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x7e, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_enterprise_v1_staff_access_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_staff_access_models_proto_rawDescData = file_moego_models_enterprise_v1_staff_access_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_staff_access_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_staff_access_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_staff_access_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_staff_access_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_staff_access_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_staff_access_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_enterprise_v1_staff_access_models_proto_goTypes = []interface{}{
	(*StaffAccess)(nil),  // 0: moego.models.enterprise.v1.StaffAccess
	(*TenantObject)(nil), // 1: moego.models.enterprise.v1.TenantObject
}
var file_moego_models_enterprise_v1_staff_access_models_proto_depIdxs = []int32{
	1, // 0: moego.models.enterprise.v1.StaffAccess.resource:type_name -> moego.models.enterprise.v1.TenantObject
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_staff_access_models_proto_init() }
func file_moego_models_enterprise_v1_staff_access_models_proto_init() {
	if File_moego_models_enterprise_v1_staff_access_models_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_tenant_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_staff_access_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAccess); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_staff_access_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_staff_access_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_staff_access_models_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_staff_access_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_staff_access_models_proto = out.File
	file_moego_models_enterprise_v1_staff_access_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_staff_access_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_staff_access_models_proto_depIdxs = nil
}
