// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/report_def.proto

package reportingpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Scope type enumeration
type ScopeFilter_ScopeType int32

const (
	// Unspecified scope type
	ScopeFilter_SCOPE_TYPE_UNSPECIFIED ScopeFilter_ScopeType = 0
	// Business scope
	ScopeFilter_BUSINESS ScopeFilter_ScopeType = 1
	// Tenant scope
	ScopeFilter_TENANT ScopeFilter_ScopeType = 2
)

// Enum value maps for ScopeFilter_ScopeType.
var (
	ScopeFilter_ScopeType_name = map[int32]string{
		0: "SCOPE_TYPE_UNSPECIFIED",
		1: "BUSINESS",
		2: "TENANT",
	}
	ScopeFilter_ScopeType_value = map[string]int32{
		"SCOPE_TYPE_UNSPECIFIED": 0,
		"BUSINESS":               1,
		"TENANT":                 2,
	}
)

func (x ScopeFilter_ScopeType) Enum() *ScopeFilter_ScopeType {
	p := new(ScopeFilter_ScopeType)
	*p = x
	return p
}

func (x ScopeFilter_ScopeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScopeFilter_ScopeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_report_def_proto_enumTypes[0].Descriptor()
}

func (ScopeFilter_ScopeType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_report_def_proto_enumTypes[0]
}

func (x ScopeFilter_ScopeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScopeFilter_ScopeType.Descriptor instead.
func (ScopeFilter_ScopeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{0, 0}
}

// Scope filter definition: business or tenant
type ScopeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// scope ids, could be business_ids or tenant_ids
	ScopeIds []uint64 `protobuf:"varint,1,rep,packed,name=scope_ids,json=scopeIds,proto3" json:"scope_ids,omitempty"`
	// if query all scopes
	AllScopes bool `protobuf:"varint,2,opt,name=all_scopes,json=allScopes,proto3" json:"all_scopes,omitempty"`
	// scope type, will set in api layer, currently can ignore in front-end
	ScopeType ScopeFilter_ScopeType `protobuf:"varint,3,opt,name=scope_type,json=scopeType,proto3,enum=moego.models.reporting.v2.ScopeFilter_ScopeType" json:"scope_type,omitempty"`
	// scope parent id: company_id or enterprise_id, will set in api layer, currently can ignore in front-end
	ScopeParentId *uint64 `protobuf:"varint,4,opt,name=scope_parent_id,json=scopeParentId,proto3,oneof" json:"scope_parent_id,omitempty"`
}

func (x *ScopeFilter) Reset() {
	*x = ScopeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScopeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScopeFilter) ProtoMessage() {}

func (x *ScopeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScopeFilter.ProtoReflect.Descriptor instead.
func (*ScopeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{0}
}

func (x *ScopeFilter) GetScopeIds() []uint64 {
	if x != nil {
		return x.ScopeIds
	}
	return nil
}

func (x *ScopeFilter) GetAllScopes() bool {
	if x != nil {
		return x.AllScopes
	}
	return false
}

func (x *ScopeFilter) GetScopeType() ScopeFilter_ScopeType {
	if x != nil {
		return x.ScopeType
	}
	return ScopeFilter_SCOPE_TYPE_UNSPECIFIED
}

func (x *ScopeFilter) GetScopeParentId() uint64 {
	if x != nil && x.ScopeParentId != nil {
		return *x.ScopeParentId
	}
	return 0
}

// Time filter definition
type TimeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field key
	FieldKey string `protobuf:"bytes,1,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// current period
	CurrentPeriod *interval.Interval `protobuf:"bytes,2,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous period
	PreviousPeriod *interval.Interval `protobuf:"bytes,3,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
}

func (x *TimeFilter) Reset() {
	*x = TimeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeFilter) ProtoMessage() {}

func (x *TimeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeFilter.ProtoReflect.Descriptor instead.
func (*TimeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{1}
}

func (x *TimeFilter) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *TimeFilter) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *TimeFilter) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

// Dimension request definition
type DimensionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// field key
	FieldKey string `protobuf:"bytes,1,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// order by asc or desc
	Asc *bool `protobuf:"varint,2,opt,name=asc,proto3,oneof" json:"asc,omitempty"`
}

func (x *DimensionRequest) Reset() {
	*x = DimensionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DimensionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DimensionRequest) ProtoMessage() {}

func (x *DimensionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DimensionRequest.ProtoReflect.Descriptor instead.
func (*DimensionRequest) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{2}
}

func (x *DimensionRequest) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *DimensionRequest) GetAsc() bool {
	if x != nil && x.Asc != nil {
		return *x.Asc
	}
	return false
}

// Dimension filter definition
type DimensionFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group by fields
	Dimensions []*DimensionRequest `protobuf:"bytes,1,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
	// expanded dimension config, from last api call
	DimensionConfig *DimensionConfig `protobuf:"bytes,2,opt,name=dimension_config,json=dimensionConfig,proto3,oneof" json:"dimension_config,omitempty"`
}

func (x *DimensionFilter) Reset() {
	*x = DimensionFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DimensionFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DimensionFilter) ProtoMessage() {}

func (x *DimensionFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DimensionFilter.ProtoReflect.Descriptor instead.
func (*DimensionFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{3}
}

func (x *DimensionFilter) GetDimensions() []*DimensionRequest {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *DimensionFilter) GetDimensionConfig() *DimensionConfig {
	if x != nil {
		return x.DimensionConfig
	}
	return nil
}

// Dimension config
type DimensionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fields of dimension
	Dimensions []*DimensionRequest `protobuf:"bytes,1,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
	// if can expand for deeper dimension
	IsExpandable bool `protobuf:"varint,2,opt,name=is_expandable,json=isExpandable,proto3" json:"is_expandable,omitempty"`
	// filters for current dimension
	Filters []*FilterRequest `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *DimensionConfig) Reset() {
	*x = DimensionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DimensionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DimensionConfig) ProtoMessage() {}

func (x *DimensionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DimensionConfig.ProtoReflect.Descriptor instead.
func (*DimensionConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{4}
}

func (x *DimensionConfig) GetDimensions() []*DimensionRequest {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *DimensionConfig) GetIsExpandable() bool {
	if x != nil {
		return x.IsExpandable
	}
	return false
}

func (x *DimensionConfig) GetFilters() []*FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Fetch data response definition
type FetchDataDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rows of fetch data result
	Rows []*RowDataDef `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	// fields
	Fields []*Field `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	// pagination info
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// total info for current query
	Total *RowDataDef `protobuf:"bytes,4,opt,name=total,proto3,oneof" json:"total,omitempty"`
}

func (x *FetchDataDef) Reset() {
	*x = FetchDataDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataDef) ProtoMessage() {}

func (x *FetchDataDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataDef.ProtoReflect.Descriptor instead.
func (*FetchDataDef) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{5}
}

func (x *FetchDataDef) GetRows() []*RowDataDef {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *FetchDataDef) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *FetchDataDef) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchDataDef) GetTotal() *RowDataDef {
	if x != nil {
		return x.Total
	}
	return nil
}

// Row data of Fetch data response definition
type RowDataDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// row data
	Data map[string]*NumberData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// sub dimension data
	SubData *FetchDataDef `protobuf:"bytes,2,opt,name=sub_data,json=subData,proto3,oneof" json:"sub_data,omitempty"`
	// dimension config of current row
	DimensionConfig *DimensionConfig `protobuf:"bytes,3,opt,name=dimension_config,json=dimensionConfig,proto3,oneof" json:"dimension_config,omitempty"`
	// row unique id
	RowUuid string `protobuf:"bytes,4,opt,name=row_uuid,json=rowUuid,proto3" json:"row_uuid,omitempty"`
	// drill config of one row
	DrillConfig *DrillConfig `protobuf:"bytes,5,opt,name=drill_config,json=drillConfig,proto3,oneof" json:"drill_config,omitempty"`
}

func (x *RowDataDef) Reset() {
	*x = RowDataDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowDataDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowDataDef) ProtoMessage() {}

func (x *RowDataDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowDataDef.ProtoReflect.Descriptor instead.
func (*RowDataDef) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{6}
}

func (x *RowDataDef) GetData() map[string]*NumberData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *RowDataDef) GetSubData() *FetchDataDef {
	if x != nil {
		return x.SubData
	}
	return nil
}

func (x *RowDataDef) GetDimensionConfig() *DimensionConfig {
	if x != nil {
		return x.DimensionConfig
	}
	return nil
}

func (x *RowDataDef) GetRowUuid() string {
	if x != nil {
		return x.RowUuid
	}
	return ""
}

func (x *RowDataDef) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

// FetchDataParams
type FetchDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// query scope: business id or all businesses
	Scope *ScopeFilter `protobuf:"bytes,2,opt,name=scope,proto3,oneof" json:"scope,omitempty"`
	// query time
	TimeRange *TimeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3,oneof" json:"time_range,omitempty"`
	// pagination params
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// order by params
	OrderBys []*v2.OrderBy `protobuf:"bytes,5,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// filter params
	Filters []*FilterRequest `protobuf:"bytes,6,rep,name=filters,proto3" json:"filters,omitempty"`
	// dimension filter
	Dimension *DimensionFilter `protobuf:"bytes,7,opt,name=dimension,proto3,oneof" json:"dimension,omitempty"`
	// metrics field keys params
	MetricKeys []string `protobuf:"bytes,8,rep,name=metric_keys,json=metricKeys,proto3" json:"metric_keys,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode bool `protobuf:"varint,9,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3" json:"dynamic_column_mode,omitempty"`
}

func (x *FetchDataParams) Reset() {
	*x = FetchDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataParams) ProtoMessage() {}

func (x *FetchDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataParams.ProtoReflect.Descriptor instead.
func (*FetchDataParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{7}
}

func (x *FetchDataParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *FetchDataParams) GetScope() *ScopeFilter {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FetchDataParams) GetTimeRange() *TimeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *FetchDataParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchDataParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *FetchDataParams) GetFilters() []*FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FetchDataParams) GetDimension() *DimensionFilter {
	if x != nil {
		return x.Dimension
	}
	return nil
}

func (x *FetchDataParams) GetMetricKeys() []string {
	if x != nil {
		return x.MetricKeys
	}
	return nil
}

func (x *FetchDataParams) GetDynamicColumnMode() bool {
	if x != nil {
		return x.DynamicColumnMode
	}
	return false
}

// FetchDataResult
type FetchDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Data *FetchDataDef `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	// report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3,oneof" json:"last_synced_time,omitempty"`
}

func (x *FetchDataResult) Reset() {
	*x = FetchDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataResult) ProtoMessage() {}

func (x *FetchDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataResult.ProtoReflect.Descriptor instead.
func (*FetchDataResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{8}
}

func (x *FetchDataResult) GetData() *FetchDataDef {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FetchDataResult) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// ExportDataParams
type ExportDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// query scope: business id or all businesses
	Scope *ScopeFilter `protobuf:"bytes,2,opt,name=scope,proto3,oneof" json:"scope,omitempty"`
	// query time
	TimeRange *TimeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3,oneof" json:"time_range,omitempty"`
	// order by params
	OrderBys []*v2.OrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// filter params
	Filters []*FilterRequest `protobuf:"bytes,5,rep,name=filters,proto3" json:"filters,omitempty"`
	// dimension filter
	Dimension *DimensionFilter `protobuf:"bytes,6,opt,name=dimension,proto3,oneof" json:"dimension,omitempty"`
}

func (x *ExportDataParams) Reset() {
	*x = ExportDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportDataParams) ProtoMessage() {}

func (x *ExportDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportDataParams.ProtoReflect.Descriptor instead.
func (*ExportDataParams) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{9}
}

func (x *ExportDataParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ExportDataParams) GetScope() *ScopeFilter {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *ExportDataParams) GetTimeRange() *TimeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ExportDataParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ExportDataParams) GetFilters() []*FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ExportDataParams) GetDimension() *DimensionFilter {
	if x != nil {
		return x.Dimension
	}
	return nil
}

// ExportDataResult
type ExportDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *ExportDataResult) Reset() {
	*x = ExportDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportDataResult) ProtoMessage() {}

func (x *ExportDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_report_def_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportDataResult.ProtoReflect.Descriptor instead.
func (*ExportDataResult) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_report_def_proto_rawDescGZIP(), []int{10}
}

func (x *ExportDataResult) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

var File_moego_models_reporting_v2_report_def_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_report_def_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x02, 0x0a, 0x0b, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x6c, 0x6c,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x0f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x48, 0x00, 0x52, 0x0d, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x22, 0x41, 0x0a, 0x09, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54,
	0x45, 0x4e, 0x41, 0x4e, 0x54, 0x10, 0x02, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x0a,
	0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x4e,
	0x0a, 0x10, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12,
	0x15, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x03,
	0x61, 0x73, 0x63, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x73, 0x63, 0x22, 0xcf,
	0x01, 0x0a, 0x0f, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x5a, 0x0a, 0x10, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0xc7, 0x01, 0x0a, 0x0f, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x78, 0x70, 0x61,
	0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x93, 0x02, 0x0a, 0x0c, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x12, 0x39, 0x0a, 0x04, 0x72,
	0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66,
	0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xf4, 0x03, 0x0a, 0x0a, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x12,
	0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x77, 0x44, 0x61, 0x74,
	0x61, 0x44, 0x65, 0x66, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x75, 0x62, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a,
	0x10, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x0f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x77,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x77,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x4e, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x02, 0x52, 0x0b, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x88, 0x01, 0x01, 0x1a, 0x5e, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x72, 0x69, 0x6c, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xe1, 0x04, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x64,
	0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x73, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x48, 0x03, 0x52, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6b,
	0x65, 0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0c, 0x0a,
	0x0a, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xae, 0x01, 0x0a, 0x0f,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xba, 0x03, 0x0a,
	0x10, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64,
	0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x49,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12,
	0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x02, 0x52, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2b, 0x0a, 0x10, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_report_def_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_report_def_proto_rawDescData = file_moego_models_reporting_v2_report_def_proto_rawDesc
)

func file_moego_models_reporting_v2_report_def_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_report_def_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_report_def_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_report_def_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_report_def_proto_rawDescData
}

var file_moego_models_reporting_v2_report_def_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_reporting_v2_report_def_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_models_reporting_v2_report_def_proto_goTypes = []interface{}{
	(ScopeFilter_ScopeType)(0),    // 0: moego.models.reporting.v2.ScopeFilter.ScopeType
	(*ScopeFilter)(nil),           // 1: moego.models.reporting.v2.ScopeFilter
	(*TimeFilter)(nil),            // 2: moego.models.reporting.v2.TimeFilter
	(*DimensionRequest)(nil),      // 3: moego.models.reporting.v2.DimensionRequest
	(*DimensionFilter)(nil),       // 4: moego.models.reporting.v2.DimensionFilter
	(*DimensionConfig)(nil),       // 5: moego.models.reporting.v2.DimensionConfig
	(*FetchDataDef)(nil),          // 6: moego.models.reporting.v2.FetchDataDef
	(*RowDataDef)(nil),            // 7: moego.models.reporting.v2.RowDataDef
	(*FetchDataParams)(nil),       // 8: moego.models.reporting.v2.FetchDataParams
	(*FetchDataResult)(nil),       // 9: moego.models.reporting.v2.FetchDataResult
	(*ExportDataParams)(nil),      // 10: moego.models.reporting.v2.ExportDataParams
	(*ExportDataResult)(nil),      // 11: moego.models.reporting.v2.ExportDataResult
	nil,                           // 12: moego.models.reporting.v2.RowDataDef.DataEntry
	(*interval.Interval)(nil),     // 13: google.type.Interval
	(*FilterRequest)(nil),         // 14: moego.models.reporting.v2.FilterRequest
	(*Field)(nil),                 // 15: moego.models.reporting.v2.Field
	(*v2.PaginationResponse)(nil), // 16: moego.utils.v2.PaginationResponse
	(*DrillConfig)(nil),           // 17: moego.models.reporting.v2.DrillConfig
	(*v2.PaginationRequest)(nil),  // 18: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),            // 19: moego.utils.v2.OrderBy
	(*timestamppb.Timestamp)(nil), // 20: google.protobuf.Timestamp
	(*NumberData)(nil),            // 21: moego.models.reporting.v2.NumberData
}
var file_moego_models_reporting_v2_report_def_proto_depIdxs = []int32{
	0,  // 0: moego.models.reporting.v2.ScopeFilter.scope_type:type_name -> moego.models.reporting.v2.ScopeFilter.ScopeType
	13, // 1: moego.models.reporting.v2.TimeFilter.current_period:type_name -> google.type.Interval
	13, // 2: moego.models.reporting.v2.TimeFilter.previous_period:type_name -> google.type.Interval
	3,  // 3: moego.models.reporting.v2.DimensionFilter.dimensions:type_name -> moego.models.reporting.v2.DimensionRequest
	5,  // 4: moego.models.reporting.v2.DimensionFilter.dimension_config:type_name -> moego.models.reporting.v2.DimensionConfig
	3,  // 5: moego.models.reporting.v2.DimensionConfig.dimensions:type_name -> moego.models.reporting.v2.DimensionRequest
	14, // 6: moego.models.reporting.v2.DimensionConfig.filters:type_name -> moego.models.reporting.v2.FilterRequest
	7,  // 7: moego.models.reporting.v2.FetchDataDef.rows:type_name -> moego.models.reporting.v2.RowDataDef
	15, // 8: moego.models.reporting.v2.FetchDataDef.fields:type_name -> moego.models.reporting.v2.Field
	16, // 9: moego.models.reporting.v2.FetchDataDef.pagination:type_name -> moego.utils.v2.PaginationResponse
	7,  // 10: moego.models.reporting.v2.FetchDataDef.total:type_name -> moego.models.reporting.v2.RowDataDef
	12, // 11: moego.models.reporting.v2.RowDataDef.data:type_name -> moego.models.reporting.v2.RowDataDef.DataEntry
	6,  // 12: moego.models.reporting.v2.RowDataDef.sub_data:type_name -> moego.models.reporting.v2.FetchDataDef
	5,  // 13: moego.models.reporting.v2.RowDataDef.dimension_config:type_name -> moego.models.reporting.v2.DimensionConfig
	17, // 14: moego.models.reporting.v2.RowDataDef.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	1,  // 15: moego.models.reporting.v2.FetchDataParams.scope:type_name -> moego.models.reporting.v2.ScopeFilter
	2,  // 16: moego.models.reporting.v2.FetchDataParams.time_range:type_name -> moego.models.reporting.v2.TimeFilter
	18, // 17: moego.models.reporting.v2.FetchDataParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	19, // 18: moego.models.reporting.v2.FetchDataParams.order_bys:type_name -> moego.utils.v2.OrderBy
	14, // 19: moego.models.reporting.v2.FetchDataParams.filters:type_name -> moego.models.reporting.v2.FilterRequest
	4,  // 20: moego.models.reporting.v2.FetchDataParams.dimension:type_name -> moego.models.reporting.v2.DimensionFilter
	6,  // 21: moego.models.reporting.v2.FetchDataResult.data:type_name -> moego.models.reporting.v2.FetchDataDef
	20, // 22: moego.models.reporting.v2.FetchDataResult.last_synced_time:type_name -> google.protobuf.Timestamp
	1,  // 23: moego.models.reporting.v2.ExportDataParams.scope:type_name -> moego.models.reporting.v2.ScopeFilter
	2,  // 24: moego.models.reporting.v2.ExportDataParams.time_range:type_name -> moego.models.reporting.v2.TimeFilter
	19, // 25: moego.models.reporting.v2.ExportDataParams.order_bys:type_name -> moego.utils.v2.OrderBy
	14, // 26: moego.models.reporting.v2.ExportDataParams.filters:type_name -> moego.models.reporting.v2.FilterRequest
	4,  // 27: moego.models.reporting.v2.ExportDataParams.dimension:type_name -> moego.models.reporting.v2.DimensionFilter
	21, // 28: moego.models.reporting.v2.RowDataDef.DataEntry.value:type_name -> moego.models.reporting.v2.NumberData
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_report_def_proto_init() }
func file_moego_models_reporting_v2_report_def_proto_init() {
	if File_moego_models_reporting_v2_report_def_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_diagram_model_proto_init()
	file_moego_models_reporting_v2_field_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_report_def_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScopeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DimensionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DimensionFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DimensionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowDataDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_report_def_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_report_def_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_report_def_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_report_def_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_report_def_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_report_def_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_report_def_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_report_def_proto = out.File
	file_moego_models_reporting_v2_report_def_proto_rawDesc = nil
	file_moego_models_reporting_v2_report_def_proto_goTypes = nil
	file_moego_models_reporting_v2_report_def_proto_depIdxs = nil
}
