// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/common_model.proto

package reportingpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ReportingType
type ReportingScene int32

const (
	// Unspecified reporting type
	ReportingScene_REPORTING_TYPE_UNSPECIFIED ReportingScene = 0
	// The reporting type is common
	ReportingScene_COMMON ReportingScene = 1
	// The reporting type is enterprise hub
	ReportingScene_ENTERPRISE_HUB ReportingScene = 2
	// Mobile app reporting
	ReportingScene_APP ReportingScene = 3
)

// Enum value maps for ReportingScene.
var (
	ReportingScene_name = map[int32]string{
		0: "REPORTING_TYPE_UNSPECIFIED",
		1: "COMMON",
		2: "ENTERPRISE_HUB",
		3: "APP",
	}
	ReportingScene_value = map[string]int32{
		"REPORTING_TYPE_UNSPECIFIED": 0,
		"COMMON":                     1,
		"ENTERPRISE_HUB":             2,
		"APP":                        3,
	}
)

func (x ReportingScene) Enum() *ReportingScene {
	p := new(ReportingScene)
	*p = x
	return p
}

func (x ReportingScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportingScene) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[0].Descriptor()
}

func (ReportingScene) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[0]
}

func (x ReportingScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportingScene.Descriptor instead.
func (ReportingScene) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{0}
}

// Enumeration of different filter parameter operator types
type Operator int32

const (
	// Unspecified operator
	Operator_OPERATOR_UNSPECIFIED Operator = 0
	// Equal to
	Operator_EQUAL Operator = 1
	// Not equal to
	Operator_NOT_EQUAL Operator = 2
	// In a collection
	Operator_IN Operator = 3
	// Not in a collection
	Operator_NOT_IN Operator = 4
	// Like multiple values
	Operator_LIKE_MULTI Operator = 5
	// Like a value
	Operator_LIKE Operator = 6
	// Not like a value
	Operator_NOT_LIKE Operator = 7
	// Prefix like a value
	Operator_PREFIX_LIKE Operator = 8
	// Suffix like a value
	Operator_SUFFIX_LIKE Operator = 9
	// Greater than a value
	Operator_GREATER_THAN Operator = 10
	// Less than a value
	Operator_LESS_THAN Operator = 11
	// Greater than or equal to a value
	Operator_GREATER_THAN_OR_EQUAL Operator = 12
	// Less than or equal to a value
	Operator_LESS_THAN_OR_EQUAL Operator = 13
	// After a certain time
	Operator_AFTER Operator = 14
	// Before a certain time
	Operator_BEFORE Operator = 15
	// On a certain date
	Operator_ON Operator = 16
	// Within a range
	Operator_RANGE Operator = 17
	// Array contains
	Operator_ARRAY_CONTAINS Operator = 18
	// Array not contains
	Operator_ARRAY_NOT_CONTAINS Operator = 19
)

// Enum value maps for Operator.
var (
	Operator_name = map[int32]string{
		0:  "OPERATOR_UNSPECIFIED",
		1:  "EQUAL",
		2:  "NOT_EQUAL",
		3:  "IN",
		4:  "NOT_IN",
		5:  "LIKE_MULTI",
		6:  "LIKE",
		7:  "NOT_LIKE",
		8:  "PREFIX_LIKE",
		9:  "SUFFIX_LIKE",
		10: "GREATER_THAN",
		11: "LESS_THAN",
		12: "GREATER_THAN_OR_EQUAL",
		13: "LESS_THAN_OR_EQUAL",
		14: "AFTER",
		15: "BEFORE",
		16: "ON",
		17: "RANGE",
		18: "ARRAY_CONTAINS",
		19: "ARRAY_NOT_CONTAINS",
	}
	Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED":  0,
		"EQUAL":                 1,
		"NOT_EQUAL":             2,
		"IN":                    3,
		"NOT_IN":                4,
		"LIKE_MULTI":            5,
		"LIKE":                  6,
		"NOT_LIKE":              7,
		"PREFIX_LIKE":           8,
		"SUFFIX_LIKE":           9,
		"GREATER_THAN":          10,
		"LESS_THAN":             11,
		"GREATER_THAN_OR_EQUAL": 12,
		"LESS_THAN_OR_EQUAL":    13,
		"AFTER":                 14,
		"BEFORE":                15,
		"ON":                    16,
		"RANGE":                 17,
		"ARRAY_CONTAINS":        18,
		"ARRAY_NOT_CONTAINS":    19,
	}
)

func (x Operator) Enum() *Operator {
	p := new(Operator)
	*p = x
	return p
}

func (x Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[1].Descriptor()
}

func (Operator) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[1]
}

func (x Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operator.Descriptor instead.
func (Operator) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{1}
}

// Insight report tab type
type TabType int32

const (
	// Unspecified tab type
	TabType_TAB_TYPE_UNSPECIFIED TabType = 0
	// Dashboard tab
	TabType_DASHBOARD_TAB TabType = 1
	// Report tab
	TabType_REPORT_TAB TabType = 2
	// Enterprise Dashboard tab
	TabType_ENTERPRISE_DASHBOARD_TAB TabType = 3
	// Enterprise Report tab
	TabType_ENTERPRISE_REPORT_TAB TabType = 4
	// App Dashboard tab
	TabType_APP_DASHBOARD_TAB TabType = 5
	// App Report tab
	TabType_APP_REPORT_TAB TabType = 6
)

// Enum value maps for TabType.
var (
	TabType_name = map[int32]string{
		0: "TAB_TYPE_UNSPECIFIED",
		1: "DASHBOARD_TAB",
		2: "REPORT_TAB",
		3: "ENTERPRISE_DASHBOARD_TAB",
		4: "ENTERPRISE_REPORT_TAB",
		5: "APP_DASHBOARD_TAB",
		6: "APP_REPORT_TAB",
	}
	TabType_value = map[string]int32{
		"TAB_TYPE_UNSPECIFIED":     0,
		"DASHBOARD_TAB":            1,
		"REPORT_TAB":               2,
		"ENTERPRISE_DASHBOARD_TAB": 3,
		"ENTERPRISE_REPORT_TAB":    4,
		"APP_DASHBOARD_TAB":        5,
		"APP_REPORT_TAB":           6,
	}
)

func (x TabType) Enum() *TabType {
	p := new(TabType)
	*p = x
	return p
}

func (x TabType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TabType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[2].Descriptor()
}

func (TabType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[2]
}

func (x TabType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TabType.Descriptor instead.
func (TabType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{2}
}

// Enumeration of option type, determined which resource to fetch when fill the filter's options
type OptionType int32

const (
	// Unspecified option type
	OptionType_OPTION_TYPE_UNSPECIFIED OptionType = 0
	// Fixed options, saved in report meta, no need to fetch
	OptionType_FIXED_OPTIONS OptionType = 1
	// Location
	OptionType_LOCATION OptionType = 2
	// Payment method
	OptionType_PAYMENT_METHOD OptionType = 3
	// Staff
	OptionType_STAFF OptionType = 4
	// Service
	OptionType_SERVICE OptionType = 5
	// Add-on
	OptionType_ADD_ON OptionType = 6
	// Service charge
	OptionType_SERVICE_CHARGE OptionType = 7
	// Package
	OptionType_PACKAGE OptionType = 8
	// Product
	OptionType_PRODUCT OptionType = 9
)

// Enum value maps for OptionType.
var (
	OptionType_name = map[int32]string{
		0: "OPTION_TYPE_UNSPECIFIED",
		1: "FIXED_OPTIONS",
		2: "LOCATION",
		3: "PAYMENT_METHOD",
		4: "STAFF",
		5: "SERVICE",
		6: "ADD_ON",
		7: "SERVICE_CHARGE",
		8: "PACKAGE",
		9: "PRODUCT",
	}
	OptionType_value = map[string]int32{
		"OPTION_TYPE_UNSPECIFIED": 0,
		"FIXED_OPTIONS":           1,
		"LOCATION":                2,
		"PAYMENT_METHOD":          3,
		"STAFF":                   4,
		"SERVICE":                 5,
		"ADD_ON":                  6,
		"SERVICE_CHARGE":          7,
		"PACKAGE":                 8,
		"PRODUCT":                 9,
	}
)

func (x OptionType) Enum() *OptionType {
	p := new(OptionType)
	*p = x
	return p
}

func (x OptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[3].Descriptor()
}

func (OptionType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[3]
}

func (x OptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OptionType.Descriptor instead.
func (OptionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{3}
}

// Trend type
type Trend int32

const (
	// Unspecified trend
	Trend_TREND_UNSPECIFIED Trend = 0
	// benefit matrix type
	Trend_BENEFIT Trend = 1
	// harmful matrix type
	Trend_HARMFUL Trend = 2
	// neutral matrix type
	Trend_NEUTRAL Trend = 3
)

// Enum value maps for Trend.
var (
	Trend_name = map[int32]string{
		0: "TREND_UNSPECIFIED",
		1: "BENEFIT",
		2: "HARMFUL",
		3: "NEUTRAL",
	}
	Trend_value = map[string]int32{
		"TREND_UNSPECIFIED": 0,
		"BENEFIT":           1,
		"HARMFUL":           2,
		"NEUTRAL":           3,
	}
)

func (x Trend) Enum() *Trend {
	p := new(Trend)
	*p = x
	return p
}

func (x Trend) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Trend) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[4].Descriptor()
}

func (Trend) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[4]
}

func (x Trend) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Trend.Descriptor instead.
func (Trend) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{4}
}

// Insights tab enum
type InsightsTab int32

const (
	// Unspecified page tab
	InsightsTab_INSIGHTS_TAB_UNSPECIFIED InsightsTab = 0
	// all
	InsightsTab_ALL InsightsTab = 1
	// overview tab
	InsightsTab_OVERVIEW InsightsTab = 2
	// sales tab
	InsightsTab_SALES InsightsTab = 3
	// clients & pets tab
	InsightsTab_CLIENT_INSIGHTS InsightsTab = 4
	// staff tab
	InsightsTab_EMPLOYEE InsightsTab = 5
	// operation tab
	InsightsTab_APPOINTMENT InsightsTab = 6
	// finance tab
	InsightsTab_FINANCE InsightsTab = 7
	// tenant tab
	InsightsTab_TENANT InsightsTab = 8
	// payroll tab
	InsightsTab_PAYROLL InsightsTab = 9
	// Customized report tab
	InsightsTab_CUSTOMIZED InsightsTab = 10
	// Legacy reports tab for mobile app, will be deprecated after mobile report redesign
	InsightsTab_LEGACY_APPOINTMENT InsightsTab = 101
)

// Enum value maps for InsightsTab.
var (
	InsightsTab_name = map[int32]string{
		0:   "INSIGHTS_TAB_UNSPECIFIED",
		1:   "ALL",
		2:   "OVERVIEW",
		3:   "SALES",
		4:   "CLIENT_INSIGHTS",
		5:   "EMPLOYEE",
		6:   "APPOINTMENT",
		7:   "FINANCE",
		8:   "TENANT",
		9:   "PAYROLL",
		10:  "CUSTOMIZED",
		101: "LEGACY_APPOINTMENT",
	}
	InsightsTab_value = map[string]int32{
		"INSIGHTS_TAB_UNSPECIFIED": 0,
		"ALL":                      1,
		"OVERVIEW":                 2,
		"SALES":                    3,
		"CLIENT_INSIGHTS":          4,
		"EMPLOYEE":                 5,
		"APPOINTMENT":              6,
		"FINANCE":                  7,
		"TENANT":                   8,
		"PAYROLL":                  9,
		"CUSTOMIZED":               10,
		"LEGACY_APPOINTMENT":       101,
	}
)

func (x InsightsTab) Enum() *InsightsTab {
	p := new(InsightsTab)
	*p = x
	return p
}

func (x InsightsTab) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InsightsTab) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[5].Descriptor()
}

func (InsightsTab) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[5]
}

func (x InsightsTab) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InsightsTab.Descriptor instead.
func (InsightsTab) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{5}
}

// target type
type DrillConfig_TargetType int32

const (
	// unspecified
	DrillConfig_TARGET_TYPE_UNSPECIFIED DrillConfig_TargetType = 0
	// report table
	DrillConfig_TARGET_TYPE_REPORT_TABLE DrillConfig_TargetType = 1
)

// Enum value maps for DrillConfig_TargetType.
var (
	DrillConfig_TargetType_name = map[int32]string{
		0: "TARGET_TYPE_UNSPECIFIED",
		1: "TARGET_TYPE_REPORT_TABLE",
	}
	DrillConfig_TargetType_value = map[string]int32{
		"TARGET_TYPE_UNSPECIFIED":  0,
		"TARGET_TYPE_REPORT_TABLE": 1,
	}
)

func (x DrillConfig_TargetType) Enum() *DrillConfig_TargetType {
	p := new(DrillConfig_TargetType)
	*p = x
	return p
}

func (x DrillConfig_TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DrillConfig_TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[6].Descriptor()
}

func (DrillConfig_TargetType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[6]
}

func (x DrillConfig_TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DrillConfig_TargetType.Descriptor instead.
func (DrillConfig_TargetType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{2, 0}
}

// Line type enumeration
type StyleConfig_LineType int32

const (
	// Unspecified line type
	StyleConfig_LINE_TYPE_UNSPECIFIED StyleConfig_LineType = 0
	// Solid line type
	StyleConfig_SOLID StyleConfig_LineType = 1
	// Dashed line type
	StyleConfig_DASHED StyleConfig_LineType = 2
)

// Enum value maps for StyleConfig_LineType.
var (
	StyleConfig_LineType_name = map[int32]string{
		0: "LINE_TYPE_UNSPECIFIED",
		1: "SOLID",
		2: "DASHED",
	}
	StyleConfig_LineType_value = map[string]int32{
		"LINE_TYPE_UNSPECIFIED": 0,
		"SOLID":                 1,
		"DASHED":                2,
	}
)

func (x StyleConfig_LineType) Enum() *StyleConfig_LineType {
	p := new(StyleConfig_LineType)
	*p = x
	return p
}

func (x StyleConfig_LineType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StyleConfig_LineType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[7].Descriptor()
}

func (StyleConfig_LineType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[7]
}

func (x StyleConfig_LineType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StyleConfig_LineType.Descriptor instead.
func (StyleConfig_LineType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{3, 0}
}

// Format enumeration
type StyleConfig_Format int32

const (
	// Unspecified format
	StyleConfig_FORMAT_UNSPECIFIED StyleConfig_Format = 0
	// Duration field format by min
	StyleConfig_DURATION_MIN StyleConfig_Format = 1
	// Duration field format by hour
	StyleConfig_DURATION_HOUR StyleConfig_Format = 2
	// Money field format by abbreviation
	StyleConfig_MONEY_ABBREVIATION StyleConfig_Format = 3
	// Rating star field format
	StyleConfig_RATING_STAR StyleConfig_Format = 4
	// Percentage field displayed as bar
	StyleConfig_PERCENTAGE_BAR StyleConfig_Format = 5
)

// Enum value maps for StyleConfig_Format.
var (
	StyleConfig_Format_name = map[int32]string{
		0: "FORMAT_UNSPECIFIED",
		1: "DURATION_MIN",
		2: "DURATION_HOUR",
		3: "MONEY_ABBREVIATION",
		4: "RATING_STAR",
		5: "PERCENTAGE_BAR",
	}
	StyleConfig_Format_value = map[string]int32{
		"FORMAT_UNSPECIFIED": 0,
		"DURATION_MIN":       1,
		"DURATION_HOUR":      2,
		"MONEY_ABBREVIATION": 3,
		"RATING_STAR":        4,
		"PERCENTAGE_BAR":     5,
	}
)

func (x StyleConfig_Format) Enum() *StyleConfig_Format {
	p := new(StyleConfig_Format)
	*p = x
	return p
}

func (x StyleConfig_Format) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StyleConfig_Format) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[8].Descriptor()
}

func (StyleConfig_Format) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[8]
}

func (x StyleConfig_Format) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StyleConfig_Format.Descriptor instead.
func (StyleConfig_Format) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{3, 1}
}

// Icon enumeration
type StyleConfig_Icon int32

const (
	// Unspecified icon
	StyleConfig_ICON_UNSPECIFIED StyleConfig_Icon = 0
	// minor refresh
	StyleConfig_MINOR_REFRESH_OUTLINED StyleConfig_Icon = 1
	// minor dollar
	StyleConfig_MINOR_DOLLAR_OUTLINED StyleConfig_Icon = 2
	// minor give dollar
	StyleConfig_MINOR_GIVE_DOLLAR_OUTLINED StyleConfig_Icon = 3
	// minor paws
	StyleConfig_MINOR_PAWS_OUTLINED StyleConfig_Icon = 4
	// contact
	StyleConfig_MINOR_CONTRACT_OUTLINED StyleConfig_Icon = 5
	// heart
	StyleConfig_MINOR_HEART_OUTLINED StyleConfig_Icon = 6
	// clock
	StyleConfig_MINOR_CLOCK_OUTLINED StyleConfig_Icon = 7
	// calendar
	StyleConfig_MINOR_CALENDAR_OUTLINED StyleConfig_Icon = 8
	// user
	StyleConfig_MINOR_USER_OUTLINED StyleConfig_Icon = 9
	// ticket
	StyleConfig_MINOR_TICKET_OUTLINED StyleConfig_Icon = 10
)

// Enum value maps for StyleConfig_Icon.
var (
	StyleConfig_Icon_name = map[int32]string{
		0:  "ICON_UNSPECIFIED",
		1:  "MINOR_REFRESH_OUTLINED",
		2:  "MINOR_DOLLAR_OUTLINED",
		3:  "MINOR_GIVE_DOLLAR_OUTLINED",
		4:  "MINOR_PAWS_OUTLINED",
		5:  "MINOR_CONTRACT_OUTLINED",
		6:  "MINOR_HEART_OUTLINED",
		7:  "MINOR_CLOCK_OUTLINED",
		8:  "MINOR_CALENDAR_OUTLINED",
		9:  "MINOR_USER_OUTLINED",
		10: "MINOR_TICKET_OUTLINED",
	}
	StyleConfig_Icon_value = map[string]int32{
		"ICON_UNSPECIFIED":           0,
		"MINOR_REFRESH_OUTLINED":     1,
		"MINOR_DOLLAR_OUTLINED":      2,
		"MINOR_GIVE_DOLLAR_OUTLINED": 3,
		"MINOR_PAWS_OUTLINED":        4,
		"MINOR_CONTRACT_OUTLINED":    5,
		"MINOR_HEART_OUTLINED":       6,
		"MINOR_CLOCK_OUTLINED":       7,
		"MINOR_CALENDAR_OUTLINED":    8,
		"MINOR_USER_OUTLINED":        9,
		"MINOR_TICKET_OUTLINED":      10,
	}
)

func (x StyleConfig_Icon) Enum() *StyleConfig_Icon {
	p := new(StyleConfig_Icon)
	*p = x
	return p
}

func (x StyleConfig_Icon) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StyleConfig_Icon) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_common_model_proto_enumTypes[9].Descriptor()
}

func (StyleConfig_Icon) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_common_model_proto_enumTypes[9]
}

func (x StyleConfig_Icon) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StyleConfig_Icon.Descriptor instead.
func (StyleConfig_Icon) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{3, 2}
}

// A value of any type
type Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The value
	//
	// Types that are assignable to Value:
	//
	//	*Value_String_
	//	*Value_Double
	//	*Value_Int64
	//	*Value_Bool
	//	*Value_Money
	//	*Value_Timestamp
	Value isValue_Value `protobuf_oneof:"value"`
}

func (x *Value) Reset() {
	*x = Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{0}
}

func (m *Value) GetValue() isValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Value) GetString_() string {
	if x, ok := x.GetValue().(*Value_String_); ok {
		return x.String_
	}
	return ""
}

func (x *Value) GetDouble() float64 {
	if x, ok := x.GetValue().(*Value_Double); ok {
		return x.Double
	}
	return 0
}

func (x *Value) GetInt64() int64 {
	if x, ok := x.GetValue().(*Value_Int64); ok {
		return x.Int64
	}
	return 0
}

func (x *Value) GetBool() bool {
	if x, ok := x.GetValue().(*Value_Bool); ok {
		return x.Bool
	}
	return false
}

func (x *Value) GetMoney() *money.Money {
	if x, ok := x.GetValue().(*Value_Money); ok {
		return x.Money
	}
	return nil
}

func (x *Value) GetTimestamp() *timestamppb.Timestamp {
	if x, ok := x.GetValue().(*Value_Timestamp); ok {
		return x.Timestamp
	}
	return nil
}

type isValue_Value interface {
	isValue_Value()
}

type Value_String_ struct {
	// The string value
	String_ string `protobuf:"bytes,2,opt,name=string,proto3,oneof"`
}

type Value_Double struct {
	// The double value
	Double float64 `protobuf:"fixed64,3,opt,name=double,proto3,oneof"`
}

type Value_Int64 struct {
	// The int64 value
	Int64 int64 `protobuf:"varint,4,opt,name=int64,proto3,oneof"`
}

type Value_Bool struct {
	// The bool value
	Bool bool `protobuf:"varint,6,opt,name=bool,proto3,oneof"`
}

type Value_Money struct {
	// The money value
	Money *money.Money `protobuf:"bytes,7,opt,name=money,proto3,oneof"`
}

type Value_Timestamp struct {
	// The timestamp value
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=timestamp,proto3,oneof"`
}

func (*Value_String_) isValue_Value() {}

func (*Value_Double) isValue_Value() {}

func (*Value_Int64) isValue_Value() {}

func (*Value_Bool) isValue_Value() {}

func (*Value_Money) isValue_Value() {}

func (*Value_Timestamp) isValue_Value() {}

// TokenInfo for request
type TokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *TokenInfo) Reset() {
	*x = TokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenInfo) ProtoMessage() {}

func (x *TokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenInfo.ProtoReflect.Descriptor instead.
func (*TokenInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{1}
}

func (x *TokenInfo) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *TokenInfo) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// A report configuration
type DrillConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// drill type
	TargetType DrillConfig_TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.reporting.v2.DrillConfig_TargetType" json:"target_type,omitempty"`
	// The target report ID
	TargetId string `protobuf:"bytes,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// The filter of drill config
	Filters []*FilterRequest `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// The title of the drill
	Title *string `protobuf:"bytes,4,opt,name=title,proto3,oneof" json:"title,omitempty"`
}

func (x *DrillConfig) Reset() {
	*x = DrillConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrillConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrillConfig) ProtoMessage() {}

func (x *DrillConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrillConfig.ProtoReflect.Descriptor instead.
func (*DrillConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{2}
}

func (x *DrillConfig) GetTargetType() DrillConfig_TargetType {
	if x != nil {
		return x.TargetType
	}
	return DrillConfig_TARGET_TYPE_UNSPECIFIED
}

func (x *DrillConfig) GetTargetId() string {
	if x != nil {
		return x.TargetId
	}
	return ""
}

func (x *DrillConfig) GetFilters() []*FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *DrillConfig) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

// Style config for diagram
type StyleConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Color value
	Color string `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
	// Line type only for line chart
	LineType *StyleConfig_LineType `protobuf:"varint,2,opt,name=line_type,json=lineType,proto3,enum=moego.models.reporting.v2.StyleConfig_LineType,oneof" json:"line_type,omitempty"`
	// Stack name for bar chart, same stack will overlap
	Stack *string `protobuf:"bytes,3,opt,name=stack,proto3,oneof" json:"stack,omitempty"`
	// Format of the data
	Format *StyleConfig_Format `protobuf:"varint,4,opt,name=format,proto3,enum=moego.models.reporting.v2.StyleConfig_Format,oneof" json:"format,omitempty"`
	// Field icon
	Icon *StyleConfig_Icon `protobuf:"varint,5,opt,name=icon,proto3,enum=moego.models.reporting.v2.StyleConfig_Icon,oneof" json:"icon,omitempty"`
	// Style map for dynamic rows
	SubStyleMap map[string]*StyleConfig `protobuf:"bytes,6,rep,name=sub_style_map,json=subStyleMap,proto3" json:"sub_style_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *StyleConfig) Reset() {
	*x = StyleConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StyleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StyleConfig) ProtoMessage() {}

func (x *StyleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StyleConfig.ProtoReflect.Descriptor instead.
func (*StyleConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{3}
}

func (x *StyleConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *StyleConfig) GetLineType() StyleConfig_LineType {
	if x != nil && x.LineType != nil {
		return *x.LineType
	}
	return StyleConfig_LINE_TYPE_UNSPECIFIED
}

func (x *StyleConfig) GetStack() string {
	if x != nil && x.Stack != nil {
		return *x.Stack
	}
	return ""
}

func (x *StyleConfig) GetFormat() StyleConfig_Format {
	if x != nil && x.Format != nil {
		return *x.Format
	}
	return StyleConfig_FORMAT_UNSPECIFIED
}

func (x *StyleConfig) GetIcon() StyleConfig_Icon {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return StyleConfig_ICON_UNSPECIFIED
}

func (x *StyleConfig) GetSubStyleMap() map[string]*StyleConfig {
	if x != nil {
		return x.SubStyleMap
	}
	return nil
}

// Filter params definition
type FilterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The field key of the filter parameter
	FieldKey string `protobuf:"bytes,1,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// The operator of the filter parameter
	Operator Operator `protobuf:"varint,2,opt,name=operator,proto3,enum=moego.models.reporting.v2.Operator" json:"operator,omitempty"`
	// The option value or input values
	Values []*Value `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	// The label of the filter option
	Label *string `protobuf:"bytes,4,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// The preview value of the filter option
	ValuePreview *string `protobuf:"bytes,5,opt,name=value_preview,json=valuePreview,proto3,oneof" json:"value_preview,omitempty"`
	// Invert select values
	InvertSelect *bool `protobuf:"varint,6,opt,name=invert_select,json=invertSelect,proto3,oneof" json:"invert_select,omitempty"`
}

func (x *FilterRequest) Reset() {
	*x = FilterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterRequest) ProtoMessage() {}

func (x *FilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterRequest.ProtoReflect.Descriptor instead.
func (*FilterRequest) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{4}
}

func (x *FilterRequest) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *FilterRequest) GetOperator() Operator {
	if x != nil {
		return x.Operator
	}
	return Operator_OPERATOR_UNSPECIFIED
}

func (x *FilterRequest) GetValues() []*Value {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *FilterRequest) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *FilterRequest) GetValuePreview() string {
	if x != nil && x.ValuePreview != nil {
		return *x.ValuePreview
	}
	return ""
}

func (x *FilterRequest) GetInvertSelect() bool {
	if x != nil && x.InvertSelect != nil {
		return *x.InvertSelect
	}
	return false
}

// Filter group params definition
type FilterRequestGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group name, = FilterGroup.group_name
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	// The filters
	Filters []*FilterRequest `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *FilterRequestGroup) Reset() {
	*x = FilterRequestGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterRequestGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterRequestGroup) ProtoMessage() {}

func (x *FilterRequestGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_common_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterRequestGroup.ProtoReflect.Descriptor instead.
func (*FilterRequestGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_common_model_proto_rawDescGZIP(), []int{5}
}

func (x *FilterRequestGroup) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *FilterRequestGroup) GetFilters() []*FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

var File_moego_models_reporting_v2_common_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_common_model_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x01, 0x0a,
	0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x05, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x14, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2a, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x05, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x69, 0x0a, 0x09, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x22, 0xb0, 0x02, 0x0a, 0x0b, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x52, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x88, 0x01, 0x01, 0x22, 0x47, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1c, 0x0a, 0x18, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x88, 0x08, 0x0a, 0x0b, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x51, 0x0a,
	0x09, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x08, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x48, 0x02, 0x52, 0x06, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x48, 0x03, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x75, 0x62,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x66, 0x0a, 0x10, 0x53, 0x75,
	0x62, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x3c, 0x0a, 0x08, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x4f, 0x4c,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x41, 0x53, 0x48, 0x45, 0x44, 0x10, 0x02,
	0x22, 0x82, 0x01, 0x0a, 0x06, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x48, 0x4f, 0x55, 0x52, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x4f, 0x4e, 0x45,
	0x59, 0x5f, 0x41, 0x42, 0x42, 0x52, 0x45, 0x56, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x10,
	0x04, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x42, 0x41, 0x52, 0x10, 0x05, 0x22, 0xae, 0x02, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x52, 0x45,
	0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x19, 0x0a, 0x15, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x44, 0x4f, 0x4c, 0x4c, 0x41, 0x52,
	0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x4d,
	0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x47, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x4f, 0x4c, 0x4c, 0x41, 0x52,
	0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x4d,
	0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x57, 0x53, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x52, 0x54,
	0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x4d,
	0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x43, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49,
	0x4e, 0x45, 0x44, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x43,
	0x41, 0x4c, 0x45, 0x4e, 0x44, 0x41, 0x52, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44,
	0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x09, 0x12, 0x19, 0x0a, 0x15, 0x4d,
	0x49, 0x4e, 0x4f, 0x52, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x4c,
	0x49, 0x4e, 0x45, 0x44, 0x10, 0x0a, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x69, 0x63,
	0x6f, 0x6e, 0x22, 0xdb, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x4b, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x28,
	0x0a, 0x0d, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x65,
	0x72, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x02, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x22, 0x77, 0x0a, 0x12, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x2a, 0x59, 0x0a, 0x0e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x48, 0x55, 0x42, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x50, 0x50, 0x10, 0x03, 0x2a, 0xc6, 0x02, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x45,
	0x51, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x51,
	0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x0a, 0x0a,
	0x06, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x4b,
	0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x49, 0x4b,
	0x45, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x10,
	0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x52, 0x45, 0x46, 0x49, 0x58, 0x5f, 0x4c, 0x49, 0x4b, 0x45,
	0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x55, 0x46, 0x46, 0x49, 0x58, 0x5f, 0x4c, 0x49, 0x4b,
	0x45, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x54,
	0x48, 0x41, 0x4e, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48,
	0x41, 0x4e, 0x10, 0x0b, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f,
	0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x0c, 0x12,
	0x16, 0x0a, 0x12, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4f, 0x52, 0x5f,
	0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x46, 0x54, 0x45, 0x52,
	0x10, 0x0e, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x10, 0x0f, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4e, 0x10, 0x10, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10,
	0x11, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41,
	0x49, 0x4e, 0x53, 0x10, 0x12, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x10, 0x13, 0x2a, 0xaa, 0x01,
	0x0a, 0x07, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x42,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x41, 0x42, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x54, 0x41, 0x42, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50,
	0x52, 0x49, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x54,
	0x41, 0x42, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49,
	0x53, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x04, 0x12,
	0x15, 0x0a, 0x11, 0x41, 0x50, 0x50, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x41, 0x42, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x50, 0x50, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x10, 0x06, 0x2a, 0xb0, 0x01, 0x0a, 0x0a, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f,
	0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x4f, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x53,
	0x54, 0x41, 0x46, 0x46, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x44, 0x44, 0x5f, 0x4f, 0x4e, 0x10, 0x06, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x10, 0x07, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x08,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x09, 0x2a, 0x45, 0x0a,
	0x05, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x45, 0x4e, 0x44, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x48, 0x41,
	0x52, 0x4d, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x55, 0x54, 0x52,
	0x41, 0x4c, 0x10, 0x03, 0x2a, 0xcf, 0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x54, 0x61, 0x62, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53,
	0x5f, 0x54, 0x41, 0x42, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4f,
	0x56, 0x45, 0x52, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x41, 0x4c,
	0x45, 0x53, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49,
	0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x4d, 0x50,
	0x4c, 0x4f, 0x59, 0x45, 0x45, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x49, 0x4e, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x10,
	0x08, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x59, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x09, 0x12, 0x0e,
	0x0a, 0x0a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x16,
	0x0a, 0x12, 0x4c, 0x45, 0x47, 0x41, 0x43, 0x59, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x65, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_common_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_common_model_proto_rawDescData = file_moego_models_reporting_v2_common_model_proto_rawDesc
)

func file_moego_models_reporting_v2_common_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_common_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_common_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_common_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_common_model_proto_rawDescData
}

var file_moego_models_reporting_v2_common_model_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_moego_models_reporting_v2_common_model_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_reporting_v2_common_model_proto_goTypes = []interface{}{
	(ReportingScene)(0),           // 0: moego.models.reporting.v2.ReportingScene
	(Operator)(0),                 // 1: moego.models.reporting.v2.Operator
	(TabType)(0),                  // 2: moego.models.reporting.v2.TabType
	(OptionType)(0),               // 3: moego.models.reporting.v2.OptionType
	(Trend)(0),                    // 4: moego.models.reporting.v2.Trend
	(InsightsTab)(0),              // 5: moego.models.reporting.v2.InsightsTab
	(DrillConfig_TargetType)(0),   // 6: moego.models.reporting.v2.DrillConfig.TargetType
	(StyleConfig_LineType)(0),     // 7: moego.models.reporting.v2.StyleConfig.LineType
	(StyleConfig_Format)(0),       // 8: moego.models.reporting.v2.StyleConfig.Format
	(StyleConfig_Icon)(0),         // 9: moego.models.reporting.v2.StyleConfig.Icon
	(*Value)(nil),                 // 10: moego.models.reporting.v2.Value
	(*TokenInfo)(nil),             // 11: moego.models.reporting.v2.TokenInfo
	(*DrillConfig)(nil),           // 12: moego.models.reporting.v2.DrillConfig
	(*StyleConfig)(nil),           // 13: moego.models.reporting.v2.StyleConfig
	(*FilterRequest)(nil),         // 14: moego.models.reporting.v2.FilterRequest
	(*FilterRequestGroup)(nil),    // 15: moego.models.reporting.v2.FilterRequestGroup
	nil,                           // 16: moego.models.reporting.v2.StyleConfig.SubStyleMapEntry
	(*money.Money)(nil),           // 17: google.type.Money
	(*timestamppb.Timestamp)(nil), // 18: google.protobuf.Timestamp
}
var file_moego_models_reporting_v2_common_model_proto_depIdxs = []int32{
	17, // 0: moego.models.reporting.v2.Value.money:type_name -> google.type.Money
	18, // 1: moego.models.reporting.v2.Value.timestamp:type_name -> google.protobuf.Timestamp
	6,  // 2: moego.models.reporting.v2.DrillConfig.target_type:type_name -> moego.models.reporting.v2.DrillConfig.TargetType
	14, // 3: moego.models.reporting.v2.DrillConfig.filters:type_name -> moego.models.reporting.v2.FilterRequest
	7,  // 4: moego.models.reporting.v2.StyleConfig.line_type:type_name -> moego.models.reporting.v2.StyleConfig.LineType
	8,  // 5: moego.models.reporting.v2.StyleConfig.format:type_name -> moego.models.reporting.v2.StyleConfig.Format
	9,  // 6: moego.models.reporting.v2.StyleConfig.icon:type_name -> moego.models.reporting.v2.StyleConfig.Icon
	16, // 7: moego.models.reporting.v2.StyleConfig.sub_style_map:type_name -> moego.models.reporting.v2.StyleConfig.SubStyleMapEntry
	1,  // 8: moego.models.reporting.v2.FilterRequest.operator:type_name -> moego.models.reporting.v2.Operator
	10, // 9: moego.models.reporting.v2.FilterRequest.values:type_name -> moego.models.reporting.v2.Value
	14, // 10: moego.models.reporting.v2.FilterRequestGroup.filters:type_name -> moego.models.reporting.v2.FilterRequest
	13, // 11: moego.models.reporting.v2.StyleConfig.SubStyleMapEntry.value:type_name -> moego.models.reporting.v2.StyleConfig
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_common_model_proto_init() }
func file_moego_models_reporting_v2_common_model_proto_init() {
	if File_moego_models_reporting_v2_common_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_common_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_common_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_common_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrillConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_common_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StyleConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_common_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_common_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterRequestGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_common_model_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Value_String_)(nil),
		(*Value_Double)(nil),
		(*Value_Int64)(nil),
		(*Value_Bool)(nil),
		(*Value_Money)(nil),
		(*Value_Timestamp)(nil),
	}
	file_moego_models_reporting_v2_common_model_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_common_model_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_common_model_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_common_model_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_common_model_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_common_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_common_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_common_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_common_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_common_model_proto = out.File
	file_moego_models_reporting_v2_common_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_common_model_proto_goTypes = nil
	file_moego_models_reporting_v2_common_model_proto_depIdxs = nil
}
