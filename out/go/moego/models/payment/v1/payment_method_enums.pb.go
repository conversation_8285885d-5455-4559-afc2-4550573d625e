// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/payment_method_enums.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// stripe payment method
type StripePaymentMethod int32

const (
	// unspecified
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_UNSPECIFIED StripePaymentMethod = 0
	// credit card
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_CARD StripePaymentMethod = 1
	// card on file
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_COF StripePaymentMethod = 2
	// bluetooth reader
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_BLUETOOTH_READER StripePaymentMethod = 3
	// smart reader
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_SMART_READER StripePaymentMethod = 4
	// apple pay
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_APPLE_PAY StripePaymentMethod = 5
	// google pay
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_GOOGLE_PAY StripePaymentMethod = 6
	// tap to pay on ios
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_TTPOI StripePaymentMethod = 7
	// ach
	StripePaymentMethod_STRIPE_PAYMENT_METHOD_ACH StripePaymentMethod = 8
)

// Enum value maps for StripePaymentMethod.
var (
	StripePaymentMethod_name = map[int32]string{
		0: "STRIPE_PAYMENT_METHOD_UNSPECIFIED",
		1: "STRIPE_PAYMENT_METHOD_CARD",
		2: "STRIPE_PAYMENT_METHOD_COF",
		3: "STRIPE_PAYMENT_METHOD_BLUETOOTH_READER",
		4: "STRIPE_PAYMENT_METHOD_SMART_READER",
		5: "STRIPE_PAYMENT_METHOD_APPLE_PAY",
		6: "STRIPE_PAYMENT_METHOD_GOOGLE_PAY",
		7: "STRIPE_PAYMENT_METHOD_TTPOI",
		8: "STRIPE_PAYMENT_METHOD_ACH",
	}
	StripePaymentMethod_value = map[string]int32{
		"STRIPE_PAYMENT_METHOD_UNSPECIFIED":      0,
		"STRIPE_PAYMENT_METHOD_CARD":             1,
		"STRIPE_PAYMENT_METHOD_COF":              2,
		"STRIPE_PAYMENT_METHOD_BLUETOOTH_READER": 3,
		"STRIPE_PAYMENT_METHOD_SMART_READER":     4,
		"STRIPE_PAYMENT_METHOD_APPLE_PAY":        5,
		"STRIPE_PAYMENT_METHOD_GOOGLE_PAY":       6,
		"STRIPE_PAYMENT_METHOD_TTPOI":            7,
		"STRIPE_PAYMENT_METHOD_ACH":              8,
	}
)

func (x StripePaymentMethod) Enum() *StripePaymentMethod {
	p := new(StripePaymentMethod)
	*p = x
	return p
}

func (x StripePaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StripePaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[0].Descriptor()
}

func (StripePaymentMethod) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[0]
}

func (x StripePaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StripePaymentMethod.Descriptor instead.
func (StripePaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_enums_proto_rawDescGZIP(), []int{0}
}

// SquarePaymentMethod is the method of a square payment
type SquarePaymentMethod int32

const (
	// unspecified
	SquarePaymentMethod_SQUARE_PAYMENT_METHOD_UNSPECIFIED SquarePaymentMethod = 0
	// card
	SquarePaymentMethod_CARD SquarePaymentMethod = 1
	// card on file
	SquarePaymentMethod_COF SquarePaymentMethod = 2
	// terminal
	SquarePaymentMethod_TERMINAL SquarePaymentMethod = 3
	// reader
	SquarePaymentMethod_READER SquarePaymentMethod = 4
	// pos
	SquarePaymentMethod_POS SquarePaymentMethod = 5
)

// Enum value maps for SquarePaymentMethod.
var (
	SquarePaymentMethod_name = map[int32]string{
		0: "SQUARE_PAYMENT_METHOD_UNSPECIFIED",
		1: "CARD",
		2: "COF",
		3: "TERMINAL",
		4: "READER",
		5: "POS",
	}
	SquarePaymentMethod_value = map[string]int32{
		"SQUARE_PAYMENT_METHOD_UNSPECIFIED": 0,
		"CARD":                              1,
		"COF":                               2,
		"TERMINAL":                          3,
		"READER":                            4,
		"POS":                               5,
	}
)

func (x SquarePaymentMethod) Enum() *SquarePaymentMethod {
	p := new(SquarePaymentMethod)
	*p = x
	return p
}

func (x SquarePaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SquarePaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[1].Descriptor()
}

func (SquarePaymentMethod) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[1]
}

func (x SquarePaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SquarePaymentMethod.Descriptor instead.
func (SquarePaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_enums_proto_rawDescGZIP(), []int{1}
}

// PaymentMethod is the method of a payment
type PaymentMethod int32

const (
	// unspecified
	PaymentMethod_PAYMENT_METHOD_UNSPECIFIED PaymentMethod = 0
	// credit card
	PaymentMethod_CREDIT_CARD PaymentMethod = 1
	// cash
	PaymentMethod_CASH PaymentMethod = 2
	// check
	PaymentMethod_CHECK PaymentMethod = 3
	// venmo
	PaymentMethod_VENMO PaymentMethod = 4
	// paypal
	PaymentMethod_PAYPAL PaymentMethod = 5
	// chase
	PaymentMethod_CHASE PaymentMethod = 6
	// other card reader
	PaymentMethod_OTHER_CARD_READER PaymentMethod = 7
	// square
	PaymentMethod_SQUARE PaymentMethod = 8
)

// Enum value maps for PaymentMethod.
var (
	PaymentMethod_name = map[int32]string{
		0: "PAYMENT_METHOD_UNSPECIFIED",
		1: "CREDIT_CARD",
		2: "CASH",
		3: "CHECK",
		4: "VENMO",
		5: "PAYPAL",
		6: "CHASE",
		7: "OTHER_CARD_READER",
		8: "SQUARE",
	}
	PaymentMethod_value = map[string]int32{
		"PAYMENT_METHOD_UNSPECIFIED": 0,
		"CREDIT_CARD":                1,
		"CASH":                       2,
		"CHECK":                      3,
		"VENMO":                      4,
		"PAYPAL":                     5,
		"CHASE":                      6,
		"OTHER_CARD_READER":          7,
		"SQUARE":                     8,
	}
)

func (x PaymentMethod) Enum() *PaymentMethod {
	p := new(PaymentMethod)
	*p = x
	return p
}

func (x PaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[2].Descriptor()
}

func (PaymentMethod) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_method_enums_proto_enumTypes[2]
}

func (x PaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethod.Descriptor instead.
func (PaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_payment_v1_payment_method_enums_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_payment_method_enums_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0xda, 0x02,
	0x0a, 0x13, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d,
	0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d,
	0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x43, 0x4f, 0x46, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x53,
	0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x5f, 0x42, 0x4c, 0x55, 0x45, 0x54, 0x4f, 0x4f, 0x54, 0x48, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x54, 0x52, 0x49, 0x50,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x04, 0x12,
	0x23, 0x0a, 0x1f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x5f, 0x50,
	0x41, 0x59, 0x10, 0x05, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x47, 0x4f,
	0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x54,
	0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54,
	0x48, 0x4f, 0x44, 0x5f, 0x54, 0x54, 0x50, 0x4f, 0x49, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x53,
	0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x5f, 0x41, 0x43, 0x48, 0x10, 0x08, 0x2a, 0x72, 0x0a, 0x13, 0x53, 0x71,
	0x75, 0x61, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x4f, 0x46, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54,
	0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x41,
	0x44, 0x45, 0x52, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f, 0x53, 0x10, 0x05, 0x2a, 0x9a,
	0x01, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x45, 0x4e, 0x4d, 0x4f, 0x10,
	0x04, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x41, 0x59, 0x50, 0x41, 0x4c, 0x10, 0x05, 0x12, 0x09, 0x0a,
	0x05, 0x43, 0x48, 0x41, 0x53, 0x45, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x07, 0x12,
	0x0a, 0x0a, 0x06, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x10, 0x08, 0x42, 0x7b, 0x0a, 0x1f, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_payment_method_enums_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_payment_method_enums_proto_rawDescData = file_moego_models_payment_v1_payment_method_enums_proto_rawDesc
)

func file_moego_models_payment_v1_payment_method_enums_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_payment_method_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_payment_method_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_payment_method_enums_proto_rawDescData)
	})
	return file_moego_models_payment_v1_payment_method_enums_proto_rawDescData
}

var file_moego_models_payment_v1_payment_method_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_payment_v1_payment_method_enums_proto_goTypes = []interface{}{
	(StripePaymentMethod)(0), // 0: moego.models.payment.v1.StripePaymentMethod
	(SquarePaymentMethod)(0), // 1: moego.models.payment.v1.SquarePaymentMethod
	(PaymentMethod)(0),       // 2: moego.models.payment.v1.PaymentMethod
}
var file_moego_models_payment_v1_payment_method_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_payment_method_enums_proto_init() }
func file_moego_models_payment_v1_payment_method_enums_proto_init() {
	if File_moego_models_payment_v1_payment_method_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_payment_method_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_payment_method_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_payment_method_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v1_payment_method_enums_proto_enumTypes,
	}.Build()
	File_moego_models_payment_v1_payment_method_enums_proto = out.File
	file_moego_models_payment_v1_payment_method_enums_proto_rawDesc = nil
	file_moego_models_payment_v1_payment_method_enums_proto_goTypes = nil
	file_moego_models_payment_v1_payment_method_enums_proto_depIdxs = nil
}
