// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/custom_fee.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CustomFeeServiceClient is the client API for CustomFeeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomFeeServiceClient interface {
	// get company custom fee list
	GetCompanyCustomFeeList(ctx context.Context, in *CompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFeeResult, error)
	// create company custom fee
	CreateCompanyCustomFee(ctx context.Context, in *AddCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error)
	// update company min vol
	UpdateCompanyCustomFee(ctx context.Context, in *UpdateCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error)
	// get business payment setting list
	GetBusinessPaymentSettingList(ctx context.Context, in *BusinessPaymentSettingParams, opts ...grpc.CallOption) (*BusinessPaymentSettingResult, error)
	// delete company custom fee
	DeleteCompanyCustomFee(ctx context.Context, in *DeleteCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error)
	// list company custom fee list
	ListCompanyCustomFees(ctx context.Context, in *ListCompanyCustomFeesParams, opts ...grpc.CallOption) (*ListCompanyCustomFeesResult, error)
}

type customFeeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomFeeServiceClient(cc grpc.ClientConnInterface) CustomFeeServiceClient {
	return &customFeeServiceClient{cc}
}

func (c *customFeeServiceClient) GetCompanyCustomFeeList(ctx context.Context, in *CompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFeeResult, error) {
	out := new(CompanyCustomFeeResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/GetCompanyCustomFeeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customFeeServiceClient) CreateCompanyCustomFee(ctx context.Context, in *AddCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error) {
	out := new(CompanyCustomFee)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/CreateCompanyCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customFeeServiceClient) UpdateCompanyCustomFee(ctx context.Context, in *UpdateCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error) {
	out := new(CompanyCustomFee)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/UpdateCompanyCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customFeeServiceClient) GetBusinessPaymentSettingList(ctx context.Context, in *BusinessPaymentSettingParams, opts ...grpc.CallOption) (*BusinessPaymentSettingResult, error) {
	out := new(BusinessPaymentSettingResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/GetBusinessPaymentSettingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customFeeServiceClient) DeleteCompanyCustomFee(ctx context.Context, in *DeleteCompanyCustomFeeParams, opts ...grpc.CallOption) (*CompanyCustomFee, error) {
	out := new(CompanyCustomFee)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/DeleteCompanyCustomFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customFeeServiceClient) ListCompanyCustomFees(ctx context.Context, in *ListCompanyCustomFeesParams, opts ...grpc.CallOption) (*ListCompanyCustomFeesResult, error) {
	out := new(ListCompanyCustomFeesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.CustomFeeService/ListCompanyCustomFees", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomFeeServiceServer is the server API for CustomFeeService service.
// All implementations must embed UnimplementedCustomFeeServiceServer
// for forward compatibility
type CustomFeeServiceServer interface {
	// get company custom fee list
	GetCompanyCustomFeeList(context.Context, *CompanyCustomFeeParams) (*CompanyCustomFeeResult, error)
	// create company custom fee
	CreateCompanyCustomFee(context.Context, *AddCompanyCustomFeeParams) (*CompanyCustomFee, error)
	// update company min vol
	UpdateCompanyCustomFee(context.Context, *UpdateCompanyCustomFeeParams) (*CompanyCustomFee, error)
	// get business payment setting list
	GetBusinessPaymentSettingList(context.Context, *BusinessPaymentSettingParams) (*BusinessPaymentSettingResult, error)
	// delete company custom fee
	DeleteCompanyCustomFee(context.Context, *DeleteCompanyCustomFeeParams) (*CompanyCustomFee, error)
	// list company custom fee list
	ListCompanyCustomFees(context.Context, *ListCompanyCustomFeesParams) (*ListCompanyCustomFeesResult, error)
	mustEmbedUnimplementedCustomFeeServiceServer()
}

// UnimplementedCustomFeeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCustomFeeServiceServer struct {
}

func (UnimplementedCustomFeeServiceServer) GetCompanyCustomFeeList(context.Context, *CompanyCustomFeeParams) (*CompanyCustomFeeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyCustomFeeList not implemented")
}
func (UnimplementedCustomFeeServiceServer) CreateCompanyCustomFee(context.Context, *AddCompanyCustomFeeParams) (*CompanyCustomFee, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCompanyCustomFee not implemented")
}
func (UnimplementedCustomFeeServiceServer) UpdateCompanyCustomFee(context.Context, *UpdateCompanyCustomFeeParams) (*CompanyCustomFee, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCompanyCustomFee not implemented")
}
func (UnimplementedCustomFeeServiceServer) GetBusinessPaymentSettingList(context.Context, *BusinessPaymentSettingParams) (*BusinessPaymentSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessPaymentSettingList not implemented")
}
func (UnimplementedCustomFeeServiceServer) DeleteCompanyCustomFee(context.Context, *DeleteCompanyCustomFeeParams) (*CompanyCustomFee, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCompanyCustomFee not implemented")
}
func (UnimplementedCustomFeeServiceServer) ListCompanyCustomFees(context.Context, *ListCompanyCustomFeesParams) (*ListCompanyCustomFeesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCompanyCustomFees not implemented")
}
func (UnimplementedCustomFeeServiceServer) mustEmbedUnimplementedCustomFeeServiceServer() {}

// UnsafeCustomFeeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomFeeServiceServer will
// result in compilation errors.
type UnsafeCustomFeeServiceServer interface {
	mustEmbedUnimplementedCustomFeeServiceServer()
}

func RegisterCustomFeeServiceServer(s grpc.ServiceRegistrar, srv CustomFeeServiceServer) {
	s.RegisterService(&CustomFeeService_ServiceDesc, srv)
}

func _CustomFeeService_GetCompanyCustomFeeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).GetCompanyCustomFeeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/GetCompanyCustomFeeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).GetCompanyCustomFeeList(ctx, req.(*CompanyCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomFeeService_CreateCompanyCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCompanyCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).CreateCompanyCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/CreateCompanyCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).CreateCompanyCustomFee(ctx, req.(*AddCompanyCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomFeeService_UpdateCompanyCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCompanyCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).UpdateCompanyCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/UpdateCompanyCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).UpdateCompanyCustomFee(ctx, req.(*UpdateCompanyCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomFeeService_GetBusinessPaymentSettingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BusinessPaymentSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).GetBusinessPaymentSettingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/GetBusinessPaymentSettingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).GetBusinessPaymentSettingList(ctx, req.(*BusinessPaymentSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomFeeService_DeleteCompanyCustomFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCompanyCustomFeeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).DeleteCompanyCustomFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/DeleteCompanyCustomFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).DeleteCompanyCustomFee(ctx, req.(*DeleteCompanyCustomFeeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomFeeService_ListCompanyCustomFees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCompanyCustomFeesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomFeeServiceServer).ListCompanyCustomFees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.CustomFeeService/ListCompanyCustomFees",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomFeeServiceServer).ListCompanyCustomFees(ctx, req.(*ListCompanyCustomFeesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomFeeService_ServiceDesc is the grpc.ServiceDesc for CustomFeeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomFeeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.CustomFeeService",
	HandlerType: (*CustomFeeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCompanyCustomFeeList",
			Handler:    _CustomFeeService_GetCompanyCustomFeeList_Handler,
		},
		{
			MethodName: "CreateCompanyCustomFee",
			Handler:    _CustomFeeService_CreateCompanyCustomFee_Handler,
		},
		{
			MethodName: "UpdateCompanyCustomFee",
			Handler:    _CustomFeeService_UpdateCompanyCustomFee_Handler,
		},
		{
			MethodName: "GetBusinessPaymentSettingList",
			Handler:    _CustomFeeService_GetBusinessPaymentSettingList_Handler,
		},
		{
			MethodName: "DeleteCompanyCustomFee",
			Handler:    _CustomFeeService_DeleteCompanyCustomFee_Handler,
		},
		{
			MethodName: "ListCompanyCustomFees",
			Handler:    _CustomFeeService_ListCompanyCustomFees_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/custom_fee.proto",
}
