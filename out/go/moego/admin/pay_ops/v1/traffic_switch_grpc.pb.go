// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/traffic_switch.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TrafficSwitchServiceClient is the client API for TrafficSwitchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TrafficSwitchServiceClient interface {
	// add to split payment whitelist
	AddToSplitPaymentWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// delete from split payment whitelist
	DeleteFromSplitPaymentWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// add to split payment blacklist
	AddToSplitPaymentBlacklist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// delete from split payment blacklist
	DeleteFromSplitPaymentBlacklist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// set split payment traffic ratio
	SetSplitPaymentTrafficRatio(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// get all split traffic switch config
	GetAllSplitTrafficSwitchConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAllSplitTrafficSwitchConfigResponse, error)
	// set split payment loan switch
	SetSplitPaymentLoanSwitch(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// add to invoice reinvent whitelist
	AddToInvoiceReinventWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error)
	// get all invoice reinvent config
	GetAllInvoiceReinventTrafficSwitchConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAllInvoiceReinventTrafficSwitchConfigResponse, error)
}

type trafficSwitchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTrafficSwitchServiceClient(cc grpc.ClientConnInterface) TrafficSwitchServiceClient {
	return &trafficSwitchServiceClient{cc}
}

func (c *trafficSwitchServiceClient) AddToSplitPaymentWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToSplitPaymentWhitelist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) DeleteFromSplitPaymentWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/DeleteFromSplitPaymentWhitelist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) AddToSplitPaymentBlacklist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToSplitPaymentBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) DeleteFromSplitPaymentBlacklist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/DeleteFromSplitPaymentBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) SetSplitPaymentTrafficRatio(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/SetSplitPaymentTrafficRatio", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) GetAllSplitTrafficSwitchConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAllSplitTrafficSwitchConfigResponse, error) {
	out := new(GetAllSplitTrafficSwitchConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/GetAllSplitTrafficSwitchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) SetSplitPaymentLoanSwitch(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/SetSplitPaymentLoanSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) AddToInvoiceReinventWhitelist(ctx context.Context, in *TrafficSwitchRequest, opts ...grpc.CallOption) (*TrafficSwitchResponse, error) {
	out := new(TrafficSwitchResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToInvoiceReinventWhitelist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *trafficSwitchServiceClient) GetAllInvoiceReinventTrafficSwitchConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAllInvoiceReinventTrafficSwitchConfigResponse, error) {
	out := new(GetAllInvoiceReinventTrafficSwitchConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TrafficSwitchService/GetAllInvoiceReinventTrafficSwitchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TrafficSwitchServiceServer is the server API for TrafficSwitchService service.
// All implementations must embed UnimplementedTrafficSwitchServiceServer
// for forward compatibility
type TrafficSwitchServiceServer interface {
	// add to split payment whitelist
	AddToSplitPaymentWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// delete from split payment whitelist
	DeleteFromSplitPaymentWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// add to split payment blacklist
	AddToSplitPaymentBlacklist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// delete from split payment blacklist
	DeleteFromSplitPaymentBlacklist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// set split payment traffic ratio
	SetSplitPaymentTrafficRatio(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// get all split traffic switch config
	GetAllSplitTrafficSwitchConfig(context.Context, *emptypb.Empty) (*GetAllSplitTrafficSwitchConfigResponse, error)
	// set split payment loan switch
	SetSplitPaymentLoanSwitch(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// add to invoice reinvent whitelist
	AddToInvoiceReinventWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error)
	// get all invoice reinvent config
	GetAllInvoiceReinventTrafficSwitchConfig(context.Context, *emptypb.Empty) (*GetAllInvoiceReinventTrafficSwitchConfigResponse, error)
	mustEmbedUnimplementedTrafficSwitchServiceServer()
}

// UnimplementedTrafficSwitchServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTrafficSwitchServiceServer struct {
}

func (UnimplementedTrafficSwitchServiceServer) AddToSplitPaymentWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToSplitPaymentWhitelist not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) DeleteFromSplitPaymentWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFromSplitPaymentWhitelist not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) AddToSplitPaymentBlacklist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToSplitPaymentBlacklist not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) DeleteFromSplitPaymentBlacklist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFromSplitPaymentBlacklist not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) SetSplitPaymentTrafficRatio(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSplitPaymentTrafficRatio not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) GetAllSplitTrafficSwitchConfig(context.Context, *emptypb.Empty) (*GetAllSplitTrafficSwitchConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllSplitTrafficSwitchConfig not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) SetSplitPaymentLoanSwitch(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSplitPaymentLoanSwitch not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) AddToInvoiceReinventWhitelist(context.Context, *TrafficSwitchRequest) (*TrafficSwitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToInvoiceReinventWhitelist not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) GetAllInvoiceReinventTrafficSwitchConfig(context.Context, *emptypb.Empty) (*GetAllInvoiceReinventTrafficSwitchConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllInvoiceReinventTrafficSwitchConfig not implemented")
}
func (UnimplementedTrafficSwitchServiceServer) mustEmbedUnimplementedTrafficSwitchServiceServer() {}

// UnsafeTrafficSwitchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TrafficSwitchServiceServer will
// result in compilation errors.
type UnsafeTrafficSwitchServiceServer interface {
	mustEmbedUnimplementedTrafficSwitchServiceServer()
}

func RegisterTrafficSwitchServiceServer(s grpc.ServiceRegistrar, srv TrafficSwitchServiceServer) {
	s.RegisterService(&TrafficSwitchService_ServiceDesc, srv)
}

func _TrafficSwitchService_AddToSplitPaymentWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).AddToSplitPaymentWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToSplitPaymentWhitelist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).AddToSplitPaymentWhitelist(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_DeleteFromSplitPaymentWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).DeleteFromSplitPaymentWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/DeleteFromSplitPaymentWhitelist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).DeleteFromSplitPaymentWhitelist(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_AddToSplitPaymentBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).AddToSplitPaymentBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToSplitPaymentBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).AddToSplitPaymentBlacklist(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_DeleteFromSplitPaymentBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).DeleteFromSplitPaymentBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/DeleteFromSplitPaymentBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).DeleteFromSplitPaymentBlacklist(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_SetSplitPaymentTrafficRatio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).SetSplitPaymentTrafficRatio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/SetSplitPaymentTrafficRatio",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).SetSplitPaymentTrafficRatio(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_GetAllSplitTrafficSwitchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).GetAllSplitTrafficSwitchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/GetAllSplitTrafficSwitchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).GetAllSplitTrafficSwitchConfig(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_SetSplitPaymentLoanSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).SetSplitPaymentLoanSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/SetSplitPaymentLoanSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).SetSplitPaymentLoanSwitch(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_AddToInvoiceReinventWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrafficSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).AddToInvoiceReinventWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/AddToInvoiceReinventWhitelist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).AddToInvoiceReinventWhitelist(ctx, req.(*TrafficSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TrafficSwitchService_GetAllInvoiceReinventTrafficSwitchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TrafficSwitchServiceServer).GetAllInvoiceReinventTrafficSwitchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TrafficSwitchService/GetAllInvoiceReinventTrafficSwitchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TrafficSwitchServiceServer).GetAllInvoiceReinventTrafficSwitchConfig(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// TrafficSwitchService_ServiceDesc is the grpc.ServiceDesc for TrafficSwitchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TrafficSwitchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.TrafficSwitchService",
	HandlerType: (*TrafficSwitchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddToSplitPaymentWhitelist",
			Handler:    _TrafficSwitchService_AddToSplitPaymentWhitelist_Handler,
		},
		{
			MethodName: "DeleteFromSplitPaymentWhitelist",
			Handler:    _TrafficSwitchService_DeleteFromSplitPaymentWhitelist_Handler,
		},
		{
			MethodName: "AddToSplitPaymentBlacklist",
			Handler:    _TrafficSwitchService_AddToSplitPaymentBlacklist_Handler,
		},
		{
			MethodName: "DeleteFromSplitPaymentBlacklist",
			Handler:    _TrafficSwitchService_DeleteFromSplitPaymentBlacklist_Handler,
		},
		{
			MethodName: "SetSplitPaymentTrafficRatio",
			Handler:    _TrafficSwitchService_SetSplitPaymentTrafficRatio_Handler,
		},
		{
			MethodName: "GetAllSplitTrafficSwitchConfig",
			Handler:    _TrafficSwitchService_GetAllSplitTrafficSwitchConfig_Handler,
		},
		{
			MethodName: "SetSplitPaymentLoanSwitch",
			Handler:    _TrafficSwitchService_SetSplitPaymentLoanSwitch_Handler,
		},
		{
			MethodName: "AddToInvoiceReinventWhitelist",
			Handler:    _TrafficSwitchService_AddToInvoiceReinventWhitelist_Handler,
		},
		{
			MethodName: "GetAllInvoiceReinventTrafficSwitchConfig",
			Handler:    _TrafficSwitchService_GetAllInvoiceReinventTrafficSwitchConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/traffic_switch.proto",
}
