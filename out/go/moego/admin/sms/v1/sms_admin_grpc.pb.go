// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/sms/v1/sms_admin.proto

package smsapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SmsServiceClient is the client API for SmsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SmsServiceClient interface {
	// create sms
	CreateSms(ctx context.Context, in *CreateSmsRequest, opts ...grpc.CallOption) (*v1.SmsModel, error)
	// get sms
	GetSms(ctx context.Context, in *GetSmsRequest, opts ...grpc.CallOption) (*v1.SmsModel, error)
}

type smsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSmsServiceClient(cc grpc.ClientConnInterface) SmsServiceClient {
	return &smsServiceClient{cc}
}

func (c *smsServiceClient) CreateSms(ctx context.Context, in *CreateSmsRequest, opts ...grpc.CallOption) (*v1.SmsModel, error) {
	out := new(v1.SmsModel)
	err := c.cc.Invoke(ctx, "/moego.admin.sms.v1.SmsService/CreateSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsServiceClient) GetSms(ctx context.Context, in *GetSmsRequest, opts ...grpc.CallOption) (*v1.SmsModel, error) {
	out := new(v1.SmsModel)
	err := c.cc.Invoke(ctx, "/moego.admin.sms.v1.SmsService/GetSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmsServiceServer is the server API for SmsService service.
// All implementations must embed UnimplementedSmsServiceServer
// for forward compatibility
type SmsServiceServer interface {
	// create sms
	CreateSms(context.Context, *CreateSmsRequest) (*v1.SmsModel, error)
	// get sms
	GetSms(context.Context, *GetSmsRequest) (*v1.SmsModel, error)
	mustEmbedUnimplementedSmsServiceServer()
}

// UnimplementedSmsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSmsServiceServer struct {
}

func (UnimplementedSmsServiceServer) CreateSms(context.Context, *CreateSmsRequest) (*v1.SmsModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSms not implemented")
}
func (UnimplementedSmsServiceServer) GetSms(context.Context, *GetSmsRequest) (*v1.SmsModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSms not implemented")
}
func (UnimplementedSmsServiceServer) mustEmbedUnimplementedSmsServiceServer() {}

// UnsafeSmsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SmsServiceServer will
// result in compilation errors.
type UnsafeSmsServiceServer interface {
	mustEmbedUnimplementedSmsServiceServer()
}

func RegisterSmsServiceServer(s grpc.ServiceRegistrar, srv SmsServiceServer) {
	s.RegisterService(&SmsService_ServiceDesc, srv)
}

func _SmsService_CreateSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServiceServer).CreateSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.sms.v1.SmsService/CreateSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServiceServer).CreateSms(ctx, req.(*CreateSmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmsService_GetSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServiceServer).GetSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.sms.v1.SmsService/GetSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServiceServer).GetSms(ctx, req.(*GetSmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SmsService_ServiceDesc is the grpc.ServiceDesc for SmsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SmsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.sms.v1.SmsService",
	HandlerType: (*SmsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSms",
			Handler:    _SmsService_CreateSms_Handler,
		},
		{
			MethodName: "GetSms",
			Handler:    _SmsService_GetSms_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/sms/v1/sms_admin.proto",
}
