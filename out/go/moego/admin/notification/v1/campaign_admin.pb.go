// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/notification/v1/campaign_admin.proto

package notificationapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// StartNotificationCampaignParams
type StartNotificationCampaignParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// marketing campaign notification
	Notification *MarketingCampaignNotification `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	// target
	//
	// Types that are assignable to Target:
	//
	//	*StartNotificationCampaignParams_List
	//	*StartNotificationCampaignParams_Filter
	Target isStartNotificationCampaignParams_Target `protobuf_oneof:"target"`
	// staff filter
	StaffFilter *StaffFilter `protobuf:"bytes,10,opt,name=staff_filter,json=staffFilter,proto3" json:"staff_filter,omitempty"`
}

func (x *StartNotificationCampaignParams) Reset() {
	*x = StartNotificationCampaignParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartNotificationCampaignParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartNotificationCampaignParams) ProtoMessage() {}

func (x *StartNotificationCampaignParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartNotificationCampaignParams.ProtoReflect.Descriptor instead.
func (*StartNotificationCampaignParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{0}
}

func (x *StartNotificationCampaignParams) GetNotification() *MarketingCampaignNotification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (m *StartNotificationCampaignParams) GetTarget() isStartNotificationCampaignParams_Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (x *StartNotificationCampaignParams) GetList() *BusinessList {
	if x, ok := x.GetTarget().(*StartNotificationCampaignParams_List); ok {
		return x.List
	}
	return nil
}

func (x *StartNotificationCampaignParams) GetFilter() *BusinessFilter {
	if x, ok := x.GetTarget().(*StartNotificationCampaignParams_Filter); ok {
		return x.Filter
	}
	return nil
}

func (x *StartNotificationCampaignParams) GetStaffFilter() *StaffFilter {
	if x != nil {
		return x.StaffFilter
	}
	return nil
}

type isStartNotificationCampaignParams_Target interface {
	isStartNotificationCampaignParams_Target()
}

type StartNotificationCampaignParams_List struct {
	// business list
	List *BusinessList `protobuf:"bytes,2,opt,name=list,proto3,oneof"`
}

type StartNotificationCampaignParams_Filter struct {
	// business filter
	Filter *BusinessFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof"`
}

func (*StartNotificationCampaignParams_List) isStartNotificationCampaignParams_Target() {}

func (*StartNotificationCampaignParams_Filter) isStartNotificationCampaignParams_Target() {}

// MarketingCampaignNotification
type MarketingCampaignNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// title
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// body
	Body string `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	// mobile_title
	MobileTitle *string `protobuf:"bytes,4,opt,name=mobile_title,json=mobileTitle,proto3,oneof" json:"mobile_title,omitempty"`
	// mobile_body
	MobileBody *string `protobuf:"bytes,5,opt,name=mobile_body,json=mobileBody,proto3,oneof" json:"mobile_body,omitempty"`
	// extra
	Extra *MarketingCampaignExtraData `protobuf:"bytes,6,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	// author
	Author *string `protobuf:"bytes,7,opt,name=author,proto3,oneof" json:"author,omitempty"`
}

func (x *MarketingCampaignNotification) Reset() {
	*x = MarketingCampaignNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingCampaignNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingCampaignNotification) ProtoMessage() {}

func (x *MarketingCampaignNotification) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingCampaignNotification.ProtoReflect.Descriptor instead.
func (*MarketingCampaignNotification) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{1}
}

func (x *MarketingCampaignNotification) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarketingCampaignNotification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MarketingCampaignNotification) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *MarketingCampaignNotification) GetMobileTitle() string {
	if x != nil && x.MobileTitle != nil {
		return *x.MobileTitle
	}
	return ""
}

func (x *MarketingCampaignNotification) GetMobileBody() string {
	if x != nil && x.MobileBody != nil {
		return *x.MobileBody
	}
	return ""
}

func (x *MarketingCampaignNotification) GetExtra() *MarketingCampaignExtraData {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *MarketingCampaignNotification) GetAuthor() string {
	if x != nil && x.Author != nil {
		return *x.Author
	}
	return ""
}

// MarketingCampaignExtraData
type MarketingCampaignExtraData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign url
	CampaignUrl *string `protobuf:"bytes,1,opt,name=campaign_url,json=campaignUrl,proto3,oneof" json:"campaign_url,omitempty"`
	// icon url
	IconUrl *string `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3,oneof" json:"icon_url,omitempty"`
}

func (x *MarketingCampaignExtraData) Reset() {
	*x = MarketingCampaignExtraData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingCampaignExtraData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingCampaignExtraData) ProtoMessage() {}

func (x *MarketingCampaignExtraData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingCampaignExtraData.ProtoReflect.Descriptor instead.
func (*MarketingCampaignExtraData) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{2}
}

func (x *MarketingCampaignExtraData) GetCampaignUrl() string {
	if x != nil && x.CampaignUrl != nil {
		return *x.CampaignUrl
	}
	return ""
}

func (x *MarketingCampaignExtraData) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

// BusinessList
type BusinessList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BusinessList) Reset() {
	*x = BusinessList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessList) ProtoMessage() {}

func (x *BusinessList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessList.ProtoReflect.Descriptor instead.
func (*BusinessList) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessList) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// BusinessFilter
// https://moego.atlassian.net/browse/TECH-681
type BusinessFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business Type: mobile/salon
	BusinessType *v1.BusinessType `protobuf:"varint,1,opt,name=business_type,json=businessType,proto3,enum=moego.models.reporting.v1.BusinessType,oneof" json:"business_type,omitempty"`
	// Business Country
	BusinessCountry *string `protobuf:"bytes,2,opt,name=business_country,json=businessCountry,proto3,oneof" json:"business_country,omitempty"`
	// Monthly Rev: amount of finished invoice during last 30 days
	MonthlyRev *v2.Range `protobuf:"bytes,3,opt,name=monthly_rev,json=monthlyRev,proto3,oneof" json:"monthly_rev,omitempty"`
	// Total Units: # of locations and vans purchased in the latest plan
	TotalUnits *v2.Range `protobuf:"bytes,4,opt,name=total_units,json=totalUnits,proto3,oneof" json:"total_units,omitempty"`
	// Total Staff: # of staff on current staff list
	TotalStaff *v2.Range `protobuf:"bytes,5,opt,name=total_staff,json=totalStaff,proto3,oneof" json:"total_staff,omitempty"`
	// Total Appt: # of appointments created on calendar of last 30 days
	TotalAppt *v2.Range `protobuf:"bytes,6,opt,name=total_appt,json=totalAppt,proto3,oneof" json:"total_appt,omitempty"`
	// Total Finished Appt: # of appointments marked as finished in the last 30 days
	TotalFinishedAppt *v2.Range `protobuf:"bytes,7,opt,name=total_finished_appt,json=totalFinishedAppt,proto3,oneof" json:"total_finished_appt,omitempty"`
	// Total Msg Usage: # of msg estimated to be sent in the current billing period
	TotalMsgUsage *v2.Range `protobuf:"bytes,8,opt,name=total_msg_usage,json=totalMsgUsage,proto3,oneof" json:"total_msg_usage,omitempty"`
	// MoeGo Pay Status: Uninitialized/Operated
	MoegoPayStatus *v1.MoeGoPayStatus `protobuf:"varint,9,opt,name=moego_pay_status,json=moegoPayStatus,proto3,enum=moego.models.reporting.v1.MoeGoPayStatus,oneof" json:"moego_pay_status,omitempty"`
	// MoeGo Pay Count: # of transactions through MoeGo Pay last 30 days
	MoegoPayCount *v2.Range `protobuf:"bytes,10,opt,name=moego_pay_count,json=moegoPayCount,proto3,oneof" json:"moego_pay_count,omitempty"`
	// MoeGo Pay Transactions: amount of transactions through MoeGo Pay last 30 days
	MoegoPayTransactions *v2.Range `protobuf:"bytes,11,opt,name=moego_pay_transactions,json=moegoPayTransactions,proto3,oneof" json:"moego_pay_transactions,omitempty"`
	// OB Status: Not Enabled/ OB 2.0
	ObStatus *v1.OBStatus `protobuf:"varint,12,opt,name=ob_status,json=obStatus,proto3,enum=moego.models.reporting.v1.OBStatus,oneof" json:"ob_status,omitempty"`
	// OB Request: # of requests submitted through OB in the last 30 days
	ObRequests *v2.Range `protobuf:"bytes,13,opt,name=ob_requests,json=obRequests,proto3,oneof" json:"ob_requests,omitempty"`
	// Van Credits: # of vans purchased
	VanCredits *v2.Range `protobuf:"bytes,14,opt,name=van_credits,json=vanCredits,proto3,oneof" json:"van_credits,omitempty"`
	// Van Created: # of vans set up
	VanCreated *v2.Range `protobuf:"bytes,15,opt,name=van_created,json=vanCreated,proto3,oneof" json:"van_created,omitempty"`
	// Location Credits: # of locations purchased
	LocationCredits *v2.Range `protobuf:"bytes,16,opt,name=location_credits,json=locationCredits,proto3,oneof" json:"location_credits,omitempty"`
	// Location Created: # of locations set up
	LocationCreated *v2.Range `protobuf:"bytes,17,opt,name=location_created,json=locationCreated,proto3,oneof" json:"location_created,omitempty"`
	// Permission Level: 101, 102, 1001, 1002, 1003
	PermissionLevel []string `protobuf:"bytes,18,rep,name=permission_level,json=permissionLevel,proto3" json:"permission_level,omitempty"`
	// Role: Owner 所有数据默认为 company owner，不需要过滤
	// optional Role role = 19;
	// Metricized At: UTC data Time of yesterday
	MetricizedAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=metricized_at,json=metricizedAt,proto3" json:"metricized_at,omitempty"`
}

func (x *BusinessFilter) Reset() {
	*x = BusinessFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessFilter) ProtoMessage() {}

func (x *BusinessFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessFilter.ProtoReflect.Descriptor instead.
func (*BusinessFilter) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{4}
}

func (x *BusinessFilter) GetBusinessType() v1.BusinessType {
	if x != nil && x.BusinessType != nil {
		return *x.BusinessType
	}
	return v1.BusinessType(0)
}

func (x *BusinessFilter) GetBusinessCountry() string {
	if x != nil && x.BusinessCountry != nil {
		return *x.BusinessCountry
	}
	return ""
}

func (x *BusinessFilter) GetMonthlyRev() *v2.Range {
	if x != nil {
		return x.MonthlyRev
	}
	return nil
}

func (x *BusinessFilter) GetTotalUnits() *v2.Range {
	if x != nil {
		return x.TotalUnits
	}
	return nil
}

func (x *BusinessFilter) GetTotalStaff() *v2.Range {
	if x != nil {
		return x.TotalStaff
	}
	return nil
}

func (x *BusinessFilter) GetTotalAppt() *v2.Range {
	if x != nil {
		return x.TotalAppt
	}
	return nil
}

func (x *BusinessFilter) GetTotalFinishedAppt() *v2.Range {
	if x != nil {
		return x.TotalFinishedAppt
	}
	return nil
}

func (x *BusinessFilter) GetTotalMsgUsage() *v2.Range {
	if x != nil {
		return x.TotalMsgUsage
	}
	return nil
}

func (x *BusinessFilter) GetMoegoPayStatus() v1.MoeGoPayStatus {
	if x != nil && x.MoegoPayStatus != nil {
		return *x.MoegoPayStatus
	}
	return v1.MoeGoPayStatus(0)
}

func (x *BusinessFilter) GetMoegoPayCount() *v2.Range {
	if x != nil {
		return x.MoegoPayCount
	}
	return nil
}

func (x *BusinessFilter) GetMoegoPayTransactions() *v2.Range {
	if x != nil {
		return x.MoegoPayTransactions
	}
	return nil
}

func (x *BusinessFilter) GetObStatus() v1.OBStatus {
	if x != nil && x.ObStatus != nil {
		return *x.ObStatus
	}
	return v1.OBStatus(0)
}

func (x *BusinessFilter) GetObRequests() *v2.Range {
	if x != nil {
		return x.ObRequests
	}
	return nil
}

func (x *BusinessFilter) GetVanCredits() *v2.Range {
	if x != nil {
		return x.VanCredits
	}
	return nil
}

func (x *BusinessFilter) GetVanCreated() *v2.Range {
	if x != nil {
		return x.VanCreated
	}
	return nil
}

func (x *BusinessFilter) GetLocationCredits() *v2.Range {
	if x != nil {
		return x.LocationCredits
	}
	return nil
}

func (x *BusinessFilter) GetLocationCreated() *v2.Range {
	if x != nil {
		return x.LocationCreated
	}
	return nil
}

func (x *BusinessFilter) GetPermissionLevel() []string {
	if x != nil {
		return x.PermissionLevel
	}
	return nil
}

func (x *BusinessFilter) GetMetricizedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.MetricizedAt
	}
	return nil
}

// StaffFilter
type StaffFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// can access online booking and wait list
	CanAccessBookingRequests *wrapperspb.BoolValue `protobuf:"bytes,1,opt,name=can_access_booking_requests,json=canAccessBookingRequests,proto3,oneof" json:"can_access_booking_requests,omitempty"`
}

func (x *StaffFilter) Reset() {
	*x = StaffFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffFilter) ProtoMessage() {}

func (x *StaffFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffFilter.ProtoReflect.Descriptor instead.
func (*StaffFilter) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{5}
}

func (x *StaffFilter) GetCanAccessBookingRequests() *wrapperspb.BoolValue {
	if x != nil {
		return x.CanAccessBookingRequests
	}
	return nil
}

// SearchNotificationCampaignTargetsParams
type SearchNotificationCampaignTargetsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *BusinessFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// order by
	OrderBys []*v2.OrderBy `protobuf:"bytes,2,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchNotificationCampaignTargetsParams) Reset() {
	*x = SearchNotificationCampaignTargetsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchNotificationCampaignTargetsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchNotificationCampaignTargetsParams) ProtoMessage() {}

func (x *SearchNotificationCampaignTargetsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchNotificationCampaignTargetsParams.ProtoReflect.Descriptor instead.
func (*SearchNotificationCampaignTargetsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{6}
}

func (x *SearchNotificationCampaignTargetsParams) GetFilter() *BusinessFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *SearchNotificationCampaignTargetsParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *SearchNotificationCampaignTargetsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// SearchNotificationCampaignTargetsResult
type SearchNotificationCampaignTargetsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business infos
	Businesses []*NotificationTargetInfo `protobuf:"bytes,1,rep,name=businesses,proto3" json:"businesses,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *SearchNotificationCampaignTargetsResult) Reset() {
	*x = SearchNotificationCampaignTargetsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchNotificationCampaignTargetsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchNotificationCampaignTargetsResult) ProtoMessage() {}

func (x *SearchNotificationCampaignTargetsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchNotificationCampaignTargetsResult.ProtoReflect.Descriptor instead.
func (*SearchNotificationCampaignTargetsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{7}
}

func (x *SearchNotificationCampaignTargetsResult) GetBusinesses() []*NotificationTargetInfo {
	if x != nil {
		return x.Businesses
	}
	return nil
}

func (x *SearchNotificationCampaignTargetsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// notification target info
type NotificationTargetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business info
	Business *v11.BusinessModel `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
	// business metrics
	Metrics *v1.BusinessMetricsModel `protobuf:"bytes,2,opt,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *NotificationTargetInfo) Reset() {
	*x = NotificationTargetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationTargetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationTargetInfo) ProtoMessage() {}

func (x *NotificationTargetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationTargetInfo.ProtoReflect.Descriptor instead.
func (*NotificationTargetInfo) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP(), []int{8}
}

func (x *NotificationTargetInfo) GetBusiness() *v11.BusinessModel {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *NotificationTargetInfo) GetMetrics() *v1.BusinessMetricsModel {
	if x != nil {
		return x.Metrics
	}
	return nil
}

var File_moego_admin_notification_v1_campaign_admin_proto protoreflect.FileDescriptor

var file_moego_admin_notification_v1_campaign_admin_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xef, 0x02, 0x0a, 0x1f, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x68, 0x0a,
	0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x00, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x4b, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x0b, 0x73, 0x74, 0x61, 0x66, 0x66, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xf2, 0x02, 0x0a, 0x1d,
	0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x2f, 0x0a, 0x0c, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x01, 0x52, 0x0a, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x02, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a,
	0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52,
	0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x22, 0x9a, 0x01, 0x0a, 0x1a, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x32, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x88, 0x01,
	0x01, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x55, 0x72, 0x6c,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x88, 0x01,
	0x01, 0x48, 0x01, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x2a, 0x0a,
	0x0c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xfd, 0x0c, 0x0a, 0x0e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0d,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2e, 0x0a, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x3b, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x76, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x02, 0x52, 0x0a, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x52, 0x65, 0x76, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x70, 0x70, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x4a, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x06, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x70, 0x70, 0x74, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x07, 0x52,
	0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x58, 0x0a, 0x10, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x50, 0x61, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x08, 0x52, 0x0e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x50,
	0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x09, 0x52, 0x0d, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x50, 0x0a, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x0a, 0x52, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x50,
	0x61, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x45, 0x0a, 0x09, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x42, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x0b, 0x52, 0x08, 0x6f, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0b, 0x6f, 0x62, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x48, 0x0c, 0x52, 0x0a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0b, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x0d, 0x52, 0x0a, 0x76, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x3b, 0x0a, 0x0b, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x0e,
	0x52, 0x0a, 0x76, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x45, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x48, 0x0f, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x10, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a,
	0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x42, 0x36, 0xfa, 0x42, 0x33, 0x92, 0x01, 0x30, 0x22,
	0x2e, 0x72, 0x2c, 0x52, 0x03, 0x31, 0x30, 0x30, 0x52, 0x03, 0x31, 0x30, 0x31, 0x52, 0x03, 0x31,
	0x30, 0x32, 0x52, 0x03, 0x31, 0x30, 0x33, 0x52, 0x04, 0x31, 0x30, 0x30, 0x30, 0x52, 0x04, 0x31,
	0x30, 0x30, 0x31, 0x52, 0x04, 0x31, 0x30, 0x30, 0x32, 0x52, 0x04, 0x31, 0x30, 0x30, 0x33, 0x52,
	0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x49, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x69, 0x7a, 0x65, 0x64, 0x41, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x72,
	0x65, 0x76, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70,
	0x74, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x62, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x76, 0x61, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x0b, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x1b, 0x63, 0x61, 0x6e,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x18, 0x63, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x88, 0x01, 0x01, 0x42, 0x1e, 0x0a, 0x1c, 0x5f, 0x63, 0x61,
	0x6e, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0x81, 0x02, 0x0a, 0x27, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12,
	0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xc2, 0x01,
	0x0a, 0x27, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xa8, 0x01, 0x0a, 0x16, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a,
	0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x32, 0xb6, 0x02,
	0x0a, 0x0f, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x71, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0xaf, 0x01, 0x0a, 0x21, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8b, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_notification_v1_campaign_admin_proto_rawDescOnce sync.Once
	file_moego_admin_notification_v1_campaign_admin_proto_rawDescData = file_moego_admin_notification_v1_campaign_admin_proto_rawDesc
)

func file_moego_admin_notification_v1_campaign_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_notification_v1_campaign_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_notification_v1_campaign_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_notification_v1_campaign_admin_proto_rawDescData)
	})
	return file_moego_admin_notification_v1_campaign_admin_proto_rawDescData
}

var file_moego_admin_notification_v1_campaign_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_admin_notification_v1_campaign_admin_proto_goTypes = []interface{}{
	(*StartNotificationCampaignParams)(nil),         // 0: moego.admin.notification.v1.StartNotificationCampaignParams
	(*MarketingCampaignNotification)(nil),           // 1: moego.admin.notification.v1.MarketingCampaignNotification
	(*MarketingCampaignExtraData)(nil),              // 2: moego.admin.notification.v1.MarketingCampaignExtraData
	(*BusinessList)(nil),                            // 3: moego.admin.notification.v1.BusinessList
	(*BusinessFilter)(nil),                          // 4: moego.admin.notification.v1.BusinessFilter
	(*StaffFilter)(nil),                             // 5: moego.admin.notification.v1.StaffFilter
	(*SearchNotificationCampaignTargetsParams)(nil), // 6: moego.admin.notification.v1.SearchNotificationCampaignTargetsParams
	(*SearchNotificationCampaignTargetsResult)(nil), // 7: moego.admin.notification.v1.SearchNotificationCampaignTargetsResult
	(*NotificationTargetInfo)(nil),                  // 8: moego.admin.notification.v1.NotificationTargetInfo
	(v1.BusinessType)(0),                            // 9: moego.models.reporting.v1.BusinessType
	(*v2.Range)(nil),                                // 10: moego.utils.v2.Range
	(v1.MoeGoPayStatus)(0),                          // 11: moego.models.reporting.v1.MoeGoPayStatus
	(v1.OBStatus)(0),                                // 12: moego.models.reporting.v1.OBStatus
	(*timestamppb.Timestamp)(nil),                   // 13: google.protobuf.Timestamp
	(*wrapperspb.BoolValue)(nil),                    // 14: google.protobuf.BoolValue
	(*v2.OrderBy)(nil),                              // 15: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),                    // 16: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                   // 17: moego.utils.v2.PaginationResponse
	(*v11.BusinessModel)(nil),                       // 18: moego.models.business.v1.BusinessModel
	(*v1.BusinessMetricsModel)(nil),                 // 19: moego.models.reporting.v1.BusinessMetricsModel
	(*emptypb.Empty)(nil),                           // 20: google.protobuf.Empty
}
var file_moego_admin_notification_v1_campaign_admin_proto_depIdxs = []int32{
	1,  // 0: moego.admin.notification.v1.StartNotificationCampaignParams.notification:type_name -> moego.admin.notification.v1.MarketingCampaignNotification
	3,  // 1: moego.admin.notification.v1.StartNotificationCampaignParams.list:type_name -> moego.admin.notification.v1.BusinessList
	4,  // 2: moego.admin.notification.v1.StartNotificationCampaignParams.filter:type_name -> moego.admin.notification.v1.BusinessFilter
	5,  // 3: moego.admin.notification.v1.StartNotificationCampaignParams.staff_filter:type_name -> moego.admin.notification.v1.StaffFilter
	2,  // 4: moego.admin.notification.v1.MarketingCampaignNotification.extra:type_name -> moego.admin.notification.v1.MarketingCampaignExtraData
	9,  // 5: moego.admin.notification.v1.BusinessFilter.business_type:type_name -> moego.models.reporting.v1.BusinessType
	10, // 6: moego.admin.notification.v1.BusinessFilter.monthly_rev:type_name -> moego.utils.v2.Range
	10, // 7: moego.admin.notification.v1.BusinessFilter.total_units:type_name -> moego.utils.v2.Range
	10, // 8: moego.admin.notification.v1.BusinessFilter.total_staff:type_name -> moego.utils.v2.Range
	10, // 9: moego.admin.notification.v1.BusinessFilter.total_appt:type_name -> moego.utils.v2.Range
	10, // 10: moego.admin.notification.v1.BusinessFilter.total_finished_appt:type_name -> moego.utils.v2.Range
	10, // 11: moego.admin.notification.v1.BusinessFilter.total_msg_usage:type_name -> moego.utils.v2.Range
	11, // 12: moego.admin.notification.v1.BusinessFilter.moego_pay_status:type_name -> moego.models.reporting.v1.MoeGoPayStatus
	10, // 13: moego.admin.notification.v1.BusinessFilter.moego_pay_count:type_name -> moego.utils.v2.Range
	10, // 14: moego.admin.notification.v1.BusinessFilter.moego_pay_transactions:type_name -> moego.utils.v2.Range
	12, // 15: moego.admin.notification.v1.BusinessFilter.ob_status:type_name -> moego.models.reporting.v1.OBStatus
	10, // 16: moego.admin.notification.v1.BusinessFilter.ob_requests:type_name -> moego.utils.v2.Range
	10, // 17: moego.admin.notification.v1.BusinessFilter.van_credits:type_name -> moego.utils.v2.Range
	10, // 18: moego.admin.notification.v1.BusinessFilter.van_created:type_name -> moego.utils.v2.Range
	10, // 19: moego.admin.notification.v1.BusinessFilter.location_credits:type_name -> moego.utils.v2.Range
	10, // 20: moego.admin.notification.v1.BusinessFilter.location_created:type_name -> moego.utils.v2.Range
	13, // 21: moego.admin.notification.v1.BusinessFilter.metricized_at:type_name -> google.protobuf.Timestamp
	14, // 22: moego.admin.notification.v1.StaffFilter.can_access_booking_requests:type_name -> google.protobuf.BoolValue
	4,  // 23: moego.admin.notification.v1.SearchNotificationCampaignTargetsParams.filter:type_name -> moego.admin.notification.v1.BusinessFilter
	15, // 24: moego.admin.notification.v1.SearchNotificationCampaignTargetsParams.order_bys:type_name -> moego.utils.v2.OrderBy
	16, // 25: moego.admin.notification.v1.SearchNotificationCampaignTargetsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	8,  // 26: moego.admin.notification.v1.SearchNotificationCampaignTargetsResult.businesses:type_name -> moego.admin.notification.v1.NotificationTargetInfo
	17, // 27: moego.admin.notification.v1.SearchNotificationCampaignTargetsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	18, // 28: moego.admin.notification.v1.NotificationTargetInfo.business:type_name -> moego.models.business.v1.BusinessModel
	19, // 29: moego.admin.notification.v1.NotificationTargetInfo.metrics:type_name -> moego.models.reporting.v1.BusinessMetricsModel
	0,  // 30: moego.admin.notification.v1.CampaignService.StartNotificationCampaign:input_type -> moego.admin.notification.v1.StartNotificationCampaignParams
	6,  // 31: moego.admin.notification.v1.CampaignService.SearchNotificationCampaignTargets:input_type -> moego.admin.notification.v1.SearchNotificationCampaignTargetsParams
	20, // 32: moego.admin.notification.v1.CampaignService.StartNotificationCampaign:output_type -> google.protobuf.Empty
	7,  // 33: moego.admin.notification.v1.CampaignService.SearchNotificationCampaignTargets:output_type -> moego.admin.notification.v1.SearchNotificationCampaignTargetsResult
	32, // [32:34] is the sub-list for method output_type
	30, // [30:32] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_moego_admin_notification_v1_campaign_admin_proto_init() }
func file_moego_admin_notification_v1_campaign_admin_proto_init() {
	if File_moego_admin_notification_v1_campaign_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartNotificationCampaignParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingCampaignNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingCampaignExtraData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchNotificationCampaignTargetsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchNotificationCampaignTargetsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationTargetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*StartNotificationCampaignParams_List)(nil),
		(*StartNotificationCampaignParams_Filter)(nil),
	}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_campaign_admin_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_notification_v1_campaign_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_notification_v1_campaign_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_notification_v1_campaign_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_notification_v1_campaign_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_notification_v1_campaign_admin_proto = out.File
	file_moego_admin_notification_v1_campaign_admin_proto_rawDesc = nil
	file_moego_admin_notification_v1_campaign_admin_proto_goTypes = nil
	file_moego_admin_notification_v1_campaign_admin_proto_depIdxs = nil
}
