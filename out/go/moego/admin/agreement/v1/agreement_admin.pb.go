// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/agreement/v1/agreement_admin.proto

package agreementapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get agreement input
type GetAgreementParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAgreementParams) Reset() {
	*x = GetAgreementParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementParams) ProtoMessage() {}

func (x *GetAgreementParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementParams.ProtoReflect.Descriptor instead.
func (*GetAgreementParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{0}
}

func (x *GetAgreementParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAgreementParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get agreement model list params
type GetAgreementListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// service type
	ServiceType *int64 `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,oneof" json:"service_type,omitempty"`
	// agreement id list
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// status: normal, deleted
	Status *int32 `protobuf:"varint,4,opt,name=status,proto3,oneof" json:"status,omitempty"`
}

func (x *GetAgreementListParams) Reset() {
	*x = GetAgreementListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListParams) ProtoMessage() {}

func (x *GetAgreementListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListParams.ProtoReflect.Descriptor instead.
func (*GetAgreementListParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{1}
}

func (x *GetAgreementListParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetAgreementListParams) GetServiceType() int64 {
	if x != nil && x.ServiceType != nil {
		return *x.ServiceType
	}
	return 0
}

func (x *GetAgreementListParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetAgreementListParams) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

// agreement model list
type GetAgreementListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list
	AgreementSimpleView []*v1.AgreementModelSimpleView `protobuf:"bytes,1,rep,name=agreement_simple_view,json=agreementSimpleView,proto3" json:"agreement_simple_view,omitempty"`
	// signed policy map
	SignedPolicyMap map[int32]string `protobuf:"bytes,2,rep,name=signed_policy_map,json=signedPolicyMap,proto3" json:"signed_policy_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// status map
	StatusMap map[int32]string `protobuf:"bytes,3,rep,name=status_map,json=statusMap,proto3" json:"status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// service type map
	ServiceTypeMap map[int32]string `protobuf:"bytes,4,rep,name=service_type_map,json=serviceTypeMap,proto3" json:"service_type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetAgreementListResult) Reset() {
	*x = GetAgreementListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgreementListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgreementListResult) ProtoMessage() {}

func (x *GetAgreementListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgreementListResult.ProtoReflect.Descriptor instead.
func (*GetAgreementListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{2}
}

func (x *GetAgreementListResult) GetAgreementSimpleView() []*v1.AgreementModelSimpleView {
	if x != nil {
		return x.AgreementSimpleView
	}
	return nil
}

func (x *GetAgreementListResult) GetSignedPolicyMap() map[int32]string {
	if x != nil {
		return x.SignedPolicyMap
	}
	return nil
}

func (x *GetAgreementListResult) GetStatusMap() map[int32]string {
	if x != nil {
		return x.StatusMap
	}
	return nil
}

func (x *GetAgreementListResult) GetServiceTypeMap() map[int32]string {
	if x != nil {
		return x.ServiceTypeMap
	}
	return nil
}

func (x *GetAgreementListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// delete agreement params
type DeleteAgreementParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *DeleteAgreementParams) Reset() {
	*x = DeleteAgreementParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgreementParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgreementParams) ProtoMessage() {}

func (x *DeleteAgreementParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgreementParams.ProtoReflect.Descriptor instead.
func (*DeleteAgreementParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteAgreementParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteAgreementParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// DeleteAgreementResponse
type DeleteAgreementResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of delete
	Number int32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *DeleteAgreementResult) Reset() {
	*x = DeleteAgreementResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgreementResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgreementResult) ProtoMessage() {}

func (x *DeleteAgreementResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgreementResult.ProtoReflect.Descriptor instead.
func (*DeleteAgreementResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteAgreementResult) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

// UpdateAgreementParams
type UpdateAgreementParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// signed policy, see definition in SignedPolicy
	SignedPolicy *v1.SignedPolicy `protobuf:"varint,3,opt,name=signed_policy,json=signedPolicy,proto3,enum=moego.models.agreement.v1.SignedPolicy,oneof" json:"signed_policy,omitempty"`
	// agreement title
	AgreementTitle *string `protobuf:"bytes,4,opt,name=agreement_title,json=agreementTitle,proto3,oneof" json:"agreement_title,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,5,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// template for send sms
	SmsTemplate *string `protobuf:"bytes,6,opt,name=sms_template,json=smsTemplate,proto3,oneof" json:"sms_template,omitempty"`
	// email template title
	EmailTemplateTitle *string `protobuf:"bytes,7,opt,name=email_template_title,json=emailTemplateTitle,proto3,oneof" json:"email_template_title,omitempty"`
	// email template body
	EmailTemplateBody *string `protobuf:"bytes,8,opt,name=email_template_body,json=emailTemplateBody,proto3,oneof" json:"email_template_body,omitempty"`
	// whether to update last_required_time
	UpdateLastRequiredTime *bool `protobuf:"varint,9,opt,name=update_last_required_time,json=updateLastRequiredTime,proto3,oneof" json:"update_last_required_time,omitempty"`
}

func (x *UpdateAgreementParams) Reset() {
	*x = UpdateAgreementParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAgreementParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgreementParams) ProtoMessage() {}

func (x *UpdateAgreementParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgreementParams.ProtoReflect.Descriptor instead.
func (*UpdateAgreementParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateAgreementParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAgreementParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateAgreementParams) GetSignedPolicy() v1.SignedPolicy {
	if x != nil && x.SignedPolicy != nil {
		return *x.SignedPolicy
	}
	return v1.SignedPolicy(0)
}

func (x *UpdateAgreementParams) GetAgreementTitle() string {
	if x != nil && x.AgreementTitle != nil {
		return *x.AgreementTitle
	}
	return ""
}

func (x *UpdateAgreementParams) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *UpdateAgreementParams) GetSmsTemplate() string {
	if x != nil && x.SmsTemplate != nil {
		return *x.SmsTemplate
	}
	return ""
}

func (x *UpdateAgreementParams) GetEmailTemplateTitle() string {
	if x != nil && x.EmailTemplateTitle != nil {
		return *x.EmailTemplateTitle
	}
	return ""
}

func (x *UpdateAgreementParams) GetEmailTemplateBody() string {
	if x != nil && x.EmailTemplateBody != nil {
		return *x.EmailTemplateBody
	}
	return ""
}

func (x *UpdateAgreementParams) GetUpdateLastRequiredTime() bool {
	if x != nil && x.UpdateLastRequiredTime != nil {
		return *x.UpdateLastRequiredTime
	}
	return false
}

// AddAgreementParams
type AddAgreementParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// creator id
	CreatorId int64 `protobuf:"varint,2,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// signed policy, see definition in SignedPolicy
	SignedPolicy v1.SignedPolicy `protobuf:"varint,3,opt,name=signed_policy,json=signedPolicy,proto3,enum=moego.models.agreement.v1.SignedPolicy" json:"signed_policy,omitempty"`
	// service type, see definition in ServiceType
	ServiceTypes int32 `protobuf:"varint,4,opt,name=service_types,json=serviceTypes,proto3" json:"service_types,omitempty"`
	// agreement title
	AgreementTitle *string `protobuf:"bytes,5,opt,name=agreement_title,json=agreementTitle,proto3,oneof" json:"agreement_title,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,6,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// template for send sms
	SmsTemplate *string `protobuf:"bytes,7,opt,name=sms_template,json=smsTemplate,proto3,oneof" json:"sms_template,omitempty"`
	// email template title
	EmailTemplateTitle *string `protobuf:"bytes,8,opt,name=email_template_title,json=emailTemplateTitle,proto3,oneof" json:"email_template_title,omitempty"`
	// email template body
	EmailTemplateBody *string `protobuf:"bytes,9,opt,name=email_template_body,json=emailTemplateBody,proto3,oneof" json:"email_template_body,omitempty"`
	// business name
	BusinessName *string `protobuf:"bytes,10,opt,name=business_name,json=businessName,proto3,oneof" json:"business_name,omitempty"`
}

func (x *AddAgreementParams) Reset() {
	*x = AddAgreementParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAgreementParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAgreementParams) ProtoMessage() {}

func (x *AddAgreementParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAgreementParams.ProtoReflect.Descriptor instead.
func (*AddAgreementParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP(), []int{6}
}

func (x *AddAgreementParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AddAgreementParams) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *AddAgreementParams) GetSignedPolicy() v1.SignedPolicy {
	if x != nil {
		return x.SignedPolicy
	}
	return v1.SignedPolicy(0)
}

func (x *AddAgreementParams) GetServiceTypes() int32 {
	if x != nil {
		return x.ServiceTypes
	}
	return 0
}

func (x *AddAgreementParams) GetAgreementTitle() string {
	if x != nil && x.AgreementTitle != nil {
		return *x.AgreementTitle
	}
	return ""
}

func (x *AddAgreementParams) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *AddAgreementParams) GetSmsTemplate() string {
	if x != nil && x.SmsTemplate != nil {
		return *x.SmsTemplate
	}
	return ""
}

func (x *AddAgreementParams) GetEmailTemplateTitle() string {
	if x != nil && x.EmailTemplateTitle != nil {
		return *x.EmailTemplateTitle
	}
	return ""
}

func (x *AddAgreementParams) GetEmailTemplateBody() string {
	if x != nil && x.EmailTemplateBody != nil {
		return *x.EmailTemplateBody
	}
	return ""
}

func (x *AddAgreementParams) GetBusinessName() string {
	if x != nil && x.BusinessName != nil {
		return *x.BusinessName
	}
	return ""
}

var File_moego_admin_agreement_v1_agreement_admin_proto protoreflect.FileDescriptor

var file_moego_admin_agreement_v1_agreement_admin_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x45, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xdc, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00,
	0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcd, 0x05, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x67, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x71, 0x0a, 0x11, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x5e, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x6e, 0x0a,
	0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x42, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x42, 0x0a, 0x14, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x2f,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0xb6, 0x05, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0d, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x0f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x01,
	0x52, 0x0e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x11, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x40, 0x48, 0x02, 0x52, 0x10, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x33, 0x0a, 0x0c, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10,
	0x01, 0x18, 0x80, 0x80, 0x04, 0x48, 0x03, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80,
	0x02, 0x48, 0x04, 0x52, 0x12, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x13, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01,
	0x18, 0x80, 0x80, 0x08, 0x48, 0x05, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x19,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x06, 0x52, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x6d, 0x73,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xc1, 0x05, 0x0a, 0x12, 0x41, 0x64, 0x64,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x58, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x2c, 0x0a, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0f, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48, 0x00, 0x52, 0x0e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x3d, 0x0a, 0x11, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80, 0x80, 0x40, 0x48, 0x01, 0x52, 0x10, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x33, 0x0a, 0x0c, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18,
	0x80, 0x80, 0x04, 0x48, 0x02, 0x52, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x02, 0x48,
	0x03, 0x52, 0x12, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x13, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x80,
	0x80, 0x08, 0x48, 0x04, 0x52, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x05, 0x52, 0x0c, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x6d, 0x73,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xc0, 0x04, 0x0a,
	0x10, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x67, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x76, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x73, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x67, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42,
	0x82, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_agreement_v1_agreement_admin_proto_rawDescOnce sync.Once
	file_moego_admin_agreement_v1_agreement_admin_proto_rawDescData = file_moego_admin_agreement_v1_agreement_admin_proto_rawDesc
)

func file_moego_admin_agreement_v1_agreement_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_agreement_v1_agreement_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_agreement_v1_agreement_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_agreement_v1_agreement_admin_proto_rawDescData)
	})
	return file_moego_admin_agreement_v1_agreement_admin_proto_rawDescData
}

var file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_admin_agreement_v1_agreement_admin_proto_goTypes = []interface{}{
	(*GetAgreementParams)(nil),          // 0: moego.admin.agreement.v1.GetAgreementParams
	(*GetAgreementListParams)(nil),      // 1: moego.admin.agreement.v1.GetAgreementListParams
	(*GetAgreementListResult)(nil),      // 2: moego.admin.agreement.v1.GetAgreementListResult
	(*DeleteAgreementParams)(nil),       // 3: moego.admin.agreement.v1.DeleteAgreementParams
	(*DeleteAgreementResult)(nil),       // 4: moego.admin.agreement.v1.DeleteAgreementResult
	(*UpdateAgreementParams)(nil),       // 5: moego.admin.agreement.v1.UpdateAgreementParams
	(*AddAgreementParams)(nil),          // 6: moego.admin.agreement.v1.AddAgreementParams
	nil,                                 // 7: moego.admin.agreement.v1.GetAgreementListResult.SignedPolicyMapEntry
	nil,                                 // 8: moego.admin.agreement.v1.GetAgreementListResult.StatusMapEntry
	nil,                                 // 9: moego.admin.agreement.v1.GetAgreementListResult.ServiceTypeMapEntry
	(*v1.AgreementModelSimpleView)(nil), // 10: moego.models.agreement.v1.AgreementModelSimpleView
	(*v2.PaginationResponse)(nil),       // 11: moego.utils.v2.PaginationResponse
	(v1.SignedPolicy)(0),                // 12: moego.models.agreement.v1.SignedPolicy
	(*v1.AgreementModel)(nil),           // 13: moego.models.agreement.v1.AgreementModel
}
var file_moego_admin_agreement_v1_agreement_admin_proto_depIdxs = []int32{
	10, // 0: moego.admin.agreement.v1.GetAgreementListResult.agreement_simple_view:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	7,  // 1: moego.admin.agreement.v1.GetAgreementListResult.signed_policy_map:type_name -> moego.admin.agreement.v1.GetAgreementListResult.SignedPolicyMapEntry
	8,  // 2: moego.admin.agreement.v1.GetAgreementListResult.status_map:type_name -> moego.admin.agreement.v1.GetAgreementListResult.StatusMapEntry
	9,  // 3: moego.admin.agreement.v1.GetAgreementListResult.service_type_map:type_name -> moego.admin.agreement.v1.GetAgreementListResult.ServiceTypeMapEntry
	11, // 4: moego.admin.agreement.v1.GetAgreementListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	12, // 5: moego.admin.agreement.v1.UpdateAgreementParams.signed_policy:type_name -> moego.models.agreement.v1.SignedPolicy
	12, // 6: moego.admin.agreement.v1.AddAgreementParams.signed_policy:type_name -> moego.models.agreement.v1.SignedPolicy
	0,  // 7: moego.admin.agreement.v1.AgreementService.GetAgreement:input_type -> moego.admin.agreement.v1.GetAgreementParams
	1,  // 8: moego.admin.agreement.v1.AgreementService.GetAgreementList:input_type -> moego.admin.agreement.v1.GetAgreementListParams
	3,  // 9: moego.admin.agreement.v1.AgreementService.DeleteAgreement:input_type -> moego.admin.agreement.v1.DeleteAgreementParams
	5,  // 10: moego.admin.agreement.v1.AgreementService.UpdateAgreement:input_type -> moego.admin.agreement.v1.UpdateAgreementParams
	6,  // 11: moego.admin.agreement.v1.AgreementService.AddAgreement:input_type -> moego.admin.agreement.v1.AddAgreementParams
	13, // 12: moego.admin.agreement.v1.AgreementService.GetAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	2,  // 13: moego.admin.agreement.v1.AgreementService.GetAgreementList:output_type -> moego.admin.agreement.v1.GetAgreementListResult
	4,  // 14: moego.admin.agreement.v1.AgreementService.DeleteAgreement:output_type -> moego.admin.agreement.v1.DeleteAgreementResult
	13, // 15: moego.admin.agreement.v1.AgreementService.UpdateAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	13, // 16: moego.admin.agreement.v1.AgreementService.AddAgreement:output_type -> moego.models.agreement.v1.AgreementModel
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_admin_agreement_v1_agreement_admin_proto_init() }
func file_moego_admin_agreement_v1_agreement_admin_proto_init() {
	if File_moego_admin_agreement_v1_agreement_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgreementListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgreementParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgreementResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAgreementParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAgreementParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_agreement_v1_agreement_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_agreement_v1_agreement_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_agreement_v1_agreement_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_agreement_v1_agreement_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_agreement_v1_agreement_admin_proto = out.File
	file_moego_admin_agreement_v1_agreement_admin_proto_rawDesc = nil
	file_moego_admin_agreement_v1_agreement_admin_proto_goTypes = nil
	file_moego_admin_agreement_v1_agreement_admin_proto_depIdxs = nil
}
