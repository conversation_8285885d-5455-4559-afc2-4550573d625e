package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceFilterDTO extends CompanyCreateServiceDto {

    /**
     * breed filter
     * 0: all, 1: customized
     */
    private Byte breedFilter;

    /**
     * customized breed list
     */
    private List<ServicePetTypeBreedsDTO> customizedBreed;

    /**
     * weight filter
     * 0: all, 1: range
     */
    private Byte weightFilter;

    /**
     * weight range
     */
    private BigDecimal[] weightRange;

    /**
     * coat filter
     * 0: all, 1: customized
     */
    private Byte coatFilter;

    /**
     * customized coat list
     */
    private List<Integer> customizedCoat;

    /**
     * pet size filter
     */
    private Boolean petSizeFilter;

    /**
     * allowed pet size list
     */
    private List<Long> customizedPetSizes;

    /**
     * service filter, only for add-on
     */
    private Boolean serviceFilter;
}
