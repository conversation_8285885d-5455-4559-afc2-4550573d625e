package com.moego.server.grooming.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.WaitListService;
import com.moego.server.grooming.web.params.waitlist.AddWaitListParam;
import com.moego.server.grooming.web.params.waitlist.CalendarViewParam;
import com.moego.server.grooming.web.params.waitlist.FromAppointmentParam;
import com.moego.server.grooming.web.params.waitlist.GetWaitListParam;
import com.moego.server.grooming.web.params.waitlist.SmartScheduleParam;
import com.moego.server.grooming.web.params.waitlist.UpdateWaitListParam;
import com.moego.server.grooming.web.vo.waitlist.AvailableInfoVO;
import com.moego.server.grooming.web.vo.waitlist.CalendarViewVO;
import com.moego.server.grooming.web.vo.waitlist.SmartScheduleListVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListDetailVO;
import com.moego.server.grooming.web.vo.waitlist.WaitListListVO;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/wait-list")
@RequiredArgsConstructor
public class WaitListController {
    private final WaitListService waitListService;

    @PostMapping
    @Auth(AuthType.BUSINESS)
    public void addWaitList(AuthContext context, @Valid @RequestBody AddWaitListParam param) {
        waitListService.addWaitList(param.toBuilder()
                .businessId(context.businessId())
                .companyId(context.companyId())
                .tokenStaffId(context.staffId())
                .build());
    }

    @PutMapping
    @Auth(AuthType.BUSINESS)
    public void updateWaitList(AuthContext context, @Valid @RequestBody UpdateWaitListParam param) {
        waitListService.updateWaitList(param.toBuilder()
                .businessId(context.businessId())
                .companyId(context.companyId())
                .tokenStaffId(context.staffId())
                .build());
    }

    @DeleteMapping
    @Auth(AuthType.BUSINESS)
    public void deleteWaitList(AuthContext context, @RequestParam("waitListId") Long id) {
        waitListService.deleteWaitList(context.businessId(), id);
    }

    @GetMapping("/detail")
    @Auth(AuthType.BUSINESS)
    public WaitListDetailVO waitListDetail(AuthContext context, @RequestParam("waitListId") Long id) {
        return waitListService.waitListDetail(context.businessId(), context.getStaffId(), id);
    }

    @PostMapping("/list")
    @Auth(AuthType.BUSINESS)
    public WaitListListVO getWaitList(AuthContext context, @RequestBody GetWaitListParam param) {
        return waitListService.getWaitList(param.toBuilder()
                .businessId(context.businessId())
                .tokenStaffId(context.staffId())
                .companyId(context.companyId())
                .build());
    }

    @PostMapping("/appointment")
    @Auth(AuthType.BUSINESS)
    public void addWaitListFromAppointment(AuthContext context, @Valid @RequestBody FromAppointmentParam param) {
        waitListService.addFromAppointment(param.toBuilder()
                .businessId(context.businessId())
                .companyId(context.companyId())
                .tokenStaffId(context.staffId())
                .build());
    }

    @PostMapping("/smartSchedule")
    @Auth(AuthType.BUSINESS)
    public SmartScheduleListVO smartSchedule(AuthContext context, @Valid @RequestBody SmartScheduleParam param) {
        return waitListService.smartSchedule(param.toBuilder()
                .businessId(context.businessId())
                .tokenStaffId(context.staffId())
                .build());
    }

    @GetMapping("/availableInfoOnApptCancel")
    @Auth(AuthType.BUSINESS)
    public AvailableInfoVO getAvailableOnApptCancel(
            AuthContext context, @RequestParam("appointmentId") Long appointmentId) {
        return waitListService.getAvailableInfoOnApptCancel(context.companyId(), context.businessId(), appointmentId);
    }

    @PostMapping("/calendarView")
    @Auth(AuthType.BUSINESS)
    public List<CalendarViewVO> calendarView(AuthContext context, @RequestBody CalendarViewParam param) {
        return waitListService.calendarView(param.toBuilder()
                .businessId(context.businessId())
                .tokenStaffId(context.staffId())
                .companyId(context.companyId())
                .build());
    }

    @PostMapping("/transferNewWaitList")
    @Auth(AuthType.BUSINESS)
    public Integer transferNewWaitList(AuthContext context) {
        long companyId = context.companyId();
        Long businessId = context.businessId();
        if (businessId == null) {
            return 0;
        }
        return waitListService.transToNewWaitList(companyId, List.of(businessId));
    }
}
