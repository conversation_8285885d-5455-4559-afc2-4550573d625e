package com.moego.server.grooming.service;

import static com.moego.common.enums.RepeatConst.REPEAT_END_TYPE_TIMES;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.server.grooming.service.utils.RepeatUtil.checkRepeatParams;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.NewReminderSettingConst;
import com.moego.common.enums.RepeatConst;
import com.moego.common.enums.RepeatModifyTypeEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.GroomingRepeatDTO;
import com.moego.server.grooming.dto.MoeRepeatInfoDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.dto.RepeatPreviewInfoDTO;
import com.moego.server.grooming.dto.RepeatPreviewSummaryDTO;
import com.moego.server.grooming.dto.SaveRepeatAppointmentListResultDTO;
import com.moego.server.grooming.dto.StaffConflictInfoDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingRepeatMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingRepeat;
import com.moego.server.grooming.mapstruct.RepeatMapper;
import com.moego.server.grooming.params.AppointmentRepeatParams;
import com.moego.server.grooming.params.DeleteAppointmentParams;
import com.moego.server.grooming.params.PetDetailParams;
import com.moego.server.grooming.params.PreviewRepeatParams;
import com.moego.server.grooming.params.RepeatStaffInfoParams;
import com.moego.server.grooming.params.SaveRepeatAppointmentListParams;
import com.moego.server.grooming.params.SaveRepeatAppointmentParams;
import com.moego.server.grooming.params.SaveRepeatParams;
import com.moego.server.grooming.service.dto.DateAndStartTimeDTO;
import com.moego.server.grooming.service.dto.RepeatAppointmentsCollection;
import com.moego.server.grooming.service.utils.AppointmentUtil;
import com.moego.server.grooming.service.utils.RepeatUtil;
import com.moego.server.message.api.IMessageService;
import com.moego.server.message.dto.RepeatExpitySettingDto;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class MoeRepeatV2Service {

    private final MoeGroomingRepeatMapper moeGroomingRepeatMapper;
    private final MoeGroomingAppointmentService appointmentService;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final MoeAppointmentConflictCheckService appointmentConflictCheckService;
    private final SmartScheduleService smartScheduleService;
    private final ICustomerCustomerService iCustomerCustomerClient;
    private final IBusinessBusinessService iBusinessBusinessClient;
    private final IMessageService iMessageClient;
    private final AppointmentRepeatService appointmentRepeatService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    public AddResultDTO addRepeatRule(SaveRepeatParams params) {
        // customer 校验
        checkCustomer(params.getBusinessId(), params.getCustomerId());

        // 转成数据库对象
        MoeGroomingRepeat newRecord = RepeatUtil.convertSaveRepeatParamsToBean(params);
        Long currentTime = CommonUtil.get10Timestamp();
        newRecord.setCreateTime(currentTime);
        newRecord.setUpdateTime(currentTime);

        moeGroomingRepeatMapper.insertSelective(newRecord);
        return new AddResultDTO().setResult(true).setId(newRecord.getId());
    }

    public Boolean modifyRepeatRule(SaveRepeatParams params) {
        // modify 时校验入参
        checkRepeatRule(params.getRepeatId(), params.getBusinessId(), params.getCustomerId());
        checkRepeatParams(params);

        MoeGroomingRepeat updateRecord = RepeatUtil.convertSaveRepeatParamsToBean(params);
        updateRecord.setId(params.getRepeatId());
        updateRecord.setUpdateTime(CommonUtil.get10Timestamp());
        return moeGroomingRepeatMapper.updateByPrimaryKeySelective(updateRecord) > 0;
    }

    public MoeRepeatInfoDTO queryRepeatRule(Long companyId, Integer repeatId) {
        MoeGroomingRepeat repeat = moeGroomingRepeatMapper.selectByPrimaryKey(repeatId);
        if (repeat == null || !companyId.equals(repeat.getCompanyId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not found repeat info");
        }
        Integer businessId = repeat.getBusinessId();
        MoeRepeatInfoDTO repeatInfo = RepeatMapper.INSTANCE.beanToDTO(repeat);

        // 设置了过期提醒时，需要额外查询当前 repeat 剩余的 upcoming 数量，如果小于设置的过期提醒数量，则返回 upcomingCount 用于前端展示
        RepeatExpitySettingDto repeatExpirySetting = iMessageClient.getRepeatExpirySetting(businessId);
        if (repeatExpirySetting != null
                && repeatExpirySetting.getExpiryApptNum() >= NewReminderSettingConst.NO_EXPIRY_APPT_NUM) {
            Integer upcomingCount =
                    appointmentQueryService.countRepeatUpcomingCountForExpiryReminder(businessId, repeatId);
            if (upcomingCount < repeatExpirySetting.getExpiryApptNum()) {
                repeatInfo.setExpiryUpcomingCount(upcomingCount);
            }
        }

        return repeatInfo;
    }

    /**
     * 根据 repeat rule 计算出所有的日期
     * 如果是已存在的 repeat，需要过滤 history 日期，并基于最近一个 date 修改规则计算 upcoming
     */
    public RepeatPreviewSummaryDTO previewRepeatDay(PreviewRepeatParams params) {
        Integer businessId = params.getBusinessId();
        Integer repeatId = params.getRepeatId();
        Integer existAppointmentId = params.getExistAppointmentId();
        List<RepeatPreviewInfoDTO> resultList = new ArrayList<>();
        Integer updateTimes = params.getTimes();

        // isExtendRepeat: 是否为扩展 repeat，后面 SS 时不对已存在的 appt 重新 smart schedule
        // 1.当基于 existAppt 创建 repeat; 2. repeat rule 只修改了 times 或 endOn 时，isExtendRepeat = true
        boolean isExtendRepeat = existAppointmentId != null && repeatId == null;
        RepeatAppointmentsCollection existRepeatAppointments = null;
        int existTimes = 0;
        if (repeatId != null) {
            MoeGroomingRepeat repeat = moeGroomingRepeatMapper.selectByPrimaryKey(repeatId);
            if (repeat == null || !businessId.equals(repeat.getBusinessId())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "repeat rule not found");
            }
            isExtendRepeat = RepeatUtil.isExtendRepeat(params, repeat);

            BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(businessId);
            // 查询 repeat appointments: 按 history、upcoming finished、upcoming unfinished 分组
            existRepeatAppointments = getRepeatAppointments(businessId, repeatId, businessDateTime);
            existTimes = existRepeatAppointments.historyOrFinishCount();

            List<RepeatAppointmentDto> historyList = existRepeatAppointments.historyList();
            if (!CollectionUtils.isEmpty(historyList)) {
                resultList.addAll(historyList.stream()
                        .map(appointment -> buildRepeatPreviewInfo(appointment, true))
                        .toList());

                String latestDate = resultList.get(resultList.size() - 1).getDate();
                // 判断是否当前 repeat 是否不需要再计算
                if (isRepeatEnd(params, existTimes, latestDate)) {
                    return new RepeatPreviewSummaryDTO(
                            resultList, resultList.size(), 0, existRepeatAppointments.totalCount() - existTimes);
                }
                // 更新 repeat 参数
                if (REPEAT_END_TYPE_TIMES.equals(params.getType())) {
                    params.setTimes(params.getTimes() - existTimes + 1);
                }
                params.setStartsOn(latestDate);
            }
        }

        // 基于已有预约创建新 repeat 时，更新 resultList
        updateRepeatResultWithExistAppointment(params, resultList);
        // 根据 repeat rule 计算 repeat dates
        List<LocalDate> upcomingDates = new ArrayList<>(RepeatUtil.checkAndComputeDate(params));
        // 根据 startsOn 和 times 更新 upcoming 列表
        boolean containHistory = (existAppointmentId != null && repeatId == null)
                || resultList.stream().anyMatch(RepeatPreviewInfoDTO::getIsHistory);
        if (containHistory
                && !CollectionUtils.isEmpty(upcomingDates)
                && Objects.equals(upcomingDates.get(0).toString(), params.getStartsOn())) {
            upcomingDates.remove(0); // 去掉第一个日期，因为第一个日期是 history 的最后一个日期
        }
        // 有 history appointment 时，总数可能超过 repeat times，需要截取
        if (Objects.equals(params.getType(), REPEAT_END_TYPE_TIMES)
                && existTimes + upcomingDates.size() > updateTimes) {
            upcomingDates = (updateTimes > existTimes) ? upcomingDates.subList(0, updateTimes - existTimes) : List.of();
        }

        // 构建 upcoming 结果
        List<RepeatPreviewInfoDTO> upcomingResultList =
                buildRepeatUpcomingList(upcomingDates, params, existRepeatAppointments, isExtendRepeat);
        if (CollectionUtils.isEmpty(upcomingResultList) && CollectionUtils.isEmpty(resultList)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No matching date");
        }

        // 只对 upcoming 的日期进行 smart schedule
        if (Boolean.TRUE.equals(params.getSmartSchedule())) {
            optimizeRepeatDateBySmartScheduling(params, upcomingResultList, isExtendRepeat);
        }
        // 合并 history、upcoming
        resultList.addAll(upcomingResultList);
        // 按日期排序
        resultList.sort(Comparator.comparing(RepeatPreviewInfoDTO::getDate));

        int conflictCount = (int)
                resultList.stream().filter(result -> !result.getIsNotConflict()).count();
        int existUpcomingCount = existRepeatAppointments != null ? existRepeatAppointments.upcomingCount() : 0;
        int removedCount = Math.max(existUpcomingCount - upcomingResultList.size(), 0);
        return new RepeatPreviewSummaryDTO(resultList, resultList.size(), conflictCount, removedCount);
    }

    /**
     * 查询 repeat 预约，按 history、upcoming finished、upcoming unfinished 分组
     */
    private RepeatAppointmentsCollection getRepeatAppointments(
            Integer businessId, Integer repeatId, BusinessDateTimeDTO businessDateTime) {
        List<RepeatAppointmentDto> repeatAppointments = appointmentQueryService.getRepeatAppointmentList(
                businessId, repeatId, RepeatConst.REPEAT_APPOINTMENT_TYPE_ALL, true);

        int totalCount = 0;
        int upcomingCount = 0;
        int historyOrFinishCount = 0; // history + upcoming finished count
        List<RepeatAppointmentDto> historyList = new ArrayList<>();
        List<RepeatAppointmentDto> upcomingFinishedList = new ArrayList<>();
        List<RepeatAppointmentDto> upcomingUnfinishedList = new ArrayList<>();
        for (RepeatAppointmentDto appointment : repeatAppointments) {
            totalCount++;
            if (AppointmentUtil.isHistory(
                    appointment.getAppointmentDate(), appointment.getStartTime(), businessDateTime)) {
                historyList.add(appointment);
                historyOrFinishCount++;
            } else if (!Objects.equals(appointment.getStatus(), AppointmentStatusEnum.FINISHED.getValue())) {
                upcomingUnfinishedList.add(appointment);
                upcomingCount++;
            } else {
                historyOrFinishCount++;
                upcomingFinishedList.add(appointment);
            }
        }
        return new RepeatAppointmentsCollection(
                totalCount,
                upcomingCount,
                historyOrFinishCount,
                historyList,
                upcomingFinishedList,
                upcomingUnfinishedList);
    }

    /**
     * 根据已有的预约的数量或最新日期判断 repeat 是否结束
     */
    private boolean isRepeatEnd(PreviewRepeatParams repeatParams, Integer existTimes, String latestDate) {
        if (REPEAT_END_TYPE_TIMES.equals(repeatParams.getType())) {
            return repeatParams.getTimes() <= existTimes;
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return !LocalDate.parse(repeatParams.getSetEndOn(), formatter)
                    .isAfter(LocalDate.parse(latestDate, formatter));
        }
    }

    /**
     * 基于已有预约创建或更新 repeat 时，对结果的处理
     */
    private void updateRepeatResultWithExistAppointment(
            PreviewRepeatParams params, List<RepeatPreviewInfoDTO> resultList) {
        if (params.getExistAppointmentId() == null) {
            return;
        }
        RepeatAppointmentDto existAppointment = appointmentQueryService.getRepeatAppointmentById(
                params.getBusinessId(), params.getExistAppointmentId());
        if (existAppointment == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointment not found");
        }
        if (params.getRepeatId() == null) {
            // 基于已有预约创建新 repeat 时, 需要将已有预约加入到 resultList
            resultList.add(buildRepeatPreviewInfo(existAppointment, false));
        }
        // 修改 appointmentDate 时，需要对 repeatDate 偏移
        if (params.getUpdateAppointmentDate() != null) {
            // OB 提交的预约可能不带 appointmentDate，判空避免报错
            Long daysDiff = StringUtils.hasText(existAppointment.getAppointmentDate())
                    ? DateUtil.getDaysDiffByTwoDate(
                            existAppointment.getAppointmentDate(), params.getUpdateAppointmentDate())
                    : 0;
            params.setStartsOn(DateUtil.getStrDateByDaysDiff(params.getStartsOn(), daysDiff));
            resultList.stream()
                    .filter(info -> !info.getIsHistory())
                    .forEach(info -> info.setDate(DateUtil.getStrDateByDaysDiff(info.getDate(), daysDiff)));
        }
        // 已有预约修改 repeat 同时修改 appointmentStartTime 时，需要覆盖所有 upcoming 的 startTime
        if (params.getUpdateAppointmentStartTime() != null) {
            Integer updateStartTime = params.getUpdateAppointmentStartTime();
            resultList.stream()
                    .filter(info -> !info.getIsHistory())
                    .forEach(info -> info.setStartTime(updateStartTime));
        }
    }

    /**
     * 根据计算出来的 upcoming dates 计算返回结果
     */
    private List<RepeatPreviewInfoDTO> buildRepeatUpcomingList(
            List<LocalDate> upcomingDates,
            PreviewRepeatParams params,
            RepeatAppointmentsCollection existRepeatAppointments,
            boolean isExtendRepeat) {
        List<Integer> upcomingAppointmentIds = new ArrayList<>();
        Map<Integer, RepeatAppointmentDto> upcomingAppointmentMap = new HashMap<>();
        if (existRepeatAppointments != null) {
            List<RepeatAppointmentDto> upcomingAppointments = existRepeatAppointments.upcomingUnfinishedList();
            upcomingAppointmentIds = upcomingAppointments.stream()
                    .map(RepeatAppointmentDto::getAppointmentId)
                    .toList();
            upcomingAppointmentMap =
                    upcomingAppointments.stream().collect(toMap(RepeatAppointmentDto::getAppointmentId, identity()));
        }

        List<RepeatStaffInfoParams> staffInfoList = params.getRepeatStaffInfoParams();
        boolean requireStaff = staffInfoList.stream().allMatch(staffInfo -> isNormal(staffInfo.getStaffId()));
        // 构建 upcoming date 的返回对象
        List<RepeatPreviewInfoDTO> upcomingResultList = new ArrayList<>();
        for (int i = 0; i < upcomingDates.size(); i++) {
            LocalDate date = upcomingDates.get(i);
            RepeatAppointmentDto existAppointment = null;
            if (i < upcomingAppointmentIds.size()) {
                Integer appointmentId = upcomingAppointmentIds.get(i);
                existAppointment = upcomingAppointmentMap.get(appointmentId);
            }
            Integer updateStartTime = params.getUpdateAppointmentStartTime();
            upcomingResultList.add(buildRepeatPreviewInfo(
                    date, updateStartTime, staffInfoList, requireStaff, existAppointment, isExtendRepeat));
        }

        if (requireStaff) {
            // 查询 upcoming 日期是否有冲突：working hour, appointment, block check
            appointmentConflictCheckService.checkConflictForRepeatV2(upcomingResultList, params);
        }
        return upcomingResultList;
    }

    /**
     * ss for repeat 查询 repeat 日期
     *
     * @param previewDateList repeat 查询的结果
     * @param repeatParams    repeat 参数
     * @return 返回 repeat 日期信息
     */
    private void optimizeRepeatDateBySmartScheduling(
            PreviewRepeatParams repeatParams, List<RepeatPreviewInfoDTO> previewDateList, boolean isExtendRepeat) {
        // 套餐控制 TODO
        //        checkSsParamsAndAvailable(businessId, params);

        // 2.判断是否 ss for repeat，对所有时间进行 smart schedule，Mobile 会计算驾驶时间，Salon 不会
        Map<String, List<LocalDate>> needSsDateMap = new HashMap<>();
        for (RepeatPreviewInfoDTO repeatDay : previewDateList) {
            // 没有修改规则，只 extend repeat 时，existing appt 不需要重新 smart schedule
            if (repeatDay.getAppointmentId() != null && isExtendRepeat) {
                // TODO 这里暂时把schedule type 设置成 ss，等前端修改了 ss not available 的判断方式，再改成 normal
                repeatDay.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_SS);
                repeatDay.setIsNotConflict(true);
                continue;
            }
            List<LocalDate> needSsDateList = new ArrayList<>();
            LocalDate curDate = LocalDate.parse(repeatDay.getDate());
            needSsDateList.add(curDate);
            Integer before = repeatParams.getSsBeforeDays();
            Integer after = repeatParams.getSsAfterDays();

            // 2.1 将当前日期、前 beforeDays 和后 afterDays，按先后顺序加入列表，比如 repeatDate 是4月5号，before=3，after=2，=>
            // 4.5，4.4，4.6，4.3，4.7，4.2
            LocalDate beforeDate = curDate;
            LocalDate afterDate = curDate;
            while (before > 0 || after > 0) {
                if (before > 0) {
                    beforeDate = beforeDate.minusDays(1);
                    needSsDateList.add(beforeDate);
                    before--;
                }
                if (after > 0) {
                    afterDate = afterDate.plusDays(1);
                    needSsDateList.add(afterDate);
                    after--;
                }
            }
            needSsDateMap.put(repeatDay.getDate(), needSsDateList);
        }
        if (CollectionUtils.isEmpty(needSsDateMap)) {
            return;
        }

        // 2.2 smart schedule，返回优化过的时间，如果无返回，说明没有找到优化的时间或不需要优化
        Integer serviceDuration = repeatParams.getRepeatStaffInfoParams().stream()
                .mapToInt(RepeatStaffInfoParams::getServiceTime)
                .sum();
        // 获取ss重新查询计算后的date和start time
        Map<String, DateAndStartTimeDTO> rescheduleTimeSlot =
                smartScheduleService.smartScheduleForRepeat(repeatParams, serviceDuration, needSsDateMap);

        for (RepeatPreviewInfoDTO repeatDay : previewDateList) {
            if (repeatDay.getAppointmentId() != null && isExtendRepeat) {
                // 没有修改规则，只 extend repeat 时，existing appt 不需要重新 smart schedule
                continue;
            }
            // 2.3 把ss查询的date和start time更新到冲突的时间里
            if (rescheduleTimeSlot.containsKey(repeatDay.getDate())) {
                DateAndStartTimeDTO dto = rescheduleTimeSlot.get(repeatDay.getDate());
                repeatDay.setDate(dto.getDate());
                repeatDay.setStartTime(dto.getStartTime());
                repeatDay.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_SS);
                repeatDay.setIsNotConflict(true);
                // 遍历更新 staffConflictInfo
                int minStartTime = repeatDay.getStaffConflictInfoList().stream()
                        .map(StaffConflictInfoDTO::getStartTime)
                        .min(Integer::compareTo)
                        .orElse(0);
                int diff = dto.getStartTime() - minStartTime; // 优化后的时间和原来的时间差
                for (StaffConflictInfoDTO staffConflictInfo : repeatDay.getStaffConflictInfoList()) {
                    staffConflictInfo.setIsNotConflict(true);
                    staffConflictInfo.setStartTime(staffConflictInfo.getStartTime() + diff);
                }
            } else {
                repeatDay.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_SS_NOT_AVAILABLE);
            }
        }
    }

    public SaveRepeatAppointmentListResultDTO saveMultipleAppointmentList(SaveRepeatAppointmentListParams params) {
        if (CollectionUtils.isEmpty(params.getAppointmentList())) {
            return null;
        }
        List<SaveRepeatAppointmentParams> updateAppointments = new ArrayList<>();
        List<SaveRepeatAppointmentParams> addAppointments = new ArrayList<>();
        for (SaveRepeatAppointmentParams appointmentParams : params.getAppointmentList()) {
            appointmentParams.setCompanyId(params.getCompanyId());
            appointmentParams.setBusinessId(params.getBusinessId());
            appointmentParams.setStaffId(params.getStaffId());
            if (appointmentParams.getAppointmentId() != null) {
                appointmentParams.setSource(null); // 已存在的预约不更新 source 字段
                updateAppointments.add(appointmentParams);
            } else {
                addAppointments.add(appointmentParams);
            }
        }
        return batchSaveAppointmentList(params, addAppointments, updateAppointments, null);
    }

    // 保存 repeat appointment list
    public SaveRepeatAppointmentListResultDTO saveRepeatAppointmentList(SaveRepeatAppointmentListParams params) {
        // 检查参数
        List<Integer> customerIds = params.getAppointmentList().stream()
                .map(SaveRepeatAppointmentParams::getCustomerId)
                .distinct()
                .toList();
        if (customerIds.size() > 1) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "could not save repeat for multiple clients");
        }
        checkRepeatRule(params.getRepeatId(), params.getBusinessId(), customerIds.get(0));

        // FIX 普通 appt 转为 repeat appt 时，需要先把 repeat_id 关联上，否则后边逻辑无法查询出来
        appointmentRepeatService.updateNormalAppointmentToRepeat(params.getAppointmentId(), params.getRepeatId());
        // 查找已存在的 appointment
        Map<Integer, RepeatAppointmentDto> existAppointmentMap = appointmentQueryService
                .getRepeatAppointmentList(
                        params.getBusinessId(),
                        params.getRepeatId(),
                        RepeatConst.REPEAT_APPOINTMENT_TYPE_UPCOMING,
                        false)
                .stream()
                .collect(toMap(RepeatAppointmentDto::getAppointmentId, identity()));
        Set<Integer> updateAppointmentIds = new HashSet<>();
        List<SaveRepeatAppointmentParams> updateAppointments = new ArrayList<>();
        List<SaveRepeatAppointmentParams> addAppointments = new ArrayList<>();
        for (SaveRepeatAppointmentParams appointmentParams : params.getAppointmentList()) {
            appointmentParams.setCompanyId(params.getCompanyId());
            appointmentParams.setBusinessId(params.getBusinessId());
            appointmentParams.setStaffId(params.getStaffId());
            if (appointmentParams.getAppointmentId() != null) {
                // 只更新已存在的预约
                if (existAppointmentMap.containsKey(appointmentParams.getAppointmentId())) {
                    appointmentParams.setSource(null); // 已存在的预约不更新 source 字段
                    updateAppointments.add(appointmentParams);
                    updateAppointmentIds.add(appointmentParams.getAppointmentId());
                }
            } else {
                addAppointments.add(appointmentParams);
            }
        }
        // 需要删除的预约 id
        List<Integer> deleteAppointmentIds = existAppointmentMap.keySet().stream()
                .filter(id -> !updateAppointmentIds.contains(id))
                .toList();

        return batchSaveAppointmentList(params, addAppointments, updateAppointments, deleteAppointmentIds);
    }

    private SaveRepeatAppointmentListResultDTO batchSaveAppointmentList(
            SaveRepeatAppointmentListParams params,
            List<SaveRepeatAppointmentParams> addAppointments,
            List<SaveRepeatAppointmentParams> updateAppointments,
            List<Integer> deleteAppointmentIds) {
        int updateCount = 0;
        Integer notifyAppointmentId = null;
        Byte notifyType = null;

        // 在 repeat preview 时可能修改了某些 appointment 的 staff，这时候需要 apply staff 对应的 service
        applyCustomizedService(params, addAppointments, updateAppointments);

        // batch add new appointments
        if (!CollectionUtils.isEmpty(addAppointments)) {
            updateCount += addAppointments.size();

            Integer firstAddId = batchAddAppointment(addAppointments, params.getRepeatId(), params.getBusinessId());
            if (firstAddId != null) {
                notifyAppointmentId = firstAddId;
                notifyType = SaveRepeatAppointmentListResultDTO.NOTIFY_TYPE_CREATED;
            }
        }
        // batch update exist appointments
        if (!CollectionUtils.isEmpty(updateAppointments)) {
            updateCount += updateAppointments.size();

            Integer firstRescheduleId = batchModifyAppointmentWithoutNotify(updateAppointments);
            if (firstRescheduleId != null) {
                // 当有 reschedule 时，优先通知 reschedule
                notifyAppointmentId = firstRescheduleId;
                notifyType = SaveRepeatAppointmentListResultDTO.NOTIFY_TYPE_RESCHEDULED;
            }
        }
        // batch delete repeat 不在当前更新列表的 appointment
        if (!CollectionUtils.isEmpty(deleteAppointmentIds)) {
            updateCount += batchDeleteAppointment(params.getBusinessId(), deleteAppointmentIds);
        }

        // 在已有预约基础上新增 repeat 需把当前预约加入 repeat
        if (params.getAppointmentId() != null) {
            appointmentRepeatService.updateRepeatAppointment(params.getAppointmentId(), params.getRepeatId());
        }

        return new SaveRepeatAppointmentListResultDTO()
                .setUpdatedCount(updateCount)
                .setNotifyAppointmentId(notifyAppointmentId)
                .setNotifyType(notifyType);
    }

    private void applyCustomizedService(
            SaveRepeatAppointmentListParams param,
            List<SaveRepeatAppointmentParams> addAppointments,
            List<SaveRepeatAppointmentParams> updateAppointments) {

        var allAppointments = Stream.concat(addAppointments.stream(), updateAppointments.stream())
                .toList();

        var customizedServiceList = listCustomizedService(param, allAppointments);
        var defaultServiceMap = listDefaultService(param, allAppointments);

        for (var appointment : allAppointments) {
            for (var petDetail : appointment.getServiceList()) {
                var customizedService = findCustomizedService(
                        customizedServiceList, petDetail.getServiceId(), petDetail.getPetId(), petDetail.getStaffId());
                if (customizedService == null) {
                    continue;
                }

                var defaultService =
                        defaultServiceMap.get(petDetail.getServiceId().longValue());

                applyDuration(customizedService, defaultService, petDetail);
                applyPrice(customizedService, defaultService, petDetail);
            }
        }
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            int serviceId,
            @Nullable Integer petId,
            @Nullable Integer staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private static void applyDuration(
            CustomizedServiceView customizedService,
            CustomizedServiceView defaultService,
            PetDetailParams inputPetDetail) {

        if (isManuallyInputDuration(inputPetDetail, defaultService)) {
            inputPetDetail.setDurationOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED);
        } else {
            inputPetDetail.setServiceTime(customizedService.getDuration());
            inputPetDetail.setDurationOverrideType(customizedService.getDurationOverrideType());
        }
    }

    private static boolean isManuallyInputDuration(
            PetDetailParams inputPetDetail, CustomizedServiceView defaultService) {
        return (inputPetDetail.getDurationOverrideType() == null
                        || inputPetDetail.getDurationOverrideType()
                                == ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                && defaultService != null
                && inputPetDetail.getServiceTime() != defaultService.getDuration();
    }

    private static void applyPrice(
            CustomizedServiceView customizedService,
            CustomizedServiceView defaultService,
            PetDetailParams inputPetDetail) {

        if (isManuallyInputPrice(inputPetDetail, defaultService)) {
            inputPetDetail.setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED);
        } else {
            inputPetDetail.setServicePrice(BigDecimal.valueOf(customizedService.getPrice()));
            inputPetDetail.setPriceOverrideType(customizedService.getPriceOverrideType());
        }
    }

    private static boolean isManuallyInputPrice(PetDetailParams inputPetDetail, CustomizedServiceView defaultService) {
        return (inputPetDetail.getPriceOverrideType() == null
                        || inputPetDetail.getPriceOverrideType()
                                == ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                && defaultService != null
                && inputPetDetail.getServicePrice().doubleValue() != defaultService.getPrice();
    }

    private Map<Long, CustomizedServiceView> listDefaultService(
            SaveRepeatAppointmentListParams param, List<SaveRepeatAppointmentParams> allAppointments) {

        var serviceIdList = allAppointments.stream()
                .map(SaveRepeatAppointmentParams::getServiceList)
                .flatMap(List::stream)
                .map(PetDetailParams::getServiceId)
                .collect(Collectors.toSet());

        var b = BatchGetCustomizedServiceRequest.newBuilder();
        b.setCompanyId(param.getCompanyId());
        for (var serviceId : serviceIdList) {
            var cb = CustomizedServiceQueryCondition.newBuilder();
            cb.setBusinessId(param.getBusinessId());
            cb.setServiceId(serviceId);
            b.addQueryConditionList(cb.build());
        }

        return serviceStub.batchGetCustomizedService(b.build()).getCustomizedServiceListList().stream()
                .collect(toMap(
                        e -> e.getQueryCondition().getServiceId(),
                        BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService,
                        (o, n) -> o));
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            SaveRepeatAppointmentListParams param, List<SaveRepeatAppointmentParams> allAppointments) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(param.getCompanyId());
        for (var appointment : allAppointments) {
            for (var petDetail : appointment.getServiceList()) {
                var cb = CustomizedServiceQueryCondition.newBuilder();
                cb.setBusinessId(param.getBusinessId());
                cb.setServiceId(petDetail.getServiceId());
                if (isNormal(petDetail.getPetId())) {
                    cb.setPetId(petDetail.getPetId());
                }
                if (isNormal(petDetail.getStaffId())) { // BD appointment repeat 可能没有 staffId
                    cb.setStaffId(petDetail.getStaffId());
                }
                builder.addQueryConditionList(cb.build());
            }
        }

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    public Integer batchModifyAppointmentWithoutNotify(List<SaveRepeatAppointmentParams> updateAppointments) {
        if (CollectionUtils.isEmpty(updateAppointments)) {
            return null;
        }

        Set<Integer> appointmentIdList = updateAppointments.stream()
                .map(SaveRepeatAppointmentParams::getAppointmentId)
                .collect(Collectors.toSet());

        Map<Integer, AppointmentWithPetDetailsDto> appointmentMap =
                appointmentQueryService.getRepeatedAppointmentListWithPetDetails(appointmentIdList).stream()
                        .collect(toMap(AppointmentWithPetDetailsDto::getAppointmentId, identity()));

        // 更新 appointment service start time
        updateAppointments.forEach(params -> appointmentService.setServiceStartTime(
                params.getServiceList(), params.getAppointmentStartTime(), params.getAllPetsStartAtSameTime()));
        // 找到第一个需要 reschedule 的 appointment id，用于返回给前端发送 reschedule 通知
        Integer firstRescheduleId = updateAppointments.stream()
                .filter(appointmentParams -> AppointmentUtil.needRescheduleAppointment(
                        appointmentParams, appointmentMap.get(appointmentParams.getAppointmentId())))
                .findFirst()
                .map(SaveRepeatAppointmentParams::getAppointmentId)
                .orElse(null);

        // 先更新第一个，剩下的异步更新
        modifyRepeatAppointmentWithoutNotify(updateAppointments.get(0), appointmentMap, true);
        if (updateAppointments.size() > 1) {
            List<SaveRepeatAppointmentParams> followingAppointments =
                    updateAppointments.subList(1, updateAppointments.size());
            ThreadPool.execute(() -> followingAppointments.forEach(
                    params -> modifyRepeatAppointmentWithoutNotify(params, appointmentMap, false)));
        }
        return firstRescheduleId;
    }

    private void modifyRepeatAppointmentWithoutNotify(
            SaveRepeatAppointmentParams params,
            Map<Integer, AppointmentWithPetDetailsDto> appointmentMap,
            boolean isFirst) {
        var modifyParams = RepeatMapper.INSTANCE.toAppointmentRepeatModifyParams(params);
        modifyParams.setRepeatType(RepeatModifyTypeEnum.ONLY_THIS.getRepeatType()); // 单个预约更新
        modifyParams.setIsGcSyncDelay(!isFirst);

        AppointmentWithPetDetailsDto appointmentWithPetDetails = appointmentMap.get(params.getAppointmentId());
        if (appointmentWithPetDetails == null) {
            return;
        }
        MoeGroomingAppointment appointment =
                appointmentService.getAppointment(appointmentWithPetDetails.getAppointmentId());
        appointmentService.modifyAppointmentRepeatWithoutNotify(appointment, modifyParams);
    }

    public Integer batchAddAppointment(
            List<SaveRepeatAppointmentParams> addAppointments, Integer repeatId, Integer businessId) {
        if (CollectionUtils.isEmpty(addAppointments)) {
            return null;
        }
        List<AppointmentRepeatParams> appointmentRepeatParams = addAppointments.stream()
                .map(params ->
                        RepeatMapper.INSTANCE.toAppointmentRepeatParams(params, params.getPreAuthParams(), repeatId))
                .toList();
        return appointmentService
                .batchAddAppointments(businessId, appointmentRepeatParams)
                .getId();
    }

    public Integer batchDeleteAppointment(Integer businessId, List<Integer> deleteAppointmentIds) {
        DeleteAppointmentParams deleteParams = new DeleteAppointmentParams();
        deleteParams.setIds(deleteAppointmentIds);
        return appointmentService.deleteAppointment(businessId, deleteParams);
    }

    private void checkCustomer(Integer businessId, Integer customerId) {
        if (businessId == null || customerId == null) {
            return;
        }
        if (!iCustomerCustomerClient.checkingBizByCustomerId(businessId, customerId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "customer mismatch");
        }
    }

    public void checkRepeatRule(Integer repeatId, Integer businessId, Integer customerId) {
        if (repeatId == null || repeatId <= 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "repeat id is required");
        }
        MoeGroomingRepeat moeGroomingRepeat = moeGroomingRepeatMapper.selectByPrimaryKey(repeatId);
        if (moeGroomingRepeat == null || !Objects.equals(moeGroomingRepeat.getBusinessId(), businessId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "repeat rule not found");
        }
        // 检查 customer，避免数据错乱
        if (customerId != null && !Objects.equals(moeGroomingRepeat.getCustomerId(), customerId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "repeat rule cannot change client");
        }
    }

    private RepeatPreviewInfoDTO buildRepeatPreviewInfo(RepeatAppointmentDto appt, boolean isHistory) {
        RepeatPreviewInfoDTO dto = new RepeatPreviewInfoDTO();
        dto.setStaffIdList(appt.getStaffIdList());
        dto.setDate(appt.getAppointmentDate());
        dto.setStartTime(appt.getStartTime());
        dto.setDuration(appt.getEndTime() - appt.getStartTime());
        dto.setIsNotConflict(true);
        dto.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_NORMAL);
        dto.setIsHistory(isHistory);
        dto.setAppointmentId(appt.getAppointmentId());
        return dto;
    }

    private RepeatPreviewInfoDTO buildRepeatPreviewInfo(
            LocalDate date,
            Integer updateStartTime,
            List<RepeatStaffInfoParams> staffInfoList,
            boolean requireStaff,
            RepeatAppointmentDto existAppointment,
            boolean isExtendRepeat) {
        RepeatPreviewInfoDTO previewInfo = new RepeatPreviewInfoDTO();
        previewInfo.setDate(date.toString());
        previewInfo.setIsHistory(false);
        previewInfo.setIsNotConflict(true);
        previewInfo.setScheduleType(RepeatConst.REPEAT_SCHEDULE_TYPE_NORMAL);
        if (requireStaff) {
            previewInfo.setStaffIdList(staffInfoList.stream()
                    .map(RepeatStaffInfoParams::getStaffId)
                    .distinct()
                    .toList());
            Integer startTime = updateStartTime != null
                    ? updateStartTime
                    : staffInfoList.stream()
                            .map(RepeatStaffInfoParams::getStartTime)
                            .min(Integer::compareTo)
                            .orElse(0);
            Integer duration = staffInfoList.stream()
                    .map(RepeatStaffInfoParams::getServiceTime)
                    .reduce(Integer::sum)
                    .orElse(0);
            previewInfo.setStartTime(startTime);
            previewInfo.setDuration(duration);
            previewInfo.setEndTime(startTime + duration);
            // 每个 staff 的 startTime、duration
            Integer minStartTime = staffInfoList.stream()
                    .map(RepeatStaffInfoParams::getStartTime)
                    .min(Integer::compareTo)
                    .orElse(0);
            Integer diff = startTime - minStartTime;
            previewInfo.setStaffConflictInfoList(staffInfoList.stream()
                    .map(staff -> new StaffConflictInfoDTO()
                            .setStaffId(staff.getStaffId())
                            .setStartTime(staff.getStartTime() + diff)
                            .setDuration(staff.getServiceTime())
                            .setIsNotConflict(true))
                    .toList());
        } else {
            Integer startTime = updateStartTime != null
                    ? updateStartTime
                    : staffInfoList.stream()
                            .map(RepeatStaffInfoParams::getStartTime)
                            .min(Integer::compareTo)
                            .orElse(0);
            previewInfo.setStartTime(startTime);
            Integer duration = staffInfoList.stream()
                    .map(RepeatStaffInfoParams::getServiceTime)
                    .reduce(Integer::sum)
                    .orElse(0);
            previewInfo.setDuration(duration);
            previewInfo.setEndTime(startTime + duration);
            previewInfo.setStaffIdList(List.of());
            previewInfo.setStaffConflictInfoList(List.of());
        }

        // 复用已存在的 appointment startTime、staff 信息
        if (existAppointment != null) {
            if (isExtendRepeat) {
                // 当没有改变 repeat rule，仅延长 repeat 时，已存在的预约日期不修改
                previewInfo.setDate(existAppointment.getAppointmentDate());
            }
            previewInfo.setAppointmentId(existAppointment.getAppointmentId());
            previewInfo.setStaffIdList(existAppointment.getStaffIdList());
            previewInfo.setStartTime(updateStartTime != null ? updateStartTime : existAppointment.getStartTime());
            previewInfo.setDuration(existAppointment.getEndTime() - existAppointment.getStartTime());
            previewInfo.setStaffConflictInfoList(existAppointment.getStaffIdList().stream()
                    .map(staffId -> new StaffConflictInfoDTO()
                            .setStaffId(staffId)
                            .setStartTime(existAppointment.getStartTime())
                            .setDuration(existAppointment.getEndTime() - existAppointment.getStartTime())
                            .setIsNotConflict(true))
                    .toList());
        }

        return previewInfo;
    }

    public MoeRepeatInfoDTO getRepeatInfoWithAppointments(Long tokenCompanyId, Integer repeatId) {
        GroomingRepeatDTO repeatDTO = queryRepeatRule(tokenCompanyId, repeatId);
        MoeRepeatInfoDTO repeatInfoDTO = RepeatMapper.INSTANCE.copyToRepeatInfoDTO(repeatDTO);

        repeatInfoDTO.setStartsOnDate(repeatDTO.getStartsOn());
        repeatInfoDTO.setSetEndOnDate(repeatDTO.getSetEndOn());

        // 不包括 finish 状态
        List<AppointmentStatusEnum> statusList = AppointmentStatusSet.IN_PROGRESS_STATUS_SET;

        List<RepeatAppointmentDto> maps = moeGroomingAppointmentMapper.selectRepeatAppointmentList(
                repeatDTO.getBusinessId(), repeatId, statusList);

        repeatInfoDTO.setExistsAppointments(maps);
        return repeatInfoDTO;
    }
}
