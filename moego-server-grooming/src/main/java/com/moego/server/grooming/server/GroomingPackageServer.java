package com.moego.server.grooming.server;

import com.moego.common.constant.CommonConstant;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.api.IGroomingPackageServiceBase;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.grooming.dto.GroomingPackageUseDetailDTO;
import com.moego.server.grooming.dto.PackageServiceDTO;
import com.moego.server.grooming.enums.PackageActivityEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingPackage;
import com.moego.server.grooming.mapperbean.MoeGroomingPackageHistory;
import com.moego.server.grooming.mapstruct.GroomingPackageMapper;
import com.moego.server.grooming.params.GetPackageParams;
import com.moego.server.grooming.params.GetPackageServicesParams;
import com.moego.server.grooming.params.GetPackagesParams;
import com.moego.server.grooming.params.UpdatePackageParams;
import com.moego.server.grooming.params.UpdatePackageServiceParams;
import com.moego.server.grooming.result.GetPackageResult;
import com.moego.server.grooming.service.MoePackageService;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class GroomingPackageServer extends IGroomingPackageServiceBase {

    private final MoePackageService packageService;

    @Override
    public GetPackageResult getPackage(GetPackageParams getPackageParams) {
        var pkg = packageService.queryGroomingPackage(null, getPackageParams.packageId());
        if (pkg == null) {
            return new GetPackageResult(null);
        }

        var dto = new GroomingPackageDTO();
        BeanUtils.copyProperties(pkg, dto);
        return new GetPackageResult(dto);
    }

    @Override
    public Boolean invalidPackageUsedHistory(List<Integer> invoiceIds) {
        packageService.invalidPackageUsedHistory(invoiceIds);
        return Boolean.TRUE;
    }

    @Override
    public List<GroomingPackageDTO> getPackages(GetPackagesParams getPackagesParams) {
        return packageService.queryCustomerPackages(
                getPackagesParams.companyId(),
                getPackagesParams.customerIds(),
                getPackagesParams.businessId(),
                getPackagesParams.currentDate());
    }

    @Override
    public List<GroomingPackageServiceDTO> getPackageServices(GetPackageServicesParams getPackageServicesParams) {
        return packageService.queryPackageServices(getPackageServicesParams.packageIds());
    }

    @Override
    public List<GroomingPackageUseDetailDTO> getGroomingPackageDetail(List<Long> groomingPackageIdList) {
        List<GroomingPackageUseDetailDTO> infoDtoList = new ArrayList<>();
        for (Long groomingPackageId : groomingPackageIdList) {
            var customerPkgDetail = packageService.queryGroomingPackage(null, groomingPackageId.intValue());

            var packageInfoListDto = packageService.queryCustomerPackageInfoById(groomingPackageId.intValue());
            var packageInfo = GroomingPackageMapper.INSTANCE.beanToDTO(customerPkgDetail);
            infoDtoList.add(new GroomingPackageUseDetailDTO(
                    packageInfo, packageInfoListDto.getPackageServices(), packageInfoListDto.getPackageHistories()));
        }
        return infoDtoList;
    }

    @Override
    public Boolean updatePackage(UpdatePackageParams updatePackageParams) {
        if (updatePackageParams.getPackageId() == 0 || updatePackageParams.getExtendValidityDay() == 0) {
            return false;
        }

        MoeGroomingPackage moeGroomingPackage = packageService.queryGroomingPackage(
                updatePackageParams.getBusinessId().intValue(),
                updatePackageParams.getPackageId().intValue());
        if (moeGroomingPackage == null) {
            return false;
        }

        // 如果是永久有效的套餐，无需延长有效期
        if (updatePackageParams.getExtendValidityDay() == 0
                || moeGroomingPackage.getExpirationDate().equals(GroomingPackageDTO.ExpirationDate.NEVER_EXPIRE)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "ExtendValidityDay is 0 or package is never expire");
        }

        try {
            moeGroomingPackage.setExpirationDate(DateUtil.daysBeforeAfter(
                    moeGroomingPackage.getExpirationDate(), updatePackageParams.getExtendValidityDay()));
            moeGroomingPackage.setEndTime(moeGroomingPackage.getEndTime()
                    + TimeUnit.DAYS.toSeconds(updatePackageParams.getExtendValidityDay()));
        } catch (ParseException e) {
            log.error("updatePackage error", e);
            return false;
        }

        moeGroomingPackage.setUpdateTime(DateUtil.get10Timestamp());
        packageService.updatePackage(moeGroomingPackage);

        MoeGroomingPackageHistory moeGroomingPackageHistory = new MoeGroomingPackageHistory();
        moeGroomingPackageHistory.setPackageId(moeGroomingPackage.getId());
        moeGroomingPackageHistory.setPackageServiceId(0);
        moeGroomingPackageHistory.setQuantity(updatePackageParams.getExtendValidityDay());
        moeGroomingPackageHistory.setUseTime(CommonUtil.get10Timestamp());
        moeGroomingPackageHistory.setActivityType(PackageActivityEnum.EXTEND_PACKAGE_VALIDITY);
        moeGroomingPackageHistory.setStatus(CommonConstant.NORMAL);
        moeGroomingPackageHistory.setInvoiceId(0);
        moeGroomingPackageHistory.setStaffId(updatePackageParams.getStaffId().intValue());
        moeGroomingPackageHistory.setAfterExtendExpireDate(moeGroomingPackage.getExpirationDate());
        packageService.insertPackageHistory(moeGroomingPackageHistory);
        return true;
    }

    @Override
    public Boolean updatePackageService(UpdatePackageServiceParams updatePackageServiceParams) {

        PackageServiceDTO dto = new PackageServiceDTO();
        dto.setId(updatePackageServiceParams.getPackageServiceId().intValue());
        dto.setQuantity(updatePackageServiceParams.getRemainingQuantity());

        return packageService.updatePackageService(updatePackageServiceParams.getStaffId(), dto);
    }
}
