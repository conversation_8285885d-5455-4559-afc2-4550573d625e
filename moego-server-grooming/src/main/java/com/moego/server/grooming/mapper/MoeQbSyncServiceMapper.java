package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncService;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    MoeQbSyncService selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeQbSyncService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncService record);

    List<MoeQbSyncService> selectByBusinessIdRealmIdServiceIds(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("serviceIdList") List<Integer> serviceIdList);

    MoeQbSyncService selectByBusinessIdRealmIdItemName(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("itemName") String itemName);

    int updateByBusinessIdRealmIdServiceId(MoeQbSyncService record);
}
