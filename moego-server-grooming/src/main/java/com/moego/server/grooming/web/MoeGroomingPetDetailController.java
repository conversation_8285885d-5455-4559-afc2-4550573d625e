package com.moego.server.grooming.web;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.params.EditPetDetailParams;
import com.moego.server.grooming.service.MoePetDetailService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.web.vo.PetDetailMonthlyQueryVo;
import com.moego.server.grooming.web.vo.PetDetailQueryVo;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming")
public class MoeGroomingPetDetailController {

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private IBusinessStaffService iBusinessStaffService;

    @Autowired
    private OrderService orderService;

    /**
     * calendar day view
     * @param context
     * @param appointmentTime
     * @return
     */
    @GetMapping("/pet/detail/day/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<GroomingPetServiceListInfoDTO>> queryPetDetailList(
            AuthContext context,
            @RequestParam(required = false) String appointmentTime,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam Integer isWaitingList,
            @RequestParam(required = false, defaultValue = "false") boolean filterNoStartTime,
            @RequestParam(required = false, defaultValue = "false") boolean filterNoStaff,
            @RequestParam(required = false, defaultValue = "false")
                    @Schema(description = "false only filters grooming, true may include", type = "boolean")
                    boolean filterGroomingOnlyService) {
        PetDetailQueryVo petDetailQueryVo = new PetDetailQueryVo();
        petDetailQueryVo.setBusinessId(context.getBusinessId());
        petDetailQueryVo.setIsWaitingList(isWaitingList);
        // 默认筛选仅包含 grooming service 的 appointment
        if (filterGroomingOnlyService) {
            petDetailQueryVo.setServiceItems(List.of(
                    ServiceItemEnum.GROOMING.getServiceItem(),
                    ServiceItemEnum.BOARDING.getServiceItem(),
                    ServiceItemEnum.DAYCARE.getServiceItem(),
                    ServiceItemEnum.DOG_WALKING.getServiceItem()));
        } else {
            petDetailQueryVo.setServiceTypeIncludes(List.of(ServiceItemEnum.GROOMING.getBitValue()));
            petDetailQueryVo.setServiceItems(List.of(ServiceItemEnum.GROOMING.getServiceItem()));
        }

        // 限制查询 60 天，避免查询过多数据导致索引失效
        if (StringUtils.isNotBlank(appointmentTime)) {
            petDetailQueryVo.setStartDate(appointmentTime);
            petDetailQueryVo.setEndDate(appointmentTime);

            petDetailQueryVo.setStartDateBegin(
                    LocalDate.parse(appointmentTime).minusDays(60).toString());
            petDetailQueryVo.setStartDateFinish(appointmentTime);
        }
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            petDetailQueryVo.setStartDate(startTime);
            petDetailQueryVo.setEndDate(endTime);

            petDetailQueryVo.setStartDateBegin(
                    LocalDate.parse(endTime).minusDays(60).toString());
            petDetailQueryVo.setStartDateFinish(endTime);
            petDetailQueryVo.setEndDateBegin(startTime);
            petDetailQueryVo.setEndDateFinish(
                    LocalDate.parse(startTime).plusDays(60).toString());
        } else if (StringUtils.isNotBlank(startTime) && StringUtils.isBlank(endTime)) {
            petDetailQueryVo.setStartDate(startTime);
            petDetailQueryVo.setEndDate(LocalDate.parse(startTime).plusDays(60).toString());

            petDetailQueryVo.setStartDateBegin(
                    LocalDate.parse(startTime).minusDays(60).toString());
            petDetailQueryVo.setStartDateFinish(
                    LocalDate.parse(startTime).plusDays(60).toString());
        } else if (StringUtils.isNotBlank(endTime) && StringUtils.isBlank(startTime)) {
            petDetailQueryVo.setStartDate(LocalDate.parse(endTime).minusDays(60).toString());
            petDetailQueryVo.setEndDate(endTime);

            petDetailQueryVo.setStartDateBegin(
                    LocalDate.parse(endTime).minusDays(60).toString());
            petDetailQueryVo.setStartDateFinish(
                    LocalDate.parse(endTime).plusDays(60).toString());
        }

        var migrateInfo = migrateHelper.getMigrationInfo(context.getBusinessId());
        List<GroomingPetServiceListInfoDTO> result = new ArrayList<>(moePetDetailService.queryPetDetailList(
                migrateInfo.isMigrate(), migrateInfo.companyId(), petDetailQueryVo, context.getStaffId()));
        if (filterNoStaff) {
            result.removeIf(dto -> dto.getStaffId() == null);
        }
        if (filterNoStartTime) {
            result.removeIf(dto -> Boolean.TRUE.equals(dto.getNoStartTime()));
        }

        boolean hide;
        if (migrateInfo.isMigrate()) {
            hide = !permissionHelper.hasPermission(
                    context.companyId(), context.staffId(), PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
        } else {
            var staffPermissions = iBusinessStaffService.getBusinessRoleByStaffId(context.getStaffId());
            hide = !PermissionUtil.checkStaffPermissionsInfo(staffPermissions, PermissionUtil.VIEW_CLIENT_PHONE);
        }

        if (hide) {
            result.forEach(
                    dto -> dto.setClientPhoneNumber(PermissionUtil.phoneNumberConfusion(dto.getClientPhoneNumber())));
        }

        // set real paid status
        setPaidStatus(result, petDetailQueryVo.getBusinessId());

        return ResponseResult.success(result);
    }

    private void setPaidStatus(final List<GroomingPetServiceListInfoDTO> result, Integer businessId) {
        var appointmentIds = result.stream()
                .map(GroomingPetServiceListInfoDTO::getTicketId)
                .distinct()
                .toList();
        var orderMap = orderService.batchGetOrderDetailByGroomingIds(businessId, appointmentIds).stream()
                .collect(Collectors.toMap(
                        detail -> detail.getOrder().getSourceId(), OrderDetailModel::getOrder, (a, b) -> b));

        result.forEach(dto -> Optional.ofNullable(orderMap.get(dto.getTicketId().longValue()))
                .ifPresent(order -> {
                    if (StringUtils.isBlank(order.getPaymentStatus())) {
                        return;
                    }
                    var paymentStatus = OrderModel.PaymentStatus.valueOf(
                            order.getPaymentStatus().toUpperCase());
                    dto.setIsPaid(
                            switch (paymentStatus) {
                                case PAID -> GroomingAppointmentEnum.PAID.intValue();
                                case PARTIAL_PAID -> GroomingAppointmentEnum.PARTIAL_PAY.intValue();
                                default -> GroomingAppointmentEnum.NOT_PAY.intValue();
                            });
                }));
    }

    /**
     * calendar monthly view
     * @param context
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/pet/detail/monthly/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<GroomingPetServiceListInfoDTO>> queryPetDetailListMonthly(
            AuthContext context,
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam Integer isWaitingList,
            @RequestParam(required = false, defaultValue = "false") boolean filterNoStartTime,
            @RequestParam(required = false, defaultValue = "false") boolean filterNoStaff,
            @RequestParam(required = false, defaultValue = "false")
                    @Schema(description = "false only filters grooming, true may include", type = "boolean")
                    boolean filterGroomingOnlyService) {
        PetDetailMonthlyQueryVo petDetailMonthlyQueryVo = new PetDetailMonthlyQueryVo();
        petDetailMonthlyQueryVo.setBusinessId(context.getBusinessId());
        petDetailMonthlyQueryVo.setIsWaitingList(isWaitingList);
        // 限制查询 60 天，避免查询过多数据导致索引失效
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            petDetailMonthlyQueryVo.setStartDate(startTime);
            petDetailMonthlyQueryVo.setEndDate(endTime);

            petDetailMonthlyQueryVo.setStartDateBegin(
                    LocalDate.parse(endTime).minusDays(60).toString());
            petDetailMonthlyQueryVo.setStartDateFinish(endTime);
            petDetailMonthlyQueryVo.setEndDateBegin(startTime);
            petDetailMonthlyQueryVo.setEndDateFinish(
                    LocalDate.parse(startTime).plusDays(60).toString());
        }

        // 默认筛选仅包含 grooming service 的 appointment
        if (filterGroomingOnlyService) {
            petDetailMonthlyQueryVo.setServiceItems(List.of(
                    ServiceItemEnum.GROOMING.getServiceItem(),
                    ServiceItemEnum.BOARDING.getServiceItem(),
                    ServiceItemEnum.DAYCARE.getServiceItem(),
                    ServiceItemEnum.DOG_WALKING.getServiceItem()));
        } else {
            petDetailMonthlyQueryVo.setServiceTypeIncludes(List.of(ServiceItemEnum.GROOMING.getBitValue()));
            petDetailMonthlyQueryVo.setServiceItems(List.of(ServiceItemEnum.GROOMING.getServiceItem()));
        }
        List<GroomingPetServiceListInfoDTO> result = new ArrayList<>(moePetDetailService.queryPetDetailListMonthly(
                petDetailMonthlyQueryVo, context.getStaffId(), context.companyId()));
        if (filterNoStaff) {
            result.removeIf(dto -> dto.getStaffId() == null);
        }
        if (filterNoStartTime) {
            result.removeIf(dto -> Boolean.TRUE.equals(dto.getNoStartTime()));
        }
        return ResponseResult.success(result);
    }

    /**
     * 拉长改变服务时间
     * @param editPetDetailParams
     * @param bindingResult
     * @return
     */
    @PutMapping("/pet/detail/time")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = AppointmentAction.UPDATE_SERVICE_TIME,
            resourceType = ResourceType.APPOINTMENT,
            details = "#editPetDetailParams")
    public ResponseResult<Integer> editServiceTime(
            AuthContext context,
            @Validated @RequestBody List<EditPetDetailParams> editPetDetailParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return moePetDetailService.editServiceTime(context.getBusinessId(), context.getStaffId(), editPetDetailParams);
    }
}
