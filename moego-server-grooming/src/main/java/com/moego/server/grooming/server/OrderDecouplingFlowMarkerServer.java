package com.moego.server.grooming.server;

import static com.moego.lib.utils.CollectionUtils.firstElement;

import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerServiceBase;
import com.moego.server.grooming.dto.OrderDecouplingFlowMarkerDTO;
import com.moego.server.grooming.mapper.OrderDecouplingFlowMarkerMapper;
import com.moego.server.grooming.mapperbean.OrderDecouplingFlowMarker;
import com.moego.server.grooming.mapperbean.OrderDecouplingFlowMarkerExample;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class OrderDecouplingFlowMarkerServer extends IOrderDecouplingFlowMarkerServiceBase {

    private final OrderDecouplingFlowMarkerMapper orderDecouplingFlowMarkerMapper;

    @Override
    public OrderDecouplingFlowMarkerDTO getOrderDecouplingFlowMarker(long orderId) {
        var e = new OrderDecouplingFlowMarkerExample();
        e.createCriteria().andOrderIdEqualTo(orderId);
        return toOrderDecouplingFlowMarkerDTO(firstElement(orderDecouplingFlowMarkerMapper.selectByExample(e)));
    }

    @Override
    public long insertOrderDecouplingFlowMarker(long orderId) {
        var entity = new OrderDecouplingFlowMarker();
        entity.setOrderId(orderId);
        orderDecouplingFlowMarkerMapper.insertSelective(entity);
        return entity.getId();
    }

    private static OrderDecouplingFlowMarkerDTO toOrderDecouplingFlowMarkerDTO(OrderDecouplingFlowMarker entity) {
        if (entity == null) {
            return null;
        }
        var dto = new OrderDecouplingFlowMarkerDTO();
        dto.setId(entity.getId());
        dto.setOrderId(entity.getOrderId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}
