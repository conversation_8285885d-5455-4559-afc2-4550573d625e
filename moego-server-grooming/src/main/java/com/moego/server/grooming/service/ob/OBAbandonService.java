package com.moego.server.grooming.service.ob;

import static com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStatus.NOT_RECOVERED_STATUSES;
import static com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep.listRecoverableSteps;
import static java.time.temporal.ChronoUnit.DAYS;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Env;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.AbandonRecordRecoverTypeEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams;
import com.moego.server.grooming.web.params.OBAbandonClientParams;
import com.moego.server.grooming.web.params.OBAbandonParams;
import com.moego.server.grooming.web.params.OBAbandonPetParams;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import jakarta.annotation.Nullable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBAbandonService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final MoeBookOnlineAbandonRecordPetMapper abandonRecordPetMapper;
    private final OBClientService clientService;
    private final Environment environment;

    @Transactional
    public void collectAbandonInfo(OBAbandonParams abandonParams) {
        // submit appt remove abandon record move to submit appt api
        if (Objects.equals(abandonParams.nextStep(), OBStepEnum.submit_appt)) {
            return;
        }
        Integer businessId = abandonParams.businessId();
        String bookingFlowId = abandonParams.bookingFlowId();
        // rollback before select service step, undo record pet info
        if (abandonParams.nextStep().getOrder() <= OBStepEnum.select_service.getOrder()) {
            abandonRecordPetMapper.deleteRecordPetByBookingFlowId(businessId, bookingFlowId);
        }
        MoeBookOnlineAbandonRecord existingAbandonRecord =
                abandonRecordMapper.selectAbandonRecordByBookingFlowId(businessId, bookingFlowId);
        if (Objects.nonNull(existingAbandonRecord)
                && !Objects.equals(
                        existingAbandonRecord.getRecoveryType().intValue(),
                        AbandonRecordRecoverTypeEnum.ABANDONED.getValue())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "abandon record is not abandoned");
        }
        List<MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetList =
                abandonRecordPetMapper.listRecordPetByBookingFlowId(
                        abandonParams.businessId(), abandonParams.bookingFlowId());
        MoeBookOnlineAbandonRecord abandonRecord = convertAbandonParamsToRecord(abandonParams);
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList = convertAbandonParamsToRecordPet(abandonParams);
        // Get before step data and merge into existing abandon record
        List<OBStepEnum> beforeStepList = OBStepEnum.listAllStepsBeforeStep(abandonParams.nextStep());
        beforeStepList.forEach(beforeStep -> {
            switch (beforeStep) {
                case welcome_page -> mergeWelcomePageWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case basic_info -> mergeBasicInfoWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case select_care_type -> mergeSelectCareTypeWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case select_address -> mergeSelectAddressWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case select_pet -> mergeSelectPetWithUpdatePriority(existingAbandonRecordPetList, abandonRecordPetList);
                case select_service -> mergeSelectServiceWithUpdatePriority(
                        existingAbandonRecordPetList, abandonRecordPetList);
                case select_groomer -> mergeSelectGroomerWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case select_date -> mergeSelectDateWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case select_time -> mergeSelectTimeWithUpdatePriority(existingAbandonRecord, abandonRecord);
                case additional_pet_info -> mergeAdditionalPetInfoWithUpdatePriority(
                        existingAbandonRecordPetList, abandonRecordPetList);
                case personal_info -> mergePersonalInfoWithUpdatePriority(existingAbandonRecord, abandonRecord);
                default -> {
                    // do nothing
                }
            }
        });
        // If there is a new visitor record with the same customer id, the identity needs to be reversed existing client
        if (Objects.nonNull(abandonRecord.getCustomerId())) {
            abandonRecordMapper.updateLeadTypeByCustomerId(
                    businessId, abandonRecord.getCustomerId(), abandonRecord.getLeadType());
        }
        if (Objects.nonNull(abandonParams.usePaymentSegSetting())
                && StringUtils.hasText(abandonRecord.getPaymentSegSettingRule())) {
            abandonRecord.setUsePaymentSegSetting(abandonParams.usePaymentSegSetting());
            abandonRecord.setPaymentSegSettingRule(abandonParams.paymentSegSettingRule());
        }
        MoeBookOnlineAbandonRecord contactedRecord = hasContactedRecord(abandonRecord);
        if (contactedRecord != null) {
            if (abandonRecord.getCustomerId() == null) {
                abandonRecord.setCustomerId(contactedRecord.getCustomerId());
            }
            abandonRecord.setLastTextedTime(contactedRecord.getLastTextedTime());
            abandonRecord.setLastEmailedTime(contactedRecord.getLastEmailedTime());
            abandonRecord.setAbandonStatus(SearchAbandonedClientParam.AbandonStatus.CONTACTED.getValue());
        }
        if (Objects.isNull(existingAbandonRecord)) {
            if (StringUtils.hasText(abandonRecord.getPhoneNumber())) {
                // first collect abandon info
                abandonRecordMapper.insertSelective(abandonRecord);
            }
        } else {
            // full overwrite record
            abandonRecord.setId(existingAbandonRecord.getId());
            abandonRecordMapper.updateByPrimaryKeySelective(abandonRecord);
        }

        processProfileRequestAddress(abandonParams, abandonRecord.getId());

        if (CollectionUtils.isEmpty(abandonRecordPetList)) {
            return;
        }
        if (CollectionUtils.isEmpty(existingAbandonRecordPetList)) {
            // batch insert record pet
            abandonRecordPetMapper.batchInsertRecordPet(abandonRecordPetList);
        } else {
            // full overwrite record pet
            abandonRecordPetMapper.batchUpdateRecordPet(abandonRecordPetList);
        }
    }

    private void processProfileRequestAddress(OBAbandonParams abandonParams, @Nullable Integer abandonRecordId) {
        boolean isProfileRequestAddress = Optional.ofNullable(abandonParams.customerData())
                .map(OBAbandonClientParams::isProfileRequestAddress)
                .orElse(false);
        if (isProfileRequestAddress) {
            Optional.ofNullable(abandonRecordId)
                    .map(abandonRecordMapper::selectByPrimaryKey)
                    .filter(e -> e.getAddressId() != null)
                    .ifPresent(abandonRecord -> {
                        abandonRecord.setAddressId(null);
                        abandonRecord.setUpdateTime(new Date());
                        abandonRecordMapper.updateByPrimaryKey(abandonRecord);
                    });
        }
    }

    /**
     * If contacted record exists, the status needs to be inherited
     *
     * @return inherited abandoned status
     */
    public MoeBookOnlineAbandonRecord hasContactedRecord(MoeBookOnlineAbandonRecord abandonRecord) {
        List<MoeBookOnlineAbandonRecord> records = new ArrayList<>();
        if (Objects.equals(abandonRecord.getLeadType(), SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue())) {
            records.addAll(
                    listNonRecoveredRecordByPhone(abandonRecord.getBusinessId(), abandonRecord.getPhoneNumber()));
        } else if (Objects.equals(
                abandonRecord.getLeadType(), SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue())) {
            records.addAll(
                    abandonRecordMapper.listByCustomerId(abandonRecord.getBusinessId(), abandonRecord.getCustomerId()));
        }
        return records.stream()
                .filter(e -> Objects.equals(
                        e.getAbandonStatus(), SearchAbandonedClientParam.AbandonStatus.CONTACTED.getValue()))
                .max(Comparator.comparing(MoeBookOnlineAbandonRecord::getAbandonTime))
                .orElse(null);
    }

    private List<MoeBookOnlineAbandonRecord> listNonRecoveredRecordByPhone(Integer businessId, String phoneNumber) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.setOrderByClause("update_time DESC");
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andPhoneNumberEqualTo(phoneNumber)
                .andAbandonTimeGreaterThanOrEqualTo(
                        Instant.now().minus(30, DAYS).getEpochSecond())
                .andIsDeletedEqualTo(false)
                .andAbandonStatusIn(List.copyOf(NOT_RECOVERED_STATUSES))
                .andAbandonStepIn(
                        listRecoverableSteps().stream().map(Enum::name).toList());
        return abandonRecordMapper.selectByExample(example);
    }

    public MoeBookOnlineAbandonRecord convertAbandonParamsToRecord(OBAbandonParams abandonParams) {
        MoeBookOnlineAbandonRecord abandonRecord = new MoeBookOnlineAbandonRecord();
        OBAbandonClientParams customerData = abandonParams.customerData();
        if (Objects.nonNull(customerData)) {
            abandonRecord.setPhoneNumber(customerData.phoneNumber());
            abandonRecord.setCustomerId(customerData.customerId());
            // Set lead type
            abandonRecord.setLeadType(
                    Objects.nonNull(customerData.customerId())
                            ? SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue()
                            : SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue());
            abandonRecord.setFirstName(customerData.firstName());
            abandonRecord.setLastName(customerData.lastName());
            abandonRecord.setEmail(customerData.email());
            // select address
            if (!customerData.isProfileRequestAddress()) {
                abandonRecord.setAddressId(customerData.addressId());
            }
            abandonRecord.setAddress1(customerData.address1());
            abandonRecord.setAddress2(customerData.address2());
            abandonRecord.setCity(customerData.city());
            abandonRecord.setState(customerData.state());
            abandonRecord.setCountry(customerData.country());
            abandonRecord.setZipcode(customerData.zipcode());
            abandonRecord.setLat(customerData.lat());
            abandonRecord.setLng(customerData.lng());
            if (!CollectionUtils.isEmpty(customerData.answersMap())) {
                abandonRecord.setCustomerQuestionAnswers(JsonUtil.toJson(customerData.answersMap()));
            }
            // block client skip abandon
            if (clientService.validCustomerBlocked(
                    abandonParams.obName(),
                    abandonParams.companyId(),
                    abandonParams.businessId(),
                    customerData.customerId())) {
                abandonRecord.setIsDeleted(Boolean.TRUE);
                abandonRecord.setDeleteType(AbandonDeleteTypeEnum.BLOCK_CLIENT.getType());
            }
        }
        // personal additional info
        BookOnlineCustomerAdditionalParams additionalParams = abandonParams.bookOnlineCustomerAdditionalParams();
        if (Objects.nonNull(additionalParams)) {
            abandonRecord.setReferralSourceId(additionalParams.getReferralSourceId());
            abandonRecord.setPreferredGroomerId(additionalParams.getPreferredGroomerId());
            abandonRecord.setPreferredFrequencyType(additionalParams.getPreferredFrequencyType());
            abandonRecord.setPreferredFrequencyDay(additionalParams.getPreferredFrequencyDay());
            if (ArrayUtils.isNotEmpty(additionalParams.getPreferredDay())) {
                abandonRecord.setPreferredDay(JsonUtil.toJson(additionalParams.getPreferredDay()));
            }
            if (ArrayUtils.isNotEmpty(additionalParams.getPreferredTime())) {
                abandonRecord.setPreferredTime(JsonUtil.toJson(additionalParams.getPreferredTime()));
            }
        }
        abandonRecord.setReferer(abandonParams.referrer());
        abandonRecord.setAbandonStep(abandonParams.nextStep().name());
        abandonRecord.setAbandonTime(DateUtil.get10Timestamp());
        abandonRecord.setBusinessId(abandonParams.businessId());
        abandonRecord.setCompanyId(abandonParams.companyId());
        abandonRecord.setCompanyId(abandonParams.companyId());
        abandonRecord.setBookingFlowId(abandonParams.bookingFlowId());
        abandonRecord.setStaffId(abandonParams.staffId());
        ServiceItemEnum careType =
                abandonParams.careType() != null ? abandonParams.careType() : ServiceItemEnum.GROOMING;
        abandonRecord.setCareType(careType.name());
        switch (careType) {
            case BOARDING -> {
                abandonRecord.setAppointmentDate(abandonParams.boardingStartDate());
                abandonRecord.setAppointmentEndDate(abandonParams.boardingEndDate());
                abandonRecord.setAppointmentStartTime(abandonParams.arrivalTime());
                abandonRecord.setAppointmentEndTime(abandonParams.pickupTime());
            }
            case DAYCARE -> {
                abandonRecord.setSpecificDates(abandonParams.daycareDates());
                abandonRecord.setAppointmentStartTime(abandonParams.arrivalTime());
                abandonRecord.setAppointmentEndTime(abandonParams.pickupTime());
            }
            case GROOMING, EVALUATION -> {
                abandonRecord.setAppointmentDate(abandonParams.appointmentDate());
                abandonRecord.setAppointmentStartTime(abandonParams.appointmentStartTime());
            }
            default -> {}
        }
        if (!CollectionUtils.isEmpty(abandonParams.agreements())) {
            abandonRecord.setAgreementInfo(JsonUtil.toJson(abandonParams.agreements()));
        }
        abandonRecord.setAdditionalNote(abandonParams.note());
        // test case skip abandon
        if (shouldSkip(abandonParams)) {
            abandonRecord.setIsDeleted(Boolean.TRUE);
            abandonRecord.setDeleteType(AbandonDeleteTypeEnum.TEST_CASE.getType());
        } else if (Objects.isNull(abandonRecord.getDeleteType())) {
            // unrecoverable
            if (Objects.nonNull(abandonParams.deleteType())) {
                abandonRecord.setIsDeleted(Boolean.TRUE);
                abandonRecord.setDeleteType(abandonParams.deleteType().getType());
            } else {
                // recoverable
                abandonRecord.setIsDeleted(Boolean.FALSE);
            }
        }
        return abandonRecord;
    }

    private boolean shouldSkip(OBAbandonParams abandonParams) {
        // 为了解决测试环境没有 abandon record 的问题（使用 impersonator 登录不会产生 abandon 数据）
        // 只有在生产环境才不采集 abandon 数据
        if (isTestEnv()) {
            return false;
        }
        return Boolean.TRUE.equals(abandonParams.skipAbandon());
    }

    private boolean isTestEnv() {
        return Arrays.stream(environment.getActiveProfiles())
                .anyMatch(profile -> Env.TEST2.getValue().equalsIgnoreCase(profile)
                        || Env.STAGING.getValue().equalsIgnoreCase(profile)
                        || Env.LOCAL.getValue().equalsIgnoreCase(profile));
    }

    public List<MoeBookOnlineAbandonRecordPet> convertAbandonParamsToRecordPet(OBAbandonParams abandonParams) {
        if (CollectionUtils.isEmpty(abandonParams.petData())) {
            return Collections.emptyList();
        }
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList = new ArrayList<>();
        for (int i = 0; i < abandonParams.petData().size(); i++) {
            OBAbandonPetParams obAbandonPetParams = abandonParams.petData().get(i);
            MoeBookOnlineAbandonRecordPet abandonRecordPet = new MoeBookOnlineAbandonRecordPet();
            abandonRecordPet.setBusinessId(abandonParams.businessId());
            abandonRecordPet.setCompanyId(abandonParams.companyId());
            abandonRecordPet.setBookingFlowId(abandonParams.bookingFlowId());
            abandonRecordPet.setPetId(obAbandonPetParams.petId());
            abandonRecordPet.setIndexId(i);
            abandonRecordPet.setPetName(obAbandonPetParams.petName());
            abandonRecordPet.setPetTypeId(obAbandonPetParams.petTypeId());
            abandonRecordPet.setBreed(obAbandonPetParams.breed());
            abandonRecordPet.setWeight(obAbandonPetParams.weight());
            abandonRecordPet.setAvatarPath(obAbandonPetParams.avatarPath());
            abandonRecordPet.setFixed(obAbandonPetParams.fixed());
            abandonRecordPet.setGender(obAbandonPetParams.gender());
            abandonRecordPet.setHairLength(obAbandonPetParams.hairLength());
            abandonRecordPet.setBehavior(obAbandonPetParams.behavior());
            abandonRecordPet.setBirthday(obAbandonPetParams.birthday());
            if (!CollectionUtils.isEmpty(obAbandonPetParams.vaccineList())) {
                abandonRecordPet.setVaccineList(JsonUtil.toJson(obAbandonPetParams.vaccineList()));
            }
            abandonRecordPet.setServiceId(obAbandonPetParams.serviceId());
            if (!CollectionUtils.isEmpty(obAbandonPetParams.addOnIds())) {
                abandonRecordPet.setAddonIds(JsonUtil.toJson(obAbandonPetParams.addOnIds()));
            }
            abandonRecordPet.setVetAddress(obAbandonPetParams.vetAddress());
            abandonRecordPet.setVetName(obAbandonPetParams.vetName());
            abandonRecordPet.setVetPhoneNumber(obAbandonPetParams.vetPhone());
            abandonRecordPet.setEmergencyContactName(obAbandonPetParams.emergencyContactName());
            abandonRecordPet.setEmergencyContactPhone(obAbandonPetParams.emergencyContactPhone());
            abandonRecordPet.setHealthIssues(obAbandonPetParams.healthIssues());
            if (MapUtils.isNotEmpty(obAbandonPetParams.petQuestionAnswers())) {
                abandonRecordPet.setPetQuestionAnswers(JsonUtil.toJson(obAbandonPetParams.petQuestionAnswers()));
            }
            abandonRecordPetList.add(abandonRecordPet);
        }
        return abandonRecordPetList;
    }

    public void mergeWelcomePageWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getPhoneNumber())) {
            updateAbandonRecord.setPhoneNumber(existingAbandonRecord.getPhoneNumber());
        }
        if (Objects.isNull(updateAbandonRecord.getReferer())) {
            updateAbandonRecord.setReferer(existingAbandonRecord.getReferer());
        }
        if (Objects.isNull(updateAbandonRecord.getCustomerId())) {
            updateAbandonRecord.setCustomerId(existingAbandonRecord.getCustomerId());
        }
        // avoid missing customer id in the front-end
        if (Objects.isNull(updateAbandonRecord.getLeadType())
                || Objects.equals(
                        updateAbandonRecord.getLeadType(),
                        SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue())) {
            updateAbandonRecord.setLeadType(
                    (Objects.nonNull(updateAbandonRecord.getCustomerId())
                                    || Objects.nonNull(existingAbandonRecord.getCustomerId()))
                            ? SearchAbandonedClientParam.LeadType.EXISTING_CLIENT.getValue()
                            : SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue());
        }
    }

    public void mergeBasicInfoWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getFirstName())) {
            updateAbandonRecord.setFirstName(existingAbandonRecord.getFirstName());
        }
        if (Objects.isNull(updateAbandonRecord.getLastName())) {
            updateAbandonRecord.setLastName(existingAbandonRecord.getLastName());
        }
        if (Objects.isNull(updateAbandonRecord.getEmail())) {
            updateAbandonRecord.setEmail(existingAbandonRecord.getEmail());
        }
        // 存在 Boarding/Daycare care type 时会将 client info 和 address 部分表单数据前置填写
        mergeClientAdditionalInfoWithUpdatePriority(existingAbandonRecord, updateAbandonRecord);
        mergeSelectAddressWithUpdatePriority(existingAbandonRecord, updateAbandonRecord);
    }

    public static void mergeSelectCareTypeWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getCareType())) {
            updateAbandonRecord.setCareType(existingAbandonRecord.getCareType());
        }
    }

    public void mergeSelectAddressWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getAddressId())) {
            updateAbandonRecord.setAddressId(existingAbandonRecord.getAddressId());
        }
        if (Objects.isNull(updateAbandonRecord.getAddress1())) {
            updateAbandonRecord.setAddress1(existingAbandonRecord.getAddress1());
        }
        if (Objects.isNull(updateAbandonRecord.getAddress2())) {
            updateAbandonRecord.setAddress2(existingAbandonRecord.getAddress2());
        }
        if (Objects.isNull(updateAbandonRecord.getCity())) {
            updateAbandonRecord.setCity(existingAbandonRecord.getCity());
        }
        if (Objects.isNull(updateAbandonRecord.getState())) {
            updateAbandonRecord.setState(existingAbandonRecord.getState());
        }
        if (Objects.isNull(updateAbandonRecord.getCountry())) {
            updateAbandonRecord.setCountry(existingAbandonRecord.getCountry());
        }
        if (Objects.isNull(updateAbandonRecord.getZipcode())) {
            updateAbandonRecord.setZipcode(existingAbandonRecord.getZipcode());
        }
        if (Objects.isNull(updateAbandonRecord.getLat())) {
            updateAbandonRecord.setLat(existingAbandonRecord.getLat());
        }
        if (Objects.isNull(updateAbandonRecord.getLng())) {
            updateAbandonRecord.setLng(existingAbandonRecord.getLng());
        }
    }

    public void mergeSelectPetWithUpdatePriority(
            List<MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetList,
            List<MoeBookOnlineAbandonRecordPet> updateAbandonRecordPetList) {
        Map<Integer, MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetMap = existingAbandonRecordPetList.stream()
                .collect(Collectors.toMap(MoeBookOnlineAbandonRecordPet::getIndexId, Function.identity()));
        updateAbandonRecordPetList.forEach(updateAbandonRecordPet -> {
            MoeBookOnlineAbandonRecordPet existingAbandonRecordPet =
                    existingAbandonRecordPetMap.get(updateAbandonRecordPet.getIndexId());
            if (Objects.isNull(existingAbandonRecordPet)) {
                return;
            }
            if (Objects.isNull(updateAbandonRecordPet.getId())) {
                updateAbandonRecordPet.setId(existingAbandonRecordPet.getId());
            }
            if (Objects.isNull(updateAbandonRecordPet.getPetId())) {
                updateAbandonRecordPet.setPetId(existingAbandonRecordPet.getPetId());
            }
            if (Objects.isNull(updateAbandonRecordPet.getPetName())) {
                updateAbandonRecordPet.setPetName(existingAbandonRecordPet.getPetName());
            }
            if (Objects.isNull(updateAbandonRecordPet.getPetTypeId())) {
                updateAbandonRecordPet.setPetTypeId(existingAbandonRecordPet.getPetTypeId());
            }
            if (Objects.isNull(updateAbandonRecordPet.getBreed())) {
                updateAbandonRecordPet.setBreed(existingAbandonRecordPet.getBreed());
            }
            if (Objects.isNull(updateAbandonRecordPet.getWeight())) {
                updateAbandonRecordPet.setWeight(existingAbandonRecordPet.getWeight());
            }
        });
    }

    public void mergeSelectServiceWithUpdatePriority(
            List<MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetList,
            List<MoeBookOnlineAbandonRecordPet> updateAbandonRecordPetList) {
        Map<Integer, MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetMap = existingAbandonRecordPetList.stream()
                .collect(Collectors.toMap(
                        MoeBookOnlineAbandonRecordPet::getIndexId, Function.identity(), (e1, e2) -> e1));
        updateAbandonRecordPetList.forEach(updateAbandonRecordPet -> {
            MoeBookOnlineAbandonRecordPet existingAbandonRecordPet =
                    existingAbandonRecordPetMap.get(updateAbandonRecordPet.getIndexId());
            if (Objects.isNull(existingAbandonRecordPet)) {
                return;
            }
            if (Objects.isNull(updateAbandonRecordPet.getServiceId())) {
                updateAbandonRecordPet.setServiceId(existingAbandonRecordPet.getServiceId());
            }
            if (Objects.isNull(updateAbandonRecordPet.getAddonIds())) {
                updateAbandonRecordPet.setAddonIds(existingAbandonRecordPet.getAddonIds());
            }
        });
    }

    static void mergeSelectGroomerWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getStaffId())) {
            updateAbandonRecord.setStaffId(existingAbandonRecord.getStaffId());
        }
    }

    public static void mergeSelectDateWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getAppointmentDate())) {
            updateAbandonRecord.setAppointmentDate(existingAbandonRecord.getAppointmentDate());
        }
    }

    public static void mergeSelectTimeWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        switch (ServiceItemEnum.valueOf(existingAbandonRecord.getCareType())) {
            case BOARDING, DAYCARE -> {
                // Arrival time
                if (Objects.isNull(updateAbandonRecord.getAppointmentStartTime())) {
                    updateAbandonRecord.setAppointmentStartTime(existingAbandonRecord.getAppointmentStartTime());
                }
                // Pickup time
                if (Objects.isNull(updateAbandonRecord.getAppointmentEndTime())) {
                    updateAbandonRecord.setAppointmentEndTime(existingAbandonRecord.getAppointmentEndTime());
                }
            }
            case GROOMING, DOG_WALKING -> {
                if (Objects.isNull(updateAbandonRecord.getStaffId())) {
                    updateAbandonRecord.setStaffId(existingAbandonRecord.getStaffId());
                }
                if (Objects.isNull(updateAbandonRecord.getAppointmentDate())) {
                    updateAbandonRecord.setAppointmentDate(existingAbandonRecord.getAppointmentDate());
                }
                if (Objects.isNull(updateAbandonRecord.getAppointmentStartTime())) {
                    updateAbandonRecord.setAppointmentStartTime(existingAbandonRecord.getAppointmentStartTime());
                }
            }
            case EVALUATION -> {
                // Service date
                if (Objects.isNull(updateAbandonRecord.getAppointmentDate())) {
                    updateAbandonRecord.setAppointmentDate(existingAbandonRecord.getAppointmentDate());
                }
                // Arrival time
                if (Objects.isNull(updateAbandonRecord.getAppointmentStartTime())) {
                    updateAbandonRecord.setAppointmentStartTime(existingAbandonRecord.getAppointmentStartTime());
                }
            }
            default -> {}
        }
    }

    public void mergeAdditionalPetInfoWithUpdatePriority(
            List<MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetList,
            List<MoeBookOnlineAbandonRecordPet> updateAbandonRecordPetList) {
        Map<Integer, MoeBookOnlineAbandonRecordPet> existingAbandonRecordPetMap = existingAbandonRecordPetList.stream()
                .collect(Collectors.toMap(MoeBookOnlineAbandonRecordPet::getIndexId, Function.identity()));
        updateAbandonRecordPetList.forEach(updateAbandonRecordPet -> {
            MoeBookOnlineAbandonRecordPet existingAbandonRecordPet =
                    existingAbandonRecordPetMap.get(updateAbandonRecordPet.getIndexId());
            if (Objects.isNull(existingAbandonRecordPet)) {
                return;
            }
            if (Objects.isNull(updateAbandonRecordPet.getAvatarPath())) {
                updateAbandonRecordPet.setAvatarPath(existingAbandonRecordPet.getAvatarPath());
            }
            if (Objects.isNull(updateAbandonRecordPet.getFixed())) {
                updateAbandonRecordPet.setFixed(existingAbandonRecordPet.getFixed());
            }
            if (Objects.isNull(updateAbandonRecordPet.getGender())) {
                updateAbandonRecordPet.setGender(existingAbandonRecordPet.getGender());
            }
            if (Objects.isNull(updateAbandonRecordPet.getHairLength())) {
                updateAbandonRecordPet.setHairLength(existingAbandonRecordPet.getHairLength());
            }
            if (Objects.isNull(updateAbandonRecordPet.getBehavior())) {
                updateAbandonRecordPet.setBehavior(existingAbandonRecordPet.getBehavior());
            }
            if (Objects.isNull(updateAbandonRecordPet.getBirthday())) {
                updateAbandonRecordPet.setBirthday(existingAbandonRecordPet.getBirthday());
            }
            if (Objects.isNull(updateAbandonRecordPet.getVaccineList())) {
                updateAbandonRecordPet.setVaccineList(existingAbandonRecordPet.getVaccineList());
            }
            if (Objects.isNull(updateAbandonRecordPet.getVetAddress())) {
                updateAbandonRecordPet.setVetAddress(existingAbandonRecordPet.getVetAddress());
            }
            if (Objects.isNull(updateAbandonRecordPet.getVetName())) {
                updateAbandonRecordPet.setVetName(existingAbandonRecordPet.getVetName());
            }
            if (Objects.isNull(updateAbandonRecordPet.getVetPhoneNumber())) {
                updateAbandonRecordPet.setVetPhoneNumber(existingAbandonRecordPet.getVetPhoneNumber());
            }
            if (Objects.isNull(updateAbandonRecordPet.getEmergencyContactName())) {
                updateAbandonRecordPet.setEmergencyContactName(existingAbandonRecordPet.getEmergencyContactName());
            }
            if (Objects.isNull(updateAbandonRecordPet.getEmergencyContactPhone())) {
                updateAbandonRecordPet.setEmergencyContactPhone(existingAbandonRecordPet.getEmergencyContactPhone());
            }
            if (Objects.isNull(updateAbandonRecordPet.getHealthIssues())) {
                updateAbandonRecordPet.setHealthIssues(existingAbandonRecordPet.getHealthIssues());
            }
            if (Objects.isNull(updateAbandonRecordPet.getPetQuestionAnswers())) {
                updateAbandonRecordPet.setPetQuestionAnswers(existingAbandonRecordPet.getPetQuestionAnswers());
            }
        });
    }

    public void mergePersonalInfoWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        mergeClientAdditionalInfoWithUpdatePriority(existingAbandonRecord, updateAbandonRecord);
        if (Objects.isNull(updateAbandonRecord.getAgreementInfo())) {
            updateAbandonRecord.setAgreementInfo(existingAbandonRecord.getAgreementInfo());
        }
        if (Objects.isNull(updateAbandonRecord.getAdditionalNote())) {
            updateAbandonRecord.setAdditionalNote(existingAbandonRecord.getAdditionalNote());
        }
        if (Objects.isNull(updateAbandonRecord.getUsePaymentSegSetting())) {
            updateAbandonRecord.setUsePaymentSegSetting(existingAbandonRecord.getUsePaymentSegSetting());
        }
        if (Objects.isNull(updateAbandonRecord.getPaymentSegSettingRule())) {
            updateAbandonRecord.setPaymentSegSettingRule(existingAbandonRecord.getPaymentSegSettingRule());
        }
    }

    private static void mergeClientAdditionalInfoWithUpdatePriority(
            MoeBookOnlineAbandonRecord existingAbandonRecord, MoeBookOnlineAbandonRecord updateAbandonRecord) {
        if (Objects.isNull(existingAbandonRecord)) {
            return;
        }
        if (Objects.isNull(updateAbandonRecord.getReferralSourceId())) {
            updateAbandonRecord.setReferralSourceId(existingAbandonRecord.getReferralSourceId());
        }
        if (Objects.isNull(updateAbandonRecord.getPreferredGroomerId())) {
            updateAbandonRecord.setPreferredGroomerId(existingAbandonRecord.getPreferredGroomerId());
        }
        if (Objects.isNull(updateAbandonRecord.getPreferredFrequencyType())) {
            updateAbandonRecord.setPreferredFrequencyType(existingAbandonRecord.getPreferredFrequencyType());
        }
        if (Objects.isNull(updateAbandonRecord.getPreferredFrequencyDay())) {
            updateAbandonRecord.setPreferredFrequencyDay(existingAbandonRecord.getPreferredFrequencyDay());
        }
        if (Objects.isNull(updateAbandonRecord.getPreferredDay())) {
            updateAbandonRecord.setPreferredDay(existingAbandonRecord.getPreferredDay());
        }
        if (Objects.isNull(updateAbandonRecord.getPreferredTime())) {
            updateAbandonRecord.setPreferredTime(existingAbandonRecord.getPreferredTime());
        }
        if (Objects.isNull(updateAbandonRecord.getCustomerQuestionAnswers())) {
            updateAbandonRecord.setCustomerQuestionAnswers(existingAbandonRecord.getCustomerQuestionAnswers());
        }
    }
}
