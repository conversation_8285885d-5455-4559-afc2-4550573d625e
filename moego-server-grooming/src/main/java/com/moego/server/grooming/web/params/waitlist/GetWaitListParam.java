package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import org.hibernate.validator.constraints.Range;

@Builder(toBuilder = true)
public record GetWaitListParam(
        @Schema(description = "business id", hidden = true) Long businessId,
        @Schema(description = "token staff id", hidden = true) Long tokenStaffId,
        @Schema(description = "company id", hidden = true) Long companyId,
        @Schema(description = "Query keyword") String keyword,
        @Valid @NotNull FilterParams filters,
        @NotNull @Valid @Schema(description = "Sort params") SortParams sort,
        @Range(min = 1) @NotNull @Schema(description = "Page number") Integer pageNum,
        @Range(min = 1, max = 100) @NotNull @Schema(description = "Page size") Integer pageSize) {}
