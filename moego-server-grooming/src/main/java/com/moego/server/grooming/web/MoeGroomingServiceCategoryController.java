package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.GroomingCompanyServiceCategoryService;
import com.moego.server.grooming.service.GroomingServiceCategoryService;
import com.moego.server.grooming.service.dto.ServiceCategoryListDto;
import com.moego.server.grooming.service.dto.ServiceCategorySaveDto;
import com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto;
import com.moego.server.grooming.utils.PetDetailDTOUtil;
import com.moego.server.grooming.web.dto.service.AddServiceCategoryResultDto;
import com.moego.server.grooming.web.dto.service.ServiceCategoryListResultDto;
import com.moego.server.grooming.web.params.ServiceCategoryBatchUpdateParams;
import com.moego.server.grooming.web.vo.DeleteIdVo;
import com.moego.server.grooming.web.vo.SortIdListVo;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class MoeGroomingServiceCategoryController {

    @Autowired
    private GroomingServiceCategoryService groomingServiceCategoryService;

    @Autowired
    private GroomingCompanyServiceCategoryService groomingCompanyServiceCategoryService;

    /**
     * setting 查询ServiceCategory
     *
     * @param context
     * @return rr
     */
    @GetMapping("/grooming/serviceCategory")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<ServiceCategoryListResultDto> getEditServiceCategory(
            AuthContext context,
            Byte type,
            @RequestParam(required = false, defaultValue = "1") Integer serviceItemType) {
        ServiceCategoryListResultDto returnMap = new ServiceCategoryListResultDto();
        returnMap.setServiceCategoryList(groomingServiceCategoryService.getEditServiceCategory(
                context.companyId(),
                context.getBusinessId(),
                type,
                PetDetailDTOUtil.mapServiceItemType(serviceItemType)));
        return ResponseResult.success(returnMap);
    }

    /**
     * setting 新建ServiceCategory
     *
     * @param context
     * @param categorySaveDto categorySaveDto
     * @param bindingResult   bindingResult
     * @return rr
     */
    @PostMapping("/grooming/serviceCategory")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<AddServiceCategoryResultDto> createServiceCategory(
            AuthContext context,
            @Validated @RequestBody ServiceCategorySaveDto categorySaveDto,
            BindingResult bindingResult) {
        if (categorySaveDto.getServiceItemType() == null) {
            categorySaveDto.setServiceItemType(ServiceItemType.GROOMING.getNumber());
        }
        BindingErrorUtil.handleResult(bindingResult);
        Integer serviceCategoryId = groomingServiceCategoryService.createServiceCategory(
                context.companyId(), context.getBusinessId(), categorySaveDto);
        AddServiceCategoryResultDto returnMap = new AddServiceCategoryResultDto();
        returnMap.setCategoryId(serviceCategoryId);
        returnMap.setResult(serviceCategoryId != null);
        return ResponseResult.success(returnMap);
    }

    /**
     * setting 更新ServiceCategory
     *
     * @param context
     * @param categoryUpdateDto categoryUpdateDto
     * @param bindingResult     bindingResult
     * @return rb
     */
    @PutMapping("/grooming/serviceCategory")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> updateServiceCategory(
            AuthContext context,
            @Validated @RequestBody ServiceCategoryUpdateDto categoryUpdateDto,
            BindingResult bindingResult) {
        if (categoryUpdateDto.getServiceItemType() == null) {
            categoryUpdateDto.setServiceItemType(ServiceItemType.GROOMING.getNumber());
        }
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(groomingServiceCategoryService.updateServiceCategory(
                context.companyId(), context.getBusinessId(), categoryUpdateDto));
    }

    @PutMapping("/grooming/company/batch/serviceCategory")
    @Auth(AuthType.COMPANY)
    public List<ServiceCategoryListDto> updateServiceCategoryBatch(
            AuthContext context, @Valid @RequestBody ServiceCategoryBatchUpdateParams batchUpdateParams) {
        if (batchUpdateParams.getServiceItemType() == null) {
            batchUpdateParams.setServiceItemType(ServiceItemType.GROOMING.getNumber());
        }
        groomingCompanyServiceCategoryService.updateServiceCategoryBatch(context.companyId(), batchUpdateParams);
        return groomingCompanyServiceCategoryService.getEditServiceCategoryForCompany(
                context.companyId(),
                batchUpdateParams.getType(),
                PetDetailDTOUtil.mapServiceItemType(batchUpdateParams.getServiceItemType()));
    }

    /**
     * setting 更新ServiceCategory 排序
     *
     * @param idListVo      idListVo
     * @param bindingResult bindingResult
     * @return rb
     */
    @PutMapping("/grooming/serviceCategory/sort")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> serviceCategorySort(
            AuthContext context, @Validated @RequestBody SortIdListVo idListVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(
                groomingServiceCategoryService.sortServiceCategory(context.companyId(), idListVo.getIdList()));
    }

    /**
     * setting 删除ServiceCategory
     *
     * @param context
     * @param idVo          idVo
     * @param bindingResult bindingResult
     * @return rr
     */
    @DeleteMapping("/grooming/serviceCategory")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Boolean> deleteServiceCategory(
            AuthContext context, @Validated @RequestBody DeleteIdVo idVo, BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        return ResponseResult.success(
                groomingServiceCategoryService.deleteServiceCategory(idVo.getId(), context.companyId()));
    }
}
