package com.moego.server.grooming.service.ob;

import com.moego.server.grooming.mapper.MoeBookOnlineGalleryMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineGallery;
import com.moego.server.grooming.web.dto.ob.GalleryDto;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBGalleryService {

    private final MoeBookOnlineGalleryMapper galleryMapper;

    public List<GalleryDto> getGalleryByBusinessId(Integer id) {
        List<MoeBookOnlineGallery> galleryList = galleryMapper.selectByBusinessId(id);
        return galleryList.stream()
                .map(gallery -> {
                    GalleryDto tmpMap = new GalleryDto();
                    tmpMap.setImageId(gallery.getId());
                    tmpMap.setSort(gallery.getSort());
                    tmpMap.setIsStar(gallery.getIsStar());
                    tmpMap.setImagePath(gallery.getImagePath());
                    return tmpMap;
                })
                .collect(Collectors.toList());
    }
}
