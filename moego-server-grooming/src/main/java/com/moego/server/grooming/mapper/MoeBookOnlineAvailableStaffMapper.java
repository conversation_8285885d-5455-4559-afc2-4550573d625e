package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeBookOnlineAvailableStaff;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * 通过 StaffTimeSyncService 已迁移到
 * moego_svc_online_booking staff_availablity表
 * moe_business_book_online available_time_sync 字段
 */
@Deprecated
public interface MoeBookOnlineAvailableStaffMapper {

    // 已双写 db
    int insert(MoeBookOnlineAvailableStaff record);
    // 已双写 db
    void updateByStaffId(MoeBookOnlineAvailableStaff record);
    // 已双写 db
    void updateSyncByBusinessId(
            @Param("businessId") Integer businessId, @Param("syncWithWorkingHour") Byte syncWithWorkingHour);

    // 给 server business 调用
    // 由 server-business 直接调用 svc-online-booking
    @Deprecated(forRemoval = true)
    List<MoeBookOnlineAvailableStaff> selectByBusinessId(@Param("businessId") Integer businessId);

    // 都是 exist check 逻辑用到
    // 合并到下面的 selectByBusinessIdAndStaffIds
    @Deprecated
    List<MoeBookOnlineAvailableStaff> selectByBusinessIdAndStaffId(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    // list query
    // 直接调用 svc-online-booking 实现
    @Deprecated
    List<MoeBookOnlineAvailableStaff> selectByBusinessIdAndStaffIds(
            @Param("businessId") Integer businessId, @Param("staffIds") Set<Integer> staffIds);

    // 查询 available type sync 开关
    // 替换为查询 moe_business_book_online 开关
    @Deprecated
    Integer selectDisableSyncCountByBusinessId(@Param("businessId") Integer businessId);
}
