package com.moego.server.grooming.web.params.waitlist;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class DatePreference {
    @Schema(description = "exact date, YYYY-MM-DD")
    private List<String> exactDate;

    @Schema(description = "day of week, value, from 0 (Sunday) to 6 (Saturday)")
    private List<@Range(min = 0, max = 6) Integer> dayOfWeek;

    private Boolean isAnyDate;
}
