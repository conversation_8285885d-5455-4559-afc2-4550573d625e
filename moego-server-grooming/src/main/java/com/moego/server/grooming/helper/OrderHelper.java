package com.moego.server.grooming.helper;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.order.v1.CancelOrderRequest;
import com.moego.idl.service.order.v1.CreateInvoiceIDRequest;
import com.moego.idl.service.order.v1.CreateNoShowOrderRequest;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.ListOrdersV1Request;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderHelper {
    private final OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    public Long createNoShowOrderForAppointment(
            MoeGroomingAppointment appointment, Long operatorId, BigDecimal amount, String description) {
        var req = CreateNoShowOrderRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setStaffId(operatorId)
                .setCustomerId(appointment.getCustomerId())
                .setSourceType(OrderSourceType.NO_SHOW)
                .setSourceId(appointment.getId())
                .setNoShowFeeAmount(MoneyUtils.toGoogleMoney(amount));
        if (description != null) {
            req.setDescription(description);
        }
        var resp = orderClient.createNoShowOrder(req.build());
        return resp.getOrder().getOrder().getId();
    }

    public void cancelNoShowOrderForAppointment(Long businessId, Integer appointmentId, Long operatorId) {
        OrderDetailModel order = getAppointmentLatestNoShowOrder(appointmentId);
        if (order == null) {
            return;
        }
        // 如果订单已经被取消，则不做处理
        if (Objects.equals(
                InvoiceStatusEnum.INVOICE_STATUS_REMOVED, order.getOrder().getStatus())) {
            return;
        }
        orderClient.cancelOrder(CancelOrderRequest.newBuilder()
                .setBusinessId(businessId)
                .setStaffId(operatorId)
                .setOrderId(order.getOrder().getId())
                .build());
    }

    /**
     * 一个预约可能关联多个 no-show 订单，该方法返回每个预约的最新的 no-show 订单
     */
    public Map<Integer, OrderDetailModel> getAppointmentLatestNoShowOrders(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return Map.of();
        }
        List<Long> sourceIds =
                groomingIds.stream().distinct().map(Integer::longValue).toList();
        GetOrderListRequest.Builder requestBuilder = GetOrderListRequest.newBuilder()
                .addAllSourceIds(sourceIds)
                .setSourceType(InvoiceStatusEnum.TYPE_NOSHOW);
        return orderClient.getOrderList(requestBuilder.build()).getOrderListList().stream()
                .collect(Collectors.toMap(
                        orderDetail -> (int) orderDetail.getOrder().getSourceId(),
                        Function.identity(),
                        BinaryOperator.maxBy(
                                Comparator.comparing(o -> o.getOrder().getId()))));
    }

    @Nullable
    OrderDetailModel getAppointmentLatestNoShowOrder(Integer groomingId) {
        return getAppointmentLatestNoShowOrders(List.of(groomingId)).get(groomingId);
    }

    List<OrderModelV1> getAllOrdersByOriginOrderId(Long businessId, Long originOrderId) {
        var request = ListOrdersV1Request.newBuilder()
                .setBusinessId(businessId)
                .setOriginOrderId(originOrderId)
                .build();
        return orderClient.listOrdersV1(request).getOrdersList();
    }

    List<OrderDetailModel> getAppointmentOriginOrders(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        var request = GetOrderListRequest.newBuilder()
                .addAllSourceIds(groomingIds.stream().map(Integer::longValue).toList())
                .setSourceType(com.moego.common.enums.order.OrderSourceType.APPOINTMENT.getSource())
                .build();
        return orderClient.getOrderList(request).getOrderListList();
    }

    /**
     * 批量拉取 extra、origin 订单
     */
    public List<Long> getOrderIds(Integer businessId, List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return List.of();
        }
        var originOrderIds = getAppointmentOriginOrders(groomingIds).stream()
                .map(k -> k.getOrder().getId())
                .toList();
        List<Long> allOrderIds = new ArrayList<>();
        // 当前没有合适的批量拉取 extra order 的方法，只能一个一个拉取
        for (var orderId : originOrderIds) {
            var orders = getAllOrdersByOriginOrderId(Long.valueOf(businessId), orderId);
            allOrderIds.addAll(orders.stream().map(OrderModelV1::getId).toList());
        }
        return allOrderIds;
    }

    public long createInvoiceId(MoeGroomingAppointment appointment) {
        return orderClient
                .createInvoiceID(CreateInvoiceIDRequest.newBuilder()
                        .setSourceType(OrderSourceType.APPOINTMENT)
                        .setSourceId(appointment.getId())
                        .setCompanyId(appointment.getCompanyId())
                        .setBusinessId(appointment.getBusinessId())
                        .setCustomerId(appointment.getCustomerId())
                        .build())
                .getInvoiceId();
    }
}
