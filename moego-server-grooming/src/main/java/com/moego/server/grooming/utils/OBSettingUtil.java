package com.moego.server.grooming.utils;

import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class OBSettingUtil {

    /**
     * 根据 {@link MoeBusinessBookOnline} 获取最远可预约天数
     *
     * @param obSetting {@link MoeBusinessBookOnline}
     * @return 最远可预约天数
     */
    public static int calculateBookingRangeEndDays(MoeBusinessBookOnline obSetting) {
        return Objects.equals(BookOnlineDTO.BookingRangeEndType.USING_OFFSET, obSetting.getBookingRangeEndType())
                ? obSetting.getBookingRangeEndOffset()
                // 当前日期可能大于 end date
                : Math.max(
                        0,
                        (int) LocalDate.now()
                                        .until(LocalDate.parse(obSetting.getBookingRangeEndDate()), ChronoUnit.DAYS)
                                + 1); // +1 为了包含当天
    }
}
