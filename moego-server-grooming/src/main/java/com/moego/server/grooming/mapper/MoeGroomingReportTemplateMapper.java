package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingReportTemplateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingReportTemplateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int deleteByExample(MoeGroomingReportTemplateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReportTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReportTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    List<MoeGroomingReportTemplate> selectByExample(MoeGroomingReportTemplateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    MoeGroomingReportTemplate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingReportTemplate record,
            @Param("example") MoeGroomingReportTemplateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingReportTemplate record,
            @Param("example") MoeGroomingReportTemplateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReportTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_template
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReportTemplate record);

    int initGroomingReportTemplate(@Param("businessId") Integer businessId, @Param("companyId") Long companyId);

    MoeGroomingReportTemplate selectByBusinessId(Integer businessId);
}
