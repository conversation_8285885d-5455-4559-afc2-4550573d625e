package com.moego.server.grooming.service.ob;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.enums.QuestionConst;
import com.moego.common.params.VaccineParams;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IPetNoteService;
import com.moego.server.customer.api.IPetPhotoService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.customer.params.PetNoteSaveVo;
import com.moego.server.customer.params.PetPhotoParams;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.mapper.MoeBookOnlineQuestionSaveMapper;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionSave;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.util.CustomQuestionUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Service
@RequiredArgsConstructor
public class OBPetService {

    private final IPetService petService;
    private final IPetNoteService petNoteService;
    private final IPetPhotoService petPhotoService;

    private final OBQuestionService questionService;
    private final MoeBookOnlineQuestionSaveMapper moeBookOnlineQuestionSaveMapper;
    private final MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;
    private final ICustomerProfileRequestService customerProfileRequestApi;

    /**
     * List customer pet by pet id list
     *
     * @param petIdList pet id list
     * @return customer pet map, key: pet id, value: customer pet detail dto
     */
    public Map<Integer, CustomerPetDetailDTO> getCustomerPetListByIdList(List<Integer> petIdList) {
        return petService.getCustomerPetListByIdList(petIdList).stream()
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));
    }

    public Integer addPet(CustomerPetAddParams customerPetAddParams) {
        ResponseResult<Integer> result = petService.insertCustomerPet(
                AuthContext.get().companyId(), AuthContext.get().getBusinessId(), customerPetAddParams);
        if (Objects.nonNull(result.getData())) {
            return result.getData();
        }
        return null;
    }

    /**
     * 存储宠物信息
     *
     * @param obParams   submit params
     * @param customerId customer id
     */
    public void savePet(Integer customerId, BookOnlineSubmitParams obParams) {
        for (BookOnlinePetParams bookOnlinePetParams : obParams.getPetData()) {
            if (bookOnlinePetParams.getPetId() != null) {
                continue;
            }

            CustomerPetAddParams tmpPetVo = new CustomerPetAddParams();
            BeanUtils.copyProperties(bookOnlinePetParams, tmpPetVo);
            tmpPetVo.setCustomerId(customerId);
            // C 端新增/更新 vaccine 的场景，都需要经过 B 端 review，这里 set 空，避免直接将 vaccine 写入到 B 端
            tmpPetVo.setVaccineList(List.of());

            ResponseResult createPetResp =
                    petService.insertCustomerPet(obParams.getCompanyId(), obParams.getBusinessId(), tmpPetVo);
            var petId = (Integer) createPetResp.getData();
            bookOnlinePetParams.setPetId(petId);

            MoeBookOnlineQuestionSave customQuestion = new MoeBookOnlineQuestionSave();
            customQuestion.setBusinessId(obParams.getBusinessId());
            customQuestion.setCompanyId(obParams.getCompanyId());
            customQuestion.setPetId(bookOnlinePetParams.getPetId());
            customQuestion.setCustomerId(customerId);
            customQuestion.setType(QuestionConst.TYPE_PET_QUESTION);
            customQuestion.setCreateTime(DateUtil.get10Timestamp());
            customQuestion.setUpdateTime(customQuestion.getCreateTime());
            List<GroomingQuestionDTO> petQuestions = questionService.getQuestionsByBusinessId(
                    obParams.getBusinessId(), QuestionConst.TYPE_PET_QUESTION.intValue());
            customQuestion.setFormJson(JsonUtil.toJson(petQuestions));
            Map<String, Object> petAnswers = bookOnlinePetParams.getPetQuestionAnswers();
            customQuestion.setQuestionJson(JsonUtil.toJson(petAnswers));
            moeBookOnlineQuestionSaveMapper.insertSelective(customQuestion);

            // save answers to pet note
            if (!CollectionUtils.isEmpty(petAnswers)) {
                PetNoteSaveVo petNoteSaveVo = new PetNoteSaveVo();
                petNoteSaveVo.setPetId(bookOnlinePetParams.getPetId());
                petNoteSaveVo.setNote(CustomQuestionUtil.generateNote(petQuestions, petAnswers));
                petNoteService.insertPetNote(0, petNoteSaveVo);
            }
            // pet image(only one image)  4535  moe_pet_photo
            if (!ObjectUtils.isEmpty(bookOnlinePetParams.getPetImage())) {
                PetPhotoParams petPhoto = new PetPhotoParams();
                petPhoto.setPetId(bookOnlinePetParams.getPetId()); // save for pet detail
                petPhoto.setPhotoUrl(bookOnlinePetParams.getPetImage());
                petPhoto.setDescription("OB client submit for " + bookOnlinePetParams.getPetName());
                petPhotoService.savePetPhoto(obParams.getBusinessId(), petPhoto);
            }

            if (!ObjectUtils.isEmpty(bookOnlinePetParams.getVaccineList())) {
                updateProfileRequest(bookOnlinePetParams.getVaccineList(), obParams.getBusinessId(), customerId, petId);
            }
        }
    }

    private void updateProfileRequest(
            List<VaccineParams> vaccines, Integer businessId, Integer customerId, Integer petId) {
        var obSetting = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
        if (obSetting == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "OB setting not found, businessId: " + businessId);
        }

        boolean autoAcceptConflict =
                OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(obSetting.getRequestSubmittedAutoType());

        var vaccineList = vaccines.stream()
                .map(e -> {
                    var vaccine = new VaccineBindingRecordDto();
                    if (isNormal(e.getVaccineBindingId())) {
                        vaccine.setVaccineBindingId(e.getVaccineBindingId());
                    }
                    vaccine.setVaccineId(e.getVaccineId());
                    vaccine.setExpirationDate(e.getExpirationDate());
                    vaccine.setDocumentUrls(e.getDocumentUrls());
                    return vaccine;
                })
                .toList();

        customerProfileRequestApi.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(businessId)
                        .companyId(obSetting.getCompanyId())
                        .customerId(customerId)
                        .pets(List.of(new CustomerProfileRequestDTO.PetProfileDTO()
                                .setPetId(petId)
                                .setVaccineList(vaccineList)))
                        .build(),
                autoAcceptConflict);
    }
}
