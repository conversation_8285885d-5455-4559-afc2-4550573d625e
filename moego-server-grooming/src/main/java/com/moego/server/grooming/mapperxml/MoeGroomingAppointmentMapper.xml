<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="CHAR" property="orderId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="appointment_date" jdbcType="VARCHAR" property="appointmentDate" />
    <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
    <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
    <result column="is_waiting_list" jdbcType="TINYINT" property="isWaitingList" />
    <result column="move_waiting_by" jdbcType="INTEGER" property="moveWaitingBy" />
    <result column="confirmed_time" jdbcType="BIGINT" property="confirmedTime" />
    <result column="check_in_time" jdbcType="BIGINT" property="checkInTime" />
    <result column="check_out_time" jdbcType="BIGINT" property="checkOutTime" />
    <result column="canceled_time" jdbcType="BIGINT" property="canceledTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_block" jdbcType="BOOLEAN" property="isBlock" />
    <result column="book_online_status" jdbcType="TINYINT" property="bookOnlineStatus" />
    <result column="customer_address_id" jdbcType="INTEGER" property="customerAddressId" />
    <result column="repeat_id" jdbcType="INTEGER" property="repeatId" />
    <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="no_show" jdbcType="TINYINT" property="noShow" />
    <result column="no_show_fee" jdbcType="DECIMAL" property="noShowFee" />
    <result column="is_pust_notification" jdbcType="TINYINT" property="isPustNotification" />
    <result column="cancel_by_type" jdbcType="TINYINT" property="cancelByType" />
    <result column="cancel_by" jdbcType="INTEGER" property="cancelBy" />
    <result column="confirm_by_type" jdbcType="TINYINT" property="confirmByType" />
    <result column="confirm_by" jdbcType="INTEGER" property="confirmBy" />
    <result column="created_by_id" jdbcType="INTEGER" property="createdById" />
    <result column="out_of_area" jdbcType="TINYINT" property="outOfArea" />
    <result column="is_deprecate" jdbcType="BOOLEAN" property="isDeprecate" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="old_appointment_date" jdbcType="VARCHAR" property="oldAppointmentDate" />
    <result column="old_appointment_start_time" jdbcType="INTEGER" property="oldAppointmentStartTime" />
    <result column="old_appointment_end_time" jdbcType="INTEGER" property="oldAppointmentEndTime" />
    <result column="old_appt_id" jdbcType="INTEGER" property="oldApptId" />
    <result column="schedule_type" jdbcType="TINYINT" property="scheduleType" />
    <result column="source_platform" jdbcType="VARCHAR" property="sourcePlatform" />
    <result column="ready_time" jdbcType="BIGINT" property="readyTime" />
    <result column="pickup_notification_send_status" jdbcType="INTEGER" property="pickupNotificationSendStatus" />
    <result column="pickup_notification_failed_reason" jdbcType="VARCHAR" property="pickupNotificationFailedReason" />
    <result column="status_before_checkin" jdbcType="TINYINT" property="statusBeforeCheckin" typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler" />
    <result column="status_before_ready" jdbcType="TINYINT" property="statusBeforeReady" typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler" />
    <result column="status_before_finish" jdbcType="TINYINT" property="statusBeforeFinish" typeHandler="com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler" />
    <result column="no_start_time" jdbcType="BIT" property="noStartTime" />
    <result column="updated_by_id" jdbcType="BIGINT" property="updatedById" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="is_auto_accept" jdbcType="BIT" property="isAutoAccept" />
    <result column="wait_list_status" jdbcType="TINYINT" property="waitListStatus" typeHandler="com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler" />
    <result column="appointment_end_date" jdbcType="VARCHAR" property="appointmentEndDate" />
    <result column="service_type_include" jdbcType="INTEGER" property="serviceTypeInclude" />
    <result column="no_show_by" jdbcType="BIGINT" property="noShowBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeCheckinCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeReadyCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeFinishCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.waitListStatusCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeCheckinCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeReadyCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.statusBeforeFinishCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.waitListStatusCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler} and #{criterion.secondValue,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, business_id, customer_id, appointment_date, appointment_start_time,
    appointment_end_time, is_waiting_list, move_waiting_by, confirmed_time, check_in_time,
    check_out_time, canceled_time, status, is_block, book_online_status, customer_address_id,
    repeat_id, is_paid, color_code, no_show, no_show_fee, is_pust_notification, cancel_by_type,
    cancel_by, confirm_by_type, confirm_by, created_by_id, out_of_area, is_deprecate,
    create_time, update_time, source, old_appointment_date, old_appointment_start_time,
    old_appointment_end_time, old_appt_id, schedule_type, source_platform, ready_time,
    pickup_notification_send_status, pickup_notification_failed_reason, status_before_checkin,
    status_before_ready, status_before_finish, no_start_time, updated_by_id, company_id,
    is_auto_accept, wait_list_status, appointment_end_date, service_type_include, no_show_by
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_appointment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_grooming_appointment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_appointment (order_id, business_id, customer_id,
      appointment_date, appointment_start_time,
      appointment_end_time, is_waiting_list, move_waiting_by,
      confirmed_time, check_in_time, check_out_time,
      canceled_time, status, is_block,
      book_online_status, customer_address_id, repeat_id,
      is_paid, color_code, no_show,
      no_show_fee, is_pust_notification, cancel_by_type,
      cancel_by, confirm_by_type, confirm_by,
      created_by_id, out_of_area, is_deprecate,
      create_time, update_time, source,
      old_appointment_date, old_appointment_start_time,
      old_appointment_end_time, old_appt_id, schedule_type,
      source_platform, ready_time, pickup_notification_send_status,
      pickup_notification_failed_reason, status_before_checkin,
      status_before_ready,
      status_before_finish,
      no_start_time, updated_by_id, company_id,
      is_auto_accept, wait_list_status,
      appointment_end_date, service_type_include,
      no_show_by)
    values (#{orderId,jdbcType=CHAR}, #{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER},
      #{appointmentDate,jdbcType=VARCHAR}, #{appointmentStartTime,jdbcType=INTEGER},
      #{appointmentEndTime,jdbcType=INTEGER}, #{isWaitingList,jdbcType=TINYINT}, #{moveWaitingBy,jdbcType=INTEGER},
      #{confirmedTime,jdbcType=BIGINT}, #{checkInTime,jdbcType=BIGINT}, #{checkOutTime,jdbcType=BIGINT},
      #{canceledTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{isBlock,jdbcType=BOOLEAN},
      #{bookOnlineStatus,jdbcType=TINYINT}, #{customerAddressId,jdbcType=INTEGER}, #{repeatId,jdbcType=INTEGER},
      #{isPaid,jdbcType=TINYINT}, #{colorCode,jdbcType=VARCHAR}, #{noShow,jdbcType=TINYINT},
      #{noShowFee,jdbcType=DECIMAL}, #{isPustNotification,jdbcType=TINYINT}, #{cancelByType,jdbcType=TINYINT},
      #{cancelBy,jdbcType=INTEGER}, #{confirmByType,jdbcType=TINYINT}, #{confirmBy,jdbcType=INTEGER},
      #{createdById,jdbcType=INTEGER}, #{outOfArea,jdbcType=TINYINT}, #{isDeprecate,jdbcType=BOOLEAN},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{source,jdbcType=INTEGER},
      #{oldAppointmentDate,jdbcType=VARCHAR}, #{oldAppointmentStartTime,jdbcType=INTEGER},
      #{oldAppointmentEndTime,jdbcType=INTEGER}, #{oldApptId,jdbcType=INTEGER}, #{scheduleType,jdbcType=TINYINT},
      #{sourcePlatform,jdbcType=VARCHAR}, #{readyTime,jdbcType=BIGINT}, #{pickupNotificationSendStatus,jdbcType=INTEGER},
      #{pickupNotificationFailedReason,jdbcType=VARCHAR}, #{statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      #{statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      #{statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      #{noStartTime,jdbcType=BIT}, #{updatedById,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT},
      #{isAutoAccept,jdbcType=BIT}, #{waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      #{appointmentEndDate,jdbcType=VARCHAR}, #{serviceTypeInclude,jdbcType=INTEGER},
      #{noShowBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_grooming_appointment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="appointmentDate != null">
        appointment_date,
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time,
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time,
      </if>
      <if test="isWaitingList != null">
        is_waiting_list,
      </if>
      <if test="moveWaitingBy != null">
        move_waiting_by,
      </if>
      <if test="confirmedTime != null">
        confirmed_time,
      </if>
      <if test="checkInTime != null">
        check_in_time,
      </if>
      <if test="checkOutTime != null">
        check_out_time,
      </if>
      <if test="canceledTime != null">
        canceled_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isBlock != null">
        is_block,
      </if>
      <if test="bookOnlineStatus != null">
        book_online_status,
      </if>
      <if test="customerAddressId != null">
        customer_address_id,
      </if>
      <if test="repeatId != null">
        repeat_id,
      </if>
      <if test="isPaid != null">
        is_paid,
      </if>
      <if test="colorCode != null">
        color_code,
      </if>
      <if test="noShow != null">
        no_show,
      </if>
      <if test="noShowFee != null">
        no_show_fee,
      </if>
      <if test="isPustNotification != null">
        is_pust_notification,
      </if>
      <if test="cancelByType != null">
        cancel_by_type,
      </if>
      <if test="cancelBy != null">
        cancel_by,
      </if>
      <if test="confirmByType != null">
        confirm_by_type,
      </if>
      <if test="confirmBy != null">
        confirm_by,
      </if>
      <if test="createdById != null">
        created_by_id,
      </if>
      <if test="outOfArea != null">
        out_of_area,
      </if>
      <if test="isDeprecate != null">
        is_deprecate,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="oldAppointmentDate != null">
        old_appointment_date,
      </if>
      <if test="oldAppointmentStartTime != null">
        old_appointment_start_time,
      </if>
      <if test="oldAppointmentEndTime != null">
        old_appointment_end_time,
      </if>
      <if test="oldApptId != null">
        old_appt_id,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="sourcePlatform != null">
        source_platform,
      </if>
      <if test="readyTime != null">
        ready_time,
      </if>
      <if test="pickupNotificationSendStatus != null">
        pickup_notification_send_status,
      </if>
      <if test="pickupNotificationFailedReason != null">
        pickup_notification_failed_reason,
      </if>
      <if test="statusBeforeCheckin != null">
        status_before_checkin,
      </if>
      <if test="statusBeforeReady != null">
        status_before_ready,
      </if>
      <if test="statusBeforeFinish != null">
        status_before_finish,
      </if>
      <if test="noStartTime != null">
        no_start_time,
      </if>
      <if test="updatedById != null">
        updated_by_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="isAutoAccept != null">
        is_auto_accept,
      </if>
      <if test="waitListStatus != null">
        wait_list_status,
      </if>
      <if test="appointmentEndDate != null">
        appointment_end_date,
      </if>
      <if test="serviceTypeInclude != null">
        service_type_include,
      </if>
      <if test="noShowBy != null">
        no_show_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=CHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="appointmentDate != null">
        #{appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStartTime != null">
        #{appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="appointmentEndTime != null">
        #{appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="isWaitingList != null">
        #{isWaitingList,jdbcType=TINYINT},
      </if>
      <if test="moveWaitingBy != null">
        #{moveWaitingBy,jdbcType=INTEGER},
      </if>
      <if test="confirmedTime != null">
        #{confirmedTime,jdbcType=BIGINT},
      </if>
      <if test="checkInTime != null">
        #{checkInTime,jdbcType=BIGINT},
      </if>
      <if test="checkOutTime != null">
        #{checkOutTime,jdbcType=BIGINT},
      </if>
      <if test="canceledTime != null">
        #{canceledTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isBlock != null">
        #{isBlock,jdbcType=BOOLEAN},
      </if>
      <if test="bookOnlineStatus != null">
        #{bookOnlineStatus,jdbcType=TINYINT},
      </if>
      <if test="customerAddressId != null">
        #{customerAddressId,jdbcType=INTEGER},
      </if>
      <if test="repeatId != null">
        #{repeatId,jdbcType=INTEGER},
      </if>
      <if test="isPaid != null">
        #{isPaid,jdbcType=TINYINT},
      </if>
      <if test="colorCode != null">
        #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="noShow != null">
        #{noShow,jdbcType=TINYINT},
      </if>
      <if test="noShowFee != null">
        #{noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="isPustNotification != null">
        #{isPustNotification,jdbcType=TINYINT},
      </if>
      <if test="cancelByType != null">
        #{cancelByType,jdbcType=TINYINT},
      </if>
      <if test="cancelBy != null">
        #{cancelBy,jdbcType=INTEGER},
      </if>
      <if test="confirmByType != null">
        #{confirmByType,jdbcType=TINYINT},
      </if>
      <if test="confirmBy != null">
        #{confirmBy,jdbcType=INTEGER},
      </if>
      <if test="createdById != null">
        #{createdById,jdbcType=INTEGER},
      </if>
      <if test="outOfArea != null">
        #{outOfArea,jdbcType=TINYINT},
      </if>
      <if test="isDeprecate != null">
        #{isDeprecate,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="oldAppointmentDate != null">
        #{oldAppointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="oldAppointmentStartTime != null">
        #{oldAppointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="oldAppointmentEndTime != null">
        #{oldAppointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="oldApptId != null">
        #{oldApptId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="sourcePlatform != null">
        #{sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="readyTime != null">
        #{readyTime,jdbcType=BIGINT},
      </if>
      <if test="pickupNotificationSendStatus != null">
        #{pickupNotificationSendStatus,jdbcType=INTEGER},
      </if>
      <if test="pickupNotificationFailedReason != null">
        #{pickupNotificationFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="statusBeforeCheckin != null">
        #{statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="statusBeforeReady != null">
        #{statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="statusBeforeFinish != null">
        #{statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="noStartTime != null">
        #{noStartTime,jdbcType=BIT},
      </if>
      <if test="updatedById != null">
        #{updatedById,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isAutoAccept != null">
        #{isAutoAccept,jdbcType=BIT},
      </if>
      <if test="waitListStatus != null">
        #{waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      </if>
      <if test="appointmentEndDate != null">
        #{appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="serviceTypeInclude != null">
        #{serviceTypeInclude,jdbcType=INTEGER},
      </if>
      <if test="noShowBy != null">
        #{noShowBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_grooming_appointment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_appointment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=CHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentDate != null">
        appointment_date = #{record.appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentStartTime != null">
        appointment_start_time = #{record.appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentEndTime != null">
        appointment_end_time = #{record.appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="record.isWaitingList != null">
        is_waiting_list = #{record.isWaitingList,jdbcType=TINYINT},
      </if>
      <if test="record.moveWaitingBy != null">
        move_waiting_by = #{record.moveWaitingBy,jdbcType=INTEGER},
      </if>
      <if test="record.confirmedTime != null">
        confirmed_time = #{record.confirmedTime,jdbcType=BIGINT},
      </if>
      <if test="record.checkInTime != null">
        check_in_time = #{record.checkInTime,jdbcType=BIGINT},
      </if>
      <if test="record.checkOutTime != null">
        check_out_time = #{record.checkOutTime,jdbcType=BIGINT},
      </if>
      <if test="record.canceledTime != null">
        canceled_time = #{record.canceledTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isBlock != null">
        is_block = #{record.isBlock,jdbcType=BOOLEAN},
      </if>
      <if test="record.bookOnlineStatus != null">
        book_online_status = #{record.bookOnlineStatus,jdbcType=TINYINT},
      </if>
      <if test="record.customerAddressId != null">
        customer_address_id = #{record.customerAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.repeatId != null">
        repeat_id = #{record.repeatId,jdbcType=INTEGER},
      </if>
      <if test="record.isPaid != null">
        is_paid = #{record.isPaid,jdbcType=TINYINT},
      </if>
      <if test="record.colorCode != null">
        color_code = #{record.colorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.noShow != null">
        no_show = #{record.noShow,jdbcType=TINYINT},
      </if>
      <if test="record.noShowFee != null">
        no_show_fee = #{record.noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="record.isPustNotification != null">
        is_pust_notification = #{record.isPustNotification,jdbcType=TINYINT},
      </if>
      <if test="record.cancelByType != null">
        cancel_by_type = #{record.cancelByType,jdbcType=TINYINT},
      </if>
      <if test="record.cancelBy != null">
        cancel_by = #{record.cancelBy,jdbcType=INTEGER},
      </if>
      <if test="record.confirmByType != null">
        confirm_by_type = #{record.confirmByType,jdbcType=TINYINT},
      </if>
      <if test="record.confirmBy != null">
        confirm_by = #{record.confirmBy,jdbcType=INTEGER},
      </if>
      <if test="record.createdById != null">
        created_by_id = #{record.createdById,jdbcType=INTEGER},
      </if>
      <if test="record.outOfArea != null">
        out_of_area = #{record.outOfArea,jdbcType=TINYINT},
      </if>
      <if test="record.isDeprecate != null">
        is_deprecate = #{record.isDeprecate,jdbcType=BOOLEAN},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.oldAppointmentDate != null">
        old_appointment_date = #{record.oldAppointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.oldAppointmentStartTime != null">
        old_appointment_start_time = #{record.oldAppointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="record.oldAppointmentEndTime != null">
        old_appointment_end_time = #{record.oldAppointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="record.oldApptId != null">
        old_appt_id = #{record.oldApptId,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleType != null">
        schedule_type = #{record.scheduleType,jdbcType=TINYINT},
      </if>
      <if test="record.sourcePlatform != null">
        source_platform = #{record.sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.readyTime != null">
        ready_time = #{record.readyTime,jdbcType=BIGINT},
      </if>
      <if test="record.pickupNotificationSendStatus != null">
        pickup_notification_send_status = #{record.pickupNotificationSendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.pickupNotificationFailedReason != null">
        pickup_notification_failed_reason = #{record.pickupNotificationFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="record.statusBeforeCheckin != null">
        status_before_checkin = #{record.statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="record.statusBeforeReady != null">
        status_before_ready = #{record.statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="record.statusBeforeFinish != null">
        status_before_finish = #{record.statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="record.noStartTime != null">
        no_start_time = #{record.noStartTime,jdbcType=BIT},
      </if>
      <if test="record.updatedById != null">
        updated_by_id = #{record.updatedById,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.isAutoAccept != null">
        is_auto_accept = #{record.isAutoAccept,jdbcType=BIT},
      </if>
      <if test="record.waitListStatus != null">
        wait_list_status = #{record.waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      </if>
      <if test="record.appointmentEndDate != null">
        appointment_end_date = #{record.appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceTypeInclude != null">
        service_type_include = #{record.serviceTypeInclude,jdbcType=INTEGER},
      </if>
      <if test="record.noShowBy != null">
        no_show_by = #{record.noShowBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_appointment
    set id = #{record.id,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=CHAR},
      business_id = #{record.businessId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      appointment_date = #{record.appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{record.appointmentStartTime,jdbcType=INTEGER},
      appointment_end_time = #{record.appointmentEndTime,jdbcType=INTEGER},
      is_waiting_list = #{record.isWaitingList,jdbcType=TINYINT},
      move_waiting_by = #{record.moveWaitingBy,jdbcType=INTEGER},
      confirmed_time = #{record.confirmedTime,jdbcType=BIGINT},
      check_in_time = #{record.checkInTime,jdbcType=BIGINT},
      check_out_time = #{record.checkOutTime,jdbcType=BIGINT},
      canceled_time = #{record.canceledTime,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      is_block = #{record.isBlock,jdbcType=BOOLEAN},
      book_online_status = #{record.bookOnlineStatus,jdbcType=TINYINT},
      customer_address_id = #{record.customerAddressId,jdbcType=INTEGER},
      repeat_id = #{record.repeatId,jdbcType=INTEGER},
      is_paid = #{record.isPaid,jdbcType=TINYINT},
      color_code = #{record.colorCode,jdbcType=VARCHAR},
      no_show = #{record.noShow,jdbcType=TINYINT},
      no_show_fee = #{record.noShowFee,jdbcType=DECIMAL},
      is_pust_notification = #{record.isPustNotification,jdbcType=TINYINT},
      cancel_by_type = #{record.cancelByType,jdbcType=TINYINT},
      cancel_by = #{record.cancelBy,jdbcType=INTEGER},
      confirm_by_type = #{record.confirmByType,jdbcType=TINYINT},
      confirm_by = #{record.confirmBy,jdbcType=INTEGER},
      created_by_id = #{record.createdById,jdbcType=INTEGER},
      out_of_area = #{record.outOfArea,jdbcType=TINYINT},
      is_deprecate = #{record.isDeprecate,jdbcType=BOOLEAN},
      create_time = #{record.createTime,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      source = #{record.source,jdbcType=INTEGER},
      old_appointment_date = #{record.oldAppointmentDate,jdbcType=VARCHAR},
      old_appointment_start_time = #{record.oldAppointmentStartTime,jdbcType=INTEGER},
      old_appointment_end_time = #{record.oldAppointmentEndTime,jdbcType=INTEGER},
      old_appt_id = #{record.oldApptId,jdbcType=INTEGER},
      schedule_type = #{record.scheduleType,jdbcType=TINYINT},
      source_platform = #{record.sourcePlatform,jdbcType=VARCHAR},
      ready_time = #{record.readyTime,jdbcType=BIGINT},
      pickup_notification_send_status = #{record.pickupNotificationSendStatus,jdbcType=INTEGER},
      pickup_notification_failed_reason = #{record.pickupNotificationFailedReason,jdbcType=VARCHAR},
      status_before_checkin = #{record.statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      status_before_ready = #{record.statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      status_before_finish = #{record.statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      no_start_time = #{record.noStartTime,jdbcType=BIT},
      updated_by_id = #{record.updatedById,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      is_auto_accept = #{record.isAutoAccept,jdbcType=BIT},
      wait_list_status = #{record.waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      appointment_end_date = #{record.appointmentEndDate,jdbcType=VARCHAR},
      service_type_include = #{record.serviceTypeInclude,jdbcType=INTEGER},
      no_show_by = #{record.noShowBy,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_appointment
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=CHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="appointmentDate != null">
        appointment_date = #{appointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time = #{appointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time = #{appointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="isWaitingList != null">
        is_waiting_list = #{isWaitingList,jdbcType=TINYINT},
      </if>
      <if test="moveWaitingBy != null">
        move_waiting_by = #{moveWaitingBy,jdbcType=INTEGER},
      </if>
      <if test="confirmedTime != null">
        confirmed_time = #{confirmedTime,jdbcType=BIGINT},
      </if>
      <if test="checkInTime != null">
        check_in_time = #{checkInTime,jdbcType=BIGINT},
      </if>
      <if test="checkOutTime != null">
        check_out_time = #{checkOutTime,jdbcType=BIGINT},
      </if>
      <if test="canceledTime != null">
        canceled_time = #{canceledTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isBlock != null">
        is_block = #{isBlock,jdbcType=BOOLEAN},
      </if>
      <if test="bookOnlineStatus != null">
        book_online_status = #{bookOnlineStatus,jdbcType=TINYINT},
      </if>
      <if test="customerAddressId != null">
        customer_address_id = #{customerAddressId,jdbcType=INTEGER},
      </if>
      <if test="repeatId != null">
        repeat_id = #{repeatId,jdbcType=INTEGER},
      </if>
      <if test="isPaid != null">
        is_paid = #{isPaid,jdbcType=TINYINT},
      </if>
      <if test="colorCode != null">
        color_code = #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="noShow != null">
        no_show = #{noShow,jdbcType=TINYINT},
      </if>
      <if test="noShowFee != null">
        no_show_fee = #{noShowFee,jdbcType=DECIMAL},
      </if>
      <if test="isPustNotification != null">
        is_pust_notification = #{isPustNotification,jdbcType=TINYINT},
      </if>
      <if test="cancelByType != null">
        cancel_by_type = #{cancelByType,jdbcType=TINYINT},
      </if>
      <if test="cancelBy != null">
        cancel_by = #{cancelBy,jdbcType=INTEGER},
      </if>
      <if test="confirmByType != null">
        confirm_by_type = #{confirmByType,jdbcType=TINYINT},
      </if>
      <if test="confirmBy != null">
        confirm_by = #{confirmBy,jdbcType=INTEGER},
      </if>
      <if test="createdById != null">
        created_by_id = #{createdById,jdbcType=INTEGER},
      </if>
      <if test="outOfArea != null">
        out_of_area = #{outOfArea,jdbcType=TINYINT},
      </if>
      <if test="isDeprecate != null">
        is_deprecate = #{isDeprecate,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="oldAppointmentDate != null">
        old_appointment_date = #{oldAppointmentDate,jdbcType=VARCHAR},
      </if>
      <if test="oldAppointmentStartTime != null">
        old_appointment_start_time = #{oldAppointmentStartTime,jdbcType=INTEGER},
      </if>
      <if test="oldAppointmentEndTime != null">
        old_appointment_end_time = #{oldAppointmentEndTime,jdbcType=INTEGER},
      </if>
      <if test="oldApptId != null">
        old_appt_id = #{oldApptId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="sourcePlatform != null">
        source_platform = #{sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="readyTime != null">
        ready_time = #{readyTime,jdbcType=BIGINT},
      </if>
      <if test="pickupNotificationSendStatus != null">
        pickup_notification_send_status = #{pickupNotificationSendStatus,jdbcType=INTEGER},
      </if>
      <if test="pickupNotificationFailedReason != null">
        pickup_notification_failed_reason = #{pickupNotificationFailedReason,jdbcType=VARCHAR},
      </if>
      <if test="statusBeforeCheckin != null">
        status_before_checkin = #{statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="statusBeforeReady != null">
        status_before_ready = #{statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="statusBeforeFinish != null">
        status_before_finish = #{statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      </if>
      <if test="noStartTime != null">
        no_start_time = #{noStartTime,jdbcType=BIT},
      </if>
      <if test="updatedById != null">
        updated_by_id = #{updatedById,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isAutoAccept != null">
        is_auto_accept = #{isAutoAccept,jdbcType=BIT},
      </if>
      <if test="waitListStatus != null">
        wait_list_status = #{waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      </if>
      <if test="appointmentEndDate != null">
        appointment_end_date = #{appointmentEndDate,jdbcType=VARCHAR},
      </if>
      <if test="serviceTypeInclude != null">
        service_type_include = #{serviceTypeInclude,jdbcType=INTEGER},
      </if>
      <if test="noShowBy != null">
        no_show_by = #{noShowBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_grooming_appointment
    set order_id = #{orderId,jdbcType=CHAR},
      business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      appointment_date = #{appointmentDate,jdbcType=VARCHAR},
      appointment_start_time = #{appointmentStartTime,jdbcType=INTEGER},
      appointment_end_time = #{appointmentEndTime,jdbcType=INTEGER},
      is_waiting_list = #{isWaitingList,jdbcType=TINYINT},
      move_waiting_by = #{moveWaitingBy,jdbcType=INTEGER},
      confirmed_time = #{confirmedTime,jdbcType=BIGINT},
      check_in_time = #{checkInTime,jdbcType=BIGINT},
      check_out_time = #{checkOutTime,jdbcType=BIGINT},
      canceled_time = #{canceledTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      is_block = #{isBlock,jdbcType=BOOLEAN},
      book_online_status = #{bookOnlineStatus,jdbcType=TINYINT},
      customer_address_id = #{customerAddressId,jdbcType=INTEGER},
      repeat_id = #{repeatId,jdbcType=INTEGER},
      is_paid = #{isPaid,jdbcType=TINYINT},
      color_code = #{colorCode,jdbcType=VARCHAR},
      no_show = #{noShow,jdbcType=TINYINT},
      no_show_fee = #{noShowFee,jdbcType=DECIMAL},
      is_pust_notification = #{isPustNotification,jdbcType=TINYINT},
      cancel_by_type = #{cancelByType,jdbcType=TINYINT},
      cancel_by = #{cancelBy,jdbcType=INTEGER},
      confirm_by_type = #{confirmByType,jdbcType=TINYINT},
      confirm_by = #{confirmBy,jdbcType=INTEGER},
      created_by_id = #{createdById,jdbcType=INTEGER},
      out_of_area = #{outOfArea,jdbcType=TINYINT},
      is_deprecate = #{isDeprecate,jdbcType=BOOLEAN},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      source = #{source,jdbcType=INTEGER},
      old_appointment_date = #{oldAppointmentDate,jdbcType=VARCHAR},
      old_appointment_start_time = #{oldAppointmentStartTime,jdbcType=INTEGER},
      old_appointment_end_time = #{oldAppointmentEndTime,jdbcType=INTEGER},
      old_appt_id = #{oldApptId,jdbcType=INTEGER},
      schedule_type = #{scheduleType,jdbcType=TINYINT},
      source_platform = #{sourcePlatform,jdbcType=VARCHAR},
      ready_time = #{readyTime,jdbcType=BIGINT},
      pickup_notification_send_status = #{pickupNotificationSendStatus,jdbcType=INTEGER},
      pickup_notification_failed_reason = #{pickupNotificationFailedReason,jdbcType=VARCHAR},
      status_before_checkin = #{statusBeforeCheckin,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      status_before_ready = #{statusBeforeReady,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      status_before_finish = #{statusBeforeFinish,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.AppointmentStatusEnumHandler},
      no_start_time = #{noStartTime,jdbcType=BIT},
      updated_by_id = #{updatedById,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      is_auto_accept = #{isAutoAccept,jdbcType=BIT},
      wait_list_status = #{waitListStatus,jdbcType=TINYINT,typeHandler=com.moego.server.grooming.mapper.typehandler.WaitListStatusEnumHandler},
      appointment_end_date = #{appointmentEndDate,jdbcType=VARCHAR},
      service_type_include = #{serviceTypeInclude,jdbcType=INTEGER},
      no_show_by = #{noShowBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <resultMap id="CustomerGroomingListResultMap" type="com.moego.server.grooming.dto.CustomerGrooming">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_end_date" property="appointmentEndDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="confirmed_time" jdbcType="BIGINT" property="confirmedTime" />
        <result column="check_in_time" jdbcType="BIGINT" property="checkInTime" />
        <result column="check_out_time" jdbcType="BIGINT" property="checkOutTime" />
        <result column="canceled_time" jdbcType="BIGINT" property="canceledTime" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="book_online_status" jdbcType="TINYINT" property="bookOnlineStatus" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="customer_address_id" jdbcType="INTEGER" property="customerAddressId" />
        <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
        <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
        <result column="cancel_by_type" jdbcType="VARCHAR" property="cancelByType" />
        <result column="no_show" jdbcType="TINYINT" property="noShow" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="service_type_include" jdbcType="INTEGER" property="serviceTypeInclude" />
        <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
        <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
        <result column="refunded_amount" jdbcType="DECIMAL" property="refundAmount" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <collection ofType="com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO" property="petServiceList">
            <result column="petDetailId" jdbcType="INTEGER" property="petDetailId" />
            <result column="pet_id" jdbcType="INTEGER" property="petId" />
            <result column="staff_id" jdbcType="INTEGER" property="staffId" />
            <result column="service_id" jdbcType="INTEGER" property="serviceId" />
            <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
            <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
            <result column="start_time" jdbcType="BIGINT" property="startTime" />
            <result column="end_time" jdbcType="BIGINT" property="endTime" />
        </collection>
    </resultMap>
    <resultMap id="GroomingBookOnlineResultMap" type="com.moego.server.grooming.dto.GroomingBookingDTO">
        <id column="id" jdbcType="INTEGER" property="groomingId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
        <result column="book_online_status" jdbcType="TINYINT" property="bookOnlineStatus" />
        <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="out_of_area" jdbcType="INTEGER" property="outOfArea" />
        <result column="staff_id" jdbcType="INTEGER" property="staffId" />
        <result column="no_start_time" jdbcType="BIT" property="noStartTime" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />

        <collection ofType="com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO" property="petList">
            <result column="petDetailId" jdbcType="INTEGER" property="petDetailId" />
            <result column="pet_id" jdbcType="INTEGER" property="petId" />
            <result column="staff_id" jdbcType="INTEGER" property="staffId" />
            <result column="service_id" jdbcType="INTEGER" property="serviceId" />
            <result column="service_time" jdbcType="INTEGER" property="serviceTime" />
            <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
            <result column="start_time" jdbcType="BIGINT" property="startTime" />
            <result column="end_time" jdbcType="BIGINT" property="endTime" />
        </collection>
    </resultMap>

    <resultMap id="GroomingAppointmentWaitingListDTOResultMap" type="com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO">
        <result column="appointment_date" property="appointmentDate" />
        <result column="ticketId" property="ticketId" />
        <result column="appointment_start_time" property="appointmentStartTime" />
        <result column="customer_address_id" property="customerAddressId" />
        <result column="customer_id" property="customerId" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="update_time" jdbcType="BIGINT" property="updateTime" />
        <collection ofType="com.moego.server.grooming.dto.WaitingListPetDetailDTO" property="waitingListPetDetails">
            <result column="petDetailId" property="petDetailId" />
            <result column="service_id" property="serviceId" />
            <result column="name" property="serviceName" />
            <result column="staff_id" property="staffId" />
            <result column="pet_id" property="petId" />
        </collection>

    </resultMap>

  <resultMap id="OBClientApptDTOResultMap" type="com.moego.server.grooming.service.dto.ob.OBClientApptDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="CHAR" property="orderId" />
    <result column="appointment_date" property="appointmentDate" />
    <result column="customer_id" property="customerId" />
    <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
    <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
  </resultMap>
    <resultMap id="CustomerGroomingAppointmentDTOResultMap" type="com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="customer_id" property="customerId" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <collection ofType="com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO" property="petDetails">
            <result column="petDetailId" property="petDetailId" />
            <result column="grooming_id" property="groomingId" />
            <result column="pet_id" property="petId" />
            <result column="staff_id" property="staffId" />
            <result column="service_id" property="serviceId" />
            <result column="service_type" property="serviceType" />
            <result column="service_time" property="serviceTime" />
            <result column="service_price" property="servicePrice" />
            <result column="status" property="status" />
            <result column="service_name" property="serviceName" />
        </collection>
    </resultMap>
    <resultMap id="BookOnlineAutoMoveAppointmentDTOResultMap" type="com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO">
        <result column="businessId" property="businessId" />
        <collection ofType="java.lang.Integer" property="groomingIds">
            <result column="groomingId" />
        </collection>
    </resultMap>
    <resultMap id="StaffAppointTimeByDateResultMap" type="com.moego.server.business.dto.TimeRangeDto">
        <result column="start_time" jdbcType="INTEGER" property="startTime" />
        <result column="end_time" jdbcType="INTEGER" property="endTime" />
    </resultMap>
    <resultMap id="StaffAppointPetCountByTimeResultMap" type="com.moego.server.business.dto.AppointmentPetCountDTO">
        <result column="staff_id" jdbcType="INTEGER" property="staffId" />
        <result column="appointment_date" jdbcType="VARCHAR" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="petCount" jdbcType="INTEGER" property="petCount" />
    </resultMap>
    <resultMap id="CustomerUpComingAppointDTOResultMap" type="com.moego.server.grooming.dto.CustomerUpComingAppointDTO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_end_date" property="appointmentEndDate" />
        <result column="customer_id" property="customerId" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="customer_address_id" property="customerAddressId" />
        <result column="created_by_id" property="createBy" />
        <result column="business_id" property="businessId" />
        <collection ofType="com.moego.server.grooming.dto.CustomerUpcomingPetDetailDTO" property="petDetails">
            <result column="petDetailId" property="petDetailId" />
            <result column="service_id" property="serviceId" />
            <result column="service_type" property="serviceType" />
            <result column="name" property="serviceName" />
            <result column="service_price" property="servicePrice" />
            <result column="pet_id" property="petId" />
            <result column="staff_id" property="staffId" />
        </collection>
    </resultMap>


    <resultMap id="AppointmentReminderSendDTOResultMap" type="com.moego.server.grooming.dto.AppointmentReminderSendDTO">
        <result column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="customer_id" property="customerId" />
        <result column="business_id" property="businessId" />
        <result column="appointment_start_time" property="appointmentStartTime" />
        <result column="appointment_end_time" property="appointmentEndTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="customer_address_id" property="customerAddressId" />
    </resultMap>

    <resultMap id="ReportAppointmentMap" type="com.moego.server.grooming.service.dto.ReportAppointmentDAO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="appointment_date" jdbcType="CHAR" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="startTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="endTime" />
        <result column="no_show" jdbcType="TINYINT" property="noShow" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="cancel_by_type" jdbcType="TINYINT" property="cancelByType" />
        <result column="cancel_by" jdbcType="INTEGER" property="cancelById" />
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="remain_amount" jdbcType="DECIMAL" property="remainAmount" />
        <result column="type" jdbcType="CHAR" property="type" />
        <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
        <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
        <result column="is_waiting_list" jdbcType="TINYINT" property="isWaitingList" />
    </resultMap>

    <resultMap id="ReportWebAppointmentMap" type="com.moego.server.grooming.service.dto.GroomingReportWebAppointment">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="confirmed_time" jdbcType="BIGINT" property="confirmedTime" />
        <result column="check_in_time" jdbcType="BIGINT" property="checkInTime" />
        <result column="check_out_time" jdbcType="BIGINT" property="checkOutTime" />
        <result column="canceled_time" jdbcType="BIGINT" property="canceledTime" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
        <result column="repeat_id" jdbcType="INTEGER" property="repeatId" />
        <result column="no_show" jdbcType="TINYINT" property="noShow" />
        <result column="cancel_by_type" jdbcType="TINYINT" property="cancelByType" />
        <result column="cancel_by" jdbcType="INTEGER" property="cancelBy" />
        <result column="confirm_by_type" jdbcType="TINYINT" property="confirmByType" />
        <result column="confirm_by" jdbcType="INTEGER" property="confirmBy" />
        <result column="created_by_id" jdbcType="INTEGER" property="createdById" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="update_time" jdbcType="BIGINT" property="updateTime" />
        <result column="no_show_fee" property="noShowFee" />
        <result column="is_waiting_list" jdbcType="TINYINT" property="isWaitingList" />
        <result column="no_show_by" property="noShowBy" />

        <collection ofType="com.moego.server.grooming.service.dto.ReportWebApptPetDetail" property="petDetails">
            <result column="pet_detail_id" property="id" />
            <result column="service_id" property="serviceId" />
            <result column="service_name" property="serviceName" />
            <result column="service_price" property="servicePrice" />
            <result column="service_type" property="serviceType" />
            <result column="pet_id" property="petId" />
            <result column="staff_id" property="staffId" />
            <result column="start_time" jdbcType="BIGINT" property="startTime" />
            <result column="end_time" jdbcType="BIGINT" property="endTime" />
            <result column="tax_id" property="taxId" />
            <result column="start_date" jdbcType="VARCHAR" property="startDate" />
            <result column="end_date" jdbcType="VARCHAR" property="endDate" />
            <result column="price_unit" jdbcType="INTEGER" property="priceUnit" />
            <result column="specific_dates" jdbcType="CHAR" property="specificDates" />
            <result column="associated_service_id" jdbcType="BIGINT" property="associatedServiceId" />
            <result column="service_item_type" jdbcType="INTEGER" property="serviceItemType" />
        </collection>

        <collection ofType="com.moego.server.grooming.service.dto.AppointmentNote" property="appointmentNotes">
            <result column="type" jdbcType="TINYINT" property="type" />
            <result column="note" jdbcType="LONGVARCHAR" property="note" />
        </collection>

    </resultMap>

    <resultMap id="GroomReportApptDetailMap" type="com.moego.server.grooming.service.dto.GroomingReportApptDetail">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="CHAR" property="orderId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="confirmed_time" jdbcType="BIGINT" property="confirmedTime" />
        <result column="check_in_time" jdbcType="BIGINT" property="checkInTime" />
        <result column="check_out_time" jdbcType="BIGINT" property="checkOutTime" />
        <result column="canceled_time" jdbcType="BIGINT" property="canceledTime" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_paid" jdbcType="TINYINT" property="isPaid" />
        <result column="repeat_id" jdbcType="INTEGER" property="repeatId" />
        <result column="no_show" jdbcType="TINYINT" property="noShow" />
        <result column="cancel_by_type" jdbcType="TINYINT" property="cancelByType" />
        <result column="cancel_by" jdbcType="INTEGER" property="cancelBy" />
        <result column="confirm_by_type" jdbcType="TINYINT" property="confirmByType" />
        <result column="confirm_by" jdbcType="INTEGER" property="confirmBy" />
        <result column="created_by_id" jdbcType="INTEGER" property="createdById" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="update_time" jdbcType="BIGINT" property="updateTime" />
        <result column="no_show_fee" property="noShowFee" />
        <result column="is_waiting_list" jdbcType="TINYINT" property="isWaitingList" />

        <collection ofType="com.moego.server.grooming.service.dto.ReportWebApptPetDetail" property="petDetails">
            <result column="pet_detail_id" property="id" />
            <result column="service_id" property="serviceId" />
            <result column="service_name" property="serviceName" />
            <result column="service_price" property="servicePrice" />
            <result column="service_type" property="serviceType" />
            <result column="pet_id" property="petId" />
            <result column="staff_id" property="staffId" />
            <result column="start_time" jdbcType="BIGINT" property="startTime" />
            <result column="end_time" jdbcType="BIGINT" property="endTime" />
            <result column="tax_id" property="taxId" />
        </collection>

        <collection ofType="com.moego.server.grooming.service.dto.AppointmentNote" property="appointmentNotes">
            <result column="type" jdbcType="TINYINT" property="type" />
            <result column="note" jdbcType="LONGVARCHAR" property="note" />
        </collection>

    </resultMap>

    <resultMap id="AppointmentPetDetailMap" type="com.moego.server.grooming.dto.AppointmentWithPetDetailsDto">
        <id column="id" jdbcType="INTEGER" property="appointmentId" />
        <result column="business_id" jdbcType="INTEGER" property="businessId" />
        <result column="customer_id" jdbcType="INTEGER" property="customerId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="appointment_date" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="appointmentEndTime" />
        <result column="source" jdbcType="INTEGER" property="source" />
        <result column="company_id" jdbcType="BIGINT" property="companyId" />
        <collection ofType="com.moego.server.grooming.dto.AppointmentServiceInfo" property="services">
            <result column="pet_detail_id" property="petDetailId" />
            <result column="service_id" property="serviceId" />
            <result column="service_name" property="serviceName" />
            <result column="service_type" property="serviceType" />
            <result column="pet_id" property="petId" />
            <result column="staff_id" property="staffId" />
            <result column="start_time" jdbcType="BIGINT" property="startTime" />
            <result column="service_time" jdbcType="BIGINT" property="serviceTime" />
            <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
            <result column="service_item_type" jdbcType="INTEGER" property="serviceItemType" />
        </collection>
    </resultMap>

    <resultMap id="BlockInfoWithStaffMap" type="com.moego.server.grooming.dto.StaffBlockInfoDTO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="staff_id" jdbcType="INTEGER" property="staffId" />
        <result column="appointment_date" jdbcType="VARCHAR" property="date" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="startTime" />
        <result column="appointment_end_time" jdbcType="INTEGER" property="endTime" />
    </resultMap>

    <resultMap id="PetBreedMAP" type="com.moego.server.grooming.dto.PetBreedInfoDTO">
        <result column="petId" jdbcType="INTEGER" property="petId" />
        <result column="amount" jdbcType="INTEGER" property="amount" />
    </resultMap>

    <resultMap id="StaffAppointPetIdByTimeResultMap" type="com.moego.server.business.dto.AppointmentPetIdDTO">
        <result column="staff_id" jdbcType="INTEGER" property="staffId" />
        <result column="appointment_date" jdbcType="VARCHAR" property="appointmentDate" />
        <result column="appointment_start_time" jdbcType="INTEGER" property="appointmentStartTime" />
        <result column="pet_id" jdbcType="INTEGER" property="petId" />
    </resultMap>

    <update id="deleteAppointments">
        update moe_grooming_appointment
        set is_deprecate = 1, update_time = unix_timestamp(now())
        where business_id = #{businessId} and id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="selectCustomerDeleteAppt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_grooming.moe_grooming_appointment
        where status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and company_id = #{companyId}
        and customer_id = #{customerId}
    </select>

    <update id="customerDelete">
        update moe_grooming_appointment
        set status        = 4,
            cancel_by     = #{staffId},
            update_time   = #{nowTime},
            canceled_time = #{nowTime}
        where status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          and company_id = #{companyId}
          and customer_id = #{customerId}
    </update>

    <select id="selectByPrimaryKeyAndBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_grooming_appointment
        where id = #{id}
        and business_id = #{businessId}
    </select>

  <select id="selectByIdListAndBusinessId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where id in
    <foreach close=")" collection="idList" item="id" open="(" separator=",">
      #{id}
    </foreach>
    and business_id = #{businessId}
  </select>

  <select id="selectByIdList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where id in
    <foreach close=")" collection="idList" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </select>

    <select id="selectBlockByApptDates" resultMap="BlockInfoWithStaffMap">
        SELECT a.id, a.appointment_date, a.appointment_start_time, a.appointment_end_time, pd.staff_id
        FROM moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.status = 1
        WHERE a.business_id = #{businessId}
        and a.service_type_include = 1
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            AND a.appointment_date IN
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND a.is_block = 1
        AND a.status = 1

    </select>

    <select id="selectBlockListByDatesAndStaffIds" resultMap="BlockInfoWithStaffMap">
        SELECT a.id, a.appointment_date, a.appointment_start_time, a.appointment_end_time, pd.staff_id
        FROM moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.status = 1
        WHERE a.business_id = #{businessId}
        and a.service_type_include = 1
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            AND a.appointment_date IN
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="staffIds != null and staffIds.size &gt; 0">
            AND pd.staff_id IN
            <foreach close=")" collection="staffIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND a.is_block = 1
        AND a.status = 1
    </select>

    <select id="queryApptsByRepeatId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_grooming_appointment
        where status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_deprecate = 0
        and is_block = 2
        and is_waiting_list = 0
        and repeat_id = #{repeatId}
        and repeat_id != 0
        and business_id = #{businessId}
        <if test="appointmentDate!=null">
            and appointment_date &gt; #{appointmentDate}
        </if>
    </select>

    <select id="queryBlocksByRepeatId" resultMap="BaseResultMap">
        select id, business_id, appointment_date, appointment_start_time, appointment_end_time, status, is_block, repeat_id, create_time, update_time, source
        from moe_grooming_appointment
        where business_id = #{businessId}
        and repeat_id = #{repeatId}
        <if test="appointmentDate!=null">
            and appointment_date &gt; #{appointmentDate}
        </if>
        and is_block = 1
        and service_type_include = 1
        order by id
    </select>

    <select id="selectBusinessCustomerIdByApptId" parameterType="int" resultMap="BaseResultMap">
        select
        business_id, customer_id
        from moe_grooming_appointment
        where business_id = #{businessId} and id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryAllBookingAutoMoveAppointment" resultMap="BookOnlineAutoMoveAppointmentDTOResultMap">
        SELECT id          as groomingId,
               business_id as businessId
        FROM moe_grooming_appointment
          FORCE INDEX (PRIMARY)
        WHERE book_online_status = 1
          and is_waiting_list = 0
          and status = 1
          and is_deprecate = 0
          and create_time &lt; unix_timestamp(NOW()) - 172800
          and business_id IN
          (
          select business_id
          from moe_business_book_online
          where is_enable = 1 and
                ((use_version = 1 and auto_move_wait = 1) or
              (use_version = 2 and request_submitted_auto_type = 'auto_move_waitlist'))
          )
          and id &gt; #{scrollId}
    </select>
    <select id="queryGroomingCustomerAppointmentNum" resultMap="CustomerGroomingListResultMap">
        SELECT
        a.id,
        a.order_id,
        a.appointment_date,
        a.appointment_end_date,
        a.appointment_start_time,
        a.appointment_end_time,
        a.confirmed_time,
        a.check_in_time,
        a.check_out_time,
        a.canceled_time,
        a.cancel_by_type,
        a.status,
        a.book_online_status,
        a.customer_address_id,
        a.is_paid,
        a.color_code,
        a.no_show,
        a.service_type_include,
        a.create_time
        FROM moe_grooming_appointment a
        WHERE a.customer_id = #{groomingCustomerQuery.customerId}
        and a.is_block = 2
        and a.is_deprecate = 0
        and a.book_online_status = 0
        <if test="groomingCustomerQuery.serviceTypeIncludes != null and groomingCustomerQuery.serviceTypeIncludes.size != 0">
          and a.service_type_include in
          <foreach close=")" collection="groomingCustomerQuery.serviceTypeIncludes" item="serviceTypeInclude" open="(" separator=",">
            #{serviceTypeInclude}
          </foreach>
        </if>
        <if test="groomingCustomerQuery.skipCancel==true">
            and status != 4
        </if>
        and a.company_id = #{groomingCustomerQuery.companyId}
        <if test="groomingCustomerQuery.businessId != null">
            and a.business_id = #{groomingCustomerQuery.businessId}
        </if>
        <if test="groomingCustomerQuery.paymentStatus !=null and groomingCustomerQuery.paymentStatus.size()&gt;0">
            and a.is_paid in
            <foreach close=")" collection="groomingCustomerQuery.paymentStatus" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="groomingCustomerQuery.appointmentStatusList !=null and groomingCustomerQuery.appointmentStatusList.size()&gt;0">
            and
            <foreach close=")" collection="groomingCustomerQuery.appointmentStatusList" item="item" open="(" separator="or">
              <choose>
                <when test="item.value == 1 or item.value == 2">
                  (a.status = #{item.value} and a.check_in_time = 0)
                </when>
                <when test="item.value == 6">
                  (a.status in (1, 2, 6) and a.check_in_time &gt; 0)
                </when>
                <otherwise>
                  (a.status = #{item.value})
                </otherwise>
              </choose>
            </foreach>
        </if>
        <choose>
            <when test="groomingCustomerQuery.type !=null and groomingCustomerQuery.type == 1">
                and a.is_waiting_list = 0
                and ((a.appointment_end_date &gt; #{groomingCustomerQuery.appointmentDate})
                or (a.appointment_end_date = #{groomingCustomerQuery.appointmentDate}
                and a.appointment_end_time &gt; #{groomingCustomerQuery.endTime}))
            </when>
            <when test="groomingCustomerQuery.type !=null and groomingCustomerQuery.type == 2">
                and a.is_waiting_list = 0
                and ((a.appointment_end_date &lt; #{groomingCustomerQuery.appointmentDate})
                or (a.appointment_end_date = #{groomingCustomerQuery.appointmentDate}
                and a.appointment_end_time &lt; #{groomingCustomerQuery.endTime}))
            </when>
            <when test="groomingCustomerQuery.type != null and groomingCustomerQuery.type == 4">
                and a.is_waiting_list = 0
                and a.status = 4
            </when>
            <when test="groomingCustomerQuery.type != null and groomingCustomerQuery.type == 5">
                and a.is_waiting_list = 0
                and a.status = 4
                and a.no_show = 1
            </when>
            <otherwise>
                and (a.is_waiting_list = 1 or a.wait_list_status in (1,2))
            </otherwise>
        </choose>
        <choose>
            <when test="groomingCustomerQuery.orderType != null and groomingCustomerQuery.orderType==1">
                order by a.appointment_date asc, a.appointment_start_time asc
            </when>
            <otherwise>
                order by a.appointment_date desc, a.appointment_start_time desc
            </otherwise>
        </choose>
    </select>

    <select id="queryGroomingCustomerAppointmentNumByCompany" resultMap="CustomerGroomingListResultMap">
      SELECT
      a.id,
      a.order_id,
      a.appointment_date,
      a.appointment_end_date,
      a.appointment_start_time,
      a.appointment_end_time,
      a.confirmed_time,
      a.check_in_time,
      a.check_out_time,
      a.canceled_time,
      a.cancel_by_type,
      a.status,
      a.book_online_status,
      a.customer_address_id,
      a.is_paid,
      a.color_code,
      a.no_show,
      a.create_time,
      a.business_id
      FROM moe_grooming_appointment a
      WHERE a.customer_id = #{vo.customerId}
      and a.is_block = 2
      and a.is_deprecate = 0
      and a.book_online_status = 0
      and a.service_type_include = 1
      <if test="vo.skipCancel==true">
        and status != 4
      </if>
      and a.company_id = #{companyId}
      <if test="vo.paymentStatus !=null and vo.paymentStatus.size()&gt;0">
        and a.is_paid in
        <foreach close=")" collection="vo.paymentStatus" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="vo.appointmentStatusList !=null and vo.appointmentStatusList.size()&gt;0">
        and
        <foreach close=")" collection="vo.appointmentStatusList" item="item" open="(" separator="or">
          <choose>
            <when test="item.value == 1 or item.value == 2">
              (a.status = #{item.value} and a.check_in_time = 0)
            </when>
            <when test="item.value == 6">
              (a.status in (1, 2, 6) and a.check_in_time &gt; 0)
            </when>
            <otherwise>
              (a.status = #{item.value})
            </otherwise>
          </choose>
        </foreach>
      </if>
      <choose>
        <when test="vo.type !=null and vo.type == 1">
          and a.is_waiting_list = 0
          and ((a.appointment_date &gt; #{vo.appointmentDate})
          or (a.appointment_date = #{vo.appointmentDate}
          and a.appointment_end_time &gt; #{vo.endTime}))
        </when>
        <when test="vo.type !=null and vo.type == 2">
          and a.is_waiting_list = 0
          and ((a.appointment_date &lt; #{vo.appointmentDate})
          or (a.appointment_date = #{vo.appointmentDate}
          and a.appointment_end_time &lt; #{vo.endTime}))
        </when>
        <when test="vo.type != null and vo.type == 4">
          and a.is_waiting_list = 0
          and a.status = 4
        </when>
        <when test="vo.type != null and vo.type == 5">
          and a.is_waiting_list = 0
          and a.status = 4
          and a.no_show = 1
        </when>
        <otherwise>
          and a.is_waiting_list = 1
        </otherwise>
      </choose>
      <choose>
        <when test="vo.orderType != null and vo.orderType==1">
          order by a.appointment_date asc, a.appointment_start_time asc
        </when>
        <otherwise>
          order by a.appointment_date desc, a.appointment_start_time desc
        </otherwise>
      </choose>
    </select>


    <select id="queryGroomingBookingAppointmentJoin" resultMap="GroomingBookOnlineResultMap">
        SELECT
        a.id,
        a.appointment_date,
        a.appointment_start_time,
        a.appointment_end_time,
        a.status,
        a.book_online_status,
        a.color_code,
        a.create_time,
        a.customer_id,
        a.out_of_area,
        a.no_start_time,
        p.id petDetailId,
        p.pet_id,
        p.staff_id,
        p.service_id,
        p.service_time,
        p.service_price,
        p.start_time,
        p.end_time
        FROM moe_grooming_appointment a
        inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.id in
        <foreach close=")" collection="groomingIds" item="item" open="(" separator=",">
            #{item}
        </foreach>

    </select>

    <select id="queryAppointmentCount" resultType="integer">
        SELECT
        count(*) as count
        FROM moe_grooming_appointment
        WHERE business_id = #{businessId}
        and is_deprecate = 0
        and status = 1
        and service_type_include = 1
        <if test="bookOnlineStatus != null">
            and book_online_status = #{bookOnlineStatus}
        </if>
        <if test="queryType==1">
            and is_waiting_list = 0
        </if>
        <if test="queryType==2">
            and is_waiting_list = 1
        </if>
    </select>
    <select id="queryAppointmentCountRange" resultType="integer">
        SELECT count(*) as count
        FROM moe_grooming_appointment
        WHERE business_id = #{businessId}
          and is_deprecate = 0
          and is_waiting_list = 0
          and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
          and service_type_include = 1
          and is_block = 2
          and appointment_date &gt;= #{startDate}
          and appointment_date &lt;= #{endDate}
    </select>

    <select id="queryGroomingBookingAppointment" resultMap="GroomingBookOnlineResultMap">
        SELECT
        DISTINCT a.id,
        a.appointment_date,
        a.appointment_start_time,
        a.appointment_end_time ,
        a.status,
        a.is_paid,
        a.book_online_status,
        a.color_code,
        a.create_time,
        a.customer_id,
        a.out_of_area,
        a.no_start_time,
        b.staff_id,
        a.business_id
        FROM moe_grooming_appointment a
        INNER JOIN moe_grooming_pet_detail b ON a.id = b.grooming_id
        WHERE a.business_id = #{groomingCustomerQuery.businessId}
        and a.service_type_include = 1
        and a.is_deprecate = 0
        and a.status = 1
        <choose>
            <when test="groomingCustomerQuery.type !=null and groomingCustomerQuery.type == 1">
                and a.book_online_status = 1 and a.is_waiting_list = 0
            </when>
            <when test="groomingCustomerQuery.type !=null and groomingCustomerQuery.type == 2">
                and a.book_online_status = 1 and a.is_waiting_list = 1
            </when>
        </choose>
        <if test="groomingCustomerQuery.orderBy != null">
            <if test="groomingCustomerQuery.orderBy=='createTime'">
                order by a.create_time
            </if>
            <if test="groomingCustomerQuery.orderBy=='appointmentDate'">
                order by a.appointment_date
            </if>
        </if>
        <if test="groomingCustomerQuery.orderType != null">
            <if test="groomingCustomerQuery.orderType=='asc'">
                asc
            </if>
            <if test="groomingCustomerQuery.orderType=='desc'">
                desc
            </if>
        </if>
    </select>
    <select id="queryGroomingCustomerAppointment" resultMap="CustomerGroomingListResultMap">
      SELECT a.id, a.order_id, a.appointment_date, a.appointment_end_date, a.appointment_start_time, a.appointment_end_time,
      a.confirmed_time, a.check_in_time, a.check_out_time, a.canceled_time, a.status, a.book_online_status, a.service_type_include,
      a.customer_address_id, a.is_paid, a.color_code, a.no_show, a.create_time, a.customer_id, p.id petDetailId,
      p.pet_id, p.staff_id, p.service_id, p.service_time, p.service_price, p.start_time, p.end_time,a.business_id
      FROM moe_grooming_appointment a
      left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
      WHERE a.id in
      <foreach close=")" collection="appointmentIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      <choose>
        <when test="orderType != null and orderType==1">
          order by a.appointment_date asc, a.appointment_start_time asc
        </when>
        <otherwise>
          order by a.appointment_date desc, a.appointment_start_time desc
        </otherwise>
      </choose>
    </select>

    <select id="selectBusinessAppointmentReminder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_grooming_appointment
        where business_id =#{businessId}
        and is_deprecate = 0
        and is_block = 2
        and is_waiting_list = 0
        and book_online_status = 0
        and appointment_date BETWEEN #{startDay} AND #{endDay}
        <if test="startMinute != null">
            and not (appointment_date = #{startDay} and appointment_start_time &lt; #{startMinute})
        </if>
        <if test="dismissIds !=null and dismissIds.size()&gt;0">
            and id not in
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="status !=null">and status = #{status}</when>
            <otherwise>and status in
              <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
                #{status.value}
              </foreach>
            </otherwise>
        </choose>
        order by appointment_date asc, appointment_start_time asc
    </select>

    <select id="queryAppointmentReminderWill" resultMap="AppointmentReminderSendDTOResultMap">
        select
        id, order_id, business_id, customer_id, appointment_date, appointment_start_time,
        appointment_end_time,status, customer_address_id, create_time,update_time
        from moe_grooming_appointment
        where business_id =#{businessId}
        and appointment_date = #{appointmentDate}
        and is_deprecate = 0
        and is_block = 2
        and book_online_status = 0
        and is_waiting_list = 0
        <if test="dismissIds !=null and dismissIds.size()&gt;0">
            and id not in
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="status !=null">and status = #{status}</when>
            <otherwise>and status in
              <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
                #{status.value}
              </foreach>
            </otherwise>
        </choose>
    </select>
    <select id="queryAppointmentReminderSendDto" resultMap="AppointmentReminderSendDTOResultMap">
        select
        id, order_id, business_id, customer_id, appointment_date, appointment_start_time,
        appointment_end_time,status, customer_address_id, create_time,update_time
        from moe_grooming_appointment
        where business_id =#{businessId}
        and id = #{groomingId}
    </select>


    <select id="queryAppointmentWaitingList" resultMap="GroomingAppointmentWaitingListDTOResultMap">
        SELECT a.id
        ticketId,a.appointment_date,a.appointment_start_time,a.customer_id,
        a.create_time,a.update_time,a.customer_address_id,
        p.id petDetailId,p.pet_id,p.staff_id,p.service_id,
        s.name
        from moe_grooming_appointment a
        LEFT join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming_service s on s.id =p.service_id
        WHERE
        a.is_waiting_list = 1
        and a.service_type_include = 1
        and a.is_deprecate = 0
        and a.book_online_status = 0
        AND a.business_id = #{waitingPetDetail.businessId}
        <if test="waitingPetDetail.startDate !=null">
            and a.appointment_date &gt;= #{waitingPetDetail.startDate}
        </if>
        <if test="waitingPetDetail.endDate !=null">
            and a.appointment_date &lt;= #{waitingPetDetail.endDate}
        </if>
        order by create_time desc
    </select>

    <select id="getPetLastAppointmentId" resultType="integer">
        SELECT a.id
        from moe_grooming_appointment a
                 left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.is_waiting_list = 0
          and a.service_type_include = 1
          and a.is_deprecate = 0
          and a.is_block = 2
          and a.status != 4
          and p.pet_id = #{petId}
          and a.company_id = #{companyId}
          <if test="businessId != null">
              and a.business_id = #{businessId}
          </if>
          and (a.appointment_date &lt; #{appointmentDate} or
               (a.appointment_date = #{appointmentDate} and a.appointment_end_time &lt;= #{endTimes}))
        order by a.appointment_date desc, a.appointment_end_time desc
        limit 1
    </select>

    <select id="queryCustomerLastOrNextAppointment" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and is_block = 2
        and status != 4
        and company_id = #{companyId}
        and customer_id = #{customerId}
        <if test="excludeBookingRequest">
            and book_online_status = 0
        </if>
        and service_type_include = 1
        <if test="ignoreGroomingId != null">
            and id != #{ignoreGroomingId}
        </if>
        <if test="last !=null and last==true">
            and (appointment_end_date &lt; #{appointmentDate} or (appointment_end_date = #{appointmentDate} and
            appointment_end_time &lt;= #{endTimes}))
            order by appointment_end_date desc,appointment_end_time desc limit 1
        </if>
        <if test="last !=null and last==false">
            and (appointment_end_date &gt; #{appointmentDate} or (appointment_end_date = #{appointmentDate} and
            appointment_end_time &gt;= #{endTimes}))
            order by appointment_date asc, appointment_end_time asc limit 1
        </if>
    </select>

  <select id="queryCustomerLastOrNextAppointmentAllServiceType" resultMap="CustomerGroomingAppointmentDTOResultMap">
    SELECT
    id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
    from moe_grooming_appointment
    WHERE is_waiting_list = 0
    and is_deprecate = 0
    and is_block = 2
    and status != 4
    and company_id = #{companyId}
    and customer_id = #{customerId}
    <if test="excludeBookingRequest">
      and book_online_status = 0
    </if>
    <if test="ignoreGroomingId != null">
      and id != #{ignoreGroomingId}
    </if>
    <if test="last !=null and last==true">
      and (appointment_end_date &lt; #{appointmentDate} or (appointment_end_date = #{appointmentDate} and
      appointment_end_time &lt;= #{endTimes}))
      order by appointment_end_date desc,appointment_end_time desc limit 1
    </if>
    <if test="last !=null and last==false">
      and (appointment_end_date &gt; #{appointmentDate} or (appointment_end_date = #{appointmentDate} and
      appointment_end_time &gt;= #{endTimes}))
      order by appointment_date asc, appointment_end_time asc limit 1
    </if>
  </select>

  <select id="getLastFinishedApptByCustomerId" resultMap="OBClientApptDTOResultMap">
      SELECT
      id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id
      from moe_grooming_appointment
      WHERE is_waiting_list = 0
      and is_deprecate = 0
      and is_block = 2
      and status = 3
      and service_type_include = 1
      and company_id = #{companyId}
      and customer_id = #{customerId}
      and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and
      appointment_end_time &lt;= #{endTimes}))
      order by appointment_date desc,appointment_end_time desc limit 1
  </select>

    <select id="selectCustomerLastAppt" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT * from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and service_type_include = 1
        and company_id = #{companyId}
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &lt;= #{endTimes}))
        and customer_id in
        <foreach close=")" collection="customerIdList" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
        order by appointment_date desc ,appointment_end_time desc limit 9999) t
        GROUP BY t.customer_id
    </select>
    <select id="selectCustomerLastFinishedAppt" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT * from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and status = 3
        and is_block = 2
        and service_type_include = 1
        and company_id = #{companyId}
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &lt;= #{endTimes}))
        and customer_id in
        <foreach close=")" collection="customerIdList" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
        order by appointment_date desc ,appointment_end_time desc limit 9999) t
        GROUP BY t.customer_id
    </select>

    <select id="selectCustomerLastTwoUncanceledApptDate" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT distinct appointment_date
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and status != 4
        and is_block = 2
        and service_type_include = 1
        and business_id = #{businessId}
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time &lt;= #{endTimes}))
        and customer_id = #{customerId}
        order by appointment_date desc
        limit 2
    </select>

    <select id="selectLapsedCustomerLastAppt" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT * from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and service_type_include = 1
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and company_id = #{companyId}
          <if test="businessId != null">
            and business_id = #{businessId}
          </if>
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &lt;= #{endTimes}))
        <if test="customerIdList !=null and customerIdList.size &gt; 0">
            and customer_id in
            <foreach close=")" collection="customerIdList" item="customerId" open="(" separator=",">
                #{customerId}
            </foreach>
        </if>
        and customer_id not in (
        select DISTINCT customer_id from moe_grooming_appointment where
        is_waiting_list = 0
        and is_deprecate = 0
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and company_id = #{companyId}
        <if test="businessId != null">
          and business_id = #{businessId}
        </if>
        and (appointment_date &gt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &gt;= #{endTimes}))
        )
        order by appointment_date desc ,appointment_end_time desc limit 99999) t
        GROUP BY t.customer_id
    </select>

    <select id="selectCustomerNextAppt" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT * from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and service_type_include = 1
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and company_id = #{companyId}
        and (appointment_date &gt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &gt;= #{endTimes}))
        and customer_id in
        <foreach close=")" collection="customerIdList" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
        order by appointment_date asc ,appointment_end_time asc limit 9999) t
        GROUP BY t.customer_id
    </select>

    <select id="selectCustomerPetNextAppt" resultMap="CustomerGroomingAppointmentDTOResultMap">
      SELECT
         a.id, a.appointment_date, a.appointment_start_time, a.appointment_end_time, a.order_id, a.customer_id,a.business_id
      from moe_grooming.moe_grooming_appointment a
      left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.status = 1
      WHERE a.is_waiting_list = 0
      and a.service_type_include = 1
      and a.is_deprecate = 0
      and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
      and a.is_block = 2
      and a.book_online_status = 0 <!--过滤ob的预约-->
      and a.company_id = #{companyId}
      and (
          a.appointment_date &gt; #{fromDate}
              or (a.appointment_date = #{fromDate} and a.appointment_end_time &gt;= #{fromMinutes})
      )
      and a.customer_id = #{customerId}
      and pd.pet_id = #{petId}
      <if test="ignoredGroomingId != null">
        and a.id != #{ignoredGroomingId}
      </if>
      group by a.id
      order by a.appointment_date, a.appointment_end_time
      limit 1
    </select>

    <select id="selectCustomerLastServiceTime" resultMap="CustomerGroomingAppointmentDTOResultMap">
      WITH RankedAppointments AS (
      SELECT id, customer_id, appointment_date, appointment_start_time, appointment_end_time, order_id, business_id,
      ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY appointment_date DESC, appointment_start_time DESC) AS rn
      FROM moe_grooming_appointment
      WHERE is_waiting_list = 0
      and is_deprecate = 0
      and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
      and is_block = 2
      and business_id = #{businessId}
      and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
      &lt;= #{endTimes}))
      <if test="dismissCustomerIds !=null and dismissCustomerIds.size&gt;0">
        and customer_id not in
        <foreach close=")" collection="dismissCustomerIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      and book_online_status = 0) <!--过滤ob的预约-->
      SELECT ra.id, ra.customer_id, ra.appointment_date, ra.appointment_start_time, ra.appointment_end_time, ra.order_id, ra.business_id, pd.pet_id
      FROM RankedAppointments ra
      LEFT JOIN moe_grooming_pet_detail pd ON ra.id = pd.grooming_id AND pd.status = 1
      WHERE rn = 1;
    </select>
  <!-- copy from selectCustomerLastServiceTime -->
  <select id="selectCustomerLastServiceTimeByCompany" resultMap="CustomerGroomingAppointmentDTOResultMap">
    WITH RankedAppointments AS (
    SELECT id, customer_id, appointment_date, appointment_start_time, appointment_end_time, order_id, business_id,
    ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY appointment_date DESC, appointment_start_time DESC) AS rn
    FROM moe_grooming_appointment
    WHERE is_waiting_list = 0
    and is_deprecate = 0
    and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and is_block = 2
    and company_id = #{companyId}
    and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
    &lt;= #{endTimes}))
    AND (customer_id IN
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach> )
    and book_online_status = 0) <!--过滤ob的预约-->
    SELECT ra.id, ra.customer_id, ra.appointment_date, ra.appointment_start_time, ra.appointment_end_time, ra.order_id, ra.business_id, pd.pet_id
    FROM RankedAppointments ra
    LEFT JOIN moe_grooming_pet_detail pd ON ra.id = pd.grooming_id AND pd.status = 1
    WHERE rn = 1;
  </select>

    <select id="selectOneCustomerLastServiceTime" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT
        a.id, a.appointment_date, a.appointment_start_time, a.appointment_end_time, a.order_id, a.customer_id, pd.pet_id,a.business_id
        FROM moe_grooming.moe_grooming_appointment a
        LEFT JOIN moe_grooming.moe_grooming_pet_detail pd ON a.id = pd.grooming_id AND pd.status = 1
        WHERE a.is_waiting_list = 0
        and a.service_type_include = 1
        AND a.is_deprecate = 0
        AND a.status IN
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        AND a.is_block = 2
        AND a.business_id = #{businessId}
        AND a.customer_id = #{customerId}
        AND (a.appointment_date &lt; #{appointmentDate} OR (a.appointment_date = #{appointmentDate} AND a.appointment_end_time &lt;= #{endTimes}))
        AND a.book_online_status = 0
        ORDER BY appointment_date DESC, appointment_end_time DESC LIMIT 1
    </select>

    <select id="selectLastServiceTime" resultType="string">
        SELECT
        MAX(appointment_date)
        FROM
        moe_grooming_appointment
        WHERE is_waiting_list = 0
        AND is_deprecate = 0
        AND service_type_include = 1
        AND status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        AND is_block = 2
        AND business_id = #{businessId}
        AND customer_id = #{customerId}
    </select>

    <!--    批量获取apponitment数量   -->
    <select id="getCustomerAppointmentNumBatch" resultType="com.moego.server.grooming.dto.CustomerStatisticsDTO">
        SELECT
        customer_id customerId,"totalApptAndRequestsCount" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        GROUP BY customer_id

        union all

        SELECT
        customer_id customerId,"totalAppts" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        and book_online_status = 0
        GROUP BY customer_id

        union all

        SELECT
        customer_id customerId,"finished" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        and book_online_status = 0
        and status = 3
        GROUP BY customer_id

        union all

        SELECT
        customer_id customerId,"cancelled" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        and book_online_status = 0
        and status = 4
        GROUP BY customer_id

        union all

        SELECT
        customer_id customerId,"noShow" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        and book_online_status = 0
        and no_show = 1
        GROUP BY customer_id

        union all

        SELECT
        customer_id customerId,"upcoming" as type ,count(*) num
        from moe_grooming_appointment
        WHERE
        company_id = #{companyId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_deprecate = 0
        and is_block = 2
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_waiting_list = 0
        and book_online_status = 0
        and (appointment_end_date &gt; #{appointmentDate} or (appointment_end_date = #{appointmentDate} and appointment_end_time &gt;=
        #{endTimes}))
        GROUP BY customer_id
    </select>

    <select id="queryCustomerIdsInWaiting" resultType="int">
        SELECT
        customer_id
        FROM moe_grooming_appointment
        WHERE is_waiting_list = 1
        and is_deprecate = 0
        and service_type_include = 1
        and is_block = 2
        <if test="customerIds!=null and customerIds.size()&gt;0">
            and customer_id in
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- https://moego.atlassian.net/browse/ERP-913: 修改为只判断是否有 unconfirmed、confirmed 预约 -->
    <select id="queryStaffUpComingAppointCount" resultType="int">
      select distinct p.grooming_id
      from moe_grooming_appointment a
             inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
             left join moe_grooming_service_operation gso on p.id = gso.grooming_service_id
      where a.is_deprecate = 0
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_block = 2
        and a.is_waiting_list = 0
        and service_type_include = 1
        and a.business_id = #{businessId}
        and (p.staff_id = #{staffId} or gso.staff_id = #{staffId})
        and (a.appointment_date &gt; #{appointmentDate} or
             (a.appointment_date = #{appointmentDate} and a.appointment_end_time &gt;= #{endTimes}))
    </select>

    <select id="queryServiceUpComingAppointCount" resultType="int">
        SELECT DISTINCT p.grooming_id
        from moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.is_deprecate = 0
          and a.service_type_include = 1
          and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and a.is_block = 2
          and a.is_waiting_list = 0
          and a.business_id = #{businessId}
          and p.service_id = #{serviceId}
          and (a.appointment_date &gt; #{appointmentDate} or (a.appointment_date = #{appointmentDate} and
                                                              a.appointment_end_time &gt;= #{endTimes}))
    </select>
    <select id="queryStaffAppointTimeByDate" resultMap="StaffAppointTimeByDateResultMap">
        SELECT pd.start_time, (pd.service_time + pd.start_time) as end_time
        from moe_grooming_appointment ap
                 INNER JOIN moe_grooming_pet_detail pd
                            on ap.id = pd.grooming_id
                                and ap.business_id = #{businessId}
        where ap.is_deprecate = 0
          and ap.service_type_include = 1
          and ap.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and ap.is_waiting_list = 0
          and pd.status = 1
          and appointment_date = #{appointmentDate}
          and staff_id = #{staffId};
    </select>

  <select id="queryTraditionalStaffAppointmentPetCountByTime" resultMap="StaffAppointPetCountByTimeResultMap">
    select pd.staff_id,
           a.appointment_date,
           a.appointment_start_time,
           count(distinct pd.pet_id) as petcount
    from moe_grooming.moe_grooming_appointment a
           left join moe_grooming.moe_grooming_pet_detail pd
                     on a.id = pd.grooming_id and pd.`status` = 1
    where a.business_id = #{businessId}
      and pd.staff_id != 0
    <if test="appointmentDates != null and appointmentDates.size &gt; 0">
      and a.appointment_date in
      <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="startTimes != null and startTimes.size() &gt; 0">
      and a.appointment_start_time in
      <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="staffIds != null and staffIds.size() &gt; 0">
      and pd.staff_id in
      <foreach close=")" collection="staffIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and a.is_block = 2
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and pd.enable_operation = false
    group by pd.staff_id, a.appointment_date, a.appointment_start_time
  </select>

  <select id="queryMultiStaffAppointmentPetCountByTime" resultMap="StaffAppointPetCountByTimeResultMap">
    select o.staff_id,
           a.appointment_date,
           a.appointment_start_time,
           count(distinct o.pet_id) as petcount
    from moe_grooming.moe_grooming_appointment a
           left join moe_grooming.moe_grooming_pet_detail pd
                     on a.id = pd.grooming_id and pd.`status` = 1
           inner join moe_grooming.moe_grooming_service_operation o
                      on pd.id = o.grooming_service_id
    where a.business_id = #{businessId}
      and pd.staff_id != 0
    <if test="appointmentDates != null and appointmentDates.size &gt; 0">
      and a.appointment_date in
      <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="startTimes != null and startTimes.size() &gt; 0">
      and a.appointment_start_time in
      <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="staffIds != null and staffIds.size() &gt; 0">
      and o.staff_id in
      <foreach close=")" collection="staffIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and a.is_block = 2
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and pd.enable_operation = true
    group by o.staff_id, a.appointment_date, a.appointment_start_time
  </select>

    <select id="queryCustomerUpComingAppoint" resultMap="CustomerUpComingAppointDTOResultMap">
        SELECT
        a.id,a.appointment_date,a.appointment_start_time,a.customer_id,
        a.create_time,a.update_time,a.customer_address_id,a.order_id,
        a.appointment_end_time,a.created_by_id,a.business_id,
        p.id petDetailId,p.pet_id,p.service_id,p.staff_id,
        s.name
        from moe_grooming_appointment a
        LEFT join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming_service s on s.id =p.service_id
        WHERE a.is_waiting_list = 0
        and a.service_type_include = 1
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_deprecate = 0
        and a.is_block = 2
        and (a.appointment_date &gt; #{appointmentDate} or (a.appointment_date = #{appointmentDate} and
        a.appointment_end_time &gt;= #{endTimes}))
        <if test="customerId!=null">and a.customer_id = #{customerId}</if>
        <if test="businessId!=null">and a.business_id = #{businessId}</if>
        order by appointment_date asc, appointment_end_time asc
    </select>

    <select id="queryCustomerUpComingAppointForClientShare" resultMap="CustomerUpComingAppointDTOResultMap">
        SELECT
        a.id,a.appointment_date,a.appointment_start_time,a.customer_id,
        a.create_time,a.update_time,a.customer_address_id,a.order_id,
        a.appointment_end_time,a.created_by_id,a.business_id,a.appointment_end_date,
        p.id petDetailId,p.pet_id,p.service_id,p.staff_id,
        s.name
        from moe_grooming_appointment a
        LEFT join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming_service s on s.id =p.service_id
        WHERE a.is_waiting_list = 0
        <choose>
            <when test="status!=null and status != 0">
                and a.status = #{status}
            </when>
          <otherwise>
              and a.status in
            <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
              #{status.value}
            </foreach>
          </otherwise>
        </choose>
        and a.is_deprecate = 0
        and a.is_block = 2
        <if test="apptIds !=null and apptIds.size()&gt;0">
            and a.id in
            <foreach close=")" collection="apptIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and (a.appointment_date &gt; #{startDate} or (a.appointment_date = #{startDate} and
        a.appointment_end_time &gt;= #{startMins}))
          <if test="endDateStr != null and endDateStr != ''">
              and a.appointment_date &lt;= #{endDateStr}
          </if>
        and a.customer_id = #{customerId}
        and a.company_id = #{companyId}
        order by a.appointment_date asc, a.appointment_end_time asc
    </select>

    <select id="queryGroomingCustomerAppointmentByIds" resultMap="CustomerGroomingListResultMap">
        SELECT
        a.id,
        a.business_id,
        a.order_id,
        a.appointment_date,
        a.appointment_end_date,
        a.appointment_start_time,
        a.appointment_end_time,
        a.confirmed_time,
        a.check_in_time,
        a.check_out_time,
        a.canceled_time,
        a.status,
        a.book_online_status,
        a.customer_id,
        a.customer_address_id,
        a.is_paid,
        a.color_code,
        a.no_show,
        a.create_time,
        p.id petDetailId,
        p.pet_id,
        p.staff_id,
        p.service_id,
        p.service_time,
        p.service_price,
        p.start_time,
        p.end_time
        FROM moe_grooming_appointment a
        left join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
            #{item}
        </foreach>
        order by a.appointment_date asc, a.appointment_end_time asc
    </select>

    <select id="queryBusinessUpComingAppoint" resultMap="CustomerUpComingAppointDTOResultMap">
        SELECT
        a.id,a.appointment_date,a.appointment_start_time,a.customer_id,
        a.create_time,a.update_time,a.customer_address_id,a.order_id,
        a.appointment_end_time,a.created_by_id,a.business_id,
        p.id petDetailId,p.pet_id,p.service_id,p.staff_id,
        s.name
        from moe_grooming_appointment a
        LEFT join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming_service s on s.id =p.service_id
        WHERE a.is_waiting_list = 0
        and a.service_type_include = 1
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_deprecate = 0
        and a.is_block = 2
        and a.appointment_date = #{appointmentDate}
        <if test="customerId!=null">and a.customer_id = #{customerId}</if>
        <if test="businessId!=null">and a.business_id = #{businessId}</if>
        order by appointment_date asc, appointment_end_time asc
    </select>

    <select id="getLatestClientUpcomingAppointId" resultType="java.lang.Integer">
        SELECT
        a.id
        from moe_grooming.moe_grooming_appointment a
        WHERE a.is_waiting_list = 0
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_deprecate = 0
        and a.is_block = 2
        and a.service_type_include = 1
        <if test="businessIds !=null and businessIds.size() &gt; 0">
            and a.business_id in
            <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerIds !=null and customerIds.size() &gt; 0">
            and a.customer_id in
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and (a.appointment_date &gt; #{appointmentDate} or (a.appointment_date = #{appointmentDate} and a.appointment_end_time &gt; #{endTime}))
        order by appointment_date, appointment_end_time
        limit 1
    </select>

    <select id="queryCustomerUpcomingAppoint" resultMap="CustomerUpComingAppointDTOResultMap">
        SELECT
        a.id,a.appointment_date,a.appointment_start_time,a.customer_id,
        a.create_time,a.update_time,a.customer_address_id,a.order_id,
        a.appointment_end_time,a.created_by_id,a.business_id,
        p.id petDetailId,p.pet_id,p.service_id,p.staff_id,
        s.type as service_type,p.service_price,s.name
        from moe_grooming.moe_grooming_appointment a
        LEFT join moe_grooming.moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming.moe_grooming_service s on s.id =p.service_id
        WHERE a.is_waiting_list = 0
        and a.service_type_include = 1
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_deprecate = 0
        and a.is_block = 2
        <if test="businessIds !=null and businessIds.size() &gt; 0">
            and a.business_id in
            <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerIds !=null and customerIds.size() &gt; 0">
            and a.customer_id in
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and (a.appointment_date &gt; #{appointmentDate} or (a.appointment_date = #{appointmentDate} and a.appointment_end_time &gt; #{endTime}))
        order by appointment_date, appointment_end_time
    </select>

    <select id="queryCustomerUpcomingAppointById" resultMap="CustomerUpComingAppointDTOResultMap">
        SELECT
        a.id,a.appointment_date,a.appointment_start_time,a.customer_id,a.create_time,
        a.update_time,a.customer_address_id,a.order_id,a.appointment_end_time,
        a.created_by_id,a.business_id,
        p.id petDetailId,p.pet_id,p.service_id,p.staff_id,
        s.type as service_type,p.service_price,s.name
        from moe_grooming.moe_grooming_appointment a
        LEFT join moe_grooming.moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        LEFT JOIN moe_grooming.moe_grooming_service s on s.id =p.service_id
        WHERE a.is_waiting_list = 0
        and a.service_type_include = 1
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_deprecate = 0
        and a.is_block = 2
        and a.id = #{appointmentId}
    </select>

    <select id="selectHasUpcomingService" resultType="java.lang.Integer">
        SELECT count(a.id)
        from moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.is_waiting_list = 0
          and a.business_id = #{businessId}
          and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          and a.is_deprecate = 0
          and a.is_block = 2
          and (a.appointment_end_date &gt; #{appointmentDate} or
               (a.appointment_end_date = #{appointmentDate} and a.appointment_end_time &gt;= #{endTimes}))
          and p.service_id = #{serviceId}
    </select>


    <select id="queryFutureAppointmentCustomerIdList" resultType="Integer">
        select DISTINCT customer_id
        from moe_grooming_appointment a
        where business_id = #{businessId}
          and (a.appointment_date &gt; #{appointmentDate} or
               (a.appointment_date = #{appointmentDate} and a.appointment_end_time &gt;= #{endTimes}))
          and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and is_deprecate = 0
          and is_block = 2
          and is_waiting_list = 0
          and service_type_include = 1
    </select>

    <select id="queryFutureAppointmentCustomerIdListByCompany" resultType="Integer">
      select DISTINCT customer_id
      from moe_grooming_appointment a
      where company_id = #{companyId}
      and (a.appointment_date &gt; #{appointmentDate} or
      (a.appointment_date = #{appointmentDate} and a.appointment_end_time &gt;= #{endTimes}))
      and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>

      and is_deprecate = 0
      and is_block = 2
      and is_waiting_list = 0
      and service_type_include = 1
    </select>

    <select id="selectBusinessRepeatAppointmentReminder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_grooming_appointment aa
        where business_id = #{businessId}
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_deprecate = 0
        and is_block = 2
        and is_waiting_list = 0
        and service_type_include = 1
        and repeat_id in
        <foreach close=")" collection="repeatIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and appointment_date between #{startDate} and #{endDate}
        <if test="dismissIds !=null and dismissIds.size()&gt;0">
            and id not in
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        group by repeat_id
    </select>

  <select id="countRepeatUpcomingCountForExpiryReminder" resultType="integer">
    select count(id)
    from moe_grooming_appointment
    where business_id = #{businessId}
    and repeat_id = #{repeatId}
    and is_deprecate = 0
    and service_type_include = 1
    and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and (appointment_date &gt; #{currentDate} or (appointment_date = #{currentDate} and
    appointment_end_time &gt;= #{currentTime}))
    order by appointment_date
  </select>

  <select id="selectRepeatAppointmentList" resultType="com.moego.server.grooming.dto.RepeatAppointmentDto">
    select id appointmentId,
        appointment_date appointmentDate,
        appointment_start_time startTime,
        appointment_end_time endTime,
        status,
        schedule_type scheduleType,
        customer_id customerId
    from moe_grooming_appointment
    where business_id = #{businessId}
    and repeat_id = #{repeatId}
    and is_deprecate = 0
    and status in
    <foreach close=")" collection="statusList" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and is_block = 2
    order by appointment_date
  </select>

  <select id="selectRepeatAppointmentListByType" resultType="com.moego.server.grooming.dto.RepeatAppointmentDto">
    select id appointmentId,
        appointment_date appointmentDate,
        appointment_start_time startTime,
        appointment_end_time endTime,
        status,
        schedule_type scheduleType,
        customer_id customerId
    from moe_grooming_appointment
    where business_id = #{businessId}
    and repeat_id = #{repeatId}
    and is_deprecate = 0
    and status in
    <foreach close=")" collection="statusList" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and is_block = 2
    <choose>
      <when test="type != null and type == 1">
        and (appointment_date &lt; #{currentDate} or (appointment_date = #{currentDate} and
        appointment_end_time &lt; #{currentTime}))
      </when>
      <otherwise>
        and (appointment_date &gt; #{currentDate} or (appointment_date = #{currentDate} and
        appointment_end_time &gt;= #{currentTime}))
      </otherwise>
    </choose>
    order by appointment_date
  </select>

  <select id="selectRepeatAppointmentById" resultType="com.moego.server.grooming.dto.RepeatAppointmentDto">
    select id appointmentId,
           appointment_date appointmentDate,
           appointment_start_time startTime,
           appointment_end_time endTime,
           status,
           schedule_type scheduleType,
           customer_id customerId
    from moe_grooming_appointment
    where business_id = #{businessId}
    and id = #{appointmentId}
  </select>

    <select id="selectCustomerIdWithInAllAppointmentDate" resultType="int">
        SELECT DISTINCT a.customer_id
        FROM moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.business_id = #{businessId}
          AND a.is_waiting_list = 0
          AND a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          AND a.is_deprecate = 0
          AND a.is_block = 2
          and p.staff_id = #{staffId}
    </select>

    <select id="selectCustomerIdIsWithAppointmentDate" resultType="int">
        SELECT count(*)
        FROM moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.business_id = #{businessId}
          AND a.is_waiting_list = 0
          and a.service_type_include = 1
          AND a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
          AND a.is_deprecate = 0
          AND a.is_block = 2
          AND p.staff_id = #{staffId}
          AND a.customer_id = #{customerId}
          <if test="startDate!=null">
          AND a.appointment_date &gt;= #{startDate}
          </if>
          <if test="endDate!=null">
          AND a.appointment_date &lt;= #{endDate}
          </if>
    </select>
    <select id="selectCustomerIdIsWithAllAppointmentDate" resultType="int">
        SELECT count(*)
        FROM moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.business_id = #{businessId}
          AND a.is_waiting_list = 0
          and a.service_type_include = 1
          AND a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          AND a.is_deprecate = 0
          AND a.is_block = 2
          and p.staff_id = #{staffId}
          AND a.customer_id = #{customerId}
    </select>

    <select id="getBusinessIdsOnSendDaily" resultType="int">
        SELECT DISTINCT business_id
        FROM moe_grooming_appointment
        WHERE appointment_date &gt;= #{yesterday}
          AND appointment_date &lt;= #{tomorrow}
          AND is_waiting_list = 0
          AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          AND is_deprecate = 0
          AND is_block = 2
      and service_type_include = 1
    </select>


    <select id="queryRepeatIdOnlyTwoGrooming" resultType="int">
        SELECT t.repeatId
        from (
                 SELECT count(*) num, repeat_id repeatId
                 FROM moe_grooming_appointment
                 where business_id = #{businessId}
                   and is_waiting_list = 0
                   and is_deprecate = 0
                   and is_block = 2
                   and repeat_id &gt; 0
                   and service_type_include = 1
                   and appointment_date &gt;= #{appointmentDate}
                 GROUP BY repeat_id) t
        where t.num = 2
    </select>

    <select id="queryRepeatLastestAppointment" parameterType="int" resultType="com.moego.server.grooming.dto.RepeatNumDTO">
        SELECT * FROM (SELECT id,customer_id customerId,repeat_id repeatId,appointment_date appointmentDate
        FROM moe_grooming_appointment
        where business_id = #{businessId}
        and repeat_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and is_waiting_list = 0
        and business_id = #{businessId}
        and is_deprecate = 0
        and is_block = 2
        and service_type_include = 1
        order by appointment_date desc) t
        GROUP BY repeatId
    </select>

    <select id="queryCustomerLastestAppointment" resultType="com.moego.server.grooming.dto.RepeatNumDTO">
        SELECT * FROM (SELECT id,customer_id customerId,repeat_id repeatId,appointment_date appointmentDate
        FROM moe_grooming_appointment
        where business_id = #{businessId}
        and customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and business_id = #{businessId}
        and is_waiting_list = 0
        and is_deprecate = 0
        and is_block = 2
        and service_type_include = 1
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and appointment_date &gt; #{appointmentDate}
        order by appointment_date desc) t
        GROUP BY customerId
    </select>

  <select id="queryBusinessApptsAndInvoiceWithDate" resultMap="GroomReportApptDetailMap">
    SELECT a.id, a.business_id, a.customer_id, a.appointment_date, a.status, a.is_paid,
        a.appointment_start_time, a.no_show, a.appointment_end_time, a.is_waiting_list, a.source,
        a.is_waiting_list
    FROM moe_grooming.moe_grooming_appointment a
    WHERE a.business_id in
      <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
        #{businessId}
      </foreach>
    AND a.is_deprecate = 0
    AND a.is_block = 2
    and a.book_online_status = 0
    and a.service_type_include = 1
    AND a.appointment_date &gt;= #{startDate}
    AND a.appointment_date &lt;= #{endDate}
  </select>

    <select id="queryByPrimaryIds" resultMap="BaseResultMap">
        SELECT id, customer_id, customer_address_id
        from moe_grooming_appointment
        where id in
        <foreach close=")" collection="appointmentIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryUnpaidApptsWithAmount" resultMap="ReportAppointmentMap">
        SELECT a.id, a.customer_id, a.order_id, a.appointment_date, a.appointment_start_time,
            a.appointment_end_time, a.no_show, a.status, a.cancel_by_type
        from moe_grooming_appointment a
        where a.business_id = #{businessId}
        <if test="startDate != null and startDate != ''">
            and a.appointment_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and a.appointment_date &lt;= #{endDate}
        </if>
            AND a.is_waiting_list = 0
            AND a.is_deprecate = 0
            AND a.is_block = 2
            and a.book_online_status = 0
            and a.status = 3
            and a.no_show = 2
            and a.is_paid != 1
            and a.service_type_include = 1
            order by a.appointment_date desc, a.appointment_start_time desc
        limit 500
    </select>

    <select id="queryWebReportUnpaidAppts" resultMap="ReportWebAppointmentMap">
        SELECT a.id,
               a.customer_id,
               a.appointment_date,
               a.appointment_start_time,
               a.status,
               a.create_time,
               a.cancel_by_type,
               a.created_by_id
        from moe_grooming_appointment a
        where a.business_id = #{businessId}
          <if test="startDate != null and startDate != ''">
              and a.appointment_date &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              and a.appointment_date &lt;= #{endDate}
          </if>
          AND a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.book_online_status = 0
          and a.status = 3
          and a.no_show = 2
          and a.is_paid != 1
        order by a.appointment_date desc, a.appointment_start_time desc
        limit 500
    </select>

    <select id="queryUnclosedAppts" resultMap="ReportAppointmentMap">
        SELECT id,
               customer_id,
               order_id,
               appointment_date,
               appointment_start_time,
               appointment_end_time,
               no_show,
               status,
               cancel_by_type
        from moe_grooming_appointment a
        where business_id = #{businessId}
          and appointment_date &gt;= #{startDate}
          and appointment_date &lt;= #{endDate}
          AND is_waiting_list = 0
          AND is_deprecate = 0
          AND is_block = 2
          and a.book_online_status = 0
          and a.service_type_include = 1
          and appointment_date &lt; #{currentDate}
          and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
    </select>

    <select id="queryCancelledApptsWithNoShowInfo" resultMap="ReportAppointmentMap">
        SELECT a.id,
               a.customer_id,
               a.order_id,
               a.appointment_date,
               a.cancel_by,
               a.appointment_start_time,
               a.appointment_end_time,
               a.no_show,
               a.status,
               a.cancel_by_type
        from moe_grooming_appointment a
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          AND a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.book_online_status = 0
          and a.status = 4
          and a.service_type_include = 1
    </select>

    <select id="queryWebReportCancelledAppts" resultMap="ReportWebAppointmentMap">
        SELECT a.id,
               a.customer_id,
               a.appointment_date,
               a.appointment_start_time,
               a.status,
               a.create_time,
               a.cancel_by_type,
               a.cancel_by,
               a.update_time,
               b.`type`,
               b.note,
               a.no_show,
               a.no_show_fee,
               a.created_by_id
        from moe_grooming_appointment a
                 left join moe_grooming_note b
                           on a.id = b.grooming_id and b.is_deleted = false
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          AND a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.book_online_status = 0
          and a.status = 4
        order by a.appointment_date desc, a.appointment_start_time desc
    </select>

    <select id="queryNoShowApptsWithNoShowInfo" resultMap="ReportAppointmentMap">
        SELECT a.id,
               a.customer_id,
               a.order_id,
               a.appointment_date,
               a.cancel_by,
               a.appointment_start_time,
               a.appointment_end_time,
               a.no_show,
               a.status,
               a.cancel_by_type
        from moe_grooming_appointment a
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          AND a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.status = 4
          and a.no_show = 1
          and a.book_online_status = 0
          and a.service_type_include = 1
    </select>

    <select id="queryApptsByOnlineBook" resultMap="ReportAppointmentMap">
        SELECT id,
               customer_id,
               order_id,
               appointment_date,
               appointment_start_time,
               appointment_end_time,
               no_show,
               status,
               cancel_by_type
        from moe_grooming_appointment a
        where business_id = #{businessId}
          and appointment_date &gt;= #{startDate}
          and appointment_date &lt;= #{endDate}
          AND is_waiting_list = 0
          AND is_deprecate = 0
          AND is_block = 2
          and a.book_online_status = 0
          and a.service_type_include = 1
          and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and source = 22168
    </select>

    <select id="queryByOnlineBook" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      from moe_grooming_appointment
      where business_id = #{businessId}
      and book_online_status = 1
      and service_type_include = 1
      AND is_waiting_list = #{isWaitingList}
      <if test="statusList != null">
        and status in
        <foreach close=")" collection="statusList" item="status" open="(" separator=",">
          #{status.value}
        </foreach>
      </if>
      AND is_deprecate = 0
      <if test="order != null">
        order by ${order}
      </if>
    </select>

    <select id="queryUpcomingAppts" resultMap="ReportAppointmentMap">
        SELECT id,
               customer_id,
               order_id,
               appointment_date,
               appointment_start_time,
               appointment_end_time,
               no_show,
               status,
               cancel_by_type
        from moe_grooming_appointment a
        where business_id = #{businessId}
          and appointment_date &gt;= #{startDate}
          and appointment_date &lt;= #{endDate}
          AND is_waiting_list = 0
          AND is_deprecate = 0
          AND is_block = 2
          and a.book_online_status = 0
          and a.service_type_include = 1
          and appointment_date &gt;= #{currentDate}
          and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
    </select>

    <select id="queryWaitListAppts" resultMap="ReportAppointmentMap">
        SELECT id,
               customer_id,
               order_id,
               appointment_date,
               appointment_start_time,
               appointment_end_time,
               no_show,
               status,
               cancel_by_type
        from moe_grooming_appointment a
        where business_id = #{businessId}
          and appointment_date &gt;= #{startDate}
          and appointment_date &lt;= #{endDate}
          AND is_waiting_list = 1
          AND is_deprecate = 0
          AND is_block = 2
          and a.book_online_status = 0
          and a.service_type_include = 1
          and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

    </select>

    <select id="queryWaitList" resultMap="BaseResultMap">
      SELECT *
      from moe_grooming_appointment
      where company_id = #{companyId}
      AND is_waiting_list = 1
      AND is_deprecate = 0
      AND is_block = 2
      and service_type_include = 1
      <if test="statusList != null">
        and status in
        <foreach close=")" collection="statusList" item="status" open="(" separator=",">
          #{status.value}
        </foreach>
      </if>
      <if test="waitListStatusList != null">
        and wait_list_status in
        <foreach close=")" collection="waitListStatusList" item="waitListStatus" open="(" separator=",">
          #{waitListStatus.value}
        </foreach>
      </if>
      <if test="businessIdList != null">
        and business_id in
        <foreach close=")" collection="businessIdList" item="businessId" open="(" separator=",">
          #{businessId}
        </foreach>
      </if>
      <if test="customerIdList != null">
        and customer_id in
        <foreach close=")" collection="customerIdList" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
      </if>
    </select>

    <select id="queryAppointmentDateByDateRange" resultType="com.moego.server.grooming.service.dto.QbQueryGroomingResultDto">
        SELECT id as groomingId, business_id as businessId, appointment_date as appointmentDate
        from moe_grooming_appointment
        where business_id = #{businessId}
        and appointment_date &gt;= #{startDate}
        and service_type_include = 1
        <if test="endDate != null">
            and appointment_date &lt;= #{endDate}
        </if>
        AND is_block = 2
        order by appointmentDate desc
    </select>
    <select id="queryAppointmentDateByDateRangeWithStaffId" resultType="com.moego.server.grooming.service.dto.QbQueryGroomingResultDto">
        select appt.id as groomingId,appt.appointment_date as appointmentDate,appt.business_id as businessId  from moe_grooming_appointment as appt left join moe_grooming_pet_detail as pd on
        appt.id=pd.grooming_id
        WHERE appt.business_id = #{businessId}
        and appt.service_type_include = 1
        and appt.status != 4
        and appt.is_deprecate = 0
        and appt.is_waiting_list = 0
        and appt.book_online_status = 0
        and pd.staff_id = #{staffId}
        and pd.status = 1
        and appt.appointment_date &gt;= #{startDate}
        and appt.appointment_date &lt;= #{endDate}
        order by appt.appointment_date asc
    </select>

    <select id="queryWebReportNoShowAppts" resultMap="ReportWebAppointmentMap">
        SELECT a.id,
               a.customer_id,
               a.appointment_date,
               a.appointment_start_time,
               a.status,
               a.create_time,
               a.cancel_by_type,
               a.cancel_by,
               a.update_time,
               b.`type`,
               b.note,
               a.no_show,
               a.no_show_fee,
               a.created_by_id,
               a.no_show_by
        from moe_grooming_appointment a
                 left join moe_grooming_note b
                           on a.id = b.grooming_id and b.is_deleted = false
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          AND a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.no_show = 1
          and a.status = 4
          and a.book_online_status = 0
        order by a.appointment_date desc, a.appointment_start_time desc
    </select>

    <select id="queryApptsWithInvoiceAndPetDetails" resultMap="ReportWebAppointmentMap">
        SELECT a.id, a.customer_id, a.appointment_date, a.appointment_start_time, a.status,
               a.create_time, a.is_paid, c.staff_id,
               s.type as service_type, c.pet_id, c.id as pet_detail_id, c.start_time, c.end_time,
               c.service_price, c.service_id, s.name as service_name
        from moe_grooming.moe_grooming_appointment a
            left join moe_grooming.moe_grooming_pet_detail c on a.id = c.grooming_id and c.status = 1
            left join moe_grooming.moe_grooming_service s on c.service_id = s.id
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          and a.service_type_include = 1
          and a.is_waiting_list = 0
          and a.is_deprecate = 0
          and a.is_block = 2
          and a.book_online_status = 0
          and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

        order by a.appointment_date desc, a.appointment_start_time desc
    </select>

  <select id="queryApptIdsByPageV2" resultType="integer">
    select distinct a.id
    from moe_grooming.moe_grooming_appointment a
    left join moe_grooming.moe_grooming_pet_detail c on a.id = c.grooming_id and c.status = 1
    left join moe_grooming.moe_grooming_service_operation gso on c.id = gso.grooming_service_id
    where a.business_id in
    <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
      #{businessId}
    </foreach>
    and a.appointment_date &gt;= DATE_SUB(#{startDate}, INTERVAL 60 DAY)
    and a.appointment_date &lt;= #{endDate}
    AND a.appointment_end_date &gt;= #{startDate}
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and a.is_block = 2
    and a.book_online_status = 0
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    and c.start_date &gt;= #{startDate}
    and c.start_date &lt;= #{endDate}
    <if test="staffId != null">
      and (c.staff_id = #{staffId} or gso.staff_id = #{staffId})
    </if>
    order by c.start_date desc, c.start_time desc
    <if test="offset != null and size != null">
      limit #{offset}, #{size}
    </if>
  </select>

  <select id="getPayrollApptsCountV2" resultType="integer">
    SELECT count(*)
    from (
    SELECT a.id, c.start_date
    from moe_grooming.moe_grooming_appointment a
    left join moe_grooming.moe_grooming_pet_detail c on a.id = c.grooming_id and c.status = 1
    where a.business_id in
    <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
      #{businessId}
    </foreach>
    and a.appointment_date &gt;= DATE_SUB(#{startDate}, INTERVAL 60 DAY)
    and a.appointment_date &lt;= #{endDate}
    and a.appointment_end_date &gt;= #{startDate}
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and a.is_block = 2
    and a.book_online_status = 0
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    <if test="staffId != null">
      and c.staff_id = #{staffId}
    </if>
    and c.start_date &gt;= #{startDate}
    and c.start_date &lt;= #{endDate}
    GROUP BY a.id, c.start_date
    union
    select a.id, c.start_date
    from moe_grooming.moe_grooming_appointment a
    left join moe_grooming.moe_grooming_pet_detail c on a.id = c.grooming_id and c.status = 1
    left join moe_grooming.moe_grooming_service_operation gso
    on c.id = gso.grooming_service_id
    where a.business_id in
    <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
      #{businessId}
    </foreach>
    and a.appointment_date &gt;= DATE_SUB(#{startDate}, INTERVAL 60 DAY)
    and a.appointment_date &lt;= #{endDate}
    and a.appointment_end_date &gt;= #{startDate}
    and a.service_type_include = 1
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and a.is_block = 2
    and a.book_online_status = 0
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    <if test="staffId != null">
      and gso.staff_id = #{staffId}
    </if>
    and c.start_date &gt;= #{startDate}
    and c.start_date &lt;= #{endDate}
    group by a.id, c.start_date
    ) as temp
  </select>

    <select id="queryWebReportApptEmployee" resultMap="ReportWebAppointmentMap">
        SELECT a.id, a.customer_id, a.appointment_date, a.status,a.is_paid,
               c.staff_id, s.type as service_type, s.name as service_name, c.pet_id,
               c.start_date, c.end_date, c.price_unit, c.specific_dates, c.associated_service_id,
               c.id as pet_detail_id, c.start_time, c.end_time, c.service_price, c.service_id, c.service_item_type
        from moe_grooming.moe_grooming_appointment a
            left join moe_grooming.moe_grooming_pet_detail c on a.id = c.grooming_id and c.status = 1
            left join moe_grooming.moe_grooming_service s on c.service_id = s.id
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          and a.service_type_include = 1
            <if test="staffId != null">
                and c.staff_id = #{staffId}
            </if>
          and a.is_waiting_list = 0
          and a.is_deprecate = 0
          and a.is_block = 2
          and a.book_online_status = 0
          and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and c.status = 1
    </select>

    <select id="queryReportApptEmployeeByApptIds" resultMap="GroomReportApptDetailMap">
        SELECT a.id, a.customer_id, a.appointment_date, a.status,
        c.id as pet_detail_id, c.staff_id, c.service_price,c.service_id
        from moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail c
        on a.id = c.grooming_id and c.status = 1
        where a.id in
        <foreach close=")" collection="appIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and a.service_type_include = 1
    </select>

    <select id="queryApptStaffAndCustomer" resultMap="ReportWebAppointmentMap">
        SELECT a.id, a.customer_id, a.appointment_date, a.appointment_start_time, b.staff_id, b.id as pet_detail_id
        from moe_grooming.moe_grooming_appointment a
                 left join moe_grooming.moe_grooming_pet_detail b
                           on a.id = b.grooming_id
        where a.business_id = #{businessId}
          and a.appointment_date &gt;= #{startDate}
          and a.appointment_date &lt;= #{endDate}
          and a.service_type_include = 1
          and a.is_waiting_list = 0
          AND a.is_deprecate = 0
          AND a.is_block = 2
          and a.book_online_status = 0
          and b.status = 1
          and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

        order by a.appointment_date desc, a.appointment_start_time desc
    </select>

    <select id="getAppointmentWithPetDetails" resultMap="AppointmentPetDetailMap">
      select a.appointment_date,
             a.appointment_start_time,
             a.appointment_end_time,
             a.id,
             a.status,
             a.business_id,
             a.customer_id,
             a.source,
             b.id    as pet_detail_id,
             b.service_id,
             gs.name as service_name,
             gs.type as service_type,
             b.pet_id,
             b.staff_id,
             b.start_time,
             b.service_time,
             b.service_price,
             a.company_id,
             b.service_item_type
      from moe_grooming.moe_grooming_appointment a
             left join moe_grooming.moe_grooming_pet_detail b on a.id = b.grooming_id and b.status = 1
             left join moe_grooming.moe_grooming_service gs on gs.id = b.service_id
      where a.id = #{appointmentId,jdbcType=INTEGER}
        and a.service_type_include in (1, 3, 5, 7)
        and a.is_waiting_list = 0
        and a.is_deprecate = 0
        and a.is_block = 2
        <if test="includeCancelled == null or includeCancelled == false">
          and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

        </if>
    </select>

  <select id="getAppointmentById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming.moe_grooming_appointment
    where id = #{id,jdbcType=INTEGER}
    and is_waiting_list = 0
    and is_deprecate = 0
    and is_block = 2
  </select>

  <select id="getAppointmentListWithPetDetails" resultMap="AppointmentPetDetailMap">
    select a.id, a.business_id, a.customer_id, a.appointment_date, a.appointment_start_time,
           a.appointment_end_time, a.status, b.id as pet_detail_id, b.service_id,
           gs.name as service_name, gs.type as service_type, b.pet_id, b.staff_id, b.start_time,
           b.service_time, b.service_item_type
    from moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail b on a.id = b.grooming_id and b.status = 1
        left join moe_grooming.moe_grooming_service gs on gs.id = b.service_id
    where a.id in
          <foreach close=")" collection="appointmentIds" item="appointmentId" open="(" separator=",">
            #{appointmentId,jdbcType=INTEGER}
          </foreach>
      and a.service_type_include in (1, 3, 5, 7)
      and a.is_waiting_list = 0
      and a.is_deprecate = 0
      and a.is_block = 2
      <if test="includeCancelled == null or includeCancelled == false">
        and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      </if>
  </select>

  <select id="getRepeatedAppointmentListWithPetDetails" resultMap="AppointmentPetDetailMap">
    select a.id, a.business_id, a.customer_id, a.appointment_date, a.appointment_start_time,
    a.appointment_end_time, a.status, b.id as pet_detail_id, b.service_id,
    gs.name as service_name, gs.type as service_type, b.pet_id, b.staff_id, b.start_time,
    b.service_time, b.service_item_type
    from moe_grooming.moe_grooming_appointment a
    left join moe_grooming.moe_grooming_pet_detail b on a.id = b.grooming_id and b.status = 1
    left join moe_grooming.moe_grooming_service gs on gs.id = b.service_id
    where a.id in
    <foreach close=")" collection="appointmentIds" item="appointmentId" open="(" separator=",">
      #{appointmentId,jdbcType=INTEGER}
    </foreach>
    and a.service_type_include in (1, 4, 5)
    and a.is_waiting_list = 0
    and a.is_deprecate = 0
    and a.is_block = 2
    and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
  </select>

    <select id="getPetCountUtilChristmas" resultType="int">
        SELECT count(DISTINCT pd.grooming_id, pd.pet_id)
        FROM
        moe_grooming_pet_detail pd
        LEFT JOIN moe_grooming_appointment ga ON pd.grooming_id = ga.id
        WHERE
        pd.`status` = 1
        AND ga.business_id = #{businessId}
        and ga.service_type_include = 1
        AND ga.`status` IN ( 1, 2 )
        AND ga.is_block = 2
        AND ga.is_waiting_list = 0
        AND ga.is_deprecate = 0
        AND (
            (ga.appointment_date = #{appointmentDate} AND ga.appointment_end_time &gt; #{endTimes})
            OR
            ga.appointment_date &gt; #{appointmentDate}
        )
        AND (
            (ga.appointment_date = '2021-12-24' AND ga.appointment_end_time &lt;= 1440)
            OR
            ga.appointment_date &lt; '2021-12-24'
        )
    </select>

    <select id="get2021AnnualPetCountUtilChristmas" resultType="int">
        SELECT count(DISTINCT pd.grooming_id, pd.pet_id)
        FROM moe_grooming_pet_detail pd
                 LEFT JOIN moe_grooming_appointment ga ON pd.grooming_id = ga.id
        WHERE pd.`status` = 1
          AND ga.business_id = #{businessId}
          and ga.service_type_include = 1
          AND ga.`status` IN
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          AND ga.is_block = 2
          AND ga.is_waiting_list = 0
          AND ga.is_deprecate = 0
          AND ga.appointment_date &gt;= '2021-01-01'
          AND (
                (ga.appointment_date = '2021-12-24' AND ga.appointment_end_time &lt;= 1440)
                OR
                ga.appointment_date &lt; '2021-12-24'
            )
    </select>

    <select id="getBusiness2021AllServiceDuration" resultType="int">
        SELECT sum(mgpd.service_time)
        FROM moe_grooming_pet_detail mgpd
        LEFT JOIN moe_grooming_appointment mga ON mgpd.grooming_id = mga.id
        WHERE mgpd.`status` = 1
        AND mga.business_id = #{businessId}
        and mga.service_type_include = 1
        AND mga.`status` IN
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        AND mga.is_block = 2
        AND mga.is_waiting_list = 0
        AND mga.is_deprecate = 0
        AND mga.appointment_date &gt;= '2021-01-01'
        AND mga.appointment_date &lt;= '2021-12-31'
        <if test="staffId != null">
            AND mgpd.staff_id = #{staffId}
        </if>
    </select>
    <select id="getBusiness2021LongestWorking" resultType="com.moego.server.grooming.dto.AppointmentDateWithServiceDurationDto">
        SELECT sum(mgpd.service_time) as longestServiceTime,mga.appointment_date as appointmentDate
        FROM moe_grooming_appointment mga
                 LEFT JOIN moe_grooming_pet_detail mgpd ON mgpd.grooming_id = mga.id
        WHERE mgpd.`status` = 1
          AND mga.business_id = #{businessId}
          and mga.service_type_include = 1
          AND mga.`status` IN
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          AND mga.is_block = 2
          AND mga.is_waiting_list = 0
          AND mga.is_deprecate = 0
          AND mga.appointment_date &gt;= '2021-01-01'
          AND mga.appointment_date &lt;= '2021-12-31'
        <if test="staffId != null">
            AND mgpd.staff_id = #{staffId}
        </if>
        group by mga.appointment_date
        order by longestServiceTime desc limit 1
    </select>

    <select id="getBusiness2021PetIdList" resultType="com.moego.server.grooming.dto.GroomingIdPetIdDto">
        SELECT distinct pd.grooming_id as groomingId, pd.pet_id as petId
        FROM moe_grooming_pet_detail pd
                 LEFT JOIN moe_grooming_appointment ga ON pd.grooming_id = ga.id
        WHERE pd.`status` = 1
          AND ga.business_id = #{businessId}
          and ga.service_type_include = 1
          AND ga.`status` IN
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          AND ga.is_block = 2
          AND ga.is_waiting_list = 0
          AND ga.is_deprecate = 0
          AND ga.appointment_date &gt;= '2021-01-01'
          AND ga.appointment_date &lt;= '2021-12-31'
        <if test="staffId != null">
            AND pd.staff_id = #{staffId}
        </if>
    </select>

    <update id="updateBatchCancelByPrimaryKey">
        update moe_grooming_appointment
        <set>
            status = 4,
            <if test="noShow != null">
                no_show = #{noShow},
            </if>
            <if test="cancelBy != null">
                cancel_by = #{cancelBy},
            </if>
            <if test="cancelByType != null">
                cancel_by_type = #{cancelByType},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="canceledTime != null">
                canceled_time = #{canceledTime},
            </if>
        </set>
        where business_id = #{businessId}
        AND id in
        <foreach close=")" collection="idList" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="cancelBlockByRepeatType">
        update moe_grooming_appointment
        set
        is_deprecate = 1,
        status = 4,
        update_time = #{updateTime}
        where
        business_id = #{businessId} AND
        repeat_id != 0 AND
        repeat_id = #{repeatId}
        <if test="appointmentDate != null">
            AND appointment_date &gt;= #{appointmentDate}
        </if>
        and service_type_include = 1
    </update>

    <select id="queryExpiryCustomerLastApptListCount" resultType="integer">
        SELECT
        count(DISTINCT customer_id)
        from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and service_type_include = 1
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and business_id = #{businessId}
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &lt;= #{endTimes}))
        and customer_id in
        <foreach close=")" collection="customerIdSet" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
        order by appointment_date desc ,appointment_end_time desc limit 99999) t
    </select>
    <select id="queryExpiryCustomerLastApptListWithOrderLimit" resultMap="CustomerGroomingAppointmentDTOResultMap">
        SELECT * from (SELECT
        id,appointment_date,appointment_start_time,appointment_end_time,order_id,customer_id,business_id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
        and is_deprecate = 0
        and service_type_include = 1
        and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and is_block = 2
        and business_id = #{businessId}
        and (appointment_date &lt; #{appointmentDate} or (appointment_date = #{appointmentDate} and appointment_end_time
        &lt;= #{endTimes}))
        and customer_id in
        <foreach close=")" collection="customerIdSet" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
        order by appointment_date desc ,appointment_end_time desc limit 99999) t
        GROUP BY t.customer_id
        order by appointment_date desc ,appointment_end_time desc
        limit #{startNum},#{pageSize}
    </select>
    <select id="queryExpiryCustomerUpcomingCount" resultType="com.moego.server.grooming.service.dto.CustomerIdCountResultDto">
        SELECT count(*) AS count, a.customer_id as customerId
        FROM
            moe_grooming_appointment a
      JOIN (
          SELECT DISTINCT b.customer_id
          FROM moe_grooming_appointment b
          WHERE b.business_id = #{businessId}
          AND b.repeat_id != 0
          ) vc ON a.customer_id = vc.customer_id
        WHERE
        a.is_waiting_list = 0
        and a.is_deprecate = 0
        and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
        and a.is_block = 2
        and a.business_id = #{businessId}
        and ((a.appointment_date
         &gt; #{appointmentDate})
           or (a.appointment_date = #{appointmentDate}
          and a.appointment_end_time
         &gt; #{endTimes}))
        <if test="dismissIds !=null and dismissIds.size()&gt;0">
            and a.id not in
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
            a.customer_id
        HAVING
        count &lt; #{upcomingCount}
    </select>
    <select id="queryAppointmentListBetweenTwoTime" resultMap="GroomingBookOnlineResultMap">
        SELECT
            a.id,
            a.appointment_date,
            a.appointment_start_time,
            a.appointment_end_time,
            a.status,
            a.book_online_status,
            a.color_code,
            a.create_time,
            a.customer_id,
            a.out_of_area,
            a.no_start_time,
            p.id petDetailId,
            p.pet_id,
            p.staff_id,
            p.service_id,
            p.service_time,
            p.service_price,
            p.start_time,
            p.end_time
        FROM moe_grooming_appointment a
                 inner join moe_grooming_pet_detail p on a.id = p.grooming_id and p.status = 1
        WHERE a.is_waiting_list = 0
          and a.is_deprecate = 0
          and a.service_type_include = 1
          and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
          and a.is_block = 2
          and p.staff_id = #{staffId}
          and a.business_id = #{businessId}
          and (a.appointment_date &gt;= #{startAppointmentDate} and a.appointment_start_time &gt;= #{startMins})
          and (a.appointment_date &lt;= #{endAppointmentDate} and a.appointment_start_time &lt;= #{endMins})
    </select>
    <select id="queryUpcomingApptIdList" resultType="integer">
      select id
      from moe_grooming_appointment
      WHERE is_waiting_list = 0
      and is_deprecate = 0
      and is_block = 2
      and service_type_include = 1
      and business_id = #{businessId}
      and (appointment_date &gt; #{appointmentDate} or
      (appointment_date = #{appointmentDate} and appointment_end_time
      &gt;= #{endTimes}))
      <if test="customerId != null">
        and customer_id = #{customerId}
      </if>
      <if test="statusList != null and statusList.size() &gt; 0">
        and status in
        <foreach close=")" collection="statusList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </select>
    <select id="queryCustomerUpcomingApptIdList" resultType="integer">
        select id
        from moe_grooming_appointment
        WHERE is_waiting_list = 0
          and is_deprecate = 0
          and service_type_include = 1
          and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          and is_block = 2
          and business_id = #{businessId}
          and (appointment_date &gt; #{appointmentDate} or
               (appointment_date = #{appointmentDate} and appointment_end_time
                   &gt;= #{endTimes}))
          and customer_id = #{customerId}
    </select>
    <select id="selectBlockByRepeatType" resultType="int">
        select id
        from moe_grooming_appointment
        where
        business_id = #{businessId} AND
        is_block = 1 AND
        repeat_id != 0 AND
        repeat_id = #{repeatId}
        <if test="appointmentDate != null">
            AND appointment_date &gt;= #{appointmentDate}
        </if>
        and service_type_include = 1
    </select>

    <select id="getCustomerIdsWithUncancelledRepeat" resultType="integer">
      SELECT distinct customer_id
      FROM moe_grooming.moe_grooming_appointment
      WHERE business_id = #{businessId}
      AND is_waiting_list = 0
      AND is_deprecate = 0
      AND is_block = 2
      AND status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
      and repeat_id != 0
      AND customer_id in
      <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
        #{customerId}
      </foreach>
    </select>

    <select id="countCustomerIdsWithUncancelledRepeat" resultType="java.lang.Integer">
        SELECT count(distinct customer_id)
        FROM moe_grooming.moe_grooming_appointment
        WHERE business_id = #{businessId}
        AND is_waiting_list = 0
        AND is_deprecate = 0
        AND is_block = 2
        and service_type_include = 1
        AND status in
        <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
            #{status.value}
        </foreach>
        AND repeat_id != 0
        AND customer_id in
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
    </select>

  <select id="countCustomerIdsWithUncancelledRepeatByCompanyId" resultType="java.lang.Integer">
    SELECT count(distinct customer_id)
    FROM moe_grooming.moe_grooming_appointment
    WHERE company_id = #{companyId}
      AND is_waiting_list = 0
      AND is_deprecate = 0
      AND is_block = 2
      and service_type_include = 1
      AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    AND repeat_id != 0
    AND customer_id in
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach>
  </select>

    <select id="getLastedNotCanceledAppointment" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        FROM moe_grooming_appointment
        where business_id = #{businessId}
        and customer_id = #{customerId}
        -- 没有取消的 appointment
        and status != 4
        and is_deprecate = 0
        and is_block = 2
        and repeat_id != 0
        and is_waiting_list = 0
        and service_type_include = 1
        order by create_time desc limit 1
    </select>
    <select id="selectBlockByDateRange" resultMap="BlockInfoWithStaffMap">
        select a.id, a.appointment_date, a.appointment_start_time, a.appointment_end_time, pd.staff_id
        from moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.status = 1
        where a.business_id = #{businessId}
        and a.appointment_date &gt;= #{startDate}
        and a.appointment_date &lt;= #{endDate}
        and a.service_type_include = 1
        and a.status = 1
        and a.is_block = 1
    </select>
    <select id="queryPetBreed" resultMap="PetBreedMAP">
        select pd.pet_id as petId, COUNT(distinct a.id) as amount
        from moe_grooming.moe_grooming_appointment a
        left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.status = 1
        where a.business_id = #{businessId}
        and a.appointment_date &gt;= #{startDate}
        and a.appointment_date &lt;= #{endDate}
        and a.service_type_include = 1
        and a.is_waiting_list = 0
        and a.is_deprecate = 0
        and a.is_block = 2
        and a.book_online_status = 0
        and a.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

        group by pd.pet_id
    </select>
    <select id="getAppointmentsWithin" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM moe_grooming_appointment
        WHERE business_id = #{businessId}
        AND is_block = 2
        AND is_waiting_list = 0
        AND is_deprecate = 0
        and service_type_include = 1
        AND (
        (appointment_date = #{startDate} AND
        appointment_end_time &gt; #{startMinutes})
        OR
        appointment_date &gt; #{startDate}
        )
        AND (appointment_date &lt; #{endDate}
        OR (appointment_date = #{endDate} and appointment_end_time &lt; #{endMinutes}))
    </select>

    <select id="queryStaffAppointmentPetIdByTime" resultMap="StaffAppointPetIdByTimeResultMap">
        select pd.staff_id, a.appointment_date, a.appointment_start_time, pd.pet_id
        from moe_grooming.moe_grooming_appointment a
                 left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.`status` = 1
        where a.business_id = #{businessId}
        and a.service_type_include = 1
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            and a.appointment_date in
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTimes != null and startTimes.size() &gt; 0">
            and a.appointment_start_time in
            <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_block = 2
        and a.is_waiting_list = 0
        and a.is_deprecate = 0
        group by pd.staff_id, a.appointment_date, a.appointment_start_time, pd.pet_id
        union
        select o.staff_id, a.appointment_date, o.start_time, o.pet_id
        from moe_grooming.moe_grooming_appointment a
                 left join moe_grooming.moe_grooming_service_operation o on a.id = o.grooming_id
        where a.business_id = #{businessId}
        and a.service_type_include = 1
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            and a.appointment_date in
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTimes != null and startTimes.size() &gt; 0">
            and o.start_time in
            <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_block = 2
        and a.is_waiting_list = 0
        and a.is_deprecate = 0
        group by o.staff_id, a.appointment_date, o.start_time, o.pet_id
    </select>


    <select id="queryStaffAppointmentPetIdByTimeV2" resultMap="StaffAppointPetIdByTimeResultMap">
        select pd.staff_id, pd.start_date as appointment_date, pd.start_time as appointment_start_time, pd.pet_id
        from moe_grooming.moe_grooming_appointment a
                 left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.`status` = 1
        where a.business_id = #{businessId}
        and a.service_type_include in (1, 3, 5, 7, 17)
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            and pd.start_date in
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTimes != null and startTimes.size() &gt; 0">
            and pd.start_time in
            <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_block = 2
        and a.is_waiting_list = 0
        and a.is_deprecate = 0

        union
        select o.staff_id, pd.start_date as appointment_date, o.start_time as appointment_start_time, o.pet_id
        from moe_grooming.moe_grooming_appointment a
                 left join moe_grooming.moe_grooming_pet_detail pd on a.id = pd.grooming_id and pd.`status` = 1
                 left join moe_grooming.moe_grooming_service_operation o on a.id = o.grooming_id
        where a.business_id = #{businessId}
        and a.service_type_include in (1, 3, 5, 7, 17)
        <if test="appointmentDates != null and appointmentDates.size &gt; 0">
            and pd.start_date in
            <foreach close=")" collection="appointmentDates" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTimes != null and startTimes.size() &gt; 0">
            and o.start_time in
            <foreach close=")" collection="startTimes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        and a.status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
        and a.is_block = 2
        and a.is_waiting_list = 0
        and a.is_deprecate = 0

    </select>

  <select id="queryCustomerFinishApptIds" resultType="integer">
    SELECT a.id
    FROM moe_grooming_appointment a
    WHERE a.company_id = #{companyId}
    AND a.customer_id IN
    <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    AND a.status = 3 -- only finished appointment
  </select>

  <select id="getServiceDuration" resultType="int">
    SELECT sum(detail.service_time)
    FROM moe_grooming_pet_detail detail
    LEFT JOIN moe_grooming_appointment appt ON detail.grooming_id = appt.id
    WHERE detail.`status` = 1
    AND appt.business_id = #{businessId}
    and appt.service_type_include = 1
    AND appt.`status` in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    AND appt.is_block = 2
    AND appt.is_waiting_list = 0
    AND appt.is_deprecate = 0
    AND appt.appointment_date &gt;= #{startDate}
    AND appt.appointment_date &lt;= #{endDate}
    <if test="staffId != null">
      AND detail.staff_id = #{staffId}
    </if>
    <if test="staffId != null">
      GROUP BY detail.staff_id
    </if>
  </select>
  <select id="getLongestWorkingDay" resultType="com.moego.server.grooming.mapper.po.LongestWorkingDayPo">
    SELECT appt.appointment_date as appointmentDate,
    sum(detail.service_time) as longestServiceTime
    FROM moe_grooming_appointment appt
    LEFT JOIN moe_grooming_pet_detail detail ON detail.grooming_id = appt.id
    WHERE detail.`status` = 1
    AND appt.business_id = #{businessId}
    and appt.service_type_include = 1
    AND appt.`status` in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    AND appt.is_block = 2
    AND appt.is_waiting_list = 0
    AND appt.is_deprecate = 0
    AND appt.appointment_date &gt;= #{startDate}
    AND appt.appointment_date &lt;= #{endDate}
    <if test="staffId != null">
      AND detail.staff_id = #{staffId}
    </if>
    group by
    <if test="staffId != null">
      detail.staff_id,
    </if>
    appt.appointment_date
    order by longestServiceTime desc limit 1
  </select>
  <select id="getOnlineBookingCount" resultType="int">
    SELECT count(*) as count
    FROM moe_grooming_appointment
    WHERE business_id = #{businessId}
      and is_deprecate = 0
      and is_block = 2
      and `source` = 22168
      and appointment_date &gt;= #{startDate}
      and appointment_date &lt;= #{endDate}
      and service_type_include = 1
  </select>
  <select id="getGroomedPetIds" resultType="java.lang.Integer">
    SELECT distinct detail.pet_id
    FROM moe_grooming_pet_detail detail
    left join moe_grooming_appointment appt on detail.grooming_id = appt.id
    WHERE detail.`status` = 1
    AND appt.business_id = #{businessId}
    and appt.service_type_include = 1
    AND appt.`status` in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    AND appt.is_block = 2
    AND appt.is_waiting_list = 0
    AND appt.is_deprecate = 0
    AND appt.appointment_date &gt;= #{startDate}
    AND appt.appointment_date &lt;= #{endDate}
    <if test="staffId != null">
      AND detail.staff_id = #{staffId}
    </if>
  </select>
  <select id="getNotCanceledAppointmentIds" resultType="integer">
    SELECT
    distinct appt.id
    FROM moe_grooming_appointment appt
    <if test="staffId != null">
      LEFT JOIN moe_grooming_pet_detail detail ON detail.grooming_id = appt.id
    </if>
    WHERE appt.business_id = #{businessId}
    AND appt.is_block = 2
    AND appt.is_waiting_list = 0
    AND appt.is_deprecate = 0
    AND appt.service_type_include = 1
    AND appt.`status` in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
    AND appt.appointment_date &gt;= #{startDate}
    AND appt.appointment_date &lt;= #{endDate}
    <if test="staffId != null">
      AND detail.staff_id = #{staffId}
    </if>
  </select>
    <select id="listUpcomingAppt" parameterType="com.moego.server.grooming.service.dto.client.ListUpcomingApptDTO" resultMap="BaseResultMap">
      <foreach close=")" collection="list" item="apptDTO" open="(" separator="union all">
        (
        SELECT <include refid="Base_Column_List" />
        FROM moe_grooming_appointment
        WHERE customer_id = #{apptDTO.customerId}
        and company_id = #{apptDTO.companyId}
        and service_type_include = 1
        and (appointment_date &gt; #{apptDTO.currentDate} or (appointment_date =
        #{apptDTO.currentDate} and
        appointment_end_time &gt;= #{apptDTO.currentMinutes}))
        <if test="apptDTO.isBlock != null">
          and is_block = #{apptDTO.isBlock}
        </if>
        <if test="apptDTO.isDeprecate != null">
          and is_deprecate = #{apptDTO.isDeprecate}
        </if>
        <if test="apptDTO.status != null">
          and status = #{apptDTO.status}
        </if>
        <if test="apptDTO.statusList != null and apptDTO.statusList.size &gt; 0">
          and status in
          <foreach close=")" collection="apptDTO.statusList" item="status" open="(" separator=",">
            #{status.value}
          </foreach>
        </if>
        <if test="apptDTO.isWaitingList != null">
          and is_waiting_list = #{apptDTO.isWaitingList}
        </if>
        <if test="apptDTO.source != null">
          and source = #{apptDTO.source}
        </if>
        <if test="apptDTO.createdById != null">
          and created_by_id = #{apptDTO.createdById}
        </if>
        <if test="apptDTO.bookOnlineStatus != null">
          and book_online_status = #{apptDTO.bookOnlineStatus}
        </if>
        <if test="apptDTO.shareRangeType == 3 and apptDTO.shareApptIdList != null and apptDTO.shareApptIdList.size &gt; 0">
          and id in
          <foreach close=")" collection="apptDTO.shareApptIdList" item="shareApptId" open="(" separator=",">
            #{shareApptId}
          </foreach>
        </if>
        <if test="apptDTO.shareRangeType == 1">
          and appointment_date &lt;= #{apptDTO.endDate}
        </if>
        <if test="apptDTO.shareRangeType == 2">
          order by appointment_date asc, appointment_end_time asc limit #{apptDTO.limit}
        </if>
          )
      </foreach>
      <choose>
        <when test="pageQuery.sort != null and pageQuery.sort.sortBy != null and pageQuery.sort.order != null">
          order by ${pageQuery.sort.sortBy} ${pageQuery.sort.order}
        </when>
        <when test="pageQuery.sortList != null and pageQuery.sortList.size &gt; 0">
          <foreach collection="pageQuery.sortList" item="sort" open="order by " separator=",">
            ${sort.sortBy} ${sort.order}
          </foreach>
        </when>
        <otherwise>
          order by create_time desc
        </otherwise>
      </choose>
    </select>
    <select id="listClientAppt" parameterType="com.moego.server.grooming.service.dto.client.ListCommonApptDTO" resultMap="BaseResultMap">
      <foreach close=")" collection="list" item="apptDTO" open="(" separator="union all">
        SELECT <include refid="Base_Column_List" />
        FROM moe_grooming_appointment
        WHERE company_id = #{apptDTO.companyId}
        and customer_id = #{apptDTO.customerId}
        and service_type_include = 1
        <if test="apptDTO.isHistory != null">
          <if test="apptDTO.isHistory == true">
            and (appointment_date &lt; #{apptDTO.currentDate} or (appointment_date = #{apptDTO.currentDate} and appointment_end_time &lt;= #{apptDTO.currentMinutes}))
          </if>
          <if test="apptDTO.isHistory == false">
            and (appointment_date &gt; #{apptDTO.currentDate} or (appointment_date = #{apptDTO.currentDate} and appointment_end_time &gt;= #{apptDTO.currentMinutes}))
          </if>
        </if>
        <if test="apptDTO.isDeprecate != null">
          and is_deprecate = #{apptDTO.isDeprecate}
        </if>
        <if test="apptDTO.status != null">
          and status = #{apptDTO.status}
        </if>
        <if test="apptDTO.statusList != null and apptDTO.statusList.size &gt; 0">
          and status in
          <foreach close=")" collection="apptDTO.statusList" item="status" open="(" separator=",">
            #{status.value}
          </foreach>
        </if>
        <if test="apptDTO.source != null">
          and source = #{apptDTO.source}
        </if>
        <if test="apptDTO.createdById != null">
          and created_by_id = #{apptDTO.createdById}
        </if>
        <if test="apptDTO.isBlock != null">
          and is_block = #{apptDTO.isBlock}
        </if>
        <if test="apptDTO.bookOnlineStatus != null">
          and book_online_status = #{apptDTO.bookOnlineStatus}
        </if>
        <if test="apptDTO.isWaitingList != null">
          and is_waiting_list = #{apptDTO.isWaitingList}
        </if>
      </foreach>
      <choose>
          <when test="pageQuery.sort != null and pageQuery.sort.sortBy != null and pageQuery.sort.order != null">
              order by ${pageQuery.sort.sortBy} ${pageQuery.sort.order}
          </when>
          <when test="pageQuery.sortList != null and pageQuery.sortList.size &gt; 0">
              <foreach collection="pageQuery.sortList" item="sort" open="order by ISNULL(appointment_date) || appointment_date =''," separator=",">
                  ${sort.sortBy} ${sort.order}
              </foreach>
          </when>
          <otherwise>
              order by create_time desc
          </otherwise>
      </choose>
    </select>
    <select id="countBetween" resultType="com.moego.server.grooming.mapper.po.WebsiteAppointmentSummaryPO">
        select max(id) as `maxId`, count(*) as `count`
        from moe_grooming_appointment
        where service_type_include = 1
        <if test="startId != null">
            and id &gt;= #{startId}
        </if>
        <if test="endId != null">
            and id &lt;= #{endId}
        </if>
    </select>

  <select id="getCustomerFirstAppt" resultMap="BaseResultMap">
    select
    id, business_id, customer_id, source, create_time
    from moe_grooming_appointment
    where business_id = #{businessId,jdbcType=INTEGER}
      and customer_id = #{customerId,jdbcType=INTEGER}
      and service_type_include = 1
    order by create_time asc limit 1
  </select>

  <select id="listCustomerIdByFilter" resultType="int">
    SELECT customerId FROM (
    SELECT customerId
    <foreach close="" collection="clientsFilter.filters" item="filter" open="," separator=",">
      MAX(CASE WHEN type = #{filter.property.column} THEN num ELSE 0 END) AS ${filter.property.column}
    </foreach>
    FROM
    <foreach close=")" collection="clientsFilter.filters" item="filter" open="(" separator="UNION ALL">
      SELECT
      customer_id customerId, #{filter.property.column} AS type, COUNT(*) num
      FROM
      moe_grooming_appointment
      WHERE
      company_id = #{clientsFilter.companyId}
      <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size &gt; 0">
        AND customer_id IN
        <foreach close=")" collection="clientsFilter.customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
      </if>
      <choose>
        <when test="filter.property == @com.moego.common.enums.PropertyEnum@total_appt_ob_requests_cnt">
          AND is_deprecate = 0
          AND is_block = 2
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@total_appt_cnt or           filter.property == @com.moego.common.enums.PropertyEnum@client_total_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND book_online_status = 0
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@finished_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND book_online_status = 0
          AND status = 3
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@upcoming_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

          AND is_waiting_list = 0
          AND book_online_status = 0
          AND (appointment_date &gt; #{clientsFilter.businessDateTime.currentDate} OR
          (appointment_date = #{clientsFilter.businessDateTime.currentDate} AND appointment_end_time &gt;= #{clientsFilter.businessDateTime.currentMinutes}))
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@no_show_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND book_online_status = 0
          AND no_show = 1
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@cancelled_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND book_online_status = 0
          AND status = 4
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@waitlist_appt_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND is_waiting_list = 1
        </when>

        <when test="filter.property == @com.moego.common.enums.PropertyEnum@unpaid_invoice_cnt">
          AND is_deprecate = 0
          AND is_block = 2
          AND status = 3
          AND is_paid != 1
        </when>
      </choose>
      GROUP BY customer_id
    </foreach>
    t GROUP BY customerId ) t WHERE
    <include refid="criteriaFilter" />
    <if test="clientsFilter.sort != null">
      ORDER BY ${clientsFilter.sort.property.column} ${clientsFilter.sort.order}
    </if>
    <if test="clientsFilter.offset != null and clientsFilter.limit != null">
      LIMIT #{clientsFilter.offset}, #{clientsFilter.limit}
    </if>
  </select>

  <sql id="criteriaFilter">
      <foreach close=")" collection="clientsFilter.filters" index="idx" item="filter" open="(">
        <include refid="criteriaSingleFilter" />
        <if test="idx != clientsFilter.filters.size - 1">
          ${clientsFilter.connector}
        </if>
      </foreach>
  </sql>

  <sql id="criteriaSingleFilter">
    <choose>
      <when test="filter.isSingle">
        ${filter.condition} #{filter.singleValue}
      </when>
      <when test="filter.isBetween">
        ${filter.condition} #{filter.singleValue} and #{filter.secondValue}
      </when>
      <when test="filter.isList">
        ${filter.condition}
        <foreach close=")" collection="filter.listValues" item="listItem" open="(" separator=",">
          #{listItem}
        </foreach>
      </when>
    </choose>
  </sql>

  <select id="listCustomerIdByDateFilter" resultType="int">
    <foreach collection="clientsFilter.filters" index="idx" item="filter">
    <if test="clientsFilter.type == @com.moego.common.enums.filter.TypeEnum@TYPE_AND and     clientsFilter.filters.size &gt; 1 and idx == 0">
        SELECT t${idx}.customer_id FROM
    </if>
     (
      SELECT
        customer_id
      FROM
        moe_grooming_appointment
      WHERE
      company_id = #{clientsFilter.companyId}
        <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size &gt; 0">
          AND customer_id IN
          <foreach close=")" collection="clientsFilter.customerIds" item="customerId" open="(" separator=",">
            #{customerId}
          </foreach>
        </if>
        AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

        AND book_online_status = 0
        AND is_deprecate = 0
        AND is_block = 2
      <choose>
        <when test="filter.property == @com.moego.common.enums.PropertyEnum@last_appt_date">
          AND (appointment_date &lt; #{clientsFilter.businessDateTime.currentDate} OR
               (appointment_date = #{clientsFilter.businessDateTime.currentDate} AND appointment_end_time &lt;= #{clientsFilter.businessDateTime.currentMinutes}))
        </when>
        <when test="filter.property == @com.moego.common.enums.PropertyEnum@next_appt_date">
          AND (appointment_date &gt; #{clientsFilter.businessDateTime.currentDate} OR
               (appointment_date = #{clientsFilter.businessDateTime.currentDate} AND appointment_end_time &gt;= #{clientsFilter.businessDateTime.currentMinutes}))
        </when>
      </choose>
      GROUP BY
          customer_id
      HAVING
      <include refid="criteriaSingleFilter" />
      )
      <choose>
        <when test="clientsFilter.type == @com.moego.common.enums.filter.TypeEnum@TYPE_AND">
          <if test="clientsFilter.filters.size &gt; 1">
            t${idx}
            <if test="idx == 0">
              INNER JOIN
            </if>
            <if test="idx &gt; 0">
              ON t${idx - 1}.customer_id = t${idx}.customer_id
            </if>
            <if test="idx != 0 and idx != clientsFilter.filters.size - 1">
              INNER JOIN
            </if>
          </if>
        </when>
        <when test="clientsFilter.type == @com.moego.common.enums.filter.TypeEnum@TYPE_OR">
          <if test="idx != clientsFilter.filters.size - 1">
            UNION
          </if>
        </when>
      </choose>
    </foreach>
    <if test="clientsFilter.sort != null">
      ORDER BY ${clientsFilter.sort.property.column} ${clientsFilter.sort.order}
    </if>
    <if test="clientsFilter.offset != null and clientsFilter.limit != null">
      LIMIT #{clientsFilter.offset}, #{clientsFilter.limit}
    </if>
  </select>

  <select id="listCustomerIdByGroomerFilter" resultType="java.lang.Integer">
    SELECT
        customer_id customerId
    FROM (
      SELECT
        a.customer_id,
        a.id,
        ROW_NUMBER() OVER (PARTITION BY a.customer_id ORDER BY a.appointment_date DESC,a.appointment_end_time DESC) AS rn
      FROM
        moe_grooming_appointment a
      WHERE
        a.company_id = #{clientsFilter.companyId}
        AND a.status in
          <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
            #{status.value}
          </foreach>
        AND a.book_online_status = 0
        AND a.is_deprecate = 0
        AND a.is_block = 2
        AND (a.appointment_date &lt; #{clientsFilter.businessDateTime.currentDate} OR
        (a.appointment_date = #{clientsFilter.businessDateTime.currentDate} AND a.appointment_end_time &lt;= #{clientsFilter.businessDateTime.currentMinutes}))
    ) customer_last_appt
    JOIN
    moe_grooming_pet_detail d
    ON
    customer_last_appt.id = d.grooming_id
    WHERE
    customer_last_appt.rn = 1
    AND d.status = 1
    AND <include refid="criteriaFilter" />
  </select>

  <select id="listCustomerApptCount" resultType="com.moego.server.grooming.dto.CustomerApptCountDTO">
    SELECT customer_id customerId,
           MAX(CASE WHEN type = 'totalApptCount' THEN num ELSE 0 END) AS totalApptCount,
           MAX(CASE WHEN type = 'totalApptAndRequestsCount' THEN num ELSE 0 END) AS totalApptAndRequestsCount,
           MAX(CASE WHEN type = 'finishedApptCount' THEN num ELSE 0 END) AS finishedApptCount
    FROM (
           SELECT
             customer_id,
             "totalApptCount" as type,
             COUNT(*) num
           FROM
             moe_grooming_appointment
           WHERE
             company_id = #{businessDateClients.businessClients.companyId}
             AND customer_id IN
             <foreach close=")" collection="businessDateClients.businessClients.customerIds" item="customerId" open="(" separator=",">
                #{customerId}
             </foreach>
             AND is_deprecate = 0
             AND is_block = 2
             AND book_online_status = 0
           GROUP BY customer_id
           UNION ALL
           SELECT
               customer_id,
               "totalApptAndRequestsCount" as type,
               COUNT(*) num
           FROM
               moe_grooming_appointment
           WHERE
             company_id = #{businessDateClients.businessClients.companyId}
             AND customer_id IN
             <foreach close=")" collection="businessDateClients.businessClients.customerIds" item="customerId" open="(" separator=",">
                 #{customerId}
             </foreach>
             AND is_deprecate = 0
             AND is_block = 2
           GROUP BY customer_id
           UNION ALL
           SELECT
             customer_id,
             "finishedApptCount" as type,
             COUNT(*) num
           FROM
             moe_grooming_appointment
           WHERE
              company_id = #{businessDateClients.businessClients.companyId}
              AND customer_id IN
              <foreach close=")" collection="businessDateClients.businessClients.customerIds" item="customerId" open="(" separator=",">
                #{customerId}
              </foreach>
             AND is_deprecate = 0
             AND is_block = 2
             AND book_online_status = 0
             AND status = 3
           GROUP BY customer_id
         ) t GROUP BY customer_id
  </select>

  <select id="listCustomerApptDate" resultType="com.moego.server.grooming.dto.CustomerApptDateDTO">
    SELECT
      customer_id customerId,
      MAX(CASE WHEN type = 'lastApptDate' THEN date ELSE NULL END) AS lastApptDate,
      MAX(CASE WHEN type = 'nextApptDate' THEN date ELSE NULL END) AS nextApptDate
    FROM (
           SELECT
             customer_id,
             "lastApptDate" as type,
             MAX(appointment_date) date
           FROM
             moe_grooming_appointment
           WHERE
             company_id = #{businessDateClients.businessClients.companyId}
             AND customer_id IN
             <foreach close=")" collection="businessDateClients.businessClients.customerIds" item="customerId" open="(" separator=",">
               #{customerId}
             </foreach>
             AND is_waiting_list = 0
             AND is_deprecate = 0
             AND is_block = 2
             AND book_online_status = 0
             AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

             AND (appointment_date &lt; #{businessDateClients.businessDateTime.currentDate} OR
                  (appointment_date = #{businessDateClients.businessDateTime.currentDate} AND appointment_end_time &lt;= #{businessDateClients.businessDateTime.currentMinutes}))
           GROUP BY customer_id
           UNION ALL
           SELECT
             customer_id,
             "nextApptDate" as type,
             MIN(appointment_date) date
           FROM
             moe_grooming_appointment
           WHERE
            company_id = #{businessDateClients.businessClients.companyId}
             AND customer_id IN
             <foreach close=")" collection="businessDateClients.businessClients.customerIds" item="customerId" open="(" separator=",">
               #{customerId}
             </foreach>
             AND is_waiting_list = 0
             AND is_deprecate = 0
             AND is_block = 2
             AND book_online_status = 0
             AND status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

             AND (appointment_date &gt; #{businessDateClients.businessDateTime.currentDate} OR
                  (appointment_date = #{businessDateClients.businessDateTime.currentDate} AND appointment_end_time &gt;= #{businessDateClients.businessDateTime.currentMinutes}))
            GROUP BY customer_id
          ) t GROUP BY customer_id
  </select>

  <select id="batchSelectCustomerDeleteAppt" resultType="com.moego.server.grooming.mapperbean.MoeGroomingAppointment">
    SELECT
      id, appointment_date appointmentDate
    FROM moe_grooming_appointment
    WHERE
        status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      AND service_type_include = 1
      AND business_id = #{businessId}
      AND customer_id IN
      <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
        #{customerId}
      </foreach>
  </select>

  <update id="batchDeleteApptByCustomerId">
    UPDATE
        moe_grooming_appointment
    SET
        status        = 4,
        cancel_by     = #{staffId},
        update_time   = #{nowTime},
        canceled_time = #{nowTime}
    WHERE
        status IN
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@IN_PROGRESS_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      AND service_type_include = 1
      AND business_id = #{businessId}
      AND customer_id IN
      <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
        #{customerId}
      </foreach>
  </update>

  <select id="queryApptsCreatedBetween" resultMap="BaseResultMap">
    select
      id, customer_id, create_time
    from moe_grooming_appointment
    where
      business_id = #{businessId}
    and book_online_status = 0
    and service_type_include = 1
    and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

    and is_block = 2
    and is_deprecate = 0
    and is_waiting_list = 0
    and create_time &gt;= #{startTime}
    and create_time &lt;= #{endTime}
    and customer_id in
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach>
    order by create_time desc
  </select>

  <select id="getPetHistoryAppointment" resultType="java.lang.Integer">
    WITH FilteredAppointments AS (
      SELECT distinct
        a.id AS grooming_id,
        a.appointment_date as appointment_date,
        c.pet_id
      FROM moe_grooming.moe_grooming_appointment a
      LEFT JOIN moe_grooming.moe_grooming_pet_detail c
        ON a.id = c.grooming_id AND c.status = 1
      WHERE a.business_id = #{businessId}
        AND a.service_type_include = 1
        AND a.is_deprecate = 0
        AND a.is_block = 2
        AND a.book_online_status = 0
        AND a.status IN
        <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
          #{status.value}
        </foreach>
        AND a.is_waiting_list = 0
        AND a.appointment_end_date &lt; #{startDate}
        AND a.customer_id IN
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
        AND c.pet_id IN
        <foreach close=")" collection="petIds" item="petId" open="(" separator=",">
          #{petId}
        </foreach>
    ),
    RankedAppointments AS (
      select
        fa.grooming_id,
        fa.appointment_date,
        fa.pet_id,
        ROW_NUMBER() OVER (PARTITION BY fa.pet_id ORDER BY fa.appointment_date DESC) AS rn
      from FilteredAppointments fa
    )
    select distinct
    ra.grooming_id
    from RankedAppointments ra
    WHERE ra.rn &lt;= 5
  </select>

  <select id="getAllApptByStartDateRange" resultMap="BaseResultMap">
    SELECT id,status,appointment_date,customer_id,business_id,is_paid,no_show,is_waiting_list,source,
    appointment_start_time, appointment_end_time, repeat_id,create_time,created_by_id from moe_grooming_appointment
    where is_deprecate = 0 AND is_block = 2 AND is_waiting_list = 0 and book_online_status = 0
    and appointment_date &gt;= #{startDateGte}
    and appointment_date &lt;= #{startDateLte}
    <if test="businessId != null"> and business_id = #{businessId}</if>
    <if test="companyId != null"> and company_id = #{companyId}</if>
    <if test="statusList != null and statusList.size &gt; 0">
      and status in
      <foreach close=")" collection="statusList" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
    </if>
  </select>

  <select id="listAppointmentsForPayroll" resultMap="BaseResultMap">
    SELECT distinct a.id, a.status, a.appointment_date, a.customer_id, a.business_id, a.is_paid,
    a.no_show, a.is_waiting_list, a.source, a.appointment_start_time, a.appointment_end_time,
    a.repeat_id, a.create_time, a.created_by_id
    FROM moe_grooming_appointment a
    LEFT JOIN moe_grooming_pet_detail b on a.id = b.grooming_id and b.status = 1
    WHERE a.is_deprecate = 0
    AND a.is_block = 2
    AND a.is_waiting_list = 0
    AND a.book_online_status = 0
    AND a.appointment_date &gt;= DATE_SUB(#{queryStartDate}, INTERVAL 60 DAY)
    AND a.appointment_date &lt;= #{queryEndDate}
    AND a.appointment_end_date &gt;= #{queryStartDate}
    AND a.business_id = #{businessId}
    <if test="statusList != null and statusList.size &gt; 0">
      and a.status in
      <foreach close=")" collection="statusList" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
    </if>
    and b.start_date &gt;= #{queryStartDate}
    and b.start_date &lt;= #{queryEndDate}
    and b.staff_id &gt; 0
  </select>

  <select id="getAllGroomingByDateRange" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where is_deprecate = 0
      and is_block = 2
      and is_waiting_list = 0
      and book_online_status = 0
      and business_id = #{businessId}
      and appointment_date &gt;= DATE_SUB(#{startDate}, INTERVAL 60 DAY) and appointment_date &lt;= #{endDate}
      and appointment_end_date &gt;= #{startDate} and appointment_end_date &lt;= DATE_ADD(#{endDate}, INTERVAL 60 DAY)
      <if test="serviceTypeIncludeList != null and serviceTypeIncludeList.size() &gt; 0">
        and service_type_include in
        <foreach close=")" collection="serviceTypeIncludeList" item="serviceTypeInclude" open="(" separator=",">
          #{serviceTypeInclude}
        </foreach>
      </if>
      <if test="statusList != null and statusList.size &gt; 0">
        and status in
        <foreach close=")" collection="statusList" item="status" open="(" separator=",">
          #{status.value}
        </foreach>
      </if>
  </select>

  <select id="getAllGroomingByStartDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where is_deprecate = 0
      and is_block = 2
      and is_waiting_list = 0
      and book_online_status = 0
      and business_id = #{businessId}
      and appointment_date = #{date}
      <if test="serviceTypeIncludeList != null and serviceTypeIncludeList.size() &gt; 0">
        and service_type_include in
        <foreach close=")" collection="serviceTypeIncludeList" item="serviceTypeInclude" open="(" separator=",">
          #{serviceTypeInclude}
        </foreach>
      </if>
      <if test="statusList != null and statusList.size &gt; 0">
        and status in
        <foreach close=")" collection="statusList" item="status" open="(" separator=",">
          #{status.value}
        </foreach>
      </if>
  </select>

  <select id="countOBRequestsRevenue" resultType="java.math.BigDecimal">
    SELECT
      SUM(mgpd.service_price)
    FROM
      moe_grooming_appointment mga
        INNER JOIN moe_grooming_pet_detail mgpd on
        mga.id = mgpd.grooming_id
    WHERE
      mga.business_id = #{businessId}
      and mga.service_type_include = 1
      AND mga.status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>

      AND mgpd.status = 1
      AND mga.is_block = 2
      AND mga.is_deprecate = 0
      AND mga.source = 22168
      AND mga.create_time &gt;= #{startTime}
      AND mga.create_time &lt;= #{endTime}
  </select>
  <select id="getCustomerNewAppointment" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    FROM moe_grooming_appointment
    where business_id = #{businessId}
    and customer_id = #{customerId}
    and service_type_include = 1
    order by create_time desc limit 1
  </select>
  <select id="countUpcomingApptByCustomerIds" resultType="com.moego.server.grooming.mapper.po.CountUpcomingApptByCustomerIdsPO">
    select
        customer_id as customerId, count(*) as upcomingApptCount
    from
        moe_grooming_appointment
    WHERE
        is_waiting_list = 0
      and is_deprecate = 0
      and status in
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      and service_type_include = 1
      and is_block = 2
      <if test="businessId != null">
        and business_id = #{businessId}
      </if>
      and (appointment_date &gt; #{appointmentDate} or
           (appointment_date = #{appointmentDate} and appointment_end_time
             &gt;= #{endTimes}))
      <if test="customerIds != null and customerIds.size() &gt; 0">
        and customer_id in
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
      </if>
    group by customer_id
  </select>

  <select id="getLatestAppointmentDate" resultType="com.moego.server.grooming.mapper.po.GetLatestAppointmentDatePO">
    -- 我是真不想写这么抽象的 SQL :)

    SELECT
        customer_id as customerId,
        appointment_date as appointmentDate
    FROM (SELECT
            customer_id,
            appointment_date,
            ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY appointment_date DESC) AS row_num
        FROM (SELECT
                customer_id,
                appointment_date,
                ROW_NUMBER() OVER (PARTITION BY customer_id, appointment_date ORDER BY appointment_date DESC) AS row_num
              FROM moe_grooming_appointment
              WHERE is_waiting_list = 0
                AND is_deprecate = 0
                AND service_type_include = 1
                AND status in
                <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
                    #{status.value}
                </foreach>
                AND is_block = 2
                <if test="businessId != null">
                AND business_id = #{businessId}
                </if>
                <if test="customerIds != null and customerIds.size() &gt; 0">
                AND customer_id in
                <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
                    #{customerId}
                </foreach>
                </if>
                AND (appointment_date &lt; #{date} OR (appointment_date = #{date} AND appointment_end_time &lt;= #{nowMinutes}))
              ) AS tmp
        WHERE row_num = 1
    ) AS subquery
    WHERE
        row_num &lt;= #{latestCount};
  </select>

  <select id="countPendingOBRequestsRevenue" resultType="java.math.BigDecimal">
    SELECT
      SUM(mgpd.service_price)
    FROM
      moe_grooming_appointment mga
        INNER JOIN moe_grooming_pet_detail mgpd on
        mga.id = mgpd.grooming_id
    WHERE
      mga.business_id = #{businessId}
      and mga.service_type_include = 1
      AND mga.book_online_status = 1
      AND mga.status = 1
      AND mgpd.status = 1
      AND mga.is_block = 2
      AND mga.is_deprecate = 0
      AND mga.source = 22168
  </select>

  <select id="getAppointmentsDateBetween" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM
      moe_grooming_appointment
    WHERE
      business_id = #{param.businessId}
      AND is_deprecate = 0
      AND is_block = 2
      and service_type_include = 1
      AND status IN
    <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
      #{status.value}
    </foreach>
      AND is_waiting_list = 0
      <if test="param.staffId != null">
        AND staff_id = #{param.staffId}
      </if>
      <if test="param.startDate != null">
        AND appointment_date &gt;= #{param.startDate}
      </if>
      <if test="param.endDate != null">
        AND appointment_date &lt;= #{param.endDate}
      </if>
  </select>
  <select id="findNotNewCustomerIdList" resultType="java.lang.Integer">
    SELECT
      DISTINCT customer_id
    FROM
      moe_grooming_appointment
    WHERE
      business_id = #{businessId}
      AND is_deprecate = 0
      AND is_block = 2
      AND status = 3
      AND is_waiting_list = 0
      and service_type_include = 1
      AND customer_id IN
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach>
  </select>
  <select id="countAppointmentFromSourcePlatform" resultType="com.moego.server.grooming.mapper.po.BusinessCountPO">
    select business_id businessId, count(*) count
    from moe_grooming_appointment
    where
      business_id in
          <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
            #{businessId}
          </foreach>
      and status in
          <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
            #{status.value}
          </foreach>
      and service_type_include = 1
      and source_platform = #{sourcePlatform}
      and appointment_date &gt;= '2023-09-01' -- google reserve 上线时间
    group by business_id
  </select>
  <select id="describeAppointmentReports" parameterType="com.moego.server.grooming.params.report.DescribeAppointmentReportsParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where service_type_include = 1
      <if test="ids != null and ids.size()&gt;0">
        and id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="businessIds != null and businessIds.size()&gt;0">
        and business_id in
        <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
          #{businessId}
        </foreach>
      </if>
    order by id
  </select>

  <update id="batchUpdateByPrimaryKeySelective" parameterType="java.util.List">
    update moe_grooming_appointment
    <set>
      <trim prefix="is_waiting_list = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.isWaitingList},is_waiting_list)
        </foreach>
      </trim>
      <trim prefix="wait_list_status = case id" suffix="end,">
        <foreach collection="records" item="item">
          when #{item.id} then ifnull(#{item.waitListStatus.value},wait_list_status)
        </foreach>
      </trim>
    </set>
    , update_time = unix_timestamp(now())
    <where>
      id in
      <foreach close=")" collection="records" item="item" open="(" separator=",">
        #{item.id}
      </foreach>
    </where>
  </update>
  <select id="scanAppointmentReports" parameterType="com.moego.server.grooming.params.report.ScanAppointmentReportsParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where service_type_include = 1
      <if test="startId != null">
        and id &gt; #{startId}
      </if>
    order by id
    limit #{limit}
  </select>

  <select id="listActiveAppointmentIdByCustomerId" resultType="java.lang.Integer">
    select id
    from moe_grooming_appointment
    where business_id = #{businessId}
      and customer_id = #{customerId}
      and is_deprecate = 0
      and is_block = 2
      and is_waiting_list = 0
      and book_online_status = 0
      and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
  </select>
  <select id="listActiveAppointmentIdByServiceTypeInclude" resultType="java.lang.Integer">
    select id
    from moe_grooming_appointment
    where business_id = #{businessId}
      <if test="serviceTypeIncludeList != null and serviceTypeIncludeList.size() &gt; 0">
        and service_type_include in
        <foreach close=")" collection="serviceTypeIncludeList" item="serviceTypeInclude" open="(" separator=",">
          #{serviceTypeInclude}
        </foreach>
      </if>
      and is_deprecate = 0
      and is_block = 2
      and is_waiting_list = 0
      and book_online_status = 0
      and status in
      <foreach close=")" collection="@com.moego.server.grooming.constant.AppointmentStatusSet@ACTIVE_STATUS_SET" item="status" open="(" separator=",">
        #{status.value}
      </foreach>
      and appointment_date &gt;= DATE_SUB(#{startDate}, INTERVAL 60 DAY) and appointment_date &lt;= #{endDate}
      and appointment_end_date &gt;= #{startDate} and appointment_end_date &lt;= DATE_ADD(#{endDate}, INTERVAL 60 DAY)
  </select>
  <select id="queryDeletableAppts" resultType="java.lang.Integer">
    select id
    from moe_grooming_appointment
    where company_id = #{companyId}
      and id in
      <foreach close=")" collection="apptIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      and (is_waiting_list = 1
      or status = 4
      or repeat_id != 0)
  </select>
  <select id="getAppointmentByIdsAndDateRange" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_grooming_appointment
    where id in
    <foreach close=")" collection="idList" item="id" open="(" separator=",">
      #{id}
    </foreach>
    and business_id = #{businessId}
    and appointment_date &gt;= #{startDate}
    and appointment_date &lt;= #{endDate}
  </select>

  <insert id="batchInsertBlockRepeat" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO moe_grooming_appointment (
        business_id, appointment_date, appointment_start_time, appointment_end_time, status, is_block, repeat_id,
        color_code, created_by_id, is_deprecate, create_time, update_time, source, company_id, appointment_end_date
    )
    VALUES
    <foreach collection="moeGroomingAppointments" item="item" separator=",">
        (
            #{item.businessId}, #{item.appointmentDate}, #{item.appointmentStartTime}, #{item.appointmentEndTime}, #{item.status}, #{item.isBlock}, #{item.repeatId},
            #{item.colorCode}, #{item.createdById}, #{item.isDeprecate}, #{item.createTime}, #{item.updateTime}, #{item.source}, #{item.companyId}, #{item.appointmentEndDate}
        )
    </foreach>
  </insert>
</mapper>
