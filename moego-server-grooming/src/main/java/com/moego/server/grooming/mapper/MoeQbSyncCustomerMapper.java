package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncCustomer;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncCustomerMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncCustomer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncCustomer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    MoeQbSyncCustomer selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncCustomer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_customer
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncCustomer record);

    List<MoeQbSyncCustomer> selectByBusinessIdRealmIdCustomerIds(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("customerIdList") List<Integer> customerIdList);

    int updateByBusinessIdRealmIdCustomerId(MoeQbSyncCustomer record);
}
