package com.moego.server.grooming.web;

import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.MoeGroomingExchangeRateService;
import com.moego.server.grooming.service.dto.ExchangeRateDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 4/27/21 3:55 PM
 */
@RestController
@RequestMapping("/grooming")
public class MoeGroomingExchangeRateController {

    @Autowired
    private MoeGroomingExchangeRateService exchangeRateService;

    @GetMapping("/exchange/rate")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<ExchangeRateDto> getExchangeRate() {
        return ResponseResult.success(exchangeRateService.getLatestExchangeRate());
    }
}
