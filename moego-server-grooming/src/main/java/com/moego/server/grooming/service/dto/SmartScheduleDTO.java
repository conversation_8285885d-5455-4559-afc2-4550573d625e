package com.moego.server.grooming.service.dto;

import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.ss.SmartScheduleStaffMap;
import com.moego.server.grooming.web.vo.SmartScheduleParam;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/8/4
 */
@Data
@Accessors(chain = true)
public class SmartScheduleDTO {

    private SmartScheduleStaffMap[] resultArray;

    private Map<Integer, CustomerAddressDto> customerAddressDtoMap;

    private SmartScheduleParam request;

    private int startIndex;

    private int endIndex;

    private Boolean isApplyClientPreference;

    private Integer[] preferredDay;

    private Integer[] preferredTime;

    private String timezoneName;
}
