package com.moego.server.grooming.service;

import static com.moego.common.utils.PageUtil.selectPage;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.PageDTO;
import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.dto.clients.FilterDTO;
import com.moego.common.enums.ArrivalWindowConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessCalendarEnum;
import com.moego.common.enums.BusinessCustomerConst;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.NewReminderSettingConst;
import com.moego.common.enums.RepeatConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.mybatis.Callback;
import com.moego.common.mybatis.MybatisUtils;
import com.moego.common.response.ResponseResult;
import com.moego.common.security.Des3Util;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffWorkingRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.ICustomerGroomingService;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AppointmentReminderSendDTO;
import com.moego.server.grooming.dto.AppointmentServiceInfo;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BusinessUpcomingDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.CustomerLastServiceDTO;
import com.moego.server.grooming.dto.CustomerNextLastApptMapDto;
import com.moego.server.grooming.dto.CustomerRebookReminderDTO;
import com.moego.server.grooming.dto.CustomerStatisticsDTO;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.CustomerUpcomingBusinessDTO;
import com.moego.server.grooming.dto.CustomerUpcomingPetDetailDTO;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.ExpiryCustomerCountLastApptDto;
import com.moego.server.grooming.dto.ExpiryCustomerCountListDto;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.grooming.dto.MoeGroomingAppointmentDTO;
import com.moego.server.grooming.dto.PetDetailServiceDTO;
import com.moego.server.grooming.dto.PetLastServiceDTO;
import com.moego.server.grooming.dto.RepeatAppointmentDto;
import com.moego.server.grooming.dto.RepeatNumDTO;
import com.moego.server.grooming.dto.SimpleServiceDto;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.EvaluationServiceDetailMapper;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapper.po.CountUpcomingApptByCustomerIdsPO;
import com.moego.server.grooming.mapper.po.GetLatestAppointmentDatePO;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingNote;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.AutoAssignConverter;
import com.moego.server.grooming.params.AdminQueryAppointmentParams;
import com.moego.server.grooming.params.AppointReminderParams;
import com.moego.server.grooming.params.AppointmentReminderSendParams;
import com.moego.server.grooming.params.ClientCustomerUpcomingParams;
import com.moego.server.grooming.params.DateRangeParams;
import com.moego.server.grooming.params.QueryReminderRebookParams;
import com.moego.server.grooming.params.QuerySpecificSupportReminderRebookParams;
import com.moego.server.grooming.params.QueryUpcomingApptParams;
import com.moego.server.grooming.params.RepeatReminderParams;
import com.moego.server.grooming.service.dto.ArrivalWindowTime;
import com.moego.server.grooming.service.dto.CustomerIdCountResultDto;
import com.moego.server.grooming.service.dto.StaffListWorkTimeResultDto;
import com.moego.server.grooming.utils.AddressUtil;
import com.moego.server.grooming.web.vo.UpcomingPreviewVo;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import com.moego.server.message.enums.ReminderTypeEnum;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class MoeAppointmentQueryService {

    @Value("${customer.upcoming.url}")
    private String upcomingUrl;

    @Value("${customer.upcoming.key}")
    private String upcomingKey;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    @Autowired
    private MoeGroomingPetDetailMapper moeGroomingPetDetailMapper;

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IPetClient iPetClient;

    @Autowired
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private MoeGroomingNoteService moeGroomingNoteService;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    AppointmentServiceDetailService appointmentServiceDetailService;

    @Autowired
    EvaluationServiceDetailMapper evaluationServiceDetailMapper;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private ICustomerCustomerService iCustomerCustomerClient;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private ICustomerGroomingService iCustomerGroomingClient;

    @Resource
    private IMessageClient messageClient;

    @Autowired
    private AutoAssignService autoAssignService;

    @Autowired
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    @Autowired
    private AppointmentServiceDetailService serviceDetailService;

    @Autowired
    private EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationServiceBlockingStub;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private CompanyHelper companyHelper;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    public List<GroomingBookingDTO> queryNeedSendCalendarReminderAppt(
            Integer businessId, Integer staffId, Integer beforeMins) {
        // 查询business信息
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }
        // 计算商家时区的当前时间
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime startDateTime = LocalDateTime.now(ZoneId.of(businessInfo.getTimezoneName()));
        String startDate = df.format(startDateTime);
        Integer startMins = DateUtil.getMinsByHourMins(startDateTime.getHour(), startDateTime.getMinute());

        LocalDateTime endDateTime =
                startDateTime.plusMinutes(beforeMins + NewReminderSettingConst.PUSH_CALENDAR_BUFFER_TIME);
        String endDate = df.format(endDateTime);
        Integer endMins = DateUtil.getMinsByHourMins(endDateTime.getHour(), endDateTime.getMinute());
        return moeGroomingAppointmentMapper.queryAppointmentListBetweenTwoTime(
                businessId, staffId, startDate, startMins, endDate, endMins);
    }

    public AppointmentReminderSendDTO queryAppointmentWithDetail(Integer businessId, Integer groomingId) {
        AppointmentReminderSendParams appointmentReminderSendParams = new AppointmentReminderSendParams();
        appointmentReminderSendParams.setBusinessId(businessId);
        appointmentReminderSendParams.setGroomingId(groomingId);
        List<AppointmentReminderSendDTO> sendDTOList =
                queryAppointmentReminderWithDetailWill(appointmentReminderSendParams, true);
        if (CollectionUtils.isEmpty(sendDTOList)) {
            return null;
        } else {
            return sendDTOList.get(0);
        }
    }

    public List<AppointmentReminderSendDTO> queryAppointmentReminderWithDetailWill(
            AppointmentReminderSendParams appointmentReminderSendParams, Boolean isQueryOne) {
        Integer businessId = appointmentReminderSendParams.getBusinessId();
        List<Integer> dismissIds = appointmentReminderSendParams.getDismissIds();

        Integer status = appointmentReminderSendParams.getStatus();
        if (Objects.isNull(businessId)) {
            return new ArrayList<>();
        }
        // 获取未来需要提醒的全部预约
        List<AppointmentReminderSendDTO> appointments = null;
        if (isQueryOne != null && isQueryOne && appointmentReminderSendParams.getGroomingId() != null) {
            AppointmentReminderSendDTO appointment = moeGroomingAppointmentMapper.queryAppointmentReminderSendDto(
                    businessId, appointmentReminderSendParams.getGroomingId());
            if (appointment != null) {
                appointments = new ArrayList<>();
                appointments.add(appointment);
            }
        } else {
            appointments = moeGroomingAppointmentMapper.queryAppointmentReminderWill(
                    businessId, dismissIds, appointmentReminderSendParams.getAppointmentDate(), status);
        }
        if (CollectionUtils.isEmpty(appointments)) {
            return new ArrayList<>();
        }
        // 查询详细信息：如 pet，service，staffname等
        List<Integer> groomingIds = new ArrayList<>();
        appointments.forEach(e -> {
            groomingIds.add(e.getId());
        });
        var allPetDetailsPair = appointmentServiceDetailService.getAppointmentServiceDetails(groomingIds);
        List<GroomingPetDetailDTO> groomingPetDetailDTOS = allPetDetailsPair.key();
        List<EvaluationServiceDetail> evaluations = allPetDetailsPair.value();
        List<Integer> staffIds = new ArrayList<>();
        groomingPetDetailDTOS.forEach(p -> {
            staffIds.add(p.getStaffId());
        });
        // 查询staff信息，获取staff name
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        staffIdListParams.setStaffIdList(staffIds);
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);
        // 将staff id和 staff info 映射
        Map<Integer, MoeStaffDto> staffMap = new HashMap<>();
        staffList.forEach(s -> {
            staffMap.put(s.getId(), s);
        });
        // 查询pet信息获取petname
        Map<Integer, CustomerPetDetailDTO> petIdMap =
                appointmentServiceDetailService.getPetMap(groomingPetDetailDTOS, evaluations);

        // 解析pet列表，获取每个预约对应的宠物信息
        Map<Integer, List<GroomingPetDetailDTO>> groomingPetDetailMap = new HashMap<>();
        for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOS) {
            Integer groomId = groomingPetDetailDTO.getGroomingId();
            Integer staffId = groomingPetDetailDTO.getStaffId();
            if (staffMap.get(staffId) != null) {
                groomingPetDetailDTO.setStaffFirstName(staffMap.get(staffId).getFirstName());
                groomingPetDetailDTO.setStaffLastName(staffMap.get(staffId).getLastName());
            } else {
                groomingPetDetailDTO.setStaffFirstName("");
                groomingPetDetailDTO.setStaffLastName("");
            }
            groomingPetDetailDTO.setPetName(
                    petIdMap.get(groomingPetDetailDTO.getPetId()).getPetName());
            groomingPetDetailDTO.setPetLifeStatus(
                    petIdMap.get(groomingPetDetailDTO.getPetId()).getLifeStatus());

            List<GroomingPetDetailDTO> groomingPetDetail = groomingPetDetailMap.get(groomId);
            if (CollectionUtils.isEmpty(groomingPetDetail)) {
                groomingPetDetail = new ArrayList<>();
            }
            groomingPetDetail.add(groomingPetDetailDTO);
            groomingPetDetailMap.put(groomId, groomingPetDetail);
        }

        Map<Integer, List<EvaluationServiceDetailDTO>> evaluationPetDetailMap =
                appointmentServiceDetailService.toEvaluationServiceDetailDTOList(evaluations).stream()
                        .collect(groupingBy(k -> k.getGroomingId().intValue(), Collectors.toList()));

        for (AppointmentReminderSendDTO appointmentReminderSendDTO : appointments) {
            appointmentReminderSendDTO.setGroomingPetDetails(
                    groomingPetDetailMap.getOrDefault(appointmentReminderSendDTO.getId(), Collections.emptyList()));
            appointmentReminderSendDTO.setEvaluationServiceDetails(
                    evaluationPetDetailMap.getOrDefault(appointmentReminderSendDTO.getId(), Collections.emptyList()));
        }

        return appointments;
    }

    public ExpiryCustomerCountListDto queryExpiryCustomerList(
            Integer businessId, Integer upcomingNums, Integer pageNum, Integer pageSize, List<Integer> dismissIds) {
        Integer startNum = 0;
        if (pageSize <= 0) {
            pageSize = 10;
        }
        if (pageNum > 1) {
            startNum = (pageNum - 1) * pageSize;
        }
        // 查询 business 当前时间
        BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(businessId);
        String nowDate = businessDateTime.getCurrentDate();
        Integer nowMinutes = businessDateTime.getCurrentMinutes();
        // 查询符合条件customer
        List<CustomerIdCountResultDto> customerIdCountResultDtos =
                moeGroomingAppointmentMapper.queryExpiryCustomerUpcomingCount(
                        businessId, nowDate, nowMinutes, upcomingNums, dismissIds);
        ExpiryCustomerCountListDto customerCountListDto = new ExpiryCustomerCountListDto();
        customerCountListDto.setCount(0);
        customerCountListDto.setCustomerList(Collections.emptyList());
        if (CollectionUtils.isEmpty(customerIdCountResultDtos)) {
            return customerCountListDto;
        }
        Map<Integer, CustomerIdCountResultDto> countResultDtoMap = new HashMap<>();
        for (CustomerIdCountResultDto resultDto : customerIdCountResultDtos) {
            countResultDtoMap.put(resultDto.getCustomerId(), resultDto);
        }
        // 查询符合条件customer的last appt，并排序
        List<CustomerGroomingAppointmentDTO> customerGroomingAppointmentDTOS =
                moeGroomingAppointmentMapper.queryExpiryCustomerLastApptListWithOrderLimit(
                        businessId, nowDate, nowMinutes, countResultDtoMap.keySet(), pageSize, startNum);
        if (CollectionUtils.isEmpty(customerGroomingAppointmentDTOS)) {
            return customerCountListDto;
        }
        // 组装返回结果
        List<ExpiryCustomerCountLastApptDto> countLastApptDtos = new ArrayList<>();
        for (CustomerGroomingAppointmentDTO customerApptDto : customerGroomingAppointmentDTOS) {
            ExpiryCustomerCountLastApptDto countLastApptDto = new ExpiryCustomerCountLastApptDto();
            BeanUtils.copyProperties(customerApptDto, countLastApptDto);
            countLastApptDto.setGroomingId(customerApptDto.getId());
            countLastApptDto.setUpcomingCount(
                    countResultDtoMap.get(customerApptDto.getCustomerId()).getCount());
            countLastApptDtos.add(countLastApptDto);
        }
        customerCountListDto.setCustomerList(countLastApptDtos);
        customerCountListDto.setCount(moeGroomingAppointmentMapper.queryExpiryCustomerLastApptListCount(
                businessId, nowDate, nowMinutes, countResultDtoMap.keySet()));
        return customerCountListDto;
    }

    public Integer queryExpiryCustomerListCount(Integer businessId, Integer upcomingNums, List<Integer> dismissIds) {
        // 查询business信息
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }
        // 计算商家时区的当前时间
        LocalDateTime now = LocalDateTime.now();
        String nowDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        // 查询符合条件customer
        List<CustomerIdCountResultDto> customerIdCountResultDtos =
                moeGroomingAppointmentMapper.queryExpiryCustomerUpcomingCount(
                        businessId, nowDate, nowMinutes, upcomingNums, dismissIds);
        if (CollectionUtils.isEmpty(customerIdCountResultDtos)) {
            return 0;
        }
        List<Integer> customerList = new ArrayList<>();
        for (CustomerIdCountResultDto resultDto : customerIdCountResultDtos) {
            customerList.add(resultDto.getCustomerId());
        }
        return moeGroomingAppointmentMapper.queryExpiryCustomerLastApptListCount(
                businessId, nowDate, nowMinutes, new HashSet<>(customerList));
    }

    public List<Integer> queryUpcomingApptIdList(QueryUpcomingApptParams params) {
        BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(params.getBusinessId());
        return moeGroomingAppointmentMapper.queryUpcomingApptIdList(
                params.getBusinessId(),
                businessDateTime.getCurrentDate(),
                businessDateTime.getCurrentMinutes(),
                params.getCustomerId(),
                params.getStatusList());
    }

    public List<Integer> queryCustomerIdUpcomingApptIdList(Integer businessId, Integer customerId) {
        // 查询business信息
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }
        // 计算商家时区的当前时间
        LocalDateTime now = LocalDateTime.now();
        String nowDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        return moeGroomingAppointmentMapper.queryCustomerUpcomingApptIdList(
                businessId, nowDate, nowMinutes, customerId);
    }

    /**
     * @param businessId
     * @param customerIds
     * @return
     */
    public Map<Integer, CustomerGroomingAppointmentDTO> getAllCustomerLastAppointment(
            boolean isMigrated, Long companyId, Integer businessId, List<Integer> customerIds) {

        String timezoneName;
        if (isMigrated) {
            timezoneName = companyServiceBlockingStub
                    .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPreferenceSetting()
                    .getTimeZone()
                    .getName();
        } else {
            timezoneName = iBusinessBusinessClient
                    .getBusinessInfo(new InfoIdParams(businessId))
                    .getTimezoneName();
        }
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);

        List<CustomerGroomingAppointmentDTO> lastApptList = moeGroomingAppointmentMapper.selectLapsedCustomerLastAppt(
                customerIds, companyId, isMigrated ? null : businessId, date, nowMinutes);
        Map<Integer, CustomerGroomingAppointmentDTO> lastApptMap = new HashMap<>();
        for (CustomerGroomingAppointmentDTO lastAppt : lastApptList) {
            lastApptMap.put(lastAppt.getCustomerId(), lastAppt);
        }
        return lastApptMap;
    }

    public CustomerLastFinishedApptMapDto getCustomerLastFinishedAppointment(
            boolean isMigrated, Long companyId, Integer businessId, List<Integer> ids) {
        CustomerLastFinishedApptMapDto lastFinishedApptMapDto = new CustomerLastFinishedApptMapDto();
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        String timezoneName;
        if (isMigrated) {
            timezoneName = companyServiceBlockingStub
                    .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPreferenceSetting()
                    .getTimeZone()
                    .getName();
        } else {
            MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
            if (businessInfo == null) {
                throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "business not exists");
            }
            timezoneName = businessInfo.getTimezoneName();
        }
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        List<CustomerGroomingAppointmentDTO> lastFinishedApptList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(ids)) {
            lastFinishedApptList =
                    moeGroomingAppointmentMapper.selectCustomerLastFinishedAppt(ids, companyId, date, nowMinutes);
        }
        Map<Integer, CustomerGroomingAppointmentDTO> lastFinishedApptMap = new HashMap<>();
        for (CustomerGroomingAppointmentDTO lastFinishedAppt : lastFinishedApptList) {
            lastFinishedApptMap.put(lastFinishedAppt.getCustomerId(), lastFinishedAppt);
        }
        lastFinishedApptMapDto.setLastFinishedApptMap(lastFinishedApptMap);
        return lastFinishedApptMapDto;
    }

    public Integer getCustomerLastAppointmentInterval(Integer businessId, Integer customerId) {
        Integer interval = null;
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        List<CustomerGroomingAppointmentDTO> lastUncanceledApptList =
                moeGroomingAppointmentMapper.selectCustomerLastTwoUncanceledApptDate(
                        customerId, businessId, date, nowMinutes);

        if (CollectionUtils.isEmpty(lastUncanceledApptList) || lastUncanceledApptList.size() != 2) {
            return interval;
        }
        CustomerGroomingAppointmentDTO appointmentDTO1 = lastUncanceledApptList.get(0);
        CustomerGroomingAppointmentDTO appointmentDTO2 = lastUncanceledApptList.get(1);
        try {
            Date date1 = DateUtil.convertStringToDate(appointmentDTO1.getAppointmentDate());
            Date date2 = DateUtil.convertStringToDate(appointmentDTO2.getAppointmentDate());
            interval = DateUtil.daysBetween(date1, date2);
            interval = Math.abs(interval);
        } catch (ParseException e) {
            log.error("appointment date parse error, apptDate1:" + appointmentDTO1.getAppointmentDate()
                    + " apptDate2:"
                    + appointmentDTO2.getAppointmentDate());
        }
        return interval;
    }

    public Set<Integer> queryStaffIdListByDateRange(Integer businessId, DateRangeParams dateRangeParams) {
        List<Integer> staffIdList = moeGroomingPetDetailMapper.queryStaffIdListByDateRange(
                businessId, dateRangeParams.getStartDate(), dateRangeParams.getEndDate());
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Collections.emptySet();
        } else {
            return new HashSet<>(staffIdList);
        }
    }

    public List<AppointmentReminderSendDTO> queryAppointmentReminderWill(
            AppointmentReminderSendParams appointmentReminderSendParams) {
        Integer businessId = appointmentReminderSendParams.getBusinessId();
        List<Integer> dismissIds = appointmentReminderSendParams.getDismissIds();

        if (Objects.isNull(businessId)) {
            return new ArrayList<>();
        }
        List<AppointmentReminderSendDTO> appointments = moeGroomingAppointmentMapper.queryAppointmentReminderWill(
                businessId,
                dismissIds,
                appointmentReminderSendParams.getAppointmentDate(),
                appointmentReminderSendParams.getStatus());
        if (CollectionUtils.isEmpty(appointments)) {
            return new ArrayList<>();
        }
        List<Integer> groomingIds = new ArrayList<>();
        appointments.forEach(e -> groomingIds.add(e.getId()));
        List<GroomingPetDetailDTO> groomingPetDetailDTOS =
                moeGroomingPetDetailMapper.queryPetDetailByGroomingIds(groomingIds);

        Map<Integer, GroomingPetDetailDTO> groomingFirstPetDetail = new HashMap<>();

        List<Integer> petIds = new ArrayList<>();
        for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOS) {
            petIds.add(groomingPetDetailDTO.getPetId());
            GroomingPetDetailDTO groomingPetDetail = groomingFirstPetDetail.get(groomingPetDetailDTO.getGroomingId());
            if (groomingPetDetail == null) {
                groomingFirstPetDetail.put(groomingPetDetailDTO.getGroomingId(), groomingPetDetailDTO);
                continue;
            }

            if (groomingFirstPetDetail.get(groomingPetDetailDTO.getGroomingId()).getId()
                    > groomingPetDetailDTO.getId()) {
                groomingFirstPetDetail.put(groomingPetDetailDTO.getGroomingId(), groomingPetDetailDTO);
            }
        }

        List<Integer> staffIds = new ArrayList<>();

        for (AppointmentReminderSendDTO appointmentReminderSendDTO : appointments) {
            GroomingPetDetailDTO groomingPetDetailDTO = groomingFirstPetDetail.get(appointmentReminderSendDTO.getId());

            if (groomingPetDetailDTO == null) {
                continue;
            }
            appointmentReminderSendDTO.setPetId(groomingPetDetailDTO.getPetId());
            appointmentReminderSendDTO.setServiceId(groomingPetDetailDTO.getServiceId());
            appointmentReminderSendDTO.setServiceName(groomingPetDetailDTO.getServiceName());
            appointmentReminderSendDTO.setStaffId(groomingPetDetailDTO.getStaffId());

            staffIds.add(groomingPetDetailDTO.getStaffId());
        }

        // 多个petname去重组装
        List<CustomerPetDetailDTO> customerPetListByIdList = iPetClient.getCustomerPetListByIdList(petIds);

        Map<Integer, Set<Integer>> groomingPetIds = new HashMap<>();
        for (AppointmentReminderSendDTO appointmentReminderSendDTO : appointments) {
            Set<Integer> pets = new HashSet<>();
            for (GroomingPetDetailDTO groomingPetDetailDTO : groomingPetDetailDTOS) {
                // 获取预约pet 去重
                if (groomingPetDetailDTO.getGroomingId().equals(appointmentReminderSendDTO.getId())) {
                    pets.add(groomingPetDetailDTO.getPetId());
                }
            }
            groomingPetIds.put(appointmentReminderSendDTO.getId(), pets);
        }

        for (AppointmentReminderSendDTO appointmentReminderSendDTO : appointments) {
            StringJoiner groomingPetName = new StringJoiner(",");
            for (Integer petId : groomingPetIds.get(appointmentReminderSendDTO.getId())) {
                // 多个petname组装在一起
                for (CustomerPetDetailDTO customerPetDetailDTO : customerPetListByIdList) {
                    if (customerPetDetailDTO.getPetId().equals(petId)) {
                        groomingPetName.add(customerPetDetailDTO.getPetName());
                        break;
                    }
                }
            }
            appointmentReminderSendDTO.setPetName(groomingPetName.toString());
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        staffIdListParams.setStaffIdList(staffIds);
        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);

        for (AppointmentReminderSendDTO appointmentReminderSendDTO : appointments) {
            for (MoeStaffDto moeStaffDto : staffList) {
                if (moeStaffDto.getId().equals(appointmentReminderSendDTO.getStaffId())) {
                    appointmentReminderSendDTO.setStaffFirstName(moeStaffDto.getFirstName());
                    appointmentReminderSendDTO.setStaffLastName(moeStaffDto.getLastName());
                }
            }
        }

        return appointments;
    }

    public PageDTO<MoeGroomingAppointmentDTO> queryAppointmentReminder(AppointReminderParams appointReminderParams) {
        Integer businessId = appointReminderParams.getBusinessId();
        List<Integer> dismissIds = appointReminderParams.getDismissIds();
        String startDay = appointReminderParams.getReminderAppointStartDay();
        String endDay = StringUtils.isEmpty(appointReminderParams.getReminderAppointEndDay())
                ? "2100-12-31"
                : appointReminderParams.getReminderAppointEndDay();
        Integer status = appointReminderParams.getStatus();
        if (Objects.isNull(businessId)) {
            return null;
        }
        if (Objects.isNull(startDay)) {
            return null;
        }

        List<MoeGroomingAppointment> appointments = moeGroomingAppointmentMapper.selectBusinessAppointmentReminder(
                businessId,
                dismissIds,
                startDay,
                endDay,
                appointReminderParams.getReminderAppointStartMinute(),
                status);
        if (CollectionUtils.isEmpty(appointments)) {
            return PageDTO.createEmpty();
        }
        // 过滤包含指定类型的预约
        if (!CollectionUtils.isEmpty(appointReminderParams.getServiceItemTypes())) {
            appointments = filterSpecificAppointments(appointments, appointReminderParams.getServiceItemTypes());
        }

        List<MoeGroomingAppointmentDTO> appointmentDtos = new ArrayList<>(appointments.size());
        for (MoeGroomingAppointment appointment : appointments) {
            MoeGroomingAppointmentDTO dto = new MoeGroomingAppointmentDTO();
            BeanUtils.copyProperties(appointment, dto);
            if (AppointmentStatusEnum.CHECK_IN.getValue().equals(appointment.getStatus())
                    || AppointmentStatusEnum.READY.getValue().equals(appointment.getStatus())) {
                // check-in 和 ready 状态的预约，对于 reminder 来说统一展示 confirmed
                dto.setStatus(AppointmentStatusEnum.CONFIRMED.getValue());
            }
            appointmentDtos.add(dto);
        }
        return PageDTO.create(
                appointmentDtos,
                appointments.size(),
                appointReminderParams.getPageNo(),
                appointReminderParams.getPageSize());
    }

    private List<MoeGroomingAppointment> filterSpecificAppointments(
            List<MoeGroomingAppointment> appointments, List<Integer> filterServiceItemTypes) {
        var appointmentIds =
                appointments.stream().map(MoeGroomingAppointment::getId).toList();
        var allPetDetails = moeGroomingPetDetailMapper.selectByAppointmentIdList(appointmentIds);
        var serviceItemTypes = new HashSet<>(filterServiceItemTypes);
        var appointmentIdToPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        var appointmentIdToAppointment =
                appointments.stream().collect(Collectors.toMap(MoeGroomingAppointment::getId, Function.identity()));

        var serviceIds = allPetDetails.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .distinct()
                .map(Integer::longValue)
                .toList();

        var serviceIdToService = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(serviceIds)
                        .build())
                .getServicesList()
                .stream()
                .collect(toMap(ServiceBriefView::getId, Function.identity()));

        var filteredAppointmentIds = appointmentIdToPetDetails.entrySet().stream()
                .filter(entry -> {
                    var petDetails = entry.getValue();
                    var appointment = appointmentIdToAppointment.get(entry.getKey());
                    // Only grooming add-on 允许 reminder
                    var onlyHasGroomingAddon = petDetails.stream()
                            .allMatch(p -> isAllowedGroomingAddOn(serviceIdToService, p, serviceItemTypes));
                    if (onlyHasGroomingAddon) {
                        return true;
                    }
                    // 其他情况需要要求 service 类型包含在配置里才允许 reminder, 并且只有预约第一天的 service，会计入统计
                    return petDetails.stream()
                            .anyMatch(p -> isInFirstDayOfAppointment(appointment, p)
                                    && isAllowedService(serviceIdToService, p, serviceItemTypes));
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        return appointments.stream()
                .filter(appointment -> filteredAppointmentIds.contains(appointment.getId()))
                .toList();
    }

    private static boolean isInFirstDayOfAppointment(
            MoeGroomingAppointment appointment, MoeGroomingPetDetail petDetail) {
        var appointmentDate = appointment.getAppointmentDate();
        var petDetailDate = petDetail.getStartDate();
        return appointmentDate.equals(petDetailDate);
    }

    private static boolean isAllowedGroomingAddOn(
            Map<Long, ServiceBriefView> serviceIdToService, MoeGroomingPetDetail p, Set<Integer> serviceItemTypes) {
        var service = serviceIdToService.get(p.getServiceId().longValue());
        if (service == null) {
            return false;
        }
        return Objects.equals(service.getType().getNumber(), ServiceType.ADDON_VALUE)
                && Objects.equals(p.getServiceItemType(), ServiceItemType.GROOMING_VALUE)
                && serviceItemTypes.contains(p.getServiceItemType());
    }

    private static boolean isAllowedService(
            Map<Long, ServiceBriefView> serviceIdToService, MoeGroomingPetDetail p, Set<Integer> serviceItemTypes) {
        var service = serviceIdToService.get(p.getServiceId().longValue());
        if (service == null) {
            return false;
        }
        return Objects.equals(service.getType().getNumber(), ServiceType.SERVICE_VALUE)
                && serviceItemTypes.contains(p.getServiceItemType());
    }

    public Map<String, CustomerGroomingAppointmentDTO> getCustomerLastAndNextAppointment(
            Integer customerId, Integer businessId, Integer ignoreGroomingId, boolean excludeBookingRequest) {
        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String s = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        Future<CustomerGroomingAppointmentDTO> lastFuture = ThreadPool.submit(() -> {
            CustomerGroomingAppointmentDTO last =
                    moeGroomingAppointmentMapper.queryCustomerLastOrNextAppointmentAllServiceType(
                            businessInfo.getCompanyId().longValue(),
                            customerId,
                            s,
                            nowMinutes,
                            ignoreGroomingId,
                            true,
                            excludeBookingRequest);
            if (last != null) {
                getGroomingAppointmentDTO(businessId, last);
            }
            return last;
        });
        Future<CustomerGroomingAppointmentDTO> nextFuture = ThreadPool.submit(() -> {
            CustomerGroomingAppointmentDTO next =
                    moeGroomingAppointmentMapper.queryCustomerLastOrNextAppointmentAllServiceType(
                            businessInfo.getCompanyId().longValue(),
                            customerId,
                            s,
                            nowMinutes,
                            ignoreGroomingId,
                            false,
                            excludeBookingRequest);
            if (next != null) {
                getGroomingAppointmentDTO(businessId, next);
            }
            return next;
        });

        Map<String, CustomerGroomingAppointmentDTO> res = new HashMap<>();
        try {
            var next = nextFuture.get();
            var last = lastFuture.get();
            if (next != null) {
                res.put("next", next);
            }
            if (last != null) {
                res.put("last", last);
            }
        } catch (ExecutionException | InterruptedException e) {
            log.error("moego thread execute task failed for customer lastAndNextAppoint", e);
        }
        return res;
    }

    private void getGroomingAppointmentDTO(Integer businessId, CustomerGroomingAppointmentDTO next) {
        List<CustomerGroomingAppointmentPetDetailDTO> customerGroomingAppointmentPetDetailLast =
                moeGroomingPetDetailMapper.queryCustomerAppointmentPetDetail(next.getId());
        companyGroomingServiceQueryService.obServiceQueryForPetDetailDto(
                businessId, customerGroomingAppointmentPetDetailLast);
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingId(businessId, next.getId());
        customerGroomingAppointmentPetDetailLast.forEach(
                petDetail -> petDetail.setOperationList(operationMap.get(petDetail.getPetDetailId())));
        next.setPetDetails(customerGroomingAppointmentPetDetailLast);
    }

    public Map<Integer, CustomerGroomingAppointmentDTO> getCustomerNextAppointment(
            Integer businessId, List<Integer> ids) {
        Map<Integer, CustomerGroomingAppointmentDTO> res = new HashMap<>();

        if (CollectionUtils.isEmpty(ids)) {
            return res;
        }

        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        // 批量处理
        List<CustomerGroomingAppointmentDTO> nextApptList = moeGroomingAppointmentMapper.selectCustomerNextAppt(
                ids, businessInfo.getCompanyId().longValue(), date, nowMinutes);
        for (CustomerGroomingAppointmentDTO nextAppt : nextApptList) {
            res.put(nextAppt.getCustomerId(), nextAppt);
        }
        return res;
    }

    public CustomerGroomingAppointmentDTO getCustomerPetNextAppointment(
            Long companyId, Integer businessId, Integer customerId, Integer petId) {
        BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(businessId);
        String currentDate = businessDateTime.getCurrentDate();
        Integer currentMinutes = businessDateTime.getCurrentMinutes();

        return getCustomerPetNextAppointment(companyId, customerId, petId, null, currentDate, currentMinutes);
    }

    public CustomerGroomingAppointmentDTO getCustomerPetNextAppointment(
            Long companyId,
            Integer customerId,
            Integer petId,
            Integer ignoredGroomingId,
            String fromDate,
            Integer fromMinutes) {
        return moeGroomingAppointmentMapper.selectCustomerPetNextAppt(
                companyId, customerId, petId, ignoredGroomingId, fromDate, fromMinutes);
    }

    public CustomerNextLastApptMapDto getCustomerNextLastAppointment(
            boolean isMigrated, Long companyId, Integer businessId, List<Integer> ids) {
        CustomerNextLastApptMapDto apptMapDto = new CustomerNextLastApptMapDto();
        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);
        String timezoneName;
        if (isMigrated) {
            timezoneName = companyServiceBlockingStub
                    .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPreferenceSetting()
                    .getTimeZone()
                    .getName();
        } else {
            MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
            if (businessInfo == null) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
            }
            timezoneName = businessInfo.getTimezoneName();
        }
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        List<CustomerGroomingAppointmentDTO> lastApptList =
                moeGroomingAppointmentMapper.selectCustomerLastAppt(ids, companyId, date, nowMinutes);
        Map<Integer, CustomerGroomingAppointmentDTO> lastApptMap = new HashMap<>();
        for (CustomerGroomingAppointmentDTO lastAppt : lastApptList) {
            lastApptMap.put(lastAppt.getCustomerId(), lastAppt);
        }
        apptMapDto.setLastApptMap(lastApptMap);
        List<CustomerGroomingAppointmentDTO> nextApptList =
                moeGroomingAppointmentMapper.selectCustomerNextAppt(ids, companyId, date, nowMinutes);
        Map<Integer, CustomerGroomingAppointmentDTO> nextApptMap = new HashMap<>();
        for (CustomerGroomingAppointmentDTO nextAppt : nextApptList) {
            nextApptMap.put(nextAppt.getCustomerId(), nextAppt);
        }
        apptMapDto.setNextApptMap(nextApptMap);
        return apptMapDto;
    }

    public List<CustomerAppointmentNumInfoDTO> getCustomerAppointmentNum(
            boolean isMigrated, Long companyId, Integer businessId, List<Integer> customerIds) {
        if (customerIds == null || customerIds.isEmpty()) {
            return new ArrayList<>();
        }
        String timezoneName;
        if (isMigrated) {
            timezoneName = companyServiceBlockingStub
                    .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPreferenceSetting()
                    .getTimeZone()
                    .getName();
        } else {
            // 获取businessInfo
            InfoIdParams businessIdParams = new InfoIdParams();
            businessIdParams.setInfoId(businessId);
            MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
            if (businessInfo == null) {
                throw new BizException(Code.CODE_PARAMS_ERROR_VALUE, "business not exists");
            }
            timezoneName = businessInfo.getTimezoneName();
        }

        /*
        获取appointmentNum
         */
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        // 批量获取appointment数量
        List<CustomerStatisticsDTO> customerAppointmentNumBatch =
                moeGroomingAppointmentMapper.getCustomerAppointmentNumBatch(customerIds, date, nowMinutes, companyId);
        // 处理customerAppointmentNumBatch 转成map ，方便获取对应的值
        Map<Integer, Map<String, Integer>> customerAppointmentNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(customerAppointmentNumBatch)) {
            customerAppointmentNumBatch.forEach(s -> {
                Integer customerId = s.getCustomerId();
                Map<String, Integer> statisticMap;
                if (customerAppointmentNumMap.containsKey(customerId)) {
                    statisticMap = customerAppointmentNumMap.get(customerId);
                } else {
                    statisticMap = new HashMap<>();
                }
                statisticMap.put(s.getType(), s.getNum());
                customerAppointmentNumMap.put(customerId, statisticMap);
            });
        }
        List<CustomerAppointmentNumInfoDTO> customerAppointmentNumInfoDTOS = new LinkedList<>();
        for (Integer customerId : customerIds) {
            CustomerAppointmentNumInfoDTO customerAppointmentNumInfo = new CustomerAppointmentNumInfoDTO();
            customerAppointmentNumInfo.setCustomerId(customerId);
            // 初始赋值
            customerAppointmentNumInfo.setCancelled(0);
            customerAppointmentNumInfo.setFinished(0);
            customerAppointmentNumInfo.setNoShow(0);
            customerAppointmentNumInfo.setTotalAppts(0);
            customerAppointmentNumInfo.setUpcoming(0);

            if (!customerAppointmentNumMap.isEmpty() && customerAppointmentNumMap.containsKey(customerId)) {
                Map<String, Integer> statisticMap = customerAppointmentNumMap.get(customerId);
                if (statisticMap.containsKey("totalApptAndRequestsCount")) {
                    customerAppointmentNumInfo.setTotalApptAndRequestsCount(
                            statisticMap.get("totalApptAndRequestsCount"));
                }
                if (statisticMap.containsKey("totalAppts")) {
                    customerAppointmentNumInfo.setTotalAppts(statisticMap.get("totalAppts"));
                }
                if (statisticMap.containsKey("finished")) {
                    customerAppointmentNumInfo.setFinished(statisticMap.get("finished"));
                }
                if (statisticMap.containsKey("cancelled")) {
                    customerAppointmentNumInfo.setCancelled(statisticMap.get("cancelled"));
                }
                if (statisticMap.containsKey("noShow")) {
                    customerAppointmentNumInfo.setNoShow(statisticMap.get("noShow"));
                }
                if (statisticMap.containsKey("upcoming")) {
                    customerAppointmentNumInfo.setUpcoming(statisticMap.get("upcoming"));
                }
            }

            customerAppointmentNumInfoDTOS.add(customerAppointmentNumInfo);
        }

        return customerAppointmentNumInfoDTOS;
    }

    public Map<Integer, List<CustomerAppointmentNumInfoDTO>> getCustomerAppointmentNumGroupByCustomerId(
            boolean isMraited, Long companyId, Integer businessId, List<Integer> customerIds) {
        Map<Integer, List<CustomerAppointmentNumInfoDTO>> res = new HashMap<>();
        List<CustomerAppointmentNumInfoDTO> customerAppointmentNum =
                getCustomerAppointmentNum(isMraited, companyId, businessId, customerIds);
        for (CustomerAppointmentNumInfoDTO customerAppointmentNumInfoDTO : customerAppointmentNum) {
            if (res.get(customerAppointmentNumInfoDTO.getCustomerId()) == null) {
                List<CustomerAppointmentNumInfoDTO> customerAppointmentNumInfoDTOS = new ArrayList<>();
                customerAppointmentNumInfoDTOS.add(customerAppointmentNumInfoDTO);
                res.put(customerAppointmentNumInfoDTO.getCustomerId(), customerAppointmentNumInfoDTOS);
            } else {
                res.get(customerAppointmentNumInfoDTO.getCustomerId()).add(customerAppointmentNumInfoDTO);
            }
        }

        return res;
    }

    public Set<Integer> listCustomerIdByDateFilter(ClientsFilterDTO clientsFilterDTO) {
        if (Objects.isNull(clientsFilterDTO.companyId())
                || CollectionUtils.isEmpty(clientsFilterDTO.filters())
                || Objects.isNull(clientsFilterDTO.type())) {
            return Collections.emptySet();
        }
        return moeGroomingAppointmentMapper.listCustomerIdByDateFilter(clientsFilterDTO);
    }

    public Set<Integer> listCustomerIdByGroomerFilter(ClientsFilterDTO clientsFilterDTO) {
        if (Objects.isNull(clientsFilterDTO.companyId())
                || CollectionUtils.isEmpty(clientsFilterDTO.filters())
                || Objects.isNull(clientsFilterDTO.type())) {
            return Collections.emptySet();
        }
        return moeGroomingAppointmentMapper.listCustomerIdByGroomerFilter(clientsFilterDTO);
    }

    public Map<Integer, CustomerApptCountDTO> listCustomerApptCount(BusinessDateClientsDTO businessDateClientsDTO) {
        if (Objects.isNull(businessDateClientsDTO)
                || Objects.isNull(businessDateClientsDTO.businessClients())
                || Objects.isNull(businessDateClientsDTO.businessDateTime())
                || Objects.isNull(businessDateClientsDTO.businessClients().companyId())
                || CollectionUtils.isEmpty(
                        businessDateClientsDTO.businessClients().customerIds())) {
            return Collections.emptyMap();
        }
        List<CustomerApptCountDTO> apptCountDTOList =
                moeGroomingAppointmentMapper.listCustomerApptCount(businessDateClientsDTO);
        if (CollectionUtils.isEmpty(apptCountDTOList)) {
            return Collections.emptyMap();
        }
        return apptCountDTOList.stream()
                .collect(Collectors.toMap(CustomerApptCountDTO::getCustomerId, Function.identity()));
    }

    public Map<Integer, CustomerApptDateDTO> listCustomerApptDate(BusinessDateClientsDTO businessDateClientsDTO) {
        if (Objects.isNull(businessDateClientsDTO)
                || Objects.isNull(businessDateClientsDTO.businessClients())
                || Objects.isNull(businessDateClientsDTO.businessDateTime())
                || Objects.isNull(businessDateClientsDTO.businessClients().companyId())
                || CollectionUtils.isEmpty(
                        businessDateClientsDTO.businessClients().customerIds())) {
            return Collections.emptyMap();
        }
        List<CustomerApptDateDTO> apptDateDTOList =
                moeGroomingAppointmentMapper.listCustomerApptDate(businessDateClientsDTO);
        if (CollectionUtils.isEmpty(apptDateDTOList)) {
            return Collections.emptyMap();
        }
        return apptDateDTOList.stream()
                .collect(Collectors.toMap(CustomerApptDateDTO::getCustomerId, Function.identity()));
    }

    public List<Integer> queryCustomerIdsInWaiting(List<Integer> customerIds) {
        return moeGroomingAppointmentMapper.queryCustomerIdsInWaiting(customerIds);
    }

    public List<Integer> queryFutureAppointmentCustomerIdList(Integer businessId) {
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(infoIdParams);

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        return moeGroomingAppointmentMapper.queryFutureAppointmentCustomerIdList(businessId, date, nowMinutes);
    }

    public ResponseResult<BusinessUpcomingDTO> queryBusinessUpComingAppoint(Integer businessId, String dateString) {
        BusinessUpcomingDTO businessUpcomingDTO = new BusinessUpcomingDTO();

        // 传入时间取传入时间，默认取次日
        if (!StringUtils.hasText(dateString)) {
            dateString = DateUtil.getNowDateString();
        }

        List<CustomerUpComingAppointDTO> upComingAppoint =
                moeGroomingAppointmentMapper.queryBusinessUpComingAppoint(null, businessId, dateString);

        queryUpcomingAppointmentInfo(upComingAppoint, null);

        businessUpcomingDTO.setUpComingAppoints(upComingAppoint);
        businessUpcomingDTO.setTotalAppointmentNum(upComingAppoint.size());
        BigDecimal totalPrice = new BigDecimal(0);
        Set<Integer> petNum = new HashSet<>();
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : upComingAppoint) {
            if (customerUpComingAppointDTO.getEstimatePrice() == null) {
                customerUpComingAppointDTO.setEstimatePrice(new BigDecimal(0));
                log.warn(
                        "grooming invoice estimatePrice is null , grooming id = {}",
                        customerUpComingAppointDTO.getId());
            }
            totalPrice = totalPrice.add(customerUpComingAppointDTO.getEstimatePrice());
            for (CustomerUpcomingPetDetailDTO petDetail : customerUpComingAppointDTO.getPetDetails()) {
                petNum.add(petDetail.getPetId());
            }
        }
        businessUpcomingDTO.setPetNum(petNum.size());
        businessUpcomingDTO.setTotalPrice(totalPrice);

        return ResponseResult.success(businessUpcomingDTO);
    }

    private void queryUpcomingAppointmentInfo(
            List<CustomerUpComingAppointDTO> customerUpComingAppoint, Integer tokenStaffId) {
        if (CollectionUtils.isEmpty(customerUpComingAppoint)) {
            return;
        }
        // 查询用户信息
        queryUpcomingCustomerInfo(customerUpComingAppoint, tokenStaffId);

        // 查询宠物信息
        queryUpcomingCustomerPetInfo(customerUpComingAppoint);

        // 查询员工信息
        queryUpcomingCustomerStaffInfo(customerUpComingAppoint);

        // 查询价格
        queryUpcomingPrice(customerUpComingAppoint);

        // 查询ticketComments
        queryUpcomingNote(customerUpComingAppoint);
    }

    public ResponseResult<List<CustomerUpComingAppointDTO>> queryCustomerUpComingAppointUnEncode(
            Integer customerId, Integer businessId, String appointmentDate, Integer minutes) {
        List<CustomerUpComingAppointDTO> customerUpComingAppoint =
                moeGroomingAppointmentMapper.queryCustomerUpComingAppoint(
                        customerId, businessId, appointmentDate, minutes);

        queryUpcomingAppointmentInfo(customerUpComingAppoint, null);
        return ResponseResult.success(customerUpComingAppoint);
    }

    // 先查询这段时间内的所有预约，再检查 petDetail、evaluationDetail、operation
    public List<Integer> selectCustomerIdWithInCustomDaysAppointment(
            Integer businessId, Integer staffId, Integer beforeDay, Integer afterDay) {
        if (beforeDay == null && afterDay == null) {
            return selectCustomerIdWithInAllDaysAppointment(businessId, staffId);
        }

        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(infoIdParams);

        LocalDateTime now = LocalDateTime.now();
        String startDate = null;
        String endDate = null;

        if (beforeDay != null) {
            startDate = DateUtil.convertLocalDateToDateString(
                    now.plusDays(-beforeDay), businessDto.getTimezoneName(), DateUtil.STANDARD_DATE);
        }
        if (afterDay != null) {
            endDate = DateUtil.convertLocalDateToDateString(
                    now.plusDays(afterDay), businessDto.getTimezoneName(), DateUtil.STANDARD_DATE);
        }

        List<MoeGroomingAppointment> allAppts = moeGroomingAppointmentMapper.getAllApptByStartDateRange(
                null, businessId, startDate, endDate, AppointmentStatusSet.ACTIVE_STATUS_SET);

        Set<Integer> apptIdsForStaff = serviceDetailService.appointmentsFilterByStaff(
                allAppts.stream().map(MoeGroomingAppointment::getId).toList(), staffId);
        return allAppts.stream()
                .filter(k -> apptIdsForStaff.contains(k.getId()))
                .map(MoeGroomingAppointment::getCustomerId)
                .distinct()
                .collect(Collectors.toList());
    }

    public Integer selectCustomerIdIsWithInCustomAppointmentDate(
            Integer businessId, Integer staffId, Integer customerId, Integer beforeDay, Integer afterDay) {
        if (beforeDay == null && afterDay == null) {
            return selectCustomerIdIsWithInAllAppointmentDate(businessId, staffId, customerId);
        }

        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(infoIdParams);

        LocalDateTime now = LocalDateTime.now();
        String startDate = null;
        String endDate = null;

        if (beforeDay != null) {
            startDate = DateUtil.convertLocalDateToDateString(
                    now.plusDays(-beforeDay), businessDto.getTimezoneName(), DateUtil.STANDARD_DATE);
        }
        if (afterDay != null) {
            endDate = DateUtil.convertLocalDateToDateString(
                    now.plusDays(afterDay), businessDto.getTimezoneName(), DateUtil.STANDARD_DATE);
        }

        return moeGroomingAppointmentMapper.selectCustomerIdIsWithAppointmentDate(
                businessId, staffId, startDate, endDate, customerId);
    }

    public Integer selectCustomerIdIsWithIn7AppointmentDate(Integer businessId, Integer staffId, Integer customerId) {
        return selectCustomerIdIsWithInCustomAppointmentDate(businessId, staffId, customerId, 7, 7);
    }

    public List<Integer> selectCustomerIdWithInAllDaysAppointment(Integer businessId, Integer staffId) {
        Set<Integer> customerIds = new HashSet<>();
        customerIds.addAll(moeGroomingAppointmentMapper.selectCustomerIdWithInAllAppointmentDate(businessId, staffId));
        customerIds.addAll(evaluationServiceDetailMapper.selectCustomerIdWithInAllAppointmentDate(businessId, staffId));
        return customerIds.stream().toList();
    }

    public Integer selectCustomerIdIsWithInAllAppointmentDate(Integer businessId, Integer staffId, Integer customerId) {
        return moeGroomingAppointmentMapper.selectCustomerIdIsWithAllAppointmentDate(businessId, staffId, customerId);
    }

    @Deprecated
    public List<Integer> getBusinessIdsOnSendDaily() {
        LocalDate now = LocalDate.now();
        LocalDate tomorrow = now.plusDays(1);
        LocalDate yesterday = now.plusDays(-1);

        String dateTomorrow = DateUtil.convertLocalDateToDateString(tomorrow);
        String dateYesterday = DateUtil.convertLocalDateToDateString(yesterday);

        return moeGroomingAppointmentMapper.getBusinessIdsOnSendDaily(dateTomorrow, dateYesterday);
    }

    public ResponseResult<CustomerLastServiceDTO> getCustomerLastService(Integer businessId, Integer customerId) {
        CustomerLastServiceDTO customerLastServiceDTO = new CustomerLastServiceDTO();
        List<Integer> lastServiceIds = new LinkedList<>();
        List<PetLastServiceDTO> petLastServices = new LinkedList<>();

        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String s = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        CustomerGroomingAppointmentDTO last = moeGroomingAppointmentMapper.queryCustomerLastOrNextAppointment(
                businessInfo.getCompanyId().longValue(), customerId, s, nowMinutes, null, true, false);

        if (last == null) {
            return ResponseResult.success(customerLastServiceDTO);
        }

        List<PetDetailServiceDTO> petDetailServices =
                moePetDetailService.queryPetDetailServiceByGroomingId(last.getId(), null);

        Map<Integer, PetLastServiceDTO> temp = new HashMap<>();
        Map<Integer, List<Integer>> tempServiceIds = new HashMap<>();
        Map<Integer, List<Integer>> tempAddOnsIds = new HashMap<>();
        for (PetDetailServiceDTO petDetailService : petDetailServices) {
            PetLastServiceDTO saveLastInfo = temp.get(petDetailService.getPetId());
            if (saveLastInfo == null) {
                saveLastInfo = new PetLastServiceDTO();
                saveLastInfo.setPetId(petDetailService.getPetId());
                temp.put(petDetailService.getPetId(), saveLastInfo);
            }

            List<Integer> serviceIds = tempServiceIds.get(petDetailService.getPetId());
            if (serviceIds == null) {
                serviceIds = new ArrayList<>();
                tempServiceIds.put(petDetailService.getPetId(), serviceIds);
            }
            List<Integer> addOnsIds = tempAddOnsIds.get(petDetailService.getPetId());
            if (addOnsIds == null) {
                addOnsIds = new ArrayList<>();
                tempAddOnsIds.put(petDetailService.getPetId(), addOnsIds);
            }

            lastServiceIds.add(petDetailService.getServiceId());
            if (petDetailService.getType() == 1) {
                serviceIds.add(petDetailService.getServiceId());
            } else if (petDetailService.getType() == 2) {
                addOnsIds.add(petDetailService.getServiceId());
            }
        }

        for (Map.Entry<Integer, PetLastServiceDTO> entry : temp.entrySet()) {
            Integer petId = entry.getKey();
            PetLastServiceDTO saveLastInfo = entry.getValue();

            List<Integer> serviceIds = tempServiceIds.get(petId);
            List<Integer> addOnsIds = tempAddOnsIds.get(petId);

            saveLastInfo.setLastServiceIds(serviceIds);
            saveLastInfo.setLastAddOnsIds(addOnsIds);

            petLastServices.add(saveLastInfo);
        }

        customerLastServiceDTO.setServiceIds(lastServiceIds);
        customerLastServiceDTO.setPetLastServices(petLastServices);
        return ResponseResult.success(customerLastServiceDTO);
    }

    public String getCustomerAlertNote(Long companyId, Integer businessId, Integer customerId) {
        MoeGroomingNote lastAlertNote = moeGroomingNoteService.getLastNoteByCustomerId(
                companyId, businessId, customerId, GroomingAppointmentEnum.NOTE_ALERT);
        if (lastAlertNote != null) {
            return lastAlertNote.getNote();
        }
        return "";
    }

    public CustomerRebookReminderDTO getCustomerRebookReminderByCustomerId(
            Integer businessId, Integer customerId, String timezoneName) {
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        CustomerGroomingAppointmentDTO lastServiceAppt =
                moeGroomingAppointmentMapper.selectOneCustomerLastServiceTime(businessId, customerId, date, nowMinutes);
        if (lastServiceAppt == null) {
            return null;
        }

        MoeBusinessCustomerDTO moeBusinessCustomerDTO = iCustomerCustomerClient.getCustomerWithDeleted(customerId);
        if (moeBusinessCustomerDTO == null || BooleanEnum.VALUE_FALSE.equals(moeBusinessCustomerDTO.getStatus())) {
            throw new CommonException(ResponseCodeEnum.CUSTOMER_NOT_FOUND);
        }

        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate appointmentDate = LocalDate.parse(lastServiceAppt.getAppointmentDate(), fmt);
        LocalDate expectedServiceDate = appointmentDate.plusDays(moeBusinessCustomerDTO.getPreferredFrequencyDay());

        CustomerRebookReminderDTO customerRebookReminderDTO = new CustomerRebookReminderDTO();

        customerRebookReminderDTO.setAppointmentDate(lastServiceAppt.getAppointmentDate());
        customerRebookReminderDTO.setAppointmentStartTime(lastServiceAppt.getAppointmentStartTime());
        customerRebookReminderDTO.setAppointmentEndTime(lastServiceAppt.getAppointmentEndTime());
        customerRebookReminderDTO.setBusinessId(lastServiceAppt.getBusinessId());
        customerRebookReminderDTO.setCustomerId(lastServiceAppt.getCustomerId());
        customerRebookReminderDTO.setGroomingId(lastServiceAppt.getId());
        customerRebookReminderDTO.setExpectedServiceDate(expectedServiceDate.format(fmt));

        customerRebookReminderDTO.setCustomerFirstName(moeBusinessCustomerDTO.getFirstName());
        customerRebookReminderDTO.setCustomerLastName(moeBusinessCustomerDTO.getLastName());
        customerRebookReminderDTO.setPreferredFrequencyDay(moeBusinessCustomerDTO.getPreferredFrequencyDay());

        return customerRebookReminderDTO;
    }

    /**
     * 获取预约和预约中每个pet的详情，不包含已删除的预约
     *
     * @param groomingIds
     * @return
     */
    public List<GroomingBookingDTO> queryApptWithPetDetailByIds(List<Integer> groomingIds) {
        if (CollectionUtils.isEmpty(groomingIds)) {
            return Collections.emptyList();
        }
        return moeGroomingAppointmentMapper.queryGroomingBookingAppointmentJoin(groomingIds);
    }

    public ResponseResult<PetLastServiceDTO> getPetLastService(
            boolean migrated, long companyId, Integer businessId, Integer petId) {
        PetLastServiceDTO petLastServiceDTO = new PetLastServiceDTO();
        petLastServiceDTO.setPetId(petId);

        List<Integer> serviceIds = new ArrayList<>();
        List<Integer> addOnsIds = new ArrayList<>();

        // FIXME(account structure): 迁移后可以换 companyServiceBlockingStub 查询 company 的时区
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String s = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        // 查询当前时间之前的最近一条预约  todo： moe_grooming_appointment. appointment_date  appointment_end_time增加索引
        // CREATE INDEX app_date_end_time_IDX USING BTREE ON moe_grooming.moe_grooming_appointment
        // (appointment_date,appointment_end_time);
        Integer groomingId = moeGroomingAppointmentMapper.getPetLastAppointmentId(
                companyId, migrated ? null : businessId, petId, s, nowMinutes);
        List<SimpleServiceDto> serviceList = new ArrayList<>();
        if (groomingId != null) {
            List<PetDetailServiceDTO> petDetailServices =
                    moePetDetailService.queryPetDetailServiceByGroomingId(groomingId, petId);

            for (PetDetailServiceDTO petDetailService : petDetailServices) {
                SimpleServiceDto simpleServiceDto = new SimpleServiceDto();
                simpleServiceDto.setServiceId(petDetailService.getServiceId());
                simpleServiceDto.setServicePrice(petDetailService.getServicePrice());
                simpleServiceDto.setServiceTime(petDetailService.getServiceTime());
                serviceList.add(simpleServiceDto);
                if (petDetailService.getType() == 1) {
                    serviceIds.add(petDetailService.getServiceId());
                } else if (petDetailService.getType() == 2) {
                    addOnsIds.add(petDetailService.getServiceId());
                }
            }
        }

        petLastServiceDTO.setLastAddOnsIds(addOnsIds);
        petLastServiceDTO.setLastServiceIds(serviceIds);
        petLastServiceDTO.setServiceList(serviceList);
        return ResponseResult.success(petLastServiceDTO);
    }

    public CustomerUpComingAppointDTO getClientCustomerUpcomingAppointById(Integer appointmentId) {
        CustomerUpComingAppointDTO upcomingAppoint =
                moeGroomingAppointmentMapper.queryCustomerUpcomingAppointById(appointmentId);
        // 增加判空
        if (upcomingAppoint == null) {
            return null;
        }
        queryUpcomingAppointmentInfo(Collections.singletonList(upcomingAppoint), null);
        return upcomingAppoint;
    }

    public MoeGroomingAppointment getLastedNotCanceledAppointment(Integer businessId, Integer customerId) {
        return moeGroomingAppointmentMapper.getLastedNotCanceledAppointment(businessId, customerId);
    }

    public PageInfo<MoeGroomingAppointment> getAppointmentsCreatedBetween(
            Integer businessId,
            Long startDate,
            Long endDate,
            List<Integer> customerIds,
            Integer pageNum,
            Integer pageSize) {
        try (Page<MoeGroomingAppointment> page = PageHelper.startPage(pageNum, pageSize)) {
            return page.doSelectPageInfo(() -> moeGroomingAppointmentMapper.queryApptsCreatedBetween(
                    businessId, startDate, endDate, new HashSet<>(customerIds)));
        }
    }

    public Set<Integer> listCustomerIdByCountFilter(ClientsFilterDTO clientsFilterDTO) {
        if (Objects.isNull(clientsFilterDTO.companyId())
                || CollectionUtils.isEmpty(clientsFilterDTO.filters())
                || Objects.isNull(clientsFilterDTO.type())) {
            return Collections.emptySet();
        }
        // Needs to reverse the result
        boolean needReversed = clientsFilterDTO.filters().stream().anyMatch(FilterDTO::isReversed);
        if (!needReversed) {
            ClientsFilterDTO replaceClientsFilter = ClientsFilterDTO.builder()
                    .companyId(clientsFilterDTO.companyId())
                    .preferredBusinessIds(clientsFilterDTO.preferredBusinessIds())
                    .filters(clientsFilterDTO.filters())
                    .connector(clientsFilterDTO.connector())
                    .type(clientsFilterDTO.type())
                    .businessDateTime(clientsFilterDTO.businessDateTime())
                    .sort(clientsFilterDTO.sort())
                    .offset(clientsFilterDTO.offset())
                    .limit(clientsFilterDTO.limit())
                    .build();
            return moeGroomingAppointmentMapper.listCustomerIdByFilter(replaceClientsFilter);
        }
        List<Set<Integer>> filterSetList = new ArrayList<>();
        List<CompletableFuture<Set<Integer>>> futureList = new ArrayList<>();
        ClientsFilterDTO.ClientsFilterDTOBuilder filterBuilder = ClientsFilterDTO.builder()
                .companyId(clientsFilterDTO.companyId())
                .preferredBusinessIds(clientsFilterDTO.preferredBusinessIds())
                .customerIds(clientsFilterDTO.customerIds())
                .businessDateTime(clientsFilterDTO.businessDateTime())
                .type(clientsFilterDTO.type())
                .connector(clientsFilterDTO.connector());
        // Forward filtering
        List<FilterDTO> forwardFilterDTOList = clientsFilterDTO.filters().stream()
                .filter(filterDTO -> !filterDTO.isReversed())
                .toList();
        if (!CollectionUtils.isEmpty(forwardFilterDTOList)) {
            futureList.add(CompletableFuture.supplyAsync(
                    () -> {
                        ClientsFilterDTO forwardFilter =
                                filterBuilder.filters(forwardFilterDTOList).build();
                        return moeGroomingAppointmentMapper.listCustomerIdByFilter(forwardFilter);
                    },
                    ThreadPool.getExecutor()));
        }
        // Reverse filter
        clientsFilterDTO.filters().stream()
                .filter(FilterDTO::isReversed)
                .forEach(filterDTO -> futureList.add(CompletableFuture.supplyAsync(
                        () -> {
                            ClientsFilterDTO reverseFilter = filterBuilder
                                    .filters(Collections.singletonList(filterDTO))
                                    .build();
                            Set<Integer> filterCustomerIds =
                                    moeGroomingAppointmentMapper.listCustomerIdByFilter(reverseFilter);
                            Set<Integer> allCustomerIds = new HashSet<>(clientsFilterDTO.customerIds());
                            allCustomerIds.removeAll(filterCustomerIds);
                            return allCustomerIds;
                        },
                        ThreadPool.getExecutor())));
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        futureList.forEach(future -> {
            try {
                filterSetList.add(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.error("listCustomerIdByCountFilter error", e);
            }
        });
        // Intersect or union
        return switch (clientsFilterDTO.type()) {
            case TYPE_AND -> filterSetList.stream()
                    .reduce((a, b) -> {
                        a.retainAll(b);
                        return a;
                    })
                    .orElse(Collections.emptySet());
            case TYPE_OR -> filterSetList.stream().flatMap(Set::stream).collect(Collectors.toSet());
        };
    }

    public MoeGroomingAppointmentDTO getCustomerNewAppointment(Integer businessId, Integer customerId) {
        MoeGroomingAppointment appt = moeGroomingAppointmentMapper.getCustomerNewAppointment(businessId, customerId);
        if (appt == null) {
            return null;
        }
        MoeGroomingAppointmentDTO dto = new MoeGroomingAppointmentDTO();
        BeanUtils.copyProperties(appt, dto);
        return dto;
    }

    public List<CustomerUpComingAppointDTO> getClientCustomerUpcomingAppointList(ClientCustomerUpcomingParams params) {
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, params.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(params.getTimezoneName());
        List<CustomerUpComingAppointDTO> upcomingList = moeGroomingAppointmentMapper.queryCustomerUpcomingAppoint(
                params.getBusinessIds(), params.getCustomerIds(), date, nowMinutes);
        if (CollectionUtils.isEmpty(upcomingList)) {
            return Collections.emptyList();
        }
        // 填充信息
        queryUpcomingAppointmentInfo(upcomingList, null);
        return upcomingList;
    }

    public Pair<List<MoeGroomingAppointment>, Pagination> adminQueryAppointment(AdminQueryAppointmentParams param) {
        if (param.getId() == null && (param.getBusinessId() == null || Strings.isBlank(param.getAppointmentDate()))) {
            return Pair.of(
                    Collections.emptyList(),
                    new Pagination(
                            param.getPagination().pageNum(),
                            param.getPagination().pageSize(),
                            0));
        }
        var example = new MoeGroomingAppointmentExample();
        var exampleCriteria = example.createCriteria();
        if (param.getId() != null) {
            exampleCriteria.andIdEqualTo(param.getId());
        }
        if (StringUtils.hasText(param.getAppointmentDate()) && param.getBusinessId() != null) {
            exampleCriteria
                    .andAppointmentDateEqualTo(param.getAppointmentDate())
                    .andBusinessIdEqualTo(param.getBusinessId());
        }
        return selectPage(param.getPagination(), () -> moeGroomingAppointmentMapper.selectByExample(example));
    }

    public CustomerUpComingAppointDTO getLatestClientCustomerUpcomingAppoint(ClientCustomerUpcomingParams params) {
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, params.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(params.getTimezoneName());

        Integer latestAppointId = moeGroomingAppointmentMapper.getLatestClientUpcomingAppointId(
                params.getBusinessIds(), params.getCustomerIds(), date, nowMinutes);

        return getClientCustomerUpcomingAppointById(latestAppointId);
    }

    public ResponseResult<String> getCustomerUpComingUrl(Integer customerId) {
        String url = upcomingUrl + Des3Util.encode(upcomingKey, customerId.toString());
        return ResponseResult.success(url);
    }

    public String getCustomerUpcomingUrl(Integer customerId) {
        if (Objects.isNull(customerId)) {
            return upcomingUrl;
        }
        return upcomingUrl + Des3Util.encode(upcomingKey, customerId.toString());
    }

    public AppointmentWithPetDetailsDto getAppointmentWithPetDetails(Integer appointmentId, boolean includeCancelled) {
        return moeGroomingAppointmentMapper.getAppointmentWithPetDetails(appointmentId, includeCancelled);
    }

    public MoeGroomingAppointment getAppointmentById(Integer appointmentId) {
        return moeGroomingAppointmentMapper.getAppointmentById(appointmentId);
    }

    public List<AppointmentWithPetDetailsDto> getAppointmentListWithPetDetails(
            Set<Integer> appointmentIds, boolean includeCancelled) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Collections.emptyList();
        }
        return moeGroomingAppointmentMapper.getAppointmentListWithPetDetails(appointmentIds, includeCancelled);
    }

    public List<AppointmentWithPetDetailsDto> getRepeatedAppointmentListWithPetDetails(Set<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return moeGroomingAppointmentMapper.getRepeatedAppointmentListWithPetDetails(appointmentIds);
    }

    public ResponseResult<GroomingTicketWindowDetailDTO> queryTicketDetailWithWindow(
            Long companyId, Integer businessId, Integer id, boolean isMigrated) {
        // 查询预约信息
        MoeGroomingAppointment moeGroomingAppointment = moeGroomingAppointmentMapper.selectByPrimaryKey(id);
        if (moeGroomingAppointment == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        if (isMigrated) {
            if (!Objects.equals(companyId, moeGroomingAppointment.getCompanyId())) {
                throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
            }
        } else {
            if (!Objects.equals(businessId, moeGroomingAppointment.getBusinessId())) {
                throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
            }
        }
        businessId = moeGroomingAppointment.getBusinessId();

        GroomingTicketWindowDetailDTO groomingTicketWindowDetailDTO = new GroomingTicketWindowDetailDTO();
        BeanUtils.copyProperties(moeGroomingAppointment, groomingTicketWindowDetailDTO);
        // FIXME: 这两个类的 status 字段类型不一致，一直没 copy 成功过
        groomingTicketWindowDetailDTO.setStatus(
                moeGroomingAppointment.getStatus().intValue());

        // 根据 grooming id 查询alertNote 和 ticket comment note
        List<MoeGroomingNote> moeGroomingNoteList =
                moeGroomingNoteService.getNoteListByGroomingIdList(Collections.singletonList(id));
        Map<Byte, MoeGroomingNote> typeNoteMap = moeGroomingNoteList.stream()
                .collect(toMap(
                        MoeGroomingNote::getType,
                        Function.identity(),
                        (k1, k2) -> k1.getUpdateTime() > k2.getUpdateTime() ? k1 : k2));
        Optional.ofNullable(typeNoteMap.get(GroomingAppointmentEnum.NOTE_ALERT))
                .ifPresent(note -> groomingTicketWindowDetailDTO.setAlertNotes(note.getNote()));
        Optional.ofNullable(typeNoteMap.get(GroomingAppointmentEnum.NOTE_COMMENT))
                .ifPresent(note -> groomingTicketWindowDetailDTO.setTicketComments(note.getNote()));
        Optional.ofNullable(typeNoteMap.get(GroomingAppointmentEnum.NOTE_ADDITIONAL))
                .ifPresent(note -> groomingTicketWindowDetailDTO.setAdditionalNote(note.getNote()));

        // 查询服务信息
        var allPetDetailsPair = appointmentServiceDetailService.getAppointmentServiceDetails(List.of(id));
        List<GroomingPetDetailDTO> groomingPetDetailDTOS = allPetDetailsPair.key();
        List<MoeGroomingPetDetail> moePetDetails =
                appointmentServiceDetailService.toMoeGroomingPetDetail(groomingPetDetailDTOS);
        List<EvaluationServiceDetail> evaluations = allPetDetailsPair.value();
        if (GroomingAppointmentEnum.IS_BLOCK_TRUE.equals(moeGroomingAppointment.getIsBlock())) {
            groomingTicketWindowDetailDTO.setEstimatedTotalPrice(BigDecimal.ZERO);
        } else {
            groomingTicketWindowDetailDTO.setEstimatedTotalPrice(
                    appointmentServiceDetailService.calculateAmount(moePetDetails, moePetDetails, evaluations));
        }

        // 设置 operation
        List<Integer> groomingServiceIdList = groomingPetDetailDTOS.stream()
                .filter(GroomingPetDetailDTO::getEnableOperation)
                .map(GroomingPetDetailDTO::getId)
                .toList();
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingServiceIdList(
                        businessId, groomingServiceIdList);
        if (!CollectionUtils.isEmpty(operationMap)) {
            groomingPetDetailDTOS.forEach(groomingPetDetailDTO -> {
                if (Boolean.TRUE.equals(groomingPetDetailDTO.getEnableOperation())) {
                    groomingPetDetailDTO.setOperationList(operationMap.get(groomingPetDetailDTO.getId()));
                    if (Objects.equals(MoePetDetailService.SEQUENCE_WORK_MODE, groomingPetDetailDTO.getWorkMode())) {
                        return;
                    }
                    boolean parallelWorkMode = groomingPetDetailDTO.getOperationList().stream()
                                    .map(GroomingServiceOperationDTO::getStartTime)
                                    .distinct()
                                    .count()
                            == 1;
                    groomingPetDetailDTO.setWorkMode(
                            parallelWorkMode
                                    ? MoePetDetailService.PARALLEL_WORK_MODE
                                    : MoePetDetailService.SEQUENCE_WORK_MODE);
                }
            });
        }

        groomingTicketWindowDetailDTO.setGroomingPetDetails(groomingPetDetailDTOS);
        groomingTicketWindowDetailDTO.setEvaluationServiceDetails(
                appointmentServiceDetailService.toEvaluationServiceDetailDTOList(evaluations));

        groomingTicketWindowDetailDTO.setAutoAssign(
                AutoAssignConverter.INSTANCE.entityToDTO(autoAssignService.getAutoAssign(id)));

        return ResponseResult.success(groomingTicketWindowDetailDTO);
    }

    public List<CustomerGroomingAppointmentDTO> getCustomerLastAppointments(
            Integer businessId, List<Integer> customerIds) {
        List<CustomerGroomingAppointmentDTO> res = new LinkedList<>();

        InfoIdParams businessIdParams = new InfoIdParams();

        businessIdParams.setInfoId(businessId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String s = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        for (Integer customerId : customerIds) {
            CustomerGroomingAppointmentDTO last = moeGroomingAppointmentMapper.queryCustomerLastOrNextAppointment(
                    businessInfo.getCompanyId().longValue(), customerId, s, nowMinutes, null, true, false);
            res.add(last);
        }

        return res;
    }

    public List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForClientShare(
            long companyId, UpcomingPreviewVo previewVo, MoeBusinessDto businessInfo, CustomerInfoDto infoDto) {
        if (previewVo.getIsPreview()) {
            BeanUtils.copyProperties(previewVo, infoDto);
        }

        // FIXME: 迁移后的 company，取 company 的时区
        LocalDateTime now = LocalDateTime.now();
        String startDate = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer startMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        // status
        // share appt 状态  0 all 1 unconfirm 2confirm 3 finished
        // limit
        // 0 all  1 in x days  2 next x appointment  3 manually apptids
        Integer limit = BusinessCustomerConst.MAX_SHARE_RANGE_LIMIT;
        List<Integer> apptIds = null;
        Integer status = 0;
        String endDateStr = null;
        if (BusinessCustomerConst.SHARE_RANGE_TYPE_MANUALLY.equals(infoDto.getShareRangeType())) {
            apptIds = infoDto.getShareApptIds();
        } else {
            if (!BusinessCustomerConst.SHARE_APPT_STATUS_ALL.equals(infoDto.getShareApptStatus())) {
                status = infoDto.getShareApptStatus().intValue();
            }
            if (BusinessCustomerConst.SHARE_RANGE_TYPE_NEXT_APPT.equals(infoDto.getShareRangeType())) {
                limit = infoDto.getShareRangeValue();
            }
            if (BusinessCustomerConst.SHARE_RANGE_TYPE_NEXT_DAYS.equals(infoDto.getShareRangeType())) {
                LocalDateTime endDateTime = LocalDateTime.now().plusDays(infoDto.getShareRangeValue());
                endDateStr = DateUtil.convertLocalDateToDateString(
                        endDateTime, businessInfo.getTimezoneName(), "yyyy-MM-dd");
            }
        }
        if (StringUtils.isEmpty(endDateStr)) {
            LocalDateTime endDateTime = LocalDateTime.now().plusDays(BusinessCustomerConst.MAX_SHARE_RANGE_MAX_DAY);
            endDateStr =
                    DateUtil.convertLocalDateToDateString(endDateTime, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        }
        // 查询预约信息
        List<CustomerUpComingAppointDTO> customerUpComingAppoint =
                moeGroomingAppointmentMapper.queryCustomerUpComingAppointForClientShare(
                        previewVo.getCustomerId(),
                        Math.toIntExact(companyId),
                        apptIds,
                        startDate,
                        startMinutes,
                        status,
                        endDateStr);
        if (CollectionUtils.isEmpty(customerUpComingAppoint)) {
            return Collections.emptyList();
        }
        List<CustomerUpComingAppointDTO> newList;
        if (customerUpComingAppoint.size() > limit) {
            newList = customerUpComingAppoint.subList(0, limit);
        } else {
            newList = customerUpComingAppoint;
        }
        queryEvaluationServiceDetail(newList);
        queryUpcomingAppointmentInfo(newList, null);

        setArrivalWindowTime(newList);

        // set business info
        var businesses = iBusinessBusinessClient.getBusinessByCompanyId((int) companyId);
        newList.forEach(appointment -> {
            var business = businesses.get(appointment.getBusinessId());
            if (business != null) {
                appointment.setBusinessName(business.getBusinessName());
                appointment.setBusinessAvatarPath(business.getAvatarPath());
            }
        });

        return newList;
    }

    private void setArrivalWindowTime(List<CustomerUpComingAppointDTO> newList) {
        var arrivalWindowMap = new HashMap<Integer, ArrivalWindowSettingDto>();

        newList.stream()
                .map(CustomerUpComingAppointDTO::getBusinessId)
                .distinct()
                .forEach(businessId -> {
                    var arrivalWindow = messageClient.getArrivalWindow(businessId);
                    if (arrivalWindow != null) {
                        arrivalWindowMap.put(businessId, arrivalWindow);
                    }
                });

        newList.forEach(appointment -> {
            Integer apptStartTime = appointment.getAppointmentStartTime();
            var businessId = appointment.getBusinessId();
            var arrivalWindow = arrivalWindowMap.get(businessId);
            var arrivalWindowEnable = Objects.nonNull(arrivalWindow)
                    && Objects.equals(ArrivalWindowConst.STATUS_TRUE, arrivalWindow.getStatus());

            if (arrivalWindowEnable) {
                List<Integer> staffIdList = appointment.getPetDetails().stream()
                        .map(CustomerUpcomingPetDetailDTO::getStaffId)
                        .distinct()
                        .toList();
                StaffListWorkTimeResultDto limitTime =
                        getApptLimitTime(businessId, staffIdList, appointment.getAppointmentDate());
                ArrivalWindowTime arrivalWindowTime =
                        getStartTimeWithArrivalWindow(apptStartTime, arrivalWindow, limitTime);
                appointment.setArrivalBeforeStartTime(arrivalWindowTime.getArrivalBeforeStartTime());
                appointment.setArrivalAfterStartTime(arrivalWindowTime.getArrivalAfterStartTime());
            } else {
                appointment.setArrivalBeforeStartTime(apptStartTime);
                appointment.setArrivalAfterStartTime(apptStartTime);
            }
        });
    }

    private StaffListWorkTimeResultDto getApptLimitTime(
            Integer businessId, List<Integer> staffIdList, String apptDate) {
        WorkingDailyQueryRangeVo workingDailyQueryRangeVo = new WorkingDailyQueryRangeVo();
        if (staffIdList == null) {
            return null;
        }
        workingDailyQueryRangeVo.setStaffIdList(staffIdList);

        workingDailyQueryRangeVo.setStartDate(apptDate);
        workingDailyQueryRangeVo.setEndDate(apptDate);
        AtomicReference<Integer> minStartTime = new AtomicReference<>(BusinessCalendarEnum.DEFAULT_CALENDAR_MAX_TIME);
        AtomicReference<Integer> maxEndTime = new AtomicReference<>(BusinessCalendarEnum.DEFAULT_CALENDAR_MIN_TIME);
        ResponseResult<List<StaffWorkingRangeDto>> listResponseResult =
                iBusinessStaffClient.queryRange(businessId, 0, workingDailyQueryRangeVo);
        listResponseResult.getData().stream()
                .filter(r -> !r.getTimeRange().get(apptDate).isEmpty())
                .forEach(r -> {
                    TimeRangeDto range = r.getTimeRange().get(apptDate).get(0);
                    if (BusinessCalendarEnum.DEFAULT_CALENDAR_MAX_TIME.equals(minStartTime.get())
                            && range.getStartTime() != 0
                            && range.getEndTime() != 0) {
                        minStartTime.set(range.getStartTime());
                        maxEndTime.set(range.getEndTime());
                    } else {
                        if (range.getStartTime() < minStartTime.get()) {
                            minStartTime.set(range.getStartTime());
                        }
                        if (range.getEndTime() > maxEndTime.get()) {
                            maxEndTime.set(range.getEndTime());
                        }
                    }
                });
        boolean isHaveWorkTime = false;
        for (StaffWorkingRangeDto datum : listResponseResult.getData()) {
            if (!datum.getTimeRange().isEmpty()
                    && !CollectionUtils.isEmpty(datum.getTimeRange().get(apptDate))) {
                isHaveWorkTime = true;
                break;
            }
        }
        if (!isHaveWorkTime) {
            minStartTime.set(BusinessCalendarEnum.DEFAULT_CALENDAR_MIN_TIME);
            maxEndTime.set(BusinessCalendarEnum.DEFAULT_CALENDAR_MAX_TIME);
        }
        StaffListWorkTimeResultDto resultDto = new StaffListWorkTimeResultDto();
        resultDto.setMinStartTime(minStartTime.get());
        resultDto.setMaxEndTime(maxEndTime.get());
        return resultDto;
    }

    public void setArrivalWindowForAppointmentDetails(
            Integer businessId,
            ArrivalWindowSettingDto arrivalWindow,
            Collection<AppointmentWithPetDetailsDto> appointments) {
        boolean arrivalWindowEnable = Objects.nonNull(arrivalWindow)
                && Objects.equals(ArrivalWindowConst.STATUS_TRUE, arrivalWindow.getStatus());
        if (!arrivalWindowEnable) {
            return;
        }

        appointments.forEach(appointment -> {
            Integer apptStartTime = appointment.getAppointmentStartTime();
            List<Integer> staffIdList = appointment.getServices().stream()
                    .map(AppointmentServiceInfo::getStaffId)
                    .distinct()
                    .toList();
            // todo 待优化，这里需要增加内部接口，放到循环外层查询
            StaffListWorkTimeResultDto limitTime =
                    getApptLimitTime(businessId, staffIdList, appointment.getAppointmentDate());
            ArrivalWindowTime arrivalWindowTime =
                    getStartTimeWithArrivalWindow(apptStartTime, arrivalWindow, limitTime);
            appointment.setArrivalBeforeStartTime(arrivalWindowTime.getArrivalBeforeStartTime());
            appointment.setArrivalAfterStartTime(arrivalWindowTime.getArrivalAfterStartTime());
        });
    }

    public ArrivalWindowTime getStartTimeWithArrivalWindow(
            Integer apptStartTime, ArrivalWindowSettingDto arrivalWindow, StaffListWorkTimeResultDto limitTime) {
        if (arrivalWindow == null) {
            return new ArrivalWindowTime(apptStartTime, apptStartTime);
        }
        Integer arrivalBefore = arrivalWindow.getArrivalBefore();
        Integer arrivalAfter = arrivalWindow.getArrivalAfter();
        Integer calendarViewStartAt = Objects.nonNull(limitTime) ? limitTime.getMinStartTime() : null;
        Integer calendarViewEndAt = Objects.nonNull(limitTime) ? limitTime.getMaxEndTime() : null;
        int apptBeforeTime = apptStartTime - arrivalBefore;
        int apptAfterTime = apptStartTime + arrivalAfter;
        // 1、当预约时间大于start time 且 (预约-arrival_before)时间小于start time时，才会将( 预约-arrival_before )时间设置为start_time
        // 2、当预约时间小于end time 且 (预约+arrival_after)时间大于end_time时，才会将(预约+arrival_after)时间设置为end_time
        // 3、当预约时间小于start time，( 预约-arrival_before )时间为预约时间
        // 4、当预约时间大于end time,(预约+arrival_after) 时间为预约时间
        // 5、当预约时间大于start time 且(预约-arrival_before)时间大于start time时，不做任何特殊处理
        // 6、当预约时间小于end time 且 (预约+arrival_after)时间小于end_time时，不做任何特殊处理
        if (Objects.nonNull(calendarViewStartAt)) {
            if (apptStartTime >= calendarViewStartAt && apptBeforeTime < calendarViewStartAt) {
                apptBeforeTime = calendarViewStartAt;
            }
            if (apptStartTime < calendarViewStartAt) {
                apptBeforeTime = apptStartTime;
            }
        }
        if (Objects.nonNull(calendarViewEndAt)) {
            if (apptStartTime <= calendarViewEndAt && apptAfterTime > calendarViewEndAt) {
                apptAfterTime = calendarViewEndAt;
            }
            if (apptStartTime > calendarViewEndAt) {
                apptAfterTime = apptStartTime;
            }
        }

        return new ArrivalWindowTime(apptBeforeTime, apptAfterTime);
    }

    public ResponseResult<CustomerUpcomingBusinessDTO> queryCustomerUpComingAppointForClientShare(String id) {
        String decodeId = Des3Util.decode(upcomingKey, id);
        if (StringUtils.isEmpty(decodeId)) {
            throw new CommonException(ResponseCodeEnum.UPCOMING_ID_ERROR);
        }
        Integer customerId = Integer.parseInt(decodeId);
        // 查询customer信息
        CustomerInfoDto infoDto = iCustomerCustomerClient.getCustomerWithDeletedNoBusinessId(customerId);
        CustomerUpcomingBusinessDTO customerUpcomingBusinessDTO = new CustomerUpcomingBusinessDTO();
        customerUpcomingBusinessDTO.setCustomerId(customerId);
        if (infoDto == null) {
            return ResponseResult.success(customerUpcomingBusinessDTO);
        }

        var companyId = infoDto.getCompanyId();
        var preferredBusinessId = infoDto.getPreferredBusinessId().intValue();
        var migrated = migrateHelper.isMigrate(companyId);
        customerUpcomingBusinessDTO.setUseCompanyInfo(migrated);

        // 查询 company 信息
        var company = companyServiceBlockingStub
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .getCompanyIdToCompanyMap()
                .get(companyId);
        if (company == null) {
            return ResponseResult.success(customerUpcomingBusinessDTO);
        }
        customerUpcomingBusinessDTO.setCompanyName(company.getName());

        var currencySymbol = companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getCurrencySymbol();
        customerUpcomingBusinessDTO.setCurrencySymbol(currencySymbol);

        // 查询business信息
        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(preferredBusinessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfoWithOwnerEmailV2(infoIdParams);
        if (businessInfo == null) {
            return ResponseResult.success(customerUpcomingBusinessDTO);
        }
        // FIXME: 对于迁移后的 company，提供一个 company 维度的接口查询该配置项
        // 将isShowPrice增加到返回值内
        Boolean isShowPrice = iBusinessBusinessClient.getBusinessShareUpcomingShowServicePrice(preferredBusinessId);
        customerUpcomingBusinessDTO.setIsShowServicePrice(isShowPrice);
        // 查询upcoming appt list
        UpcomingPreviewVo previewVo = new UpcomingPreviewVo();
        previewVo.setCustomerId(customerId);
        previewVo.setIsPreview(false);
        customerUpcomingBusinessDTO.setUpComingAppoint(
                queryCustomerUpComingAppointForClientShare(companyId, previewVo, businessInfo, infoDto));
        // isShowPrice为false时，隐藏price价格
        if (isShowPrice != null && !isShowPrice) {
            for (CustomerUpComingAppointDTO apptDto : customerUpcomingBusinessDTO.getUpComingAppoint()) {
                apptDto.setEstimatePrice(BigDecimal.ZERO);
            }
        }

        // 如果 next appt 存在，取 next appt 的 business 信息
        if (!CollectionUtils.isEmpty(customerUpcomingBusinessDTO.getUpComingAppoint())) {
            var nextApptBusinessId =
                    customerUpcomingBusinessDTO.getUpComingAppoint().get(0).getBusinessId();

            InfoIdParams params = new InfoIdParams();
            params.setInfoId(nextApptBusinessId);
            businessInfo = iBusinessBusinessClient.getBusinessInfoWithOwnerEmailV2(params);
        }

        customerUpcomingBusinessDTO.setBusinessName(businessInfo.getBusinessName());
        // 优先取 contactEmail，如果为空则取 ownerEmail
        customerUpcomingBusinessDTO.setBusinessEmail(
                StringUtils.hasText(businessInfo.getContactEmail())
                        ? businessInfo.getContactEmail()
                        : businessInfo.getOwnerEmail());
        customerUpcomingBusinessDTO.setBusinessHeadImg(businessInfo.getAvatarPath());
        customerUpcomingBusinessDTO.setBusinessSymbol(businessInfo.getCurrencySymbol());
        customerUpcomingBusinessDTO.setBusinessAddress(AddressUtil.getBusinessAddress(businessInfo));
        customerUpcomingBusinessDTO.setTimezoneName(businessInfo.getTimezoneName());
        customerUpcomingBusinessDTO.setTimezoneSeconds(businessInfo.getTimezoneSeconds());
        customerUpcomingBusinessDTO.setAppType(businessInfo.getAppType());

        return ResponseResult.success(customerUpcomingBusinessDTO);
    }

    public List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForShare(Integer customerId) {
        String id = Des3Util.encode(upcomingKey, customerId.toString());
        ResponseResult<CustomerUpcomingBusinessDTO> result = queryCustomerUpComingAppointForClientShare(id);
        return result.getData().getUpComingAppoint();
    }

    private void queryUpcomingNote(List<CustomerUpComingAppointDTO> customerUpComingAppoint) {
        List<Integer> groomingIds = new ArrayList<>();
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            groomingIds.add(customerUpComingAppointDTO.getId());
        }
        List<MoeGroomingNote> moeGroomingNoteList = moeGroomingNoteService.getNoteListByGroomingIdList(groomingIds);
        if (!CollectionUtils.isEmpty(moeGroomingNoteList)) {
            Map<Integer, List<MoeGroomingNote>> moeGroomingNoteMap =
                    moeGroomingNoteList.stream().collect(groupingBy(MoeGroomingNote::getGroomingId));
            if (moeGroomingNoteMap != null && !moeGroomingNoteMap.isEmpty()) {
                for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
                    if (moeGroomingNoteMap.containsKey(customerUpComingAppointDTO.getId())) {
                        for (MoeGroomingNote moeGroomingNote :
                                moeGroomingNoteMap.get(customerUpComingAppointDTO.getId())) {
                            if (GroomingAppointmentEnum.NOTE_ALERT.equals(moeGroomingNote.getType())) {
                                customerUpComingAppointDTO.setAlertNotes(moeGroomingNote.getNote());
                            }
                            if (GroomingAppointmentEnum.NOTE_COMMENT.equals(moeGroomingNote.getType())) {
                                customerUpComingAppointDTO.setTicketComments(moeGroomingNote.getNote());
                            }
                        }
                    }
                }
            }
        }
    }

    private void queryEvaluationServiceDetail(List<CustomerUpComingAppointDTO> customerUpComingAppoint) {
        if (CollectionUtils.isEmpty(customerUpComingAppoint)) {
            return;
        }

        List<Long> groomingIds =
                customerUpComingAppoint.stream().map(e -> e.getId().longValue()).toList();

        final var evaluationServiceDetails = evaluationServiceDetailMapper.queryByAppointmentIds(groomingIds);
        if (CollectionUtils.isEmpty(evaluationServiceDetails)) {
            return;
        }
        final var evaluationServiceDetailMap = evaluationServiceDetails.stream()
                .collect(Collectors.groupingBy(EvaluationServiceDetail::getAppointmentId));

        final var evaluationIds = evaluationServiceDetails.stream()
                .map(EvaluationServiceDetail::getServiceId)
                .collect(Collectors.toSet());
        final var evaluationServiceList = evaluationServiceBlockingStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(evaluationIds)
                        .build())
                .getEvaluationsList();
        final var evaluationServiceMap = evaluationServiceList.stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity()));

        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            if (!evaluationServiceDetailMap.containsKey(
                            customerUpComingAppointDTO.getId().longValue())
                    || CollectionUtils.isEmpty(evaluationServiceDetailMap.get(
                            customerUpComingAppointDTO.getId().longValue()))) {
                continue;
            }
            var existingDetails = customerUpComingAppointDTO.getPetDetails();
            var evaluationDetails =
                    evaluationServiceDetailMap
                            .get(customerUpComingAppointDTO.getId().longValue())
                            .stream()
                            .map(e -> {
                                if (!evaluationServiceMap.containsKey(e.getServiceId())) {
                                    return null;
                                }
                                final var evaluationBrief = evaluationServiceMap.get(e.getServiceId());
                                var detail = new CustomerUpcomingPetDetailDTO();
                                detail.setServiceName(evaluationBrief.getName());
                                detail.setStaffId(Optional.ofNullable(e.getStaffId())
                                        .orElse(0L)
                                        .intValue());
                                detail.setPetId(Optional.ofNullable(e.getPetId())
                                        .orElse(0L)
                                        .intValue());
                                detail.setServicePrice(BigDecimal.valueOf(evaluationBrief.getPrice()));
                                return detail;
                            })
                            .filter(Objects::nonNull)
                            .toList();
            customerUpComingAppointDTO.setPetDetails(
                    CollectionUtils.isEmpty(existingDetails)
                            ? evaluationDetails
                            : Stream.concat(existingDetails.stream(), evaluationDetails.stream())
                                    .collect(Collectors.toList()));
        }
    }

    private void queryUpcomingPrice(List<CustomerUpComingAppointDTO> customerUpComingAppoint) {
        List<Integer> groomingIds = new ArrayList<>();
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            groomingIds.add(customerUpComingAppointDTO.getId());
        }
        Integer businessId = CollectionUtils.isEmpty(customerUpComingAppoint)
                ? null
                : customerUpComingAppoint.get(0).getBusinessId();
        List<MoeGroomingInvoice> invoices = invoiceService.queryInvoiceByGroomingIds(businessId, groomingIds, null);

        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            for (MoeGroomingInvoice moeGroomingInvoice : invoices) {
                if (customerUpComingAppointDTO.getId().equals(moeGroomingInvoice.getGroomingId())) {
                    customerUpComingAppointDTO.setEstimatePrice(moeGroomingInvoice.getSubTotalAmount());
                    break;
                }
            }
        }
    }

    private List<GroomingCalenderCustomerInfo> getCustomerInfo(
            List<GroomingQueryDto> ticketInfo, Integer tokenStaffId) {
        GroomingCustomerInfoParams groomingCustomerInfoParams = new GroomingCustomerInfoParams();
        groomingCustomerInfoParams.setTicketInfo(ticketInfo);
        groomingCustomerInfoParams.setTokenStaffId(tokenStaffId);

        List<GroomingCalenderCustomerInfo> groomingCalenderCustomerInfos =
                iCustomerGroomingClient.getGroomingCalenderCustomerInfo(groomingCustomerInfoParams);
        return groomingCalenderCustomerInfos;
    }

    private void queryUpcomingCustomerInfo(
            List<CustomerUpComingAppointDTO> customerUpComingAppoint, Integer tokenStaffId) {
        List<GroomingQueryDto> ticketInfo = new ArrayList<>(customerUpComingAppoint.size() * 2);
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            GroomingQueryDto groomingQueryDto = new GroomingQueryDto();
            groomingQueryDto.setCustomerId(customerUpComingAppointDTO.getCustomerId());
            groomingQueryDto.setGroomingId(customerUpComingAppointDTO.getId());
            groomingQueryDto.setCustomerAddressId(customerUpComingAppointDTO.getCustomerAddressId());

            ticketInfo.add(groomingQueryDto);
        }
        List<GroomingCalenderCustomerInfo> customerInfo = getCustomerInfo(ticketInfo, tokenStaffId);
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            for (GroomingCalenderCustomerInfo groomingCalenderCustomerInfo : customerInfo) {
                if (customerUpComingAppointDTO.getCustomerId().equals(groomingCalenderCustomerInfo.getCustomerId())
                        && customerUpComingAppointDTO.getId().equals(groomingCalenderCustomerInfo.getGroomingId())) {
                    customerUpComingAppointDTO.setAddress1(groomingCalenderCustomerInfo.getAddress1());
                    customerUpComingAppointDTO.setAddress2(groomingCalenderCustomerInfo.getAddress2());
                    customerUpComingAppointDTO.setCity(groomingCalenderCustomerInfo.getCity());
                    customerUpComingAppointDTO.setCountry(groomingCalenderCustomerInfo.getCountry());
                    customerUpComingAppointDTO.setZipcode(groomingCalenderCustomerInfo.getZipcode());
                    customerUpComingAppointDTO.setState(groomingCalenderCustomerInfo.getState());

                    customerUpComingAppointDTO.setClientPhoneNumber(
                            groomingCalenderCustomerInfo.getClientPhoneNumber());

                    customerUpComingAppointDTO.setCustomerFirstName(
                            groomingCalenderCustomerInfo.getCustomerFirstName());
                    customerUpComingAppointDTO.setCustomerLastName(groomingCalenderCustomerInfo.getCustomerLastName());
                    customerUpComingAppointDTO.setEmail(groomingCalenderCustomerInfo.getEmail());
                }
            }
        }

        customerUpComingAppoint.stream().forEach(e -> {
            StringJoiner sj = AddressUtil.getFullAddress(
                    e.getAddress1(), e.getAddress2(), e.getCity(), e.getState(), e.getCountry(), e.getZipcode());
            e.setClientFullAddress(sj.toString());
        });
    }

    private void queryUpcomingCustomerPetInfo(List<CustomerUpComingAppointDTO> customerUpComingAppoint) {
        List<Integer> petIds = new ArrayList<>();
        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            if (CollectionUtils.isEmpty(customerUpComingAppointDTO.getPetDetails())) {
                continue;
            }

            for (CustomerUpcomingPetDetailDTO waitingListPetDetailDTO : customerUpComingAppointDTO.getPetDetails()) {
                petIds.add(waitingListPetDetailDTO.getPetId());
            }
        }
        List<CustomerPetDetailDTO> customerPetListByIdList = iPetClient.getCustomerPetListByIdList(petIds);

        for (CustomerUpComingAppointDTO customerUpComingAppointDTO : customerUpComingAppoint) {
            if (CollectionUtils.isEmpty(customerUpComingAppointDTO.getPetDetails())) {
                continue;
            }

            for (CustomerUpcomingPetDetailDTO waitingListPetDetailDTO : customerUpComingAppointDTO.getPetDetails()) {
                for (CustomerPetDetailDTO customerPetDetail : customerPetListByIdList) {
                    if (customerPetDetail.getPetId().equals(waitingListPetDetailDTO.getPetId())) {
                        waitingListPetDetailDTO.setPetName(customerPetDetail.getPetName());
                        waitingListPetDetailDTO.setPetBreed(customerPetDetail.getBreed());
                        break;
                    }
                }
            }
        }
    }

    private void queryUpcomingCustomerStaffInfo(List<CustomerUpComingAppointDTO> customerUpComingAppoint) {
        CustomerUpComingAppointDTO customerUpComingAppointDTO = customerUpComingAppoint.get(0);
        List<Integer> staffIds = new ArrayList<>();
        for (CustomerUpComingAppointDTO temp : customerUpComingAppoint) {
            staffIds.add(temp.getCreateBy());
            for (CustomerUpcomingPetDetailDTO petDetail : temp.getPetDetails()) {
                staffIds.add(petDetail.getStaffId());
            }
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(customerUpComingAppointDTO.getBusinessId());
        staffIdListParams.setStaffIdList(staffIds);

        List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);

        for (CustomerUpComingAppointDTO temp : customerUpComingAppoint) {
            for (MoeStaffDto moeStaffDto : staffList) {
                if (temp.getCreateBy().equals(moeStaffDto.getId())) {
                    temp.setCreateByFirstName(moeStaffDto.getFirstName());
                    temp.setCreateByLastName(moeStaffDto.getLastName());
                    break;
                }
            }

            if (CollectionUtils.isEmpty(temp.getPetDetails())) {
                continue;
            }

            for (CustomerUpcomingPetDetailDTO waitingListPetDetailDTO : temp.getPetDetails()) {
                for (MoeStaffDto moeStaffDto : staffList) {
                    if (moeStaffDto.getId().equals(waitingListPetDetailDTO.getStaffId())) {
                        waitingListPetDetailDTO.setStaffFirstName(moeStaffDto.getFirstName());
                        waitingListPetDetailDTO.setStaffLastName(moeStaffDto.getLastName());
                        break;
                    }
                }
            }
        }
    }

    static List<CustomerGroomingAppointmentDTO> filterRebookReminderPageByCustomer(
            String businessCurDate,
            List<CustomerGroomingAppointmentDTO> lastServiceAppts,
            Map<Integer, MoeBusinessCustomerDTO> customerMap,
            Integer isBefore) {
        LocalDate startDate = LocalDate.parse(businessCurDate).plusDays(isBefore);
        LocalDate endDate = startDate.plusDays(ReminderTypeEnum.REBOOK.getQueryRecentlyDays());

        List<CustomerGroomingAppointmentDTO> result = new ArrayList<>();
        for (CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO : lastServiceAppts) {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO =
                    customerMap.get(customerGroomingAppointmentDTO.getCustomerId());

            if (moeBusinessCustomerDTO == null
                    || BooleanEnum.INACTIVE_TRUE.equals(moeBusinessCustomerDTO.getInactive())
                    || !StringUtils.hasText(customerGroomingAppointmentDTO.getAppointmentDate())) {
                continue;
            }

            LocalDate appointmentDate = LocalDate.parse(customerGroomingAppointmentDTO.getAppointmentDate());
            LocalDate expectedServiceDate = appointmentDate.plusDays(moeBusinessCustomerDTO.getPreferredFrequencyDay());

            // 在start和end范围内,则返回
            if (expectedServiceDate.isAfter(startDate.minusDays(1))
                    && expectedServiceDate.isBefore(endDate.plusDays(1))) {

                result.add(customerGroomingAppointmentDTO);
            }
        }
        return result;
    }

    static List<CustomerRebookReminderDTO> buildCustomerRebookReminderDTOPageList(
            List<CustomerGroomingAppointmentDTO> lastServiceAppts, Map<Integer, MoeBusinessCustomerDTO> customerMap) {
        List<CustomerRebookReminderDTO> customerRebookReminderDTOS = new LinkedList<>();
        for (CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO : lastServiceAppts) {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO =
                    customerMap.get(customerGroomingAppointmentDTO.getCustomerId());

            if (moeBusinessCustomerDTO == null) {
                continue;
            }

            LocalDate appointmentDate = LocalDate.parse(customerGroomingAppointmentDTO.getAppointmentDate());
            LocalDate expectedServiceDate = appointmentDate.plusDays(moeBusinessCustomerDTO.getPreferredFrequencyDay());

            CustomerRebookReminderDTO customerRebookReminderDTO = new CustomerRebookReminderDTO();

            customerRebookReminderDTO.setAppointmentDate(customerGroomingAppointmentDTO.getAppointmentDate());
            customerRebookReminderDTO.setAppointmentStartTime(customerGroomingAppointmentDTO.getAppointmentStartTime());
            customerRebookReminderDTO.setAppointmentEndTime(customerGroomingAppointmentDTO.getAppointmentEndTime());
            customerRebookReminderDTO.setBusinessId(customerGroomingAppointmentDTO.getBusinessId());
            customerRebookReminderDTO.setCustomerId(customerGroomingAppointmentDTO.getCustomerId());
            customerRebookReminderDTO.setGroomingId(customerGroomingAppointmentDTO.getId());
            customerRebookReminderDTO.setExpectedServiceDate(expectedServiceDate.toString());

            customerRebookReminderDTO.setCustomerFirstName(moeBusinessCustomerDTO.getFirstName());
            customerRebookReminderDTO.setCustomerLastName(moeBusinessCustomerDTO.getLastName());
            customerRebookReminderDTO.setPreferredFrequencyDay(moeBusinessCustomerDTO.getPreferredFrequencyDay());
            customerRebookReminderDTO.setAvatarPath(moeBusinessCustomerDTO.getAvatarPath());

            customerRebookReminderDTOS.add(customerRebookReminderDTO);
        }
        return customerRebookReminderDTOS;
    }

    public PageDTO<MoeGroomingAppointmentDTO> queryRepeatAppointmentReminder(
            RepeatReminderParams repeatReminderParams) {
        final Integer businessId = repeatReminderParams.getBusinessId();
        List<Integer> dismissIds = repeatReminderParams.getDismissIds();

        String startDate = repeatReminderParams.getStartDate();
        String endDate = repeatReminderParams.getEndDate();
        if (Objects.isNull(businessId)) {
            return null;
        }

        // 查询当日之后还有两个repeat预约的repeatId
        List<Integer> repeatIds = moeGroomingAppointmentMapper.queryRepeatIdOnlyTwoGrooming(businessId, startDate);

        if (CollectionUtils.isEmpty(repeatIds)) {
            return PageDTO.createEmpty();
        }
        // 查询这些repeat中customer在最后一个repeat预约
        List<RepeatNumDTO> repeatLastestAppointment =
                moeGroomingAppointmentMapper.queryRepeatLastestAppointment(businessId, repeatIds);

        // 查询customer最后一个预约是否是repeat预约
        List<Integer> customerIds = new ArrayList<>(repeatLastestAppointment.size() * 2);
        repeatLastestAppointment.forEach(e -> customerIds.add(e.getCustomerId()));

        List<RepeatNumDTO> customerLastestAppointment =
                moeGroomingAppointmentMapper.queryCustomerLastestAppointment(businessId, customerIds, startDate);

        // 比对customer 最后一个预约是否是repeat预约,如果不是，从repeatIds中移除
        for (RepeatNumDTO customer : customerLastestAppointment) {
            for (RepeatNumDTO repeat : repeatLastestAppointment) {
                if (customer.getCustomerId().equals(repeat.getCustomerId())) {
                    if (!repeat.getId().equals(customer.getId())) {
                        repeatIds.remove(repeat.getRepeatId());
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(repeatIds)) {
            return PageDTO.createEmpty();
        }
        // 查询repeat倒数第二个预约在最近七天内的预约
        PageDTO<MoeGroomingAppointment> pageDTO = MybatisUtils.queryPage(repeatReminderParams, new Callback() {
            @Override
            public List<MoeGroomingAppointment> call(Object[] objects) {
                return moeGroomingAppointmentMapper.selectBusinessRepeatAppointmentReminder(
                        businessId, dismissIds, repeatIds, startDate, endDate);
            }
        });
        List<MoeGroomingAppointment> appointments = pageDTO.getDataList();
        if (CollectionUtils.isEmpty(appointments)) {
            return PageDTO.createEmpty();
        }
        List<MoeGroomingAppointmentDTO> appointmentDtos = new ArrayList<>(appointments.size());
        for (MoeGroomingAppointment appointment : appointments) {
            MoeGroomingAppointmentDTO dto = new MoeGroomingAppointmentDTO();
            BeanUtils.copyProperties(appointment, dto);
            appointmentDtos.add(dto);
        }
        return PageDTO.create(appointmentDtos, pageDTO.getTotal(), pageDTO.getPageNo(), pageDTO.getPageSize());
    }

    public PageDTO<CustomerRebookReminderDTO> getCustomerRebookReminderList(
            QueryReminderRebookParams queryReminderRebookParams) {
        Integer businessId = queryReminderRebookParams.getBusinessId();
        Long companyId = queryReminderRebookParams.getCompanyId();

        // 1. 查询 business/company 时区信息
        var companyDateTime = companyHelper.getCompanyDateTime(companyId);
        if (companyDateTime == null) {
            return PageDTO.createEmpty();
        }

        // 2. 过滤掉 company 下有 upcoming 预约的 customer
        List<Integer> dismissCustomerIds =
                new ArrayList<>(moeGroomingAppointmentMapper.queryFutureAppointmentCustomerIdListByCompany(
                        companyId, companyDateTime.getCurrentDate(), companyDateTime.getCurrentMinutes()));

        // 3. 查询 business 下的 customer 最近一次历史预约时间
        if (!CollectionUtils.isEmpty(queryReminderRebookParams.getDismissIds())) {
            dismissCustomerIds.addAll(queryReminderRebookParams.getDismissIds());
        }
        List<CustomerGroomingAppointmentDTO> businessLastServiceAppts =
                moeGroomingAppointmentMapper.selectCustomerLastServiceTime(
                        businessId,
                        companyDateTime.getCurrentDate(),
                        companyDateTime.getCurrentMinutes(),
                        dismissCustomerIds);
        if (CollectionUtils.isEmpty(businessLastServiceAppts)) {
            return PageDTO.createEmpty();
        }

        // 4. 查询 customer 信息. 用于后续过滤与返回数据组装
        CustomerIdListParams idListParams = new CustomerIdListParams();
        idListParams.setIdList(businessLastServiceAppts.stream()
                .map(CustomerGroomingAppointmentDTO::getCustomerId)
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList());
        Map<Integer, MoeBusinessCustomerDTO> customerMap =
                iCustomerCustomerClient.queryCustomerList(idListParams).stream()
                        .collect(Collectors.toMap(MoeBusinessCustomerDTO::getCustomerId, Function.identity()));

        // 5. 按 customer preferred frequency 过滤
        if (queryReminderRebookParams.getQueryType()) {
            businessLastServiceAppts = filterRebookReminderPageByCustomer(
                    companyDateTime.getCurrentDate(),
                    businessLastServiceAppts,
                    customerMap,
                    queryReminderRebookParams.getIsBefore());
        } else {
            businessLastServiceAppts = filterRebookReminderSendListByCustomer(
                    companyDateTime.getCurrentDate(),
                    businessLastServiceAppts,
                    customerMap,
                    queryReminderRebookParams.getIsBefore());
        }

        // 6. 过滤掉 pet 均 pass away 的预约
        businessLastServiceAppts = filterPassAwayPetAppointment(businessLastServiceAppts);

        // 7. 过滤掉 company 下最近一次预约非该 business 的预约
        List<Integer> customerIds = businessLastServiceAppts.stream()
                .map(CustomerGroomingAppointmentDTO::getCustomerId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerIds)) {
            return PageDTO.createEmpty();
        }
        List<CustomerGroomingAppointmentDTO> lastServiceAppts = moeGroomingAppointmentMapper
                .selectCustomerLastServiceTimeByCompany(
                        companyId.intValue(),
                        companyDateTime.getCurrentDate(),
                        companyDateTime.getCurrentMinutes(),
                        customerIds)
                .stream()
                .filter(e -> e.getBusinessId().equals(businessId))
                .collect(Collectors.toList());

        // 8. 组装返回数据
        if (queryReminderRebookParams.getQueryType()) {
            List<CustomerRebookReminderDTO> customerRebookReminderDTOS =
                    buildCustomerRebookReminderDTOPageList(lastServiceAppts, customerMap);
            return PageDTO.create(
                    customerRebookReminderDTOS,
                    customerRebookReminderDTOS.size(),
                    queryReminderRebookParams.getPageNo(),
                    queryReminderRebookParams.getPageSize());
        } else {
            List<CustomerRebookReminderDTO> customerRebookReminderDTOS =
                    buildCustomerRebookReminderDTOSendList(lastServiceAppts, customerMap);
            return PageDTO.create(
                    customerRebookReminderDTOS,
                    customerRebookReminderDTOS.size(),
                    1,
                    customerRebookReminderDTOS.size());
        }
    }

    public PageDTO<CustomerRebookReminderDTO> getSpecificSupportCustomerRebookReminderList(
            QuerySpecificSupportReminderRebookParams queryParams) {
        Integer businessId = queryParams.getBusinessId();
        Long companyId = queryParams.getCompanyId();

        // 1. 查询 business/company 时区信息
        var companyDateTime = companyHelper.getCompanyDateTime(companyId);
        if (companyDateTime == null) {
            return PageDTO.createEmpty();
        }

        // 2. 过滤掉 company 下有 upcoming 预约的 customer
        List<Integer> dismissCustomerIds =
                new ArrayList<>(moeGroomingAppointmentMapper.queryFutureAppointmentCustomerIdListByCompany(
                        companyId, companyDateTime.getCurrentDate(), companyDateTime.getCurrentMinutes()));

        // 3. 查询 business 下的 customer 最近一次历史预约时间
        if (!CollectionUtils.isEmpty(queryParams.getDismissIds())) {
            dismissCustomerIds.addAll(queryParams.getDismissIds());
        }
        List<CustomerGroomingAppointmentDTO> businessLastServiceAppts =
                moeGroomingAppointmentMapper.selectCustomerLastServiceTime(
                        businessId,
                        companyDateTime.getCurrentDate(),
                        companyDateTime.getCurrentMinutes(),
                        dismissCustomerIds);
        if (CollectionUtils.isEmpty(businessLastServiceAppts)) {
            return PageDTO.createEmpty();
        }

        // 4. 查询 customer 信息. 用于后续过滤与返回数据组装
        CustomerIdListParams idListParams = new CustomerIdListParams();
        idListParams.setIdList(businessLastServiceAppts.stream()
                .map(CustomerGroomingAppointmentDTO::getCustomerId)
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList());
        Map<Integer, MoeBusinessCustomerDTO> customerMap =
                iCustomerCustomerClient.queryCustomerList(idListParams).stream()
                        .collect(Collectors.toMap(MoeBusinessCustomerDTO::getCustomerId, Function.identity()));

        // 5. 按 customer preferred frequency 过滤
        businessLastServiceAppts = filterSpecificSupportRebookReminderSendListByCustomer(
                companyDateTime.getCurrentDate(), businessLastServiceAppts, customerMap, queryParams.getCycleDays());

        // 6. 过滤掉 pet 均 pass away 的预约
        businessLastServiceAppts = filterPassAwayPetAppointment(businessLastServiceAppts);

        // 7. 过滤掉 company 下最近一次预约非该 business 的预约
        List<Integer> customerIds = businessLastServiceAppts.stream()
                .map(CustomerGroomingAppointmentDTO::getCustomerId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerIds)) {
            return PageDTO.createEmpty();
        }
        List<CustomerGroomingAppointmentDTO> lastServiceAppts = moeGroomingAppointmentMapper
                .selectCustomerLastServiceTimeByCompany(
                        companyId.intValue(),
                        companyDateTime.getCurrentDate(),
                        companyDateTime.getCurrentMinutes(),
                        customerIds)
                .stream()
                .filter(e -> e.getBusinessId().equals(businessId))
                .collect(Collectors.toList());

        List<CustomerRebookReminderDTO> customerRebookReminderDTOS =
                buildCustomerRebookReminderDTOSendList(lastServiceAppts, customerMap);

        return PageDTO.create(
                customerRebookReminderDTOS, customerRebookReminderDTOS.size(), 1, customerRebookReminderDTOS.size());
    }

    // 忽略 customer preferred frequency，按照周期发送
    static List<CustomerGroomingAppointmentDTO> filterSpecificSupportRebookReminderSendListByCustomer(
            String businessCurDate,
            List<CustomerGroomingAppointmentDTO> lastServiceAppts,
            Map<Integer, MoeBusinessCustomerDTO> customerMap,
            Integer cycleDays) {
        LocalDate now = LocalDate.parse(businessCurDate);
        List<CustomerGroomingAppointmentDTO> result = new ArrayList<>();
        for (CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO : lastServiceAppts) {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO =
                    customerMap.get(customerGroomingAppointmentDTO.getCustomerId());

            if (moeBusinessCustomerDTO == null) {
                continue;
            }

            LocalDate appointmentDate = LocalDate.parse(customerGroomingAppointmentDTO.getAppointmentDate());
            long diffDays = ChronoUnit.DAYS.between(appointmentDate, now);

            // 符合发送周期则返回
            if (diffDays >= cycleDays && diffDays % cycleDays == 0) {

                result.add(customerGroomingAppointmentDTO);
            }
        }
        return result;
    }

    // 检查 customer 信息 preferred frequency
    static List<CustomerGroomingAppointmentDTO> filterRebookReminderSendListByCustomer(
            String businessCurDate,
            List<CustomerGroomingAppointmentDTO> lastServiceAppts,
            Map<Integer, MoeBusinessCustomerDTO> customerMap,
            Integer isBefore) {
        LocalDate now = LocalDate.parse(businessCurDate);
        List<CustomerGroomingAppointmentDTO> result = new ArrayList<>();
        for (CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO : lastServiceAppts) {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO =
                    customerMap.get(customerGroomingAppointmentDTO.getCustomerId());

            if (moeBusinessCustomerDTO == null) {
                continue;
            }

            LocalDate appointmentDate = LocalDate.parse(customerGroomingAppointmentDTO.getAppointmentDate());
            LocalDate expectedServiceDate = appointmentDate.plusDays(moeBusinessCustomerDTO.getPreferredFrequencyDay());

            LocalDate sendTime = expectedServiceDate.minusDays(isBefore);

            // 符合发送时间,则返回
            if (sendTime.equals(now)) {
                result.add(customerGroomingAppointmentDTO);
            }
        }
        return result;
    }

    static List<CustomerRebookReminderDTO> buildCustomerRebookReminderDTOSendList(
            List<CustomerGroomingAppointmentDTO> lastServiceAppts, Map<Integer, MoeBusinessCustomerDTO> customerMap) {
        List<CustomerRebookReminderDTO> customerRebookReminderDTOS = new LinkedList<>();
        for (CustomerGroomingAppointmentDTO customerGroomingAppointmentDTO : lastServiceAppts) {
            MoeBusinessCustomerDTO moeBusinessCustomerDTO =
                    customerMap.get(customerGroomingAppointmentDTO.getCustomerId());

            if (moeBusinessCustomerDTO == null) {
                continue;
            }

            LocalDate appointmentDate = LocalDate.parse(customerGroomingAppointmentDTO.getAppointmentDate());
            LocalDate expectedServiceDate = appointmentDate.plusDays(moeBusinessCustomerDTO.getPreferredFrequencyDay());

            CustomerRebookReminderDTO customerRebookReminderDTO = new CustomerRebookReminderDTO();

            customerRebookReminderDTO.setAppointmentDate(customerGroomingAppointmentDTO.getAppointmentDate());
            customerRebookReminderDTO.setAppointmentStartTime(customerGroomingAppointmentDTO.getAppointmentStartTime());
            customerRebookReminderDTO.setAppointmentEndTime(customerGroomingAppointmentDTO.getAppointmentEndTime());
            customerRebookReminderDTO.setBusinessId(customerGroomingAppointmentDTO.getBusinessId());
            customerRebookReminderDTO.setCustomerId(customerGroomingAppointmentDTO.getCustomerId());
            customerRebookReminderDTO.setGroomingId(customerGroomingAppointmentDTO.getId());
            customerRebookReminderDTO.setExpectedServiceDate(expectedServiceDate.toString());

            customerRebookReminderDTO.setCustomerFirstName(moeBusinessCustomerDTO.getFirstName());
            customerRebookReminderDTO.setCustomerLastName(moeBusinessCustomerDTO.getLastName());
            customerRebookReminderDTO.setPreferredFrequencyDay(moeBusinessCustomerDTO.getPreferredFrequencyDay());

            customerRebookReminderDTOS.add(customerRebookReminderDTO);
        }
        return customerRebookReminderDTOS;
    }

    /**
     * 过滤pass away pet的appointment，如果订单内有多个宠物，某个宠物pass away了，其它还是会返回
     */
    List<CustomerGroomingAppointmentDTO> filterPassAwayPetAppointment(
            List<CustomerGroomingAppointmentDTO> customerAppointmentDTOS) {
        if (CollectionUtils.isEmpty(customerAppointmentDTOS)) {
            return customerAppointmentDTOS;
        }
        Map<Integer, List<Integer>> appointmentPetIds = customerAppointmentDTOS.stream()
                .filter(k -> !CollectionUtils.isEmpty(k.getPetDetails()))
                .collect(Collectors.toMap(CustomerGroomingAppointmentDTO::getId, k -> k.getPetDetails().stream()
                        .map(CustomerGroomingAppointmentPetDetailDTO::getPetId)
                        .filter(petId -> petId > 0)
                        .collect(Collectors.toList())));

        // 获得所有pass away petId
        Set<Integer> passAwayPetSet = iPetClient
                .getCustomerPetListByIdList(appointmentPetIds.values().stream()
                        .flatMap(Collection::stream)
                        .distinct()
                        .toList())
                .stream()
                .filter(k -> k.getLifeStatus() != null
                        && CustomerPetEnum.LIFE_STATUS_DIE.equals(
                                k.getLifeStatus().byteValue()))
                .map(CustomerPetDetailDTO::getPetId)
                .collect(Collectors.toSet());

        // 如果某个appointment的所有petId都pass away，就过滤该appointment
        Set<Integer> skipApptIds = appointmentPetIds.keySet().stream()
                .filter(apptId -> passAwayPetSet.containsAll(appointmentPetIds.get(apptId)))
                .collect(Collectors.toSet());
        return customerAppointmentDTOS.stream()
                .filter(dto -> !skipApptIds.contains(dto.getId()))
                .collect(Collectors.toList());
    }

    public Map<Integer, Integer> countUpcomingApptCountForCustomers(
            Integer businessId, Collection<Integer> customerIds) {
        BusinessDateTimeDTO businessTime = Optional.ofNullable(iBusinessBusinessClient.getBusinessDateTime(businessId))
                .orElseThrow(() -> new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists"));
        return moeGroomingAppointmentMapper
                .countUpcomingApptByCustomerIds(
                        businessId, businessTime.getCurrentDate(), businessTime.getCurrentMinutes(), customerIds)
                .stream()
                .collect(Collectors.toMap(
                        CountUpcomingApptByCustomerIdsPO::getCustomerId,
                        CountUpcomingApptByCustomerIdsPO::getUpcomingApptCount,
                        (oldV, newV) -> oldV));
    }

    public Map<Integer, Integer> getApptIntervalByCustomerIds(Integer businessId, Set<Integer> customerIds) {
        InfoIdParams param = new InfoIdParams();
        param.setInfoId(businessId);
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(param);
        if (businessInfo == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business not exists");
        }

        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        List<GetLatestAppointmentDatePO> dates =
                moeGroomingAppointmentMapper.getLatestAppointmentDate(businessId, customerIds, 2, date, nowMinutes);

        Map<Integer, Integer> customerToInterval = new HashMap<>();

        dates.stream()
                .collect(groupingBy(GetLatestAppointmentDatePO::getCustomerId))
                .forEach((customerId, apptDates) -> {
                    if (apptDates.size() != 2) {
                        return;
                    }
                    GetLatestAppointmentDatePO first = apptDates.get(0);
                    GetLatestAppointmentDatePO second = apptDates.get(1);
                    if (!StringUtils.hasText(first.getAppointmentDate())
                            || !StringUtils.hasText(second.getAppointmentDate())) {
                        return;
                    }
                    int between = DateUtil.countDaysBetween(first.getAppointmentDate(), second.getAppointmentDate());
                    customerToInterval.put(customerId, Math.abs(between));
                });
        return customerToInterval;
    }

    public Integer countRepeatUpcomingCountForExpiryReminder(Integer businessId, Integer repeatId) {
        // 查询 upcoming 需要先查一下 business date & time
        BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(businessId);
        String currentDate = businessDateTime.getCurrentDate();
        Integer currentMinutes = businessDateTime.getCurrentMinutes();
        return moeGroomingAppointmentMapper.countRepeatUpcomingCountForExpiryReminder(
                businessId, repeatId, currentDate, currentMinutes);
    }

    /**
     * 查询 repeat appointment list
     *
     * @param businessId
     * @param repeatId
     * @param type 查询类型：1-history, 2-upcoming, 3-all
     * @return
     */
    public List<RepeatAppointmentDto> getRepeatAppointmentList(
            Integer businessId, Integer repeatId, Byte type, Boolean includeFinish) {
        if (businessId == null || repeatId == null) {
            return List.of();
        }
        List<AppointmentStatusEnum> statusList = Boolean.TRUE.equals(includeFinish)
                ? AppointmentStatusSet.ACTIVE_STATUS_SET
                : AppointmentStatusSet.IN_PROGRESS_STATUS_SET;

        List<RepeatAppointmentDto> repeatAppointmentList;
        if (type == RepeatConst.REPEAT_APPOINTMENT_TYPE_ALL) {
            // 查询全部
            repeatAppointmentList =
                    moeGroomingAppointmentMapper.selectRepeatAppointmentList(businessId, repeatId, statusList);
        } else {
            // 查询 history or upcoming
            BusinessDateTimeDTO businessDateTime = iBusinessBusinessClient.getBusinessDateTime(businessId);
            String currentDate = businessDateTime.getCurrentDate();
            Integer currentMinutes = businessDateTime.getCurrentMinutes();
            repeatAppointmentList = moeGroomingAppointmentMapper.selectRepeatAppointmentListByType(
                    businessId, repeatId, statusList, currentDate, currentMinutes, type);
        }
        if (CollectionUtils.isEmpty(repeatAppointmentList)) {
            return List.of();
        }
        // 查询预约的 staffIdList
        List<Integer> appointmentIds = repeatAppointmentList.stream()
                .map(RepeatAppointmentDto::getAppointmentId)
                .distinct()
                .toList();
        Map<Integer, List<Integer>> staffIdMap = moePetDetailService.getGroomingStaffIdListMap(appointmentIds);
        repeatAppointmentList.forEach(
                appt -> appt.setStaffIdList(staffIdMap.getOrDefault(appt.getAppointmentId(), List.of())));
        return repeatAppointmentList;
    }

    public RepeatAppointmentDto getRepeatAppointmentById(Integer businessId, Integer appointmentId) {
        RepeatAppointmentDto repeatAppointment =
                moeGroomingAppointmentMapper.selectRepeatAppointmentById(businessId, appointmentId);
        if (repeatAppointment == null) {
            return null;
        }
        Map<Integer, List<Integer>> staffIdMap = moePetDetailService.getGroomingStaffIdListMap(List.of(appointmentId));
        repeatAppointment.setStaffIdList(staffIdMap.getOrDefault(appointmentId, List.of()));
        return repeatAppointment;
    }

    public MoeGroomingAppointment getAppointmentById(Integer businessId, Integer appointmentId) {
        return moeGroomingAppointmentMapper.selectByPrimaryKeyAndBusinessId(appointmentId, businessId);
    }

    public Set<Integer> getAppointmentRelatedStaffIds(Integer appointmentId) {
        Set<Integer> staffIds = new HashSet<>(moeGroomingPetDetailMapper.queryStaffIdByGroomingId(appointmentId));
        staffIds.addAll(groomingServiceOperationService.queryStaffIdByGroomingId(appointmentId));
        return staffIds;
    }
}
