package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGcCalendar;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGcCalendarMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    int insert(MoeGcCalendar record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    int insertSelective(MoeGcCalendar record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    MoeGcCalendar selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGcCalendar record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_gc_calendar
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGcCalendar record);

    List<MoeGcCalendar> selectByStaffIdSettingId(
            @Param("staffId") Integer staffId, @Param("settingId") Integer settingId);

    List<MoeGcCalendar> selectBySyncedStaffIds(
            @Param("businessId") Integer businessId, @Param("syncedStaffs") List<Integer> syncedStaffs);

    List<MoeGcCalendar> selectBySyncedStaffId(
            @Param("businessId") Integer businessId, @Param("syncedStaffId") Integer syncedStaffId);

    int updateSyncTokenByPrimaryKey(@Param("gcCalendarId") Integer gcCalendarId, @Param("syncToken") String syncToken);

    List<MoeGcCalendar> selectByBusinessId(@Param("businessId") Integer businessId);
}
