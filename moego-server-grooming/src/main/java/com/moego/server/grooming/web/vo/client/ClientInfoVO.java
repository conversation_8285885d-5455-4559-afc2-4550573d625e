package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/15
 */
@Data
@Accessors(chain = true)
public class ClientInfoVO {

    @Schema(description = "Is block ob")
    private Boolean isBlockOnlineBooking;

    @Schema(description = "Is block message")
    private Boolean isBlockMessage;
}
