package com.moego.server.grooming.service.ob;

import com.moego.common.utils.WeekUtil;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.grooming.listener.event.UpgradeLandingPageEvent;
import com.moego.server.grooming.mapper.MoeBookOnlineLandingPageConfigMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineProfileMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineLandingPageConfig;
import com.moego.server.grooming.mapperbean.MoeBookOnlineProfile;
import com.moego.server.grooming.web.params.OBLandingPageMergeParams;
import com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBLandingPageUpgradeService {

    private final MoeBookOnlineProfileMapper bookOnlineProfileMapper;

    private final MoeBookOnlineLandingPageConfigMapper landingPageConfigMapper;

    private final OBBusinessService businessService;

    private final ApplicationEventPublisher eventPublisher;

    public OBLandingPageMergeVO getLandingPageMergeProfile(Integer businessId) {
        // OB profile
        MoeBookOnlineProfile bookOnlineProfile = bookOnlineProfileMapper.selectByBusinessId(businessId);
        OBLandingPageMergeVO.OBProfileMergeVO obProfileMergeVO = new OBLandingPageMergeVO.OBProfileMergeVO();
        BeanUtils.copyProperties(bookOnlineProfile, obProfileMergeVO);
        String businessHoursJson = bookOnlineProfile.getBusinessHoursJson();
        Map<String, StaffTime> weekTimeMap = JsonUtil.toBean(businessHoursJson, new TypeRef<>() {});
        StaffTime monday = weekTimeMap.get(WeekUtil.KEY_OF_MONDAY);
        StaffTime tuesday = weekTimeMap.get(WeekUtil.KEY_OF_TUESDAY);
        StaffTime wednesday = weekTimeMap.get(WeekUtil.KEY_OF_WEDNESDAY);
        StaffTime thursday = weekTimeMap.get(WeekUtil.KEY_OF_THURSDAY);
        StaffTime friday = weekTimeMap.get(WeekUtil.KEY_OF_FRIDAY);
        StaffTime saturday = weekTimeMap.get(WeekUtil.KEY_OF_SATURDAY);
        StaffTime sunday = weekTimeMap.get(WeekUtil.KEY_OF_SATURDAY);
        BusinessWorkingHourDayDetailDTO obWorkingHours = new BusinessWorkingHourDayDetailDTO();
        obWorkingHours.setMonday(
                Objects.nonNull(monday) && BooleanUtils.isTrue(monday.getIsSelected())
                        ? monday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setTuesday(
                Objects.nonNull(tuesday) && BooleanUtils.isTrue(tuesday.getIsSelected())
                        ? tuesday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setWednesday(
                Objects.nonNull(wednesday) && BooleanUtils.isTrue(wednesday.getIsSelected())
                        ? wednesday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setThursday(
                Objects.nonNull(thursday) && BooleanUtils.isTrue(thursday.getIsSelected())
                        ? thursday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setFriday(
                Objects.nonNull(friday) && BooleanUtils.isTrue(friday.getIsSelected())
                        ? friday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setSaturday(
                Objects.nonNull(saturday) && BooleanUtils.isTrue(saturday.getIsSelected())
                        ? saturday.getTimeRange()
                        : Collections.emptyList());
        obWorkingHours.setSunday(
                Objects.nonNull(sunday) && BooleanUtils.isTrue(sunday.getIsSelected())
                        ? sunday.getTimeRange()
                        : Collections.emptyList());
        // Biz settings
        OBBusinessInfoDTO businessInfo = businessService.getBusinessInfo(businessId);
        OBLandingPageMergeVO.BizProfileMergeVO bizProfileMergeVO = new OBLandingPageMergeVO.BizProfileMergeVO();
        BeanUtils.copyProperties(businessInfo, bizProfileMergeVO);
        BusinessWorkingHourDetailDTO workingHourDetailDTO = businessService.getBusinessWorkingHours(businessId);
        return new OBLandingPageMergeVO()
                .setObProfile(obProfileMergeVO)
                .setBizProfile(bizProfileMergeVO)
                .setObWorkingHours(obWorkingHours)
                .setBizWorkingHours(workingHourDetailDTO.getTimeData());
    }

    /**
     * Upgrade landing page 3.0 <br>
     *
     * <p> Update biz info in moe_business table and flush cache </p>
     *
     * <p> Initialize landing page </p>
     * 1. Inherited moe_book_online_profile.description to moe_book_online_landing_page_config.about_us <br>
     * 2. Inherited moe_business_book_online.description to moe_book_online_landing_page_config.welcome_page_message <br>
     * 3. Inherited moe_book_online_profile.button_color to moe_book_online_landing_page_config.theme_color <br>
     * 4. Inherited ob gallery in moe_book_online_gallery to moe_book_online_landing_page_gallery <br>
     *
     * @param businessId             biz ID
     * @param landingPageMergeParams biz info
     */
    @Transactional(rollbackFor = Exception.class)
    public void upgradeLandingPage(
            Integer businessId, Long companyId, OBLandingPageMergeParams landingPageMergeParams) {
        MoeBookOnlineLandingPageConfig landingPageConfig = landingPageConfigMapper.selectByBusinessId(businessId);
        if (Objects.nonNull(landingPageConfig)) {
            log.warn("biz: [{}] has been upgraded landing page 3.0", businessId);
            return;
        }
        log.info("biz: [{}] start upgrade landing page 3.0", businessId);
        eventPublisher.publishEvent(new UpgradeLandingPageEvent(this)
                .setBusinessId(businessId)
                .setCompanyId(companyId)
                .setMergeParams(landingPageMergeParams));
    }
}
