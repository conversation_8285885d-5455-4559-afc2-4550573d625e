package com.moego.server.grooming.service.dto;

import com.moego.server.grooming.enums.StaffAvailableTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OBAvailableDateTimeDTO {

    private Map<String, Boolean[]> availableDates;

    private Map<String, List<Integer>> am;

    private Map<String, List<Integer>> pm;

    /**
     * 0 - Not selected staff
     * 1 - Selected staff available
     * 2 - Some staffs available
     * 3 - Other staffs available
     * 4 - No staff available
     */
    @Schema(description = "员工可用的类型")
    private StaffAvailableTypeEnum staffAvailableType;

    @Schema(description = "选择员工时展示，表示选择的员工有单个 slot 满足的日期及时间")
    private List<OBAvailableTimeDetailDTO> selectedStaffSingleAvailableDateTimes;

    @Schema(description = "选择单个员工时展示，或者作为单个员工有单个 slot 满足的日期及时间的推荐结果")
    private List<OBAvailableTimeDetailDTO> sameStaffSingleAvailableDateTimes;

    @Schema(description = "选择单个员工时展示，或者作为单个员工有连续 slot 排列的日期及时间的推荐结果")
    private List<OBAvailableTimeDetailDTO> sameStaffSequenceAvailableDateTimes;

    @Schema(description = "没有选择员工时展示，表示所有可用的日期及时间")
    private List<OBAvailableTimeDetailDTO> allAvailableDateTimes;

    @Schema(description = "其他可用的员工，只有当 staffAvailableType 为 Some staffs available 或 Other staffs available 时才有值")
    private List<OBAvailableGroomerDateTimeDTO> availableGroomerDateTimes;

    @Schema(description = "员工可用的日期及时间，兼容老接口")
    private Map<String, OBAvailableTimeDto> availableDateTimes;
}
