package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.grooming.enums.LandingPageComponentEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Storefront showcase component
 *
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ShowcaseComponentVO extends BaseComponentVO {

    @Schema(description = "Showcase before image url")
    private String beforeImage;

    @Schema(description = "Showcase after image url")
    private String afterImage;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.SHOWCASE.getComponent();
    }
}
