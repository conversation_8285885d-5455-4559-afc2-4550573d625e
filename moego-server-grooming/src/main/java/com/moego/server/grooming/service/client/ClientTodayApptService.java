package com.moego.server.grooming.service.client;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.AppointmentMapper;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.params.AppointmentDetailParams;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/10/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClientTodayApptService {

    private final IBusinessBusinessClient businessClient;
    private final MoeGroomingAppointmentMapper appointmentMapper;
    private final MoeGroomingPetDetailMapper petDetailMapper;

    public List<AppointmentDetailClientPortalDTO> getTodayApptList(AppointmentDetailParams params) {
        List<BaseBusinessCustomerIdDTO> linkCustomers = params.getLinkCustomers();
        List<Integer> linkBusinessIds = linkCustomers.stream()
                .map(BaseBusinessCustomerIdDTO::getBusinessId)
                .toList();
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap = businessClient.listBusinessDateTime(linkBusinessIds);
        List<CompletableFuture<List<MoeGroomingAppointment>>> futures = linkCustomers.stream()
                .map(customer -> {
                    BusinessDateTimeDTO dateTimeDTO = businessDateTimeDTOMap.get(customer.getBusinessId());
                    MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
                    example.createCriteria()
                            .andBusinessIdEqualTo(customer.getBusinessId())
                            .andCustomerIdEqualTo(customer.getCustomerId())
                            .andIsWaitingListEqualTo(GroomingAppointmentEnum.NOT_WAITING_LIST)
                            .andIsDeprecateEqualTo(GroomingAppointmentEnum.IS_DEPRECATE_FALSE)
                            .andIsBlockEqualTo(ClientApptConst.IS_BLOCK_FALSE.intValue())
                            .andAppointmentDateEqualTo(dateTimeDTO.getCurrentDate())
                            .andStatusIn(AppointmentStatusSet.ACTIVE_STATUS_SET.stream()
                                    .map(AppointmentStatusEnum::getValue)
                                    .toList())
                            .andServiceTypeIncludeEqualTo(ServiceItemType.GROOMING_VALUE);
                    // TODO BD 待确定是否筛选 Grooming only service
                    return CompletableFuture.supplyAsync(
                            () -> appointmentMapper.selectByExample(example), ThreadPool.getSubmitExecutor());
                })
                .toList();
        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();
        List<MoeGroomingAppointment> apptList =
                futures.stream().flatMap(future -> future.join().stream()).toList();
        if (CollectionUtils.isEmpty(apptList)) {
            return List.of();
        }
        List<Integer> apptIdList =
                apptList.stream().map(MoeGroomingAppointment::getId).toList();
        List<MoeGroomingPetDetail> petDetailList = petDetailMapper.selectPetDetailByGroomingIdList(apptIdList);
        Map<Integer, List<MoeGroomingPetDetail>> groomingPetDetailsMap =
                petDetailList.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        return apptList.stream()
                .map(appt -> {
                    AppointmentDetailClientPortalDTO dto = AppointmentMapper.INSTANCE.entityToDto(appt);
                    dto.setPetDetails(PetDetailConverter.INSTANCE.entityToDto(groomingPetDetailsMap.get(appt.getId())));
                    return dto;
                })
                .toList();
    }
}
