package com.moego.server.grooming.service.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.pulsar.shade.io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@Data
public class ServiceCategoryUpdateDto {

    @ApiModelProperty("可以为 null，为 null 则是创建")
    private Integer categoryId;

    @NotNull
    private String name;

    @NotNull
    private Byte type;

    private Integer serviceItemType;
}
