package com.moego.server.grooming.web;

import com.moego.common.distributed.BWListManager;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.dto.TipSplitDetailDTO;
import com.moego.server.grooming.params.PreviewSplitTipParams;
import com.moego.server.grooming.service.SplitTipsService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grooming/order")
public class OrderController {

    @Autowired
    private SplitTipsService splitTipsService;

    @Autowired
    private BWListManager bwListManager;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @GetMapping("/tipSplit/detail")
    @Auth(AuthType.BUSINESS)
    public TipSplitDetailDTO getTipSplitDetail(AuthContext context, @RequestParam Long orderId) {
        return splitTipsService.getTipSplitDetail(context.getBusinessId(), orderId);
    }

    @PostMapping("/tipSplit/preview")
    @Auth(AuthType.BUSINESS)
    public TipSplitDetailDTO previewTipSplitDetail(AuthContext context, @RequestBody PreviewSplitTipParams params) {
        return splitTipsService.previewTipSplitDetail(context.getBusinessId(), params);
    }

    @GetMapping("/whitelist/check")
    @Auth(AuthType.COMPANY)
    public Boolean isInWhitelist(AuthContext context, @RequestParam Integer businessId) {
        return bwListManager.isInWhiteList(BWListManager.ORDER_REINVENT, String.valueOf(businessId));
    }
}
