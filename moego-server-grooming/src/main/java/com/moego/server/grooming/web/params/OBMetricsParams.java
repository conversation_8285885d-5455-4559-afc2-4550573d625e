package com.moego.server.grooming.web.params;

import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.enums.OBMetricsEnum;
import com.moego.server.grooming.enums.OBMetricsTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2023/5/22
 */
public record OBMetricsParams(
        @NotNull @DateTimeFormat(pattern = DateUtil.STANDARD_DATE_TIME) String startDate,
        @NotNull @DateTimeFormat(pattern = DateUtil.STANDARD_DATE_TIME) String endDate,
        @NotEmpty @Schema(description = "Metrics name") List<OBMetricParams> metrics) {
    public record OBMetricParams(
            @Schema(description = "Metrics name") OBMetricsEnum name,
            @Schema(description = "Metrics type, sum") List<OBMetricsTypeEnum> types) {}
}
