package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.organization.v1.LocationDateTimeDef;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetWorkingLocationDateTimeRequest;
import com.moego.idl.service.organization.v1.GetWorkingLocationDateTimeResponse;
import com.moego.server.grooming.helper.NewOrderHelper;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingNoteMapper;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.params.appointment.QuickAddAppointmentParam;
import com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2;
import com.moego.server.grooming.service.storage.AppointmentStorageService;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

@ExtendWith(MockitoExtension.class)
class AppointmentServiceV2Test {

    @Mock
    private MoeGroomingPetDetailMapper moePetDetailMapper;

    @Mock
    private MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    @Mock
    private MoeGroomingServiceOperationMapper moeGroomingServiceOperationMapper;

    @Mock
    private MoeGroomingNoteMapper moeGroomingNoteMapper;

    @Mock
    private OrderService orderService;

    @Mock
    private NewOrderHelper newOrderHelper;

    @Mock
    private MoePetDetailService moePetDetailService;

    @Mock
    private AppointmentStorageService appointmentStorageService;

    @Mock
    private MoeGroomingAppointmentService appointmentService;

    @Mock
    private BrandedAppNotificationService brandedAppNotificationService;

    @Mock
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessClient;

    @Mock
    private CalendarSyncService calendarSyncService;

    @Mock
    private ActiveMQService mqService;

    @Mock
    private GroomingApptAsyncService asyncService;

    @Mock
    private ApplicationEventPublisher publisher;

    @InjectMocks
    private AppointmentServiceV2 appointmentServiceV2;

    private static final Integer BUSINESS_ID = 1;
    private static final Long COMPANY_ID = 1L;
    private static final Long CUSTOMER_ID = 1L;
    private static final Integer STAFF_ID = 1;
    private static final LocalDate TEST_DATE = LocalDate.now();

    private QuickAddAppointmentParam quickAddParam;
    private MoeGroomingPetDetail petDetail;
    private MoeGroomingAppointment appointment;

    @BeforeEach
    void setUp() {
        // Setup quick add appointment param
        quickAddParam = QuickAddAppointmentParam.builder()
                .businessId(BUSINESS_ID)
                .companyId(COMPANY_ID)
                .tokenStaffId(STAFF_ID)
                .customerId(CUSTOMER_ID)
                .startDate(TEST_DATE.toString())
                .startTime(540) // 9:00 AM
                .petList(List.of()) // petList
                .alertNotes("Test Alert Notes")
                .ticketComment("Test Ticket Comment")
                .colorCode("1") // colorCode
                .source(1) // source
                .allPetsStartAtSameTime(true)
                .build();

        // Setup pet detail
        petDetail = new MoeGroomingPetDetail();
        petDetail.setId(1);
        petDetail.setPetId(1);
        petDetail.setStartTime(540L);
        petDetail.setEndTime(600L);

        // Setup appointment
        appointment = new MoeGroomingAppointment();
        appointment.setId(1);
        appointment.setBusinessId(BUSINESS_ID);
        appointment.setCompanyId(COMPANY_ID);
        appointment.setCustomerId(CUSTOMER_ID.intValue());
    }

    @Test
    void quickAddAppointment_WhenValidInput_ShouldCreateAppointment() {
        // Arrange
        when(moePetDetailService.buildPetDetailList(any(), anyInt(), anyString(), anyInt(), any()))
                .thenReturn(List.of(petDetail));
        when(orderService.saveOrderWhenCreatingAppointment(eq(COMPANY_ID), eq(BUSINESS_ID), any(), eq(STAFF_ID)))
                .thenReturn(1L);
        when(newOrderHelper.enableNewOrder(COMPANY_ID)).thenReturn(false);

        // Mock appointmentStorageService to return appointment with ID
        doAnswer(invocation -> {
                    MoeGroomingAppointment appointment = invocation.getArgument(0);
                    appointment.setId(1); // Set the ID
                    return null;
                })
                .when(appointmentStorageService)
                .insertAppointment(any(), any(), any());

        // Act
        Long result = appointmentServiceV2.quickAddAppointment(quickAddParam);

        // Assert
        assertThat(result).isNotNull();
        verify(appointmentStorageService).insertAppointment(any(), any(), any());
    }

    @Test
    void transferStaffUpcomingAppointment_ShouldTransferAppointments() {
        // Arrange
        TransferAppointmentParamsV2 params = new TransferAppointmentParamsV2();
        params.setSourceStaffId(1L);

        TransferAppointmentParamsV2.LocationStaffPair pair = new TransferAppointmentParamsV2.LocationStaffPair();
        pair.setBusinessId(BUSINESS_ID.longValue());
        pair.setTargetStaffId(2L);
        params.setTransferList(List.of(pair));

        LocationDateTimeDef locationDateTime = LocationDateTimeDef.newBuilder()
                .setBusinessId(BUSINESS_ID.longValue())
                .setCurrentDate(TEST_DATE.toString())
                .setCurrentMinutes(540)
                .build();

        when(businessClient.getWorkingLocationDateTime(any(GetWorkingLocationDateTimeRequest.class)))
                .thenReturn(GetWorkingLocationDateTimeResponse.newBuilder()
                        .addLocationDateTime(locationDateTime)
                        .build());

        when(moePetDetailMapper.queryTransferAppointment(anyInt(), anyInt(), anyString(), anyInt()))
                .thenReturn(List.of(1));

        // Act
        Boolean result = appointmentServiceV2.transferStaffUpcomingAppointment(params);

        // Assert
        assertThat(result).isTrue();
        verify(moePetDetailMapper).transferAppointment(anyInt(), anyInt(), anyLong(), anyList());
        verify(moeGroomingServiceOperationMapper).transferOperation(anyInt(), anyInt(), anyList());
    }
}
