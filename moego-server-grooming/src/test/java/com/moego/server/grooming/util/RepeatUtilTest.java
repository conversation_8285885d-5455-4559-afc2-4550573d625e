package com.moego.server.grooming.util;

import com.moego.common.enums.RepeatConst;
import com.moego.server.grooming.params.RepeatParams;
import com.moego.server.grooming.service.utils.RepeatUtil;
import java.time.LocalDate;
import java.time.Month;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class RepeatUtilTest {

    @Test
    public void testRepeatByDay() {
        List<LocalDate> repeatDates;
        List<LocalDate> expectedResult;

        // 以 times 结束，重复 10 次，每 10 天重复一次
        RepeatParams params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29") // 无效参数
                .setTimes(10)
                .setRepeatEvery(10)
                .setType(RepeatConst.REPEAT_END_TYPE_TIMES);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2023-12-09",
                        "2023-12-19",
                        "2023-12-29",
                        "2024-01-08",
                        "2024-01-18",
                        "2024-01-28",
                        "2024-02-07",
                        "2024-02-17",
                        "2024-02-27")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 endDate 结束，到 2024-03-28 结束，每 15 天重复一次
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-03-28")
                .setRepeatEvery(15)
                .setTimes(10) // 无效参数
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE);

        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2023-12-14",
                        "2023-12-29",
                        "2024-01-13",
                        "2024-01-28",
                        "2024-02-12",
                        "2024-02-27",
                        "2024-03-13",
                        "2024-03-28")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());
    }

    @Test
    public void testRepeatByWeek() {
        List<LocalDate> repeatDates;
        List<LocalDate> expectedResult;
        // 以 times 结束，重复 10 次，每 2 周的周五重复一次
        RepeatParams params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_WEEK)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-12-29")
                .setRepeatBy(5)
                .setType(RepeatConst.REPEAT_END_TYPE_TIMES)
                .setTimes(10)
                .setRepeatEvery(2);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-12-01",
                        "2023-12-15",
                        "2023-12-29",
                        "2024-01-12",
                        "2024-01-26",
                        "2024-02-09",
                        "2024-02-23",
                        "2024-03-08",
                        "2024-03-22",
                        "2024-04-05")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 endDate 结束，到 2024-12-29 结束，每 5 周的周二重复一次
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_WEEK)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-12-29")
                .setRepeatBy(2)
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE)
                .setRepeatEvery(5)
                .setTimes(10); // 无效参数
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-12-05",
                        "2024-01-09",
                        "2024-02-13",
                        "2024-03-19",
                        "2024-04-23",
                        "2024-05-28",
                        "2024-07-02",
                        "2024-08-06",
                        "2024-09-10",
                        "2024-10-15",
                        "2024-11-19",
                        "2024-12-24")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());
    }

    @Test
    public void testRepeatByMonthWeekDay() {
        List<LocalDate> repeatDates;
        List<LocalDate> expectedResult;

        // 以 times 结束，重复10次，每月的第2个周一
        RepeatParams params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29") // 无效参数
                .setRepeatEvery(1)
                .setTimes(10)
                .setMonthWeekTimes(2)
                .setMonthWeekDay(1)
                .setType(RepeatConst.REPEAT_END_TYPE_TIMES);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-12-11",
                        "2024-01-08",
                        "2024-02-12",
                        "2024-03-11",
                        "2024-04-08",
                        "2024-05-13",
                        "2024-06-10",
                        "2024-07-08",
                        "2024-08-12",
                        "2024-09-09")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 endDate 结束，到 2024-11-29，每月的第5个周三
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29")
                .setRepeatEvery(1)
                .setTimes(10) // 无效参数
                .setMonthWeekTimes(5)
                .setMonthWeekDay(3)
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2023-12-27",
                        "2024-01-31",
                        "2024-02-28",
                        "2024-03-27",
                        "2024-04-24",
                        "2024-05-29",
                        "2024-06-26",
                        "2024-07-31",
                        "2024-08-28",
                        "2024-09-25",
                        "2024-10-30",
                        "2024-11-27")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 endDate 结束，到 2024-11-29，每3个月的第3个周二
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_WEEKDAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29")
                .setRepeatEvery(3)
                .setTimes(10) // 无效参数
                .setMonthWeekTimes(3)
                .setMonthWeekDay(2)
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of("2024-02-20", "2024-05-21", "2024-08-20", "2024-11-19")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());
    }

    @Test
    public void testRepeatByMonthDay() {
        List<LocalDate> repeatDates;
        List<LocalDate> expectedResult;

        // 以 endDate 结束，到 2024-11-29，每月的第29天
        RepeatParams params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29")
                .setRepeatEvery(1)
                .setTimes(10) // 无效参数
                .setMonthDay(29)
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2023-12-29",
                        "2024-01-29",
                        "2024-02-29",
                        "2024-03-29",
                        "2024-04-29",
                        "2024-05-29",
                        "2024-06-29",
                        "2024-07-29",
                        "2024-08-29",
                        "2024-09-29",
                        "2024-10-29",
                        "2024-11-29")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 times 结束，重复10次，每月的第29天
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29") // 无效参数
                .setRepeatEvery(1)
                .setTimes(10)
                .setMonthDay(29)
                .setType(RepeatConst.REPEAT_END_TYPE_TIMES);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2023-12-29",
                        "2024-01-29",
                        "2024-02-29",
                        "2024-03-29",
                        "2024-04-29",
                        "2024-05-29",
                        "2024-06-29",
                        "2024-07-29",
                        "2024-08-29")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 times 结束，重复10次，每2个月的第29天
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-11-29") // 无效参数
                .setRepeatEvery(2)
                .setTimes(10)
                .setMonthDay(29)
                .setType(RepeatConst.REPEAT_END_TYPE_TIMES);
        repeatDates = RepeatUtil.getDates(params);
        expectedResult = Stream.of(
                        "2023-11-29",
                        "2024-01-29",
                        "2024-03-29",
                        "2024-05-29",
                        "2024-07-29",
                        "2024-09-29",
                        "2024-11-29",
                        "2025-01-29",
                        "2025-03-29",
                        "2025-05-29")
                .map(LocalDate::parse)
                .toList();
        Assertions.assertArrayEquals(expectedResult.toArray(), repeatDates.toArray());

        // 以 endDate 结束，到 2024-11-29，每月的第30天，2024年的2月没有30号，会取最后一天
        params = new RepeatParams()
                .setRepeatType(RepeatConst.REPEAT_TYPE_MONTH)
                .setRepeatEveryType(RepeatConst.REPEAT_EVERY_TYPE_SPECIFIC_DAY)
                .setStartsOn("2023-11-29")
                .setSetEndOn("2024-03-29") // 无效参数
                .setRepeatEvery(1)
                .setTimes(10)
                .setMonthDay(30)
                .setType(RepeatConst.REPEAT_END_TYPE_END_DATE);
        repeatDates = RepeatUtil.getDates(params);
        Assertions.assertTrue(repeatDates.contains(LocalDate.of(2024, Month.FEBRUARY, 29)));
    }
}
