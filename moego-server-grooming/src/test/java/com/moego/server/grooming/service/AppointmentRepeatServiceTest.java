package com.moego.server.grooming.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentRepeatServiceTest {

    @InjectMocks
    private AppointmentRepeatService repeatService;

    @Mock
    private MoeGroomingAppointmentMapper appointmentMapper;

    @Test
    public void testUpdateNormalAppointmentToRepeat_NullAppointmentId() {
        // 测试appointmentId为null的情况
        repeatService.updateNormalAppointmentToRepeat(null, 1);

        // 验证mapper方法没有被调用
        verify(appointmentMapper, never()).selectByPrimaryKey(any());
        verify(appointmentMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    public void testUpdateNormalAppointmentToRepeat_NullRepeatId() {
        // 测试repeatId为null的情况
        repeatService.updateNormalAppointmentToRepeat(1, null);

        // 验证mapper方法没有被调用
        verify(appointmentMapper, never()).selectByPrimaryKey(any());
        verify(appointmentMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    public void testUpdateNormalAppointmentToRepeat_AlreadyHasRepeatId() {
        // 准备测试数据
        Integer appointmentId = 1;
        Integer repeatId = 2;

        // 模拟appointment已经有repeatId
        MoeGroomingAppointment existingAppointment = new MoeGroomingAppointment();
        existingAppointment.setId(appointmentId);
        existingAppointment.setRepeatId(5); // 非零的repeatId

        when(appointmentMapper.selectByPrimaryKey(appointmentId)).thenReturn(existingAppointment);

        // 执行测试
        repeatService.updateNormalAppointmentToRepeat(appointmentId, repeatId);

        // 验证select被调用但update没有被调用
        verify(appointmentMapper, times(1)).selectByPrimaryKey(appointmentId);
        verify(appointmentMapper, never()).updateByPrimaryKeySelective(any());
    }
}
