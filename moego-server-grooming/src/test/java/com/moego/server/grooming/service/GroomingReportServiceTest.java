package com.moego.server.grooming.service;

import static com.moego.server.grooming.service.utils.ReportUtil.calculateCollectedAmount;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;

import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.customer.client.ICustomerReportClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.report.ReportWebSaleService;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingPetDetailMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.service.dto.GroomingReportWebAppointment;
import com.moego.server.grooming.service.dto.report.CollectedAmountCollection;
import com.moego.server.grooming.service.report.GroomingReportService;
import com.moego.server.grooming.service.report.ReportCalculateService;
import com.moego.server.grooming.service.report.ReportOrderService;
import com.moego.server.grooming.service.utils.ReportUtil;
import com.moego.server.message.client.IGroomingReportSendClient;
import com.moego.server.payment.client.IPaymentPaymentClient;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroomingReportServiceTest {

    @Mock
    private MoeGroomingAppointmentMapper appointmentMapper;

    @Mock
    private MoeGroomingPetDetailMapper petDetailMapper;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private IPaymentPaymentClient paymentService;

    @Mock
    private IPetClient iPetClient;

    @Mock
    private ICustomerReportClient iCustomerReportClient;

    @Mock
    private OrderService orderService;

    @Mock
    private ReportOrderService reportOrderService;

    @Mock
    private IGroomingReportSendClient iGroomingReportSendClient;

    @InjectMocks
    private GroomingReportService groomingReportService;

    @Mock
    private ReportCalculateService reportCalculateService;

    private GroomingReportWebAppointment sampleAppointment;
    private List<MoeGroomingInvoiceItem> sampleInvoiceItems;

    @BeforeEach
    void setUp() {
        sampleAppointment = new GroomingReportWebAppointment();
        sampleAppointment.setId(1);
        sampleAppointment.setInvoiceId(100);
        sampleAppointment.setIsPaid(GroomingAppointmentEnum.PAID);
        sampleAppointment.setTotalAmount(new BigDecimal("100.00"));
        sampleAppointment.setPaidAmount(new BigDecimal("100.00"));
        sampleAppointment.setConvenienceFee(BigDecimal.ZERO);

        EvaluationServiceDetailDTO serviceDetail = new EvaluationServiceDetailDTO();
        serviceDetail.setServiceId(1L);
        serviceDetail.setServiceName("Test Service");
        serviceDetail.setServicePrice(new BigDecimal("50.00"));
        sampleAppointment.setEvaluationDetails(List.of(serviceDetail));

        MoeGroomingInvoice sampleInvoice = new MoeGroomingInvoice();
        sampleInvoice.setId(100);
        sampleInvoice.setTotalAmount(new BigDecimal("100.00"));

        MoeGroomingInvoiceItem invoiceItem = new MoeGroomingInvoiceItem();
        invoiceItem.setId(1);
        invoiceItem.setInvoiceId(100);
        invoiceItem.setTotalSalePrice(new BigDecimal("50.00"));
        invoiceItem.setTaxAmount(new BigDecimal("5.00"));
        invoiceItem.setDiscountAmount(BigDecimal.ZERO);
        sampleInvoiceItems = List.of(invoiceItem);
    }

    @Test
    void buildEvaluationServiceReport_WhenAppointmentPaid_CalculatesCorrectly() {
        // Arrange
        Map<Integer, BigDecimal> refundMap = new HashMap<>();
        Map<Integer, List<MoeGroomingInvoiceItem>> invoiceItemListMap = new HashMap<>();
        invoiceItemListMap.put(100, sampleInvoiceItems);
        // mock
        try (MockedStatic<ReportUtil> mockedStatic = mockStatic(ReportUtil.class)) {
            mockedStatic
                    .when(() -> calculateCollectedAmount(Mockito.any()))
                    .thenReturn(new CollectedAmountCollection(
                            BigDecimal.valueOf(5),
                            BigDecimal.ZERO,
                            BigDecimal.ZERO,
                            BigDecimal.valueOf(100),
                            BigDecimal.valueOf(100)));
        }

        // Act
        List<ReportWebSaleService> result = groomingReportService.buildEvaluationServiceReport(
                List.of(sampleAppointment), refundMap, invoiceItemListMap);

        // Assert
        assertThat(result).hasSize(1);
        ReportWebSaleService service = result.get(0);
        assertThat(service.getServiceName()).isEqualTo("Test Service");
        assertThat(service.getTotalSale()).isEqualTo(new BigDecimal("50.00"));
        assertThat(service.getTicketNum()).isEqualTo(1);
        assertThat(service.getPetNum()).isEqualTo(1);
    }
}
