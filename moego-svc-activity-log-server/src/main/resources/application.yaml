spring:
  application:
    name: moego-svc-activity-log
  profiles:
    active: local
  datasource:
    driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    url: ****************************************************************************
    username: 'moego_developer'
    password: 'Ym8CM&rW*n7Zjr!2~0wvlw+yaIOFcyg%-fao=xDc'
  config:
    import: classpath:field-mapping-rules.yaml

moego:
  server:
    url:
      grooming: http://moego-service-grooming:9206
      customer: http://moego-service-customer:9201
      business: http://moego-service-business:9203
      payment: http://moego-service-payment:9204
      message: http://moego-service-message:9205
      retail: http://moego-service-retail:9207
  messaging:
    auto-ack: true
  grpc:
    client:
      stubs:
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.auto_message.v1.**
          authority: moego-svc-auto-message:9090

mybatis-plus:
  global-config:
    banner: false
logging:
  level:
    com.clickhouse.jdbc.internal.ClickHouseConnectionImpl: error # Ignore clickhouse does not support transaction warning
