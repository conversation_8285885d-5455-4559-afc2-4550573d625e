spring:
  datasource:
    url: *************************************************************************************
    username: 'moego_developer'
    password: "Ym8CM&rW*n7Zjr!2~0wvlw+yaIOFcyg%-fao=xDc"

logging:
  level:
    com.moego.lib.messaging: trace
    com.moego.svc.activitylog.server.repository: debug

moego:
  messaging:
    pulsar:
      service-url: pulsar-proxy.pulsar:6650
      authentication: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      tenant: test2
