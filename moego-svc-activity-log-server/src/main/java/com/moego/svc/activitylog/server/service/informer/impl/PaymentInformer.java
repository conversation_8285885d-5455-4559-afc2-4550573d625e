package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PAYMENT}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PaymentInformer extends AbstractStaffOperatorCustomerOwnerInformer<PaymentDTO> {

    private final IPaymentPaymentClient paymentApi;

    @Override
    public String getOwnerId(PaymentDTO paymentDTO) {
        return String.valueOf(paymentDTO.getCustomerId());
    }

    @Override
    public String resourceType() {
        return ResourceType.PAYMENT.toString();
    }

    @Override
    public String resourceName(PaymentDTO paymentDTO) {
        return "Payment for invoice #%s".formatted(paymentDTO.getInvoiceId());
    }

    @Override
    public PaymentDTO resource(String resourceId) {
        return paymentApi.getPaymentMethodById(Integer.parseInt(resourceId));
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
