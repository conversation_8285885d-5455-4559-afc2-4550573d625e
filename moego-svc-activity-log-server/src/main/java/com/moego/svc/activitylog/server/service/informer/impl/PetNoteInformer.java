package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.customer.api.IPetNoteService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.PetNoteDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorPetOwnerInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_NOTE}.
 *
 * <AUTHOR>
 */
@Component
public class PetNoteInformer extends AbstractStaffOperatorPetOwnerInformer<PetNoteDTO> {

    private final IPetNoteService petNoteApi;

    public PetNoteInformer(IBusinessStaffService staffApi, IPetService petApi, IPetNoteService petNoteApi) {
        super(staffApi, petApi);
        this.petNoteApi = petNoteApi;
    }

    @Override
    public String resourceType() {
        return ResourceType.PET_NOTE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public String getOwnerId(PetNoteDTO petNoteDTO) {
        return String.valueOf(petNoteDTO.getPetId());
    }

    @Override
    public PetNoteDTO resource(String resourceId) {
        return petNoteApi.getPetNoteById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(PetNoteDTO petNoteDTO) {
        return petNoteDTO.getNote();
    }
}
