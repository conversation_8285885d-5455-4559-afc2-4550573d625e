package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_TYPE}.
 *
 * <AUTHOR>
 */
@Component
public class PetTypeInformer extends AbstractStaffOperatorInformer<String> {

    @Override
    public String resourceType() {
        return ResourceType.PET_TYPE.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
