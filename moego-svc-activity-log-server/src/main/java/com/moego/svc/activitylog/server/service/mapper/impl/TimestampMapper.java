package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class TimestampMapper implements Mapper<String> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC'");
    public static final long MS_THRESHOLD = 100_000_000_000L;

    @Override
    public Map<String, String> map(Set<String> values) {
        Map<String, String> result = new HashMap<>(values.size());
        for (String value : values) {
            result.put(value, getName(value));
        }
        return result;
    }

    @Override
    public String getName(String value) {
        long longValue;
        try {
            longValue = Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.error("parse number error, value: {}", value);
            return null;
        }

        if (longValue == 0L) {
            return "no record";
        }

        Instant instant =
                longValue >= MS_THRESHOLD ? Instant.ofEpochMilli(longValue) : Instant.ofEpochSecond(longValue);

        return LocalDateTime.ofInstant(instant, ZoneOffset.UTC).format(FORMATTER);
    }
}
