package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.customer.api.IPetPhotoService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.PetPhotoDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorPetOwnerInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PET_PHOTO}.
 *
 * <AUTHOR>
 */
@Component
public class PetPhotoInformer extends AbstractStaffOperatorPetOwnerInformer<PetPhotoDTO> {

    private final IPetPhotoService petPhotoApi;

    public PetPhotoInformer(IBusinessStaffService staffApi, IPetService petApi, IPetPhotoService petPhotoApi) {
        super(staffApi, petApi);
        this.petPhotoApi = petPhotoApi;
    }

    @Override
    public String resourceType() {
        return ResourceType.PET_PHOTO.toString();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }

    @Override
    public PetPhotoDTO resource(String resourceId) {
        return petPhotoApi.getPetPhotoById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(PetPhotoDTO petPhotoDTO) {
        return petPhotoDTO.getDescription();
    }

    @Override
    public String getOwnerId(PetPhotoDTO petPhotoDTO) {
        return String.valueOf(petPhotoDTO.getPetId());
    }
}
