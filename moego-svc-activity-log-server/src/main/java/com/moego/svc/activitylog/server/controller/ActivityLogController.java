package com.moego.svc.activitylog.server.controller;

import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.activity_log.v1.ActivityLogModel;
import com.moego.idl.models.activity_log.v1.ActivityLogModelSimpleView;
import com.moego.idl.models.activity_log.v1.Operator;
import com.moego.idl.models.activity_log.v1.Owner;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.activity_log.v1.CreateActivityLogResponse;
import com.moego.idl.service.activity_log.v1.GetActivityLogDetailsInput;
import com.moego.idl.service.activity_log.v1.GetActivityLogDetailsOutput;
import com.moego.idl.service.activity_log.v1.ListActivityLogDetailsRequest;
import com.moego.idl.service.activity_log.v1.ListActivityLogDetailsResponse;
import com.moego.idl.service.activity_log.v1.SearchActionPageInput;
import com.moego.idl.service.activity_log.v1.SearchActionPageOutput;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageInput;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageOutput;
import com.moego.idl.service.activity_log.v1.SearchOperatorPageInput;
import com.moego.idl.service.activity_log.v1.SearchOperatorPageOutput;
import com.moego.idl.service.activity_log.v1.SearchOwnerPageInput;
import com.moego.idl.service.activity_log.v1.SearchOwnerPageOutput;
import com.moego.idl.service.activity_log.v1.SearchResourceTypePageInput;
import com.moego.idl.service.activity_log.v1.SearchResourceTypePageOutput;
import com.moego.idl.utils.v1.PaginationResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.proto.ProtoUtils;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.messaging.MessagingOperations;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.repository.ActivityLogRepository;
import com.moego.svc.activitylog.server.service.ActivityLogService;
import com.moego.svc.activitylog.server.service.param.SearchActionParam;
import com.moego.svc.activitylog.server.service.param.SearchActivityLogParam;
import com.moego.svc.activitylog.server.service.param.SearchOperatorParam;
import com.moego.svc.activitylog.server.service.param.SearchOwnerParam;
import com.moego.svc.activitylog.server.service.param.SearchResourceTypeParam;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/3/23
 */
@GrpcService
@RequiredArgsConstructor
public class ActivityLogController extends ActivityLogServiceGrpc.ActivityLogServiceImplBase {

    private final ActivityLogService activityLogService;
    /**
     * Only for read operation !
     */
    private final ActivityLogRepository repo;

    private final MessagingOperations messagingOperations;

    @Override
    public void searchActivityLogPage(
            SearchActivityLogPageInput request, StreamObserver<SearchActivityLogPageOutput> so) {
        SearchActivityLogParam param = new SearchActivityLogParam(
                request.hasBusinessId() ? List.of(request.getBusinessId()) : List.of(),
                request.getOperatorIdList(),
                request.getActionList(),
                request.getResourceTypeList(),
                request.getResourceIdList(),
                request.getOwnerIdList(),
                request.hasStartTime() ? new Date(Timestamps.toMillis(request.getStartTime())) : null,
                request.hasEndTime() ? new Date(Timestamps.toMillis(request.getEndTime())) : null);

        var pageNum = request.hasPagination()
                ? request.getPagination().getPageNum()
                : request.getPage().getPageNo() + 1;
        var pageSize = request.hasPagination()
                ? request.getPagination().getPageSize()
                : request.getPage().getPageSize();

        IPage<ActivityLog> activityLogs = activityLogService.search(param, new Page<>(pageNum, pageSize));

        boolean queryRoot = !request.hasQueryRoot() || request.getQueryRoot();

        SearchActivityLogPageOutput.Builder output = SearchActivityLogPageOutput.newBuilder();
        List<ActivityLogModelSimpleView> views = activityLogs.getRecords().parallelStream()
                .map(log -> toSimpleView(log, queryRoot))
                .toList();
        output.addAllActivityLogs(views);
        output.setPagination(com.moego.idl.utils.v2.PaginationResponse.newBuilder()
                .setPageNum((int) pageNum)
                .setPageSize((int) pageSize)
                .setTotal((int) activityLogs.getTotal())
                .build());
        output.setPage(PaginationResponse.newBuilder()
                .setPageNo(request.getPage().getPageNo())
                .setPageSize(request.getPage().getPageSize())
                .setTotal(activityLogs.getTotal())
                .build());

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void getActivityLogDetails(
            GetActivityLogDetailsInput request, StreamObserver<GetActivityLogDetailsOutput> so) {
        ActivityLog activityLog = activityLogService.getById(request.getId());

        GetActivityLogDetailsOutput.Builder output = GetActivityLogDetailsOutput.newBuilder();
        output.setActivityLog(toModel(activityLog, true));
        output.addAllEffects(getAffects(activityLog));

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void searchOperatorPage(SearchOperatorPageInput request, StreamObserver<SearchOperatorPageOutput> so) {
        var businessId = request.hasBusinessId() ? request.getBusinessId() : null;
        SearchOperatorParam param = new SearchOperatorParam(businessId, request.getOperatorName());

        var pageNum = request.hasPagination()
                ? request.getPagination().getPageNum()
                : request.getPage().getPageNo() + 1;
        var pageSize = request.hasPagination()
                ? request.getPagination().getPageSize()
                : request.getPage().getPageSize();

        IPage<ActivityLog> operators = activityLogService.searchOperator(param, new Page<>(pageNum, pageSize));
        SearchOperatorPageOutput.Builder output = SearchOperatorPageOutput.newBuilder();
        output.setPagination(com.moego.idl.utils.v2.PaginationResponse.newBuilder()
                .setPageNum((int) pageNum)
                .setPageSize((int) pageSize)
                .setTotal((int) operators.getTotal())
                .build());
        output.setPage(PaginationResponse.newBuilder()
                .setPageNo(request.getPage().getPageNo())
                .setPageSize(request.getPage().getPageSize())
                .setTotal(operators.getTotal())
                .build());
        output.addAllOperators(operators.getRecords().stream()
                .map(operator -> {
                    Operator.Builder builder = Operator.newBuilder();
                    ofNullable(operator.getOperatorId()).ifPresent(builder::setId);
                    ofNullable(operator.getOperatorName()).ifPresent(builder::setName);
                    return builder.build();
                })
                .toList());

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void searchResourceTypePage(
            SearchResourceTypePageInput request, StreamObserver<SearchResourceTypePageOutput> so) {
        var businessId = request.hasBusinessId() ? request.getBusinessId() : null;
        SearchResourceTypeParam param = new SearchResourceTypeParam(businessId, request.getResourceType());

        var pageNum = request.hasPagination()
                ? request.getPagination().getPageNum()
                : request.getPage().getPageNo() + 1;
        var pageSize = request.hasPagination()
                ? request.getPagination().getPageSize()
                : request.getPage().getPageSize();

        IPage<ActivityLog> resourceTypes = activityLogService.searchResourceType(param, new Page<>(pageNum, pageSize));

        SearchResourceTypePageOutput.Builder output = SearchResourceTypePageOutput.newBuilder();
        output.setPagination(com.moego.idl.utils.v2.PaginationResponse.newBuilder()
                .setPageNum((int) pageNum)
                .setPageSize((int) pageSize)
                .setTotal((int) resourceTypes.getTotal())
                .build());
        output.setPage(PaginationResponse.newBuilder()
                .setPageNo(request.getPage().getPageNo())
                .setPageSize(request.getPage().getPageSize())
                .setTotal(resourceTypes.getTotal())
                .build());
        output.addAllResourceTypes(resourceTypes.getRecords().stream()
                .map(ActivityLog::getResourceType)
                .toList());

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void searchActionPage(SearchActionPageInput request, StreamObserver<SearchActionPageOutput> so) {
        var businessId = request.hasBusinessId() ? request.getBusinessId() : null;
        SearchActionParam param = new SearchActionParam(businessId, request.getAction());

        var pageNum = request.hasPagination()
                ? request.getPagination().getPageNum()
                : request.getPage().getPageNo() + 1;
        var pageSize = request.hasPagination()
                ? request.getPagination().getPageSize()
                : request.getPage().getPageSize();

        IPage<ActivityLog> actions = activityLogService.searchAction(param, new Page<>(pageNum, pageSize));

        SearchActionPageOutput.Builder output = SearchActionPageOutput.newBuilder();
        output.setPagination(com.moego.idl.utils.v2.PaginationResponse.newBuilder()
                .setPageNum((int) pageNum)
                .setPageSize((int) pageSize)
                .setTotal((int) actions.getTotal())
                .build());
        output.setPage(PaginationResponse.newBuilder()
                .setPageNo(request.getPage().getPageNo())
                .setPageSize(request.getPage().getPageSize())
                .setTotal(actions.getTotal())
                .build());
        output.addAllActions(
                actions.getRecords().stream().map(ActivityLog::getAction).toList());

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void searchOwnerPage(SearchOwnerPageInput request, StreamObserver<SearchOwnerPageOutput> so) {
        var businessId = request.hasBusinessId() ? request.getBusinessId() : null;
        SearchOwnerParam param = new SearchOwnerParam(businessId, request.getOwnerName());

        var pageNum = request.hasPagination()
                ? request.getPagination().getPageNum()
                : request.getPage().getPageNo() + 1;
        var pageSize = request.hasPagination()
                ? request.getPagination().getPageSize()
                : request.getPage().getPageSize();

        IPage<ActivityLog> owners = activityLogService.searchOwner(param, new Page<>(pageNum, pageSize));

        SearchOwnerPageOutput.Builder output = SearchOwnerPageOutput.newBuilder();
        output.setPagination(com.moego.idl.utils.v2.PaginationResponse.newBuilder()
                .setPageNum((int) pageNum)
                .setPageSize((int) pageSize)
                .setTotal((int) owners.getTotal())
                .build());
        output.setPage(PaginationResponse.newBuilder()
                .setPageNo(request.getPage().getPageNo())
                .setPageSize(request.getPage().getPageSize())
                .setTotal(owners.getTotal())
                .build());
        output.addAllOwners(owners.getRecords().stream()
                .map(owner -> {
                    Owner.Builder builder = Owner.newBuilder();
                    ofNullable(owner.getOwnerId()).ifPresent(builder::setId);
                    ofNullable(owner.getOwnerName()).ifPresent(builder::setName);
                    return builder.build();
                })
                .toList());

        so.onNext(output.build());
        so.onCompleted();
    }

    @Override
    public void listActivityLogDetails(
            ListActivityLogDetailsRequest request, StreamObserver<ListActivityLogDetailsResponse> responseObserver) {
        List<String> ids = request.getIdsList();
        if (ObjectUtils.isEmpty(ids)) {
            responseObserver.onNext(ListActivityLogDetailsResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        boolean queryRoot = !request.hasQueryRoot() || request.getQueryRoot();

        List<ActivityLogModel> models =
                repo.selectList(Wrappers.<ActivityLog>lambdaQuery().in(ActivityLog::getId, ids)).stream()
                        .map(e -> toModel(e, queryRoot))
                        .toList();
        responseObserver.onNext(ListActivityLogDetailsResponse.newBuilder()
                .addAllActivityLogs(models)
                .build());
        responseObserver.onCompleted();
    }

    private List<ActivityLogModel> getAffects(ActivityLog activityLog) {
        List<ActivityLogModel> affects = new ArrayList<>();
        if (activityLog.getIsRoot()) {
            String rootId = getRootId4Request(activityLog.getBusinessId(), activityLog.getRequestId());
            if (Objects.equals(rootId, activityLog.getId())) {
                // this log is a real root log
                ActivityLog entity = new ActivityLog();
                entity.setBusinessId(activityLog.getBusinessId());
                entity.setRequestId(activityLog.getRequestId());
                List<ActivityLogModel> affectLogs = activityLogService.search(entity).parallelStream()
                        .filter(log -> !Objects.equals(log.getId(), activityLog.getId()))
                        .sorted(Comparator.comparing(ActivityLog::getTime)
                                .reversed()
                                .thenComparing(ActivityLog::getCreatedAt)
                                .reversed()
                                .thenComparing(ActivityLog::getId)
                                .reversed())
                        .map(e -> toModel(e, true))
                        .toList();
                affects.addAll(affectLogs);
            }
        }
        return affects;
    }

    private ActivityLogModel toModel(ActivityLog log, boolean queryRoot) {
        ActivityLogModel.Builder builder = ActivityLogModel.newBuilder();
        ofNullable(log.getId()).ifPresent(builder::setId);
        ofNullable(log.getBusinessId()).ifPresent(builder::setBusinessId);
        ofNullable(log.getTime()).map(Timestamps::fromDate).ifPresent(builder::setTime);
        ofNullable(log.getAction()).ifPresent(builder::setAction);
        if (log.getOperatorId() != null || log.getOperatorName() != null) {
            Operator.Builder operatorBuilder = Operator.newBuilder();
            ofNullable(log.getOperatorId()).ifPresent(operatorBuilder::setId);
            ofNullable(log.getOperatorName()).ifPresent(operatorBuilder::setName);
            builder.setOperator(operatorBuilder.build());
        }
        if (log.getResourceId() != null || log.getResourceType() != null || log.getResourceName() != null) {
            Resource.Builder resourceBuilder = Resource.newBuilder();
            ofNullable(log.getResourceId()).ifPresent(resourceBuilder::setId);
            ofNullable(log.getResourceType()).ifPresent(resourceBuilder::setType);
            ofNullable(log.getResourceName()).ifPresent(resourceBuilder::setName);
            builder.setResource(resourceBuilder.build());
        }
        if (log.getOwnerId() != null || log.getOwnerName() != null) {
            Owner.Builder ownerBuilder = Owner.newBuilder();
            ofNullable(log.getOwnerId()).ifPresent(ownerBuilder::setId);
            ofNullable(log.getOwnerName()).ifPresent(ownerBuilder::setName);
            builder.setOwner(ownerBuilder.build());
        }
        ofNullable(log.getDetails())
                .map(d -> JsonUtil.toBean(d, Object.class))
                .map(ProtoUtils::objectToValue)
                .ifPresent(builder::setDetails);
        ofNullable(log.getRequestId()).ifPresent(builder::setRequestId);
        if (queryRoot) {
            String id = getRootId4Request(log.getBusinessId(), log.getRequestId());
            if (id != null && !id.equals(log.getId())) {
                builder.setRootActivityLogId(id);
            }
        }
        return builder.build();
    }

    private ActivityLogModelSimpleView toSimpleView(ActivityLog log, boolean queryRoot) {
        ActivityLogModelSimpleView.Builder builder = ActivityLogModelSimpleView.newBuilder();
        ofNullable(log.getId()).ifPresent(builder::setId);
        ofNullable(log.getBusinessId()).ifPresent(builder::setBusinessId);
        ofNullable(log.getTime()).map(Timestamps::fromDate).ifPresent(builder::setTime);
        ofNullable(log.getAction()).ifPresent(builder::setAction);
        if (log.getOperatorId() != null || log.getOperatorName() != null) {
            Operator.Builder operatorBuilder = Operator.newBuilder();
            ofNullable(log.getOperatorId()).ifPresent(operatorBuilder::setId);
            ofNullable(log.getOperatorName()).ifPresent(operatorBuilder::setName);
            builder.setOperator(operatorBuilder.build());
        }
        if (log.getResourceId() != null || log.getResourceType() != null || log.getResourceName() != null) {
            Resource.Builder resourceBuilder = Resource.newBuilder();
            ofNullable(log.getResourceId()).ifPresent(resourceBuilder::setId);
            ofNullable(log.getResourceType()).ifPresent(resourceBuilder::setType);
            ofNullable(log.getResourceName()).ifPresent(resourceBuilder::setName);
            builder.setResource(resourceBuilder.build());
        }
        if (log.getOwnerId() != null || log.getOwnerName() != null) {
            Owner.Builder ownerBuilder = Owner.newBuilder();
            ofNullable(log.getOwnerId()).ifPresent(ownerBuilder::setId);
            ofNullable(log.getOwnerName()).ifPresent(ownerBuilder::setName);
            builder.setOwner(ownerBuilder.build());
        }
        if (queryRoot) {
            String id = getRootId4Request(log.getBusinessId(), log.getRequestId());
            if (id != null && !id.equals(log.getId())) {
                builder.setRootActivityLogId(id);
            }
        }
        return builder.build();
    }

    private String getRootId4Request(Long businessId, String requestId) {
        if (!StringUtils.hasText(requestId)) {
            return null;
        }
        return repo
                .selectList(Wrappers.<ActivityLog>lambdaQuery()
                        .select(ActivityLog::getId)
                        .eq(ActivityLog::getBusinessId, businessId)
                        .eq(ActivityLog::getRequestId, requestId)
                        .eq(ActivityLog::getIsRoot, true)
                        .orderByAsc(ActivityLog::getTime))
                .stream()
                .findFirst()
                .map(ActivityLog::getId)
                .orElse(null);
    }

    @Override
    public void createActivityLog(
            CreateActivityLogRequest request, StreamObserver<CreateActivityLogResponse> responseObserver) {

        var headers = ofNullable(ThreadContextHolder.getContext(Headers.class))
                .map(Headers::getHeaders)
                .orElseGet(Map::of);

        messagingOperations.asyncSend(ActivityLogEvent.TOPIC, buildActivityLogEvent(request), headers);

        responseObserver.onNext(CreateActivityLogResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private static ActivityLogEvent buildActivityLogEvent(CreateActivityLogRequest request) {
        var event = new ActivityLogEvent();

        if (request.hasCompanyId()) {
            event.setCompanyId(request.getCompanyId());
        }
        if (request.hasBusinessId()) {
            event.setBusinessId(request.getBusinessId());
        }
        event.setOperatorId(request.getOperatorId());
        if (request.hasTime()) {
            event.setTime(new Date(Timestamps.toMillis(request.getTime())));
        } else {
            event.setTime(new Date());
        }
        ofNullable(TracingContext.get()).map(TracingContext::getRequestId).ifPresent(event::setRequestId);
        event.setRoot(request.getIsRoot());
        event.setAction(request.getAction());
        event.setResourceTypeV2(request.getResourceType());

        if (StringUtils.hasText(request.getResourceId())) {
            event.setResourceId(request.getResourceId());
        }
        if (StringUtils.hasText(request.getDetails())) {
            event.setDetails(request.getDetails());
        }
        if (StringUtils.hasText(request.getClassName())) {
            event.setClassName(request.getClassName());
        }

        return event;
    }
}
