package com.moego.svc.activitylog.server.listener;

import static com.moego.lib.common.util.ThreadPoolUtil.shutdown;

import com.moego.idl.service.organization.v1.BatchGetCompanyIdRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.lib.messaging.Consumer;
import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.Msg;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.server.entity.ActivityLog;
import com.moego.svc.activitylog.server.service.ActivityLogFiller;
import com.moego.svc.activitylog.server.service.ActivityLogService;
import com.moego.svc.activitylog.server.util.ActivityLogEventHolder;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Consumer(value = ActivityLogEvent.TOPIC)
@RequiredArgsConstructor
public class ActivityLogListener
        implements EventListener<ActivityLogEvent>, ApplicationListener<ApplicationReadyEvent>, DisposableBean {

    private static final Duration interval = Duration.ofSeconds(1);
    private static final Duration maxHoldTime = Duration.ofSeconds(10);

    private final ActivityLogService activityLogService;
    private final ActivityLogFiller filler;
    private final BlockingQueue<ActivityLog> queue = new LinkedBlockingQueue<>(1000);
    private final AtomicLong lastInsertTime = new AtomicLong(System.currentTimeMillis());
    private final ScheduledExecutorService timer = Executors.newSingleThreadScheduledExecutor();
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;

    @Override
    public void onEvent(Msg<ActivityLogEvent> message) {
        ActivityLogEvent event = message.getBody();

        ActivityLogEventHolder.set(event);
        try {
            ActivityLog entity = filler.populate(event);

            synchronized (this) {
                while (!queue.offer(entity)) {
                    clearAndInsert();
                }
            }
        } finally {
            ActivityLogEventHolder.remove();
        }
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        timer.scheduleWithFixedDelay(this::tryClearAndInsert, 0L, interval.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public void destroy() {
        shutdown(timer);
        log.info("ActivityLogListener timer shutdown.");
    }

    /* not private for testing */
    synchronized void clearAndInsert() {
        lastInsertTime.set(System.currentTimeMillis());

        if (queue.isEmpty()) {
            return;
        }

        try {
            List<ActivityLog> logs = new ArrayList<>(queue);

            var businessIdToCompanyId = listBusinessIdToCompanyId(logs);

            for (var activityLog : logs) {
                if (needSetCompanyId(activityLog)) {
                    Optional.ofNullable(businessIdToCompanyId.get(activityLog.getBusinessId()))
                            .ifPresent(activityLog::setCompanyId);
                }
            }

            queue.clear();

            activityLogService.saveBatch(logs);
        } catch (Exception e) {
            log.error("Failed to save activity logs.", e);
        }
    }

    private Map<Long, Long> listBusinessIdToCompanyId(List<ActivityLog> logs) {
        var businessIdList = logs.stream()
                .filter(ActivityLogListener::needSetCompanyId)
                .map(ActivityLog::getBusinessId)
                .collect(Collectors.toSet());

        if (ObjectUtils.isEmpty(businessIdList)) {
            return Map.of();
        }

        return businessStub
                .batchGetCompanyId(BatchGetCompanyIdRequest.newBuilder()
                        .addAllBusinessIds(businessIdList)
                        .build())
                .getBusinessCompanyIdMapMap();
    }

    private static boolean needSetCompanyId(ActivityLog log) {
        return (log.getCompanyId() == null || log.getCompanyId() == 0)
                && (log.getBusinessId() != null && log.getBusinessId() != 0);
    }

    private void tryClearAndInsert() {
        if (System.currentTimeMillis() - lastInsertTime.get() >= maxHoldTime.toMillis()) {
            clearAndInsert();
        }
    }
}
