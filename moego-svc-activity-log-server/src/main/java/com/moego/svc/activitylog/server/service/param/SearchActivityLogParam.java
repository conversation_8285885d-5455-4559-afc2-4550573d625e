package com.moego.svc.activitylog.server.service.param;

import java.util.Date;
import java.util.List;

/**
 * @param businessIds   business ids, in condition
 * @param operatorIds   operator ids, in condition
 * @param actions       actions, in condition
 * @param resourceTypes resource types, in condition
 * @param resourceIds   resource ids, in condition
 * @param ownerIds      owner ids, in condition
 * @param startTime     start time, greater than or equal to condition
 * @param endTime       end time, less than or equal to condition
 */
public record SearchActivityLogParam(
        List<Long> businessIds,
        List<String> operatorIds,
        List<String> actions,
        List<String> resourceTypes,
        List<String> resourceIds,
        List<String> ownerIds,
        Date startTime,
        Date endTime) {}
