package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#ONLINE_BOOKING_SETTING}.
 *
 * <AUTHOR>
 */
@Component
public class OnlineBookingSettingInformer extends AbstractStaffOperatorInformer<BookOnlineDTO> {

    @Override
    public String resourceType() {
        return ResourceType.ONLINE_BOOKING_SETTING.toString();
    }
}
