package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class StaffIdMapper implements Mapper<MoeStaffDto> {

    private final IBusinessStaffService staffApi;

    /**
     * Map staff ids to names.
     * @param staffIds staff ids
     * @return map of staff id -> name
     */
    public Map<String, String> map(Set<String> staffIds) {
        Map<String, String> result = new HashMap<>(staffIds.size());

        for (String staffId : staffIds) {
            try {
                var staffIdInt = Integer.parseInt(staffId);
                if (staffIdInt <= 0) {
                    continue;
                }
                var idParams = new StaffIdParams(null, staffIdInt);
                var staff = staffApi.getStaff(idParams);
                if (staff != null) {
                    var name = getName(staff);
                    result.put(staffId, name);
                }
            } catch (Exception e) {
                log.error("Failed to get staff name for staff id: {}", staffId, e);
            }
        }

        return result;
    }

    @Override
    public String getName(MoeStaffDto staff) {
        return String.join(" ", staff.getFirstName(), staff.getLastName());
    }
}
