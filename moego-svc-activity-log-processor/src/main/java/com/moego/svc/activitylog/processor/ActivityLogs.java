package com.moego.svc.activitylog.processor;

import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.activitylog.event.ActivityLogEvent;
import com.moego.svc.activitylog.event.enums.ResourceType;
import java.util.Date;
import java.util.Optional;
import org.springframework.lang.Nullable;

/**
 * Activity log factory.
 *
 * <AUTHOR>
 */
public class ActivityLogs {

    /**
     * Build activity log event.
     *
     * @param action       action
     * @param resourceType resource type
     * @param resourceId   resource id
     * @param details      object, not value type (String, int, boolean...)
     * @return activity log event
     */
    public static ActivityLogEvent event(
            String action, ResourceType resourceType, @Nullable Object resourceId, @Nullable Object details) {
        return activityLogEvent(action, resourceType, resourceId, details, false);
    }

    public static ActivityLogEvent rootEvent(
            String action, ResourceType resourceType, @Nullable Object resourceId, @Nullable Object details) {
        return activityLogEvent(action, resourceType, resourceId, details, true);
    }

    private static ActivityLogEvent activityLogEvent(
            String action, ResourceType resourceType, Object resourceId, Object details, boolean root) {
        ActivityLogEvent event = new ActivityLogEvent();

        // get from context
        event.setBusinessId(Optional.ofNullable(AuthContext.get())
                .map(AuthContext::getBusinessId)
                .map(Long::valueOf)
                .orElse(null));
        event.setOperatorId(Optional.ofNullable(AuthContext.get())
                .map(AuthContext::getStaffId)
                .map(String::valueOf)
                .orElse(null));
        event.setTime(new Date());
        event.setRequestId(Optional.ofNullable(TracingContext.get())
                .map(TracingContext::getRequestId)
                .orElse(null));
        event.setRoot(root);

        // set from parameters
        event.setAction(action);
        event.setResourceType(resourceType);
        event.setResourceId(resourceId != null ? resourceId.toString() : null);
        event.setDetails(details != null ? JsonUtil.toJson(details) : null);

        return event;
    }
}
