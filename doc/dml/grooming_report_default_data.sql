# 1. overall feedback
insert into moe_grooming.moe_grooming_report_question
  (business_id, category, type, `key`, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 1, 'single_choice', 'overall_feedback', 'How did it go?', '{"buildInOptions":["Absolutely pawfect","Pawsome job done","Better next time!"],"options":["Absolutely pawfect","Pawsome job done","Better next time!"]}', true, false, false, false, true, 3);

# 2.comment
insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, `key`, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 1, 'text_input', 'comment', 'Additional note', null, true, false, false, false, true, 2);

# 3.mood
insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, `key`, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 1, 'multi_choice', 'mood', 'Mood', '{"options":["Happy 😃","Well behaved 💗","Restless 🚀","A little shy 🙈","Talkative 🎙️","Anxious 😳", "Spicy 🔥"]}', true, false, false, false, true, 1);

# 4.Pet condition: Coat, Skin, Nose, Ears, Teeth, Lumps and bumps
insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Coat', '{"buildInOptions":["Good","Slightly matted","Matted","Severely matted","Need check by vet"],"options":["Good","Slightly matted","Matted","Severely matted","Need check by vet"]}', true, false, true, true, true, 7);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Skin', '{"buildInOptions":["Good","Flaky","Fleas & ticks","Lumps","Need check by vet"],"options":["Good","Flaky","Fleas & ticks","Lumps","Need check by vet"]}', true, false, true, true, true, 6);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Eyes', '{"buildInOptions":["Bright & clear","A little drippy","Buildup around eyes","Need check by vet"],"options":["Bright & clear","A little drippy","Buildup around eyes","Need check by vet"]}', true, false, true, true, true, 5);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, required, is_default, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Ears', '{"buildInOptions":["Clean","Waxy","Smelly","Need check by vet"],"options":["Clean","Waxy","Smelly","Need check by vet"]}', true, false, true, true, true, 4);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Teeth', '{"buildInOptions":["Good","Plaque buildup","Need check by vet"],"options":["Good","Plaque buildup","Need check by vet"]}', true, false, true, true, true, 3);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'single_choice', 'Anal glands', '{"buildInOptions":["Good","Little fluid","Lots of fluid","Need check by vet"],"options":["Good","Little fluid","Lots of fluid","Need check by vet"]}', true, false, true, true, true, 2);

insert into moe_grooming.moe_grooming_report_question
(business_id, category, type, title, extra_json, is_default, required, type_editable, title_editable, options_editable, sort)
values (0, 2, 'body_view', 'Lumps & bumps', null, true, false, true, true, true, 1);


# 清洗 review booster link 的脏数据
update moe_message.moe_business_review_booster set positive_yelp = '' where positive_yelp = 'https://';
update moe_message.moe_business_review_booster set positive_facebook = '' where positive_facebook = 'https://';
update moe_message.moe_business_review_booster set positive_google = '' where positive_google = 'https://';
# 有设置 review booster 的 template 的 show_review_booster 设置为 true
update moe_grooming.moe_grooming_report_template set show_review_booster = true where business_id in (
  select business_id from moe_message.moe_business_review_booster where positive_yelp != '' or positive_facebook != '' or positive_google != ''
);

# 2023-10-11 theme config dml
INSERT INTO `moe_grooming`.`moe_grooming_report_theme_config` (`name`, `code`, `color`, `light_color`, `img_url`, `icon`, `email_bottom_img_url`, `recommend`, `status`)
VALUES ('Default', 'Default', '', '', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16970246403adeabcace2b4d7c8ce7868cce7e2cac.png?name=Service_3.png', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697082699029eda2a1b0f45c0886796abea4a7092.png?name=default.png', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16971678662f7a9ab8df434f5c843c9f4d910b03c4.png?name=default-theme.png', 0, 1),
       ('Halloween', 'Halloween', '#9052D9', '#E4DBF8', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16970153891898c3f9119b4a1d9d26793654cbd4e6.png?name=hallween.png', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697082625e90f8d911a8c466693f8cc8c324671a6.png?name=halloween.png', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697167861c16878f8b7b94b4fbedc8f19b0661377.png?name=Halloween.png', 1, 1),
       ('Thanksgiving', 'Thanksgiving', '', '', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697014422e0636fb82c4e4c71af7f05a3a4a351f8.png?name=comming_soon.png', '', '', 0, 0),
       ('Christmas', 'Christmas', '', '', 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1697014422e0636fb82c4e4c71af7f05a3a4a351f8.png?name=comming_soon.png', '', '', 0, 0);


# 初始化 sort 值，倒序：Default > Thanksgiving > Halloween > Christmas
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 4 where code = 'Default';
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 3 where code = 'Thanksgiving';
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 2 where code = 'Halloween';
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 1 where code = 'Christmas';

# Halloween 皮肤仅 Growth+ 可见
update `moe_grooming`.`moe_grooming_report_theme_config` set visible_level = 1101 where code = 'Halloween';

# Thanksgiving, Halloween, Christmas 皮肤 Growth+ only, 需要升级才能使用
update `moe_grooming`.`moe_grooming_report_theme_config` set flag = 1 where code in ('Thanksgiving', 'Halloween', 'Christmas');

# Christmas 皮肤配置
UPDATE `moe_grooming`.`moe_grooming_report_theme_config`
SET `color` = '#B4ECFA',
    `light_color` = '#CBF2FF',
    `img_url` = 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/17004528063ccbe72440bb414189c6bfe29ab95027.png?name=christmas.png',
    `icon` = 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/17007311071f4592d9a7924b2e95172346044ec309.png?name=template%20icon.png',
    `email_bottom_img_url` = 'https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/170045188701ce9581ce234a7d8a02db8b06a2c3d6.png?name=christmas-bottom.png',
    `recommend` = 1,
    `status` = 1,
    `visible_level` = 0,
    `flag` = 1,
    `sort` = 3
WHERE `code` = 'Christmas';

# 更新皮肤顺序
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 4 where code = 'Default';
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 1 where code = 'Halloween';
update `moe_grooming`.`moe_grooming_report_theme_config` set sort = 2 where code = 'Thanksgiving';
# 把 Halloween、Thanksgiving 的 recommend 字段清空
update `moe_grooming`.`moe_grooming_report_theme_config` set recommend = 0 where code in ('Halloween', 'Thanksgiving');
