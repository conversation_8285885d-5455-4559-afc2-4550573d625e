-- moe_grooming.moe_book_online_abandon_record definition

CREATE TABLE `moe_book_online_abandon_record`
(
  `id`                        int unsigned                                NOT NULL AUTO_INCREMENT,
  `business_id`               int unsigned                                NOT NULL COMMENT 'moe_business.id',
  `booking_flow_id`           varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'Unique ID for each booking flow',
  `customer_id`               int unsigned                                         DEFAULT NULL COMMENT 'existing client id, moe_customer.id',
  `referer`                   varchar(1024) COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL COMMENT 'referer source url',
  `phone_number`              varchar(50) COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT 'new client & existing client phone number',
  `first_name`                varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'new client first name',
  `last_name`                 varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'new client last name',
  `email`                     varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'email',
  `referral_source_id`        int                                                  DEFAULT NULL COMMENT 'referral source, moe_customer_source.id',
  `preferred_groomer_id`      int                                                  DEFAULT NULL COMMENT 'preferred groomer id',
  `preferred_frequency_day`   int                                                  DEFAULT NULL COMMENT 'preferred frequency day, unit day',
  `preferred_frequency_type`  tinyint                                              DEFAULT NULL COMMENT '0-by days, 1-by weeks',
  `preferred_day`             varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'customer preferred apt day, int array, range: 0-6',
  `preferred_time`            varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'customer preferred time in minutes array',
  `customer_question_answers` json                                                 DEFAULT NULL COMMENT 'custom question answers, question ID to answer',
  `address_id`                int unsigned                                         DEFAULT NULL COMMENT 'existing address id, moe_address.id',
  `address1`                  varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'address line 1',
  `address2`                  varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'address line 2',
  `city`                      varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'city',
  `state`                     varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'state',
  `zipcode`                   varchar(10) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'zipcode',
  `country`                   varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'country',
  `lat`                       varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'latitude',
  `lng`                       varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'longitude',
  `abandon_step`              varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'The next step is the abandon step (welcome_page, basic_info, select_address, select_pet, select_service, select_groomer, select_time, additional_pet_info, personal_info, card_on_file, prepay, submit)',
  `abandon_time`              bigint                                      NOT NULL COMMENT 'The currently completed step time is the abandon time',
  `recovery_type`             bigint                                      NOT NULL DEFAULT '0' COMMENT 'The way the abandon behavior is recovered (book_by_email_link, schedule)',
  `recovery_time`             bigint                                      NOT NULL DEFAULT '0' COMMENT 'The time when the abandon behavior was recovered',
  `staff_id`                  int unsigned                                         DEFAULT NULL COMMENT 'select groomer id, moe_staff.id',
  `appointment_date`          varchar(100) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'appointment date, format yyyy-MM-dd',
  `appointment_start_time`    int                                                  DEFAULT NULL COMMENT 'appointment start time, current minutes offset',
  `additional_note`           longtext COLLATE utf8mb4_unicode_520_ci COMMENT 'alert note',
  `agreement_info`            json                                                 DEFAULT NULL COMMENT 'signed agreement info, contains agreement ID, base64 image signature',
  `is_deleted`                tinyint(1)                                  NOT NULL DEFAULT '0' COMMENT '0-false, 1-true',
  `delete_type`               tinyint                                              DEFAULT NULL COMMENT '1-test data, 2-manual delete, 3-block client',
  `create_time`               datetime                                    NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time`               datetime                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_ob_flow_id` (`business_id`, `booking_flow_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='Abandon user behavior record';


-- moe_grooming.moe_book_online_abandon_record_pet definition

CREATE TABLE `moe_book_online_abandon_record_pet`
(
  `id`                      int unsigned                                NOT NULL AUTO_INCREMENT,
  `business_id`             int unsigned                                NOT NULL COMMENT 'moe_business.id',
  `booking_flow_id`         varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'Unique ID for each booking flow',
  `pet_id`                  int unsigned                                         DEFAULT NULL COMMENT 'existing pet id, moe_pet.id',
  `index_id`                int unsigned                                NOT NULL COMMENT 'the index order of pet',
  `service_id`              int unsigned                                         DEFAULT NULL COMMENT 'pet select service id, moe_grooming_service.id',
  `addon_ids`               json                                                 DEFAULT NULL COMMENT 'pet select add-on ids, moe_grooming_service.id',
  `pet_type_id`             int unsigned                                         DEFAULT NULL COMMENT 'pet type id, 1-dog, 2-cat, 11-other',
  `avatar_path`             varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'avatar path',
  `pet_name`                varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'new client last name',
  `breed`                   varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'pet breed, moe_pet_breed.name',
  `weight`                  varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'weight',
  `hair_length`             varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'pet hair length, moe_pet_hair_length.name',
  `vaccine_list`            json                                                 DEFAULT NULL COMMENT 'vaccine list, contains vaccine ID, expiration date, document url',
  `birthday`                varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'birthday, format yyyy-MM-dd',
  `gender`                  tinyint                                              DEFAULT '0' COMMENT '1-male, 2-female',
  `fixed`                   varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'is fixed',
  `behavior`                varchar(50) COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT 'behavior, moe_behavior.name',
  `vet_name`                varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'vet name',
  `vet_phone_number`        varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'vet phone number',
  `vet_address`             varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'vet address',
  `emergency_contact_name`  varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'emergency contact name',
  `emergency_contact_phone` varchar(255) COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT 'emergency contact phone number',
  `health_issues`           text COLLATE utf8mb4_unicode_520_ci COMMENT 'health issues',
  `pet_question_answers`    json                                                 DEFAULT NULL COMMENT 'custom question answers, question ID to answer',
  `create_time`             datetime                                    NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time`             datetime                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_ob_flow_id` (`business_id`, `booking_flow_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='Abandon user pet info';
