CREATE TABLE `appointment_outbox` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `topic` varchar(255) NOT NULL COMMENT 'topic name',
  `event_id` varchar(128) NOT NULL COMMENT 'id for event record',
  `event_time` datetime NOT NULL COMMENT 'time for event record',
  `event_key` varchar(128) NOT NULL COMMENT 'key for event record',
  `event_type` int NOT NULL COMMENT 'defined in com.moego.idl.models.event_bus.v1.EventType',
  `event_detail` text NOT NULL COMMENT 'detail for event record',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'message push status.1 pending 2 sent 3 failed',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time  ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='store appointment related messages that need to be pushed';