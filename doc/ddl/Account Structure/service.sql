ALTER TABLE `moe_grooming`.`moe_grooming_service`
    ADD COLUMN `is_all_location` tinyint NOT NULL DEFAULT 1;

ALTER TABLE `moe_grooming`.`moe_grooming_service`
    ADD INDEX `company_id`(`company_id` ASC) USING BTREE COMMENT 'company 查询';

CREATE TABLE `moe_grooming`.`moe_grooming_service_location` (
     `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
     `company_id` bigint NOT NULL DEFAULT '0',
     `business_id` int NOT NULL DEFAULT '0' COMMENT '商家id',
     `service_id` int NOT NULL DEFAULT '0',
     `tax_id` int DEFAULT NULL COMMENT '税费id',
     `price` decimal(20,4) DEFAULT NULL COMMENT '服务价格',
     `duration` int DEFAULT NULL COMMENT '服务时间',
     `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `unique_location` (`business_id`,`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



ALTER TABLE `moe_grooming`.`moe_grooming_service_coat_binding`
    MODIFY COLUMN `business_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT 'business id' AFTER `id`;
ALTER TABLE `moe_grooming`.`moe_grooming_service_coat_binding`
DROP INDEX `index_service_business`,
ADD UNIQUE INDEX `index_service_company`(`company_id` ASC, `service_id` ASC) USING BTREE;

ALTER TABLE `moe_grooming`.`moe_grooming_appointment`
  ADD INDEX `company_id_customer_id` (`company_id`,`customer_id`) USING BTREE;

CREATE TABLE `moe_business`.`moe_as_tranfer_record` (
     `id` int unsigned NOT NULL AUTO_INCREMENT,
     `company_id` bigint NOT NULL DEFAULT '0',
     `from_id` bigint NOT NULL DEFAULT '0',
     `to_id` bigint NOT NULL DEFAULT '0',
     `entity_type` varchar(50) NOT NULL DEFAULT '',
     `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
     PRIMARY KEY (`id`),
     KEY `index_query` (`company_id`,`entity_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE `moe_grooming`.`moe_grooming_service`
    ADD INDEX `company_id`(`company_id` ASC) USING BTREE COMMENT 'company 查询';

ALTER TABLE `moe_grooming`.`moe_grooming_service_category`
    ADD INDEX `idx_company_id`(`company_id` ASC) USING BTREE;

