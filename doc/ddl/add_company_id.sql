#ALTER TABLE moe_grooming.moe_book_online_abandon_record ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_abandon_record_pet ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_available_staff ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
ALTER TABLE moe_grooming.moe_book_online_deposit ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0; #依赖 payment
#ALTER TABLE moe_grooming.moe_book_online_gallery ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_landing_page_config ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_landing_page_gallery ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_metrics ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_notification ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_pet_limit ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_pet_limit_breed_binding ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_profile ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_question ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_question_save ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_staff_service ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_book_online_staff_time ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
ALTER TABLE moe_grooming.moe_bookonline_on_show_policy ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;#表没用到，可以直接删除
#ALTER TABLE moe_grooming.moe_business_book_online ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
ALTER TABLE moe_grooming.moe_data_migration_appt ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;#表没用到，可以直接删除
#ALTER TABLE moe_grooming.moe_gc_appt ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_gc_auth_staff ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_gc_calendar ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_gc_event ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_gc_setting ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_gc_watch_event ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_appointment ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_customer_services ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_note ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_online_fee_invoice ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
ALTER TABLE moe_grooming.moe_grooming_package ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;#依赖 retail
#ALTER TABLE moe_grooming.moe_grooming_repeat ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_report ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_report_question ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_report_setting ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_report_template ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_report_resource ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_service_breed_binding ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_service_category ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_service_coat_binding ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_grooming_service_operation ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
ALTER TABLE moe_grooming.moe_invoice_deposit ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;#依赖 payment
#ALTER TABLE moe_grooming.moe_qb_connect ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_setting ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_account ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_customer ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_invoice ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_payment ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_payment_method ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_product ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_service ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_sync_service_charge ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;
#ALTER TABLE moe_grooming.moe_qb_task ADD COLUMN company_id BIGINT NOT NULL DEFAULT 0;

ALTER TABLE `moe_grooming`.`moe_book_online_deposit`
  ADD COLUMN `company_id` bigint NOT NULL DEFAULT 0 AFTER `business_id`,
  ADD INDEX `idx_company_id`(`company_id` ASC) USING BTREE;

ALTER TABLE `moe_grooming`.`moe_grooming_package`
  ADD COLUMN `company_id` bigint NOT NULL DEFAULT 0 AFTER `business_id`,
  ADD INDEX `idx_company_id`(`company_id` ASC) USING BTREE;
