{"arn": "arn:aws:mediaconvert:us-west-2:693727988157:presets/System-Ott_Dash_Mp4_Avc_16x9_960x540p_30Hz_3.5Mbps", "category": "OTT-DASH", "createdAt": 1720828092.0, "description": "OTT, DASH (Wi-Fi), MP4, AVC, 16x9 DAR, 960x540p, 29.97 Hz, 3.5 Mbps CBR", "lastUpdated": 1720828092.0, "name": "System-<PERSON><PERSON>_Dash_Mp4_Avc_16x9_960x540p_30Hz_3.5Mbps", "settings": {"containerSettings": {"container": "MPD"}, "videoDescription": {"afdSignaling": "NONE", "antiAlias": "ENABLED", "codecSettings": {"codec": "H_264", "h264Settings": {"adaptiveQuantization": "HIGH", "bitrate": 3500000, "codecLevel": "LEVEL_4", "codecProfile": "HIGH", "entropyEncoding": "CABAC", "fieldEncoding": "PAFF", "flickerAdaptiveQuantization": "ENABLED", "framerateControl": "SPECIFIED", "framerateConversionAlgorithm": "DUPLICATE_DROP", "framerateDenominator": 1001, "framerateNumerator": 30000, "gopBReference": "ENABLED", "gopClosedCadence": 1, "gopSize": 90.0, "gopSizeUnits": "FRAMES", "hrdBufferInitialFillPercentage": 90, "hrdBufferSize": 7000000, "interlaceMode": "PROGRESSIVE", "minIInterval": 0, "numberBFramesBetweenReferenceFrames": 3, "numberReferenceFrames": 3, "parControl": "SPECIFIED", "parDenominator": 1, "parNumerator": 1, "qualityTuningLevel": "MULTI_PASS_HQ", "rateControlMode": "CBR", "repeatPps": "DISABLED", "sceneChangeDetect": "ENABLED", "slices": 1, "slowPal": "DISABLED", "spatialAdaptiveQuantization": "ENABLED", "syntax": "DEFAULT", "telecine": "NONE", "temporalAdaptiveQuantization": "ENABLED", "unregisteredSeiTimecode": "DISABLED"}}, "colorMetadata": "INSERT", "dropFrameTimecode": "ENABLED", "height": 540, "respondToAfd": "NONE", "scalingBehavior": "DEFAULT", "sharpness": 50, "timecodeInsertion": "DISABLED", "videoPreprocessors": {"deinterlacer": {"algorithm": "INTERPOLATE", "control": "NORMAL", "mode": "DEINTERLACE"}}, "width": 960}}, "type": "SYSTEM"}